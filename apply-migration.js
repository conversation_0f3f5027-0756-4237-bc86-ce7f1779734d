const postgres = require('postgres');
const fs = require('fs');
require('dotenv').config({ path: '.env.local' });

async function applyMigration() {
  const sql = postgres(process.env.DATABASE_URL, {
    ssl: 'require'
  });
  
  try {
    console.log('🔧 Application de la migration pour ajouter email_notifications_enabled...');
    
    // Lire le fichier de migration
    const migrationSQL = fs.readFileSync('migrations/fix-missing-email-notifications-column.sql', 'utf8');
    
    // Exécuter la migration
    await sql.unsafe(migrationSQL);
    
    console.log('✅ Migration appliquée avec succès !');
    
    // Vérifier que la colonne existe maintenant
    const result = await sql`
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'users' 
      AND column_name = 'email_notifications_enabled'
    `;
    
    if (result.length > 0) {
      console.log('✅ Colonne email_notifications_enabled créée avec succès');
    } else {
      console.log('❌ La colonne n\'a pas été créée');
    }
    
  } catch (error) {
    console.error('❌ Erreur lors de l\'application de la migration:', error);
  } finally {
    await sql.end();
  }
}

applyMigration(); 