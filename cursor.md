# Stream2Spin - Documentation Technique Complète

## 🚀 Guide de Démarrage Rapide pour IA

### 🎯 Objectif du Projet
**Stream2Spin** transforme les habitudes d'écoute Spotify en recommandations de vinyles personnalisées. L'application analyse les top tracks des utilisateurs, évite les doublons via Discogs, et propose des liens d'achat affiliés.

### 📋 Checklist de Compréhension
- [ ] **Authentification** : Spotify OAuth uniquement, pas de mots de passe
- [ ] **Algorithme** : Scoring basé sur position dans top tracks (track #1 = 50 points)
- [ ] **Wishlist Persistante** : INDÉPENDANTE des recommandations (données complètes sauvegardées)
- [ ] **Monétisation** : Liens de recherche Amazon avec tag d'affiliation (pas d'API Amazon PAAPI)
- [ ] **Architecture** : Next.js 15 + PostgreSQL + Drizzle ORM
- [ ] **Déploiement** : Vercel avec cron jobs automatiques
- [ ] **Sécurité** : Middleware NextAuth + protection des routes
- [ ] **Performance** : Cache DB + optimisations requêtes
- [ ] **UX** : Glassmorphism + animations + génération interactive
- [x] **Fonctionnalités Sociales** : Suivi, feed, notifications (Epic Social V1 - ✅ DÉPLOYÉ EN STAGING)

### 🚨 ALERTE IMPORTANTE - Wishlist Persistante (Janvier 2025)
⚠️ **RÈGLE CRITIQUE** : La wishlist est TOTALEMENT INDÉPENDANTE des recommandations !

**Ancien bug (CORRIGÉ)** : Les albums disparaissaient de la wishlist avec affichage dégradé
**Nouveau comportement** : Albums PERSISTENT avec toutes leurs données (pochette, liens, player)

```typescript
// ✅ OBLIGATOIRE lors de l'ajout à la wishlist
await db.insert(wishlistItems).values({
  userId,
  artistName,
  albumTitle,
  albumCoverUrl,     // ← CRITIQUE : Pochette persistante
  affiliateLinks,    // ← CRITIQUE : Liens d'achat persistants
  topTrackName,      // ← CRITIQUE : Player persistant
  topTrackId,        // ← CRITIQUE : Player persistant
  // ... TOUS les champs enrichis
});
```

### 🚨 CORRECTION CRITIQUE - Login Epic Social V1 (Janvier 2025)
⚠️ **PROBLÈME RÉSOLU** : Login cassé après implémentation US 11 Epic Social V1 !

**Cause identifiée** : DrizzleAdapter incompatible avec nouvelles colonnes Epic Social V1
- Epic Social V1 a ajouté colonnes `NOT NULL` à table `users`
- `profile_visibility`, `share_recommendations`, `share_wishlist`, `share_collection`
- DrizzleAdapter ne connaît pas ces colonnes → échec insertion utilisateur

**Solution appliquée** : Gestion manuelle création utilisateur dans `auth.ts`
```typescript
// ❌ ANCIEN : DrizzleAdapter cassé avec Epic Social V1
// adapter: DrizzleAdapter(db, { usersTable: users, ... }),

// ✅ NOUVEAU : Gestion manuelle dans callback JWT
if (user && account?.provider === "spotify") {
  // Vérifier si utilisateur existe
  const existingUser = await db.query.users.findFirst({...});
  
  if (!existingUser) {
    // Créer avec TOUTES les colonnes Epic Social V1
    await db.insert(users).values({
      id: user.id,
      name: user.name,
      email: user.email,
      image: user.image,
      
      // ✅ Nouvelles colonnes Epic Social V1
      profileVisibility: 'users_only',
      shareRecommendations: true,
      shareWishlist: true,
      shareCollection: true,
      emailOnNewFollower: true,
      // ... autres colonnes
    });
  }
  
  // Gérer manuellement compte OAuth
  await db.insert(accounts).values({...}).onConflictDoUpdate({...});
}
```

**Impact** :
- ✅ Login Spotify fonctionne avec Epic Social V1
- ✅ Nouveaux utilisateurs créés avec toutes colonnes sociales
- ✅ Valeurs par défaut sécurisées (`users_only`, `true` pour partages)
- ✅ Compatible avec toutes fonctionnalités sociales existantes
- ✅ Utilisateurs existants non impactés

**Test** : https://stream2spin-staging.vercel.app/login

### 🎉 EPIC SOCIAL V1 - FONCTIONNALITÉS SOCIALES COMPLÈTES (Janvier 2025)
✅ **DÉPLOYÉ EN STAGING** : Système social complet avec suivi, feed et notifications

#### 🚀 Nouvelles Fonctionnalités Principales

**1. Système de Suivi** :
- Boutons de suivi sur tous les profils publics
- Feed social avec recommandations des utilisateurs suivis
- Gestion des followers/following avec compteurs en temps réel
- Modales dédiées pour lister followers et following

**2. Profils Publics Enrichis** :
- Contrôle granulaire de visibilité : `private`, `users_only`, `public`
- Paramètres de partage individuels (recommandations, wishlist, collection)
- URLs publiques `/u/[userId]` pour partage externe
- Métriques sociales intégrées (followers, following)

**3. Notifications Système** :
- Notifications push pour nouveaux followers
- Badge de notification dans le header
- Emails automatiques pour events sociaux
- Queue Vercel pour traitement asynchrone

**4. Découverte Utilisateurs** :
- Page sociale dédiée `/social` avec suggestions
- Recherche d'utilisateurs par nom
- Algorithme de suggestions basé sur les goûts musicaux
- Section "Utilisateurs à découvrir" intelligente

#### 📊 Architecture Technique

**Base de Données** :
```sql
-- Nouvelles tables Epic Social V1
CREATE TABLE followers (follower_id, following_id, created_at)
CREATE TABLE notifications (recipient_id, actor_id, type, is_read)
CREATE TABLE recommendation_history (user_id, artist_name, album_title, ...)

-- Nouvelles colonnes users
ALTER TABLE users ADD COLUMN profile_visibility ENUM
ALTER TABLE users ADD COLUMN share_recommendations BOOLEAN
ALTER TABLE users ADD COLUMN share_wishlist BOOLEAN
ALTER TABLE users ADD COLUMN share_collection BOOLEAN
```

**API Routes Nouvelles** :
- `/api/social/followers` - Gestion des suivis
- `/api/social/following` - Liste des abonnements
- `/api/queues/refresh-recommendations` - Queue notifications

**Composants Principaux** :
- `FollowButton.tsx` - Bouton de suivi intelligent
- `SocialFeed.tsx` - Feed des recommandations sociales
- `ProfileSuggestions.tsx` - Suggestions d'utilisateurs
- `NotificationBell.tsx` - Indicateur de notifications
- `SocialStats.tsx` - Métriques sociales

#### 🎨 UX/UI Améliorations

**Modales Redesignées** :
- `AuthModal.tsx` avec raisons contextuelles (follow, wishlist)
- `SocialListModal.tsx` pour followers/following
- Animations et transitions fluides
- Design glassmorphism cohérent

**Navigation Enrichie** :
- Badge notifications dans le header
- Menu social dans la sidebar
- Boutons de suivi intégrés partout
- États de chargement optimisés

#### 🌐 Internationalisation
- Support complet FR/EN pour toutes les fonctionnalités
- Messages contextuels selon l'action (follow, wishlist)
- Emails multilingues automatiques

### 🔧 CORRECTION RÉCENTE - Modale de Login pour Wishlist (Janvier 2025)
✅ **BUG CORRIGÉ** : La modale de login affichait le mauvais message pour la wishlist

**Problème** : Utilisateurs non connectés voyaient "Connectez-vous pour suivre" au lieu de "Connectez-vous pour ajouter à la wishlist"
**Solution** : Ajout du prop `loginReason="wishlist"` dans tous les composants de wishlist

**Fichiers corrigés** :
- `components/public/wishlist-button.tsx`
- `components/public/public-album-card.tsx` 
- `components/public/public-wishlist-card.tsx`
- `components/public/login-modal.tsx` (icônes différenciées + titres centrés)

**Comportement attendu** :
- Clic sur ❤️ (wishlist) → Modale avec icône Heart + détails de l'album + message wishlist
- Clic sur "Suivre" → Modale avec icône UserPlus + profil utilisateur + message suivi
- Bouton "Suivre" → Design glassmorphism uniforme avec icône UserPlus (connecté et non connecté)

### 🚀 AMÉLIORATION - Recherche et Page Sociale (Juillet 2025)
✅ **FONCTIONNALITÉ AMÉLIORÉE** : La recherche d'utilisateurs est maintenant centralisée et la page `/social` a été repensée pour être un véritable hub social.

**1. Recherche d'Utilisateurs dans le Header**
- **Centralisation** : La barre de recherche d'utilisateurs a été déplacée dans le header principal, la rendant accessible depuis n'importe quelle page.
- **Responsive** : Elle est désormais visible et fonctionnelle sur les appareils mobiles.
- **Interaction** : Les résultats de recherche incluent un bouton `Suivre`/`Ne plus suivre` pour des actions rapides.
- **Correction de Bug** : Le bug qui empêchait la navigation en cliquant sur un résultat de recherche a été corrigé de manière robuste.

**2. Refonte de la Page `/social`**
- **Hub Social** : La barre de recherche redondante a été supprimée de la page.
- **Nouvelles Listes** : La page affiche maintenant deux nouvelles listes : "Abonnés" et "Abonnements".
- **Aperçu Rapide** : Chaque liste affiche le compteur total et un aperçu des 5 derniers utilisateurs.
- **Modale "Voir plus"** : Un bouton "Voir plus" ouvre une modale affichant la liste complète des utilisateurs.
- **"Suivre en retour"** : Le bouton d'action propose intelligemment de "Suivre en retour" un abonné que l'utilisateur ne suit pas encore.

**Fichiers modifiés** :
- `components/social/UserSearch.tsx` (fortement refactorisé)
- `components/layout/Header.tsx` (intégration de la recherche)
- `app/social/page.tsx` (nouvelle structure avec les listes)
- `components/social/SocialConnections.tsx` (nouveau composant pour les listes)
- `components/social/FollowButton.tsx` (ajout de la variante "Suivre en retour")
- `app/actions/social.ts` (mise à jour de `searchUsers` et `getFollowers`/`getFollowing`)

**Comportement attendu** :
- Une expérience de recherche unifiée et fonctionnelle dans tout le site.
- Une page `/social` qui sert de tableau de bord pour les connexions de l'utilisateur.

### ✨ AMÉLIORATION CONTINUE - Feed Social (Juillet 2025)
✅ **FONCTIONNALITÉ AMÉLIORÉE** : Le feed social a été optimisé pour la pertinence et l'expérience utilisateur.

**1. Algorithme de Pertinence et Dédoublonnage**
- **Tri par Pertinence** : Le feed n'est plus chronologique. Il est désormais trié par `listenScore` (du plus élevé au plus faible), présentant les albums les plus pertinents en premier.
- **Dédoublonnage Intelligent** : Si plusieurs personnes suivies ont le même album dans leurs recommandations, seul l'album avec le score le plus élevé est affiché, évitant ainsi les répétitions.

**2. Expérience Utilisateur (UX)**
- **Infinite Scroll** : Le bouton "Voir plus" a été remplacé par un défilement infini, permettant une navigation plus fluide et continue.
- **Layout Amélioré** :
    - Le feed affiche un maximum de **deux albums par ligne** sur grand écran pour une meilleure lisibilité.
    - La colonne de droite (Abonnés/Abonnements) est maintenant **fixe au scroll** sur la vue desktop, gardant les informations de connexion toujours visibles.
    - Le cadre et le titre "Feed Social" ont été retirés pour un design plus épuré et intégré.
- **Texte Contextuel** : Le label "Votre titre phare" a été rendu dynamique. Il affiche maintenant "Titre phare de {Nom de l'utilisateur}" dans le contexte du feed social, ce qui est plus clair pour l'utilisateur.

**3. Fiabilité**
- **Correction de Bugs 500** : Une série de bugs critiques causant des erreurs 500 sur les pages `/social` et `/recommendations` a été résolue. Le problème venait d'une mauvaise utilisation des hooks de traduction et de requêtes Drizzle incorrectes dans le composant partagé `AlbumCard`.

**Fichiers modifiés** :
- `app/actions/social.ts` (nouvelle logique de tri et de dédoublonnage)
- `components/social/SocialFeed.tsx` (implémentation de l'infinite scroll et du nouveau layout)
- `app/social/page.tsx` (ajustements du layout et de la colonne sticky)
- `components/recommendations/album-card.tsx` (correction des bugs et ajout du texte contextuel)
- `messages/*.json` (ajout des nouvelles clés de traduction)

### ⚡️ US-07 : Rafraîchissement Asynchrone "Juste-à-Temps" (Juillet 2025)
✅ **FONCTIONNALITÉ IMPLÉMENTÉE** : Un système de rafraîchissement intelligent a été mis en place pour garantir la fraîcheur des données du feed social sans gaspiller de ressources.

**1. Principe de Fonctionnement**
- **Déclenchement** : Lorsqu'un utilisateur visite son feed social (`/social`), le système détecte automatiquement les profils qu'il suit dont les recommandations datent de plus de 24 heures.
- **Mise en File d'Attente** : Pour chaque profil "périmé", un job est ajouté à une file d'attente asynchrone gérée par **Upstash QStash** (via l'intégration Vercel KV). Cette opération est instantanée et ne ralentit pas la navigation de l'utilisateur.
- **Traitement en Arrière-Plan** : Un "worker" (une API route sécurisée) est appelé par QStash pour traiter chaque job. Le worker exécute la logique complète de génération de recommandations pour l'utilisateur concerné, mettant à jour ses données en base.

**2. Avantages**
- **Efficacité** : Les ressources serveur ne sont utilisées que pour les utilisateurs dont les données sont réellement consultées, évitant des cron jobs coûteux pour les milliers d'utilisateurs inactifs.
- **Fraîcheur des Données** : Le contenu du feed social reste pertinent et à jour pour les utilisateurs actifs.
- **Robustesse** : Le système de file d'attente garantit que les rafraîchissements seront effectués, même en cas d'échec temporaire (avec des tentatives automatiques).

**Fichiers créés/modifiés** :
- `lib/queue.ts` (configuration du client QStash)
- `app/api/queues/refresh-recommendations/route.ts` (worker de traitement des jobs)
- `app/actions/social.ts` (ajout de la logique de détection et de mise en file d'attente dans `getSocialFeed`)
- `package.json` (ajout de `@vercel/kv` et `@upstash/qstash`)
- `.env.local` (nécessite de nouvelles variables d'environnement pour QStash)

### ✨ AMÉLIORATION UX - Cohérence des Filtres (Juillet 2025)
✅ **FONCTIONNALITÉ AMÉLIORÉE** : L'affichage des filtres actifs a été rendu cohérent sur l'ensemble de l'application pour une meilleure expérience utilisateur.

**1. Problème**
- Les "tags" indiquant les filtres actifs (période d'écoute, etc.) n'étaient visibles que lorsque la section des filtres était repliée.
- En ouvrant les filtres, l'utilisateur perdait le contexte des filtres actuellement appliqués.

**2. Solution Implémentée**
- La logique d'affichage des tags de filtres actifs a été extraite dans un composant `ActiveFilterTags` au sein des fichiers concernés.
- Ce composant est maintenant affiché en permanence dans l'en-tête de la barre de filtres, que celle-ci soit ouverte ou fermée.

**3. Comportement Attendu**
- L'utilisateur voit toujours les filtres qui sont appliqués, améliorant la clarté et l'ergonomie de l'interface.
- Le comportement est désormais uniforme sur la page de recommandations personnelles (`/recommendations`) et sur les pages de profil public (`/u/[publicListId]`).

**Fichiers modifiés** :
- `app/recommendations/RecommendationsClient.tsx`
- `components/public/public-recommendations-tab.tsx`

### 🔧 Setup Rapide pour Développement
```bash
# 1. Cloner et installer
git clone <repo>
cd Stream2Spin
pnpm install

# 2. Variables d'environnement minimales
cp .env.example .env.local
# Remplir: DATABASE_URL, NEXTAUTH_SECRET, AUTH_SPOTIFY_ID, AUTH_SPOTIFY_SECRET

# 3. Base de données
pnpm db:migrate

# 4. Lancer
pnpm dev
```

### 🎵 Workflow Principal
1. **Connexion Spotify** → Récupération top tracks (3 timeframes)
2. **Analyse algorithmique** → Scoring par position + estimation écoutes
3. **Croisement Discogs** → Évitement des doublons
4. **Génération liens** → Liens de recherche Amazon avec tag d'affiliation
5. **Sauvegarde DB** → Recommandations avec liens affiliés
6. **Notifications** → Emails hebdomadaires + push

## Vue d'ensemble

**Stream2Spin** est une application web Next.js qui analyse les habitudes d'écoute Spotify des utilisateurs pour leur recommander des vinyles correspondant à leurs goûts musicaux. L'application croise les données Spotify avec les collections Discogs pour éviter les doublons et propose des liens d'achat affiliés.

### Concept Principal
- **Analyse des données Spotify** : Récupération des top tracks sur différentes périodes (court, moyen, long terme)
- **Algorithme de scoring** : Calcul de scores basés sur la fréquence d'écoute et la position dans les classements
- **Évitement des doublons** : Croisement avec les collections Discogs pour ne pas recommander des albums déjà possédés
- **Monétisation** : Liens de recherche Amazon avec tag d'affiliation (API Amazon PAAPI non utilisée actuellement)
- **Expérience utilisateur** : Interface moderne avec glassmorphism et animations

## Architecture Technique

### 🏗️ Stack Technologique

```typescript
// 🎯 Technologies principales
- Next.js 15 avec App Router (SSR + SSG)
- React 19 avec Server Components (performance optimale)
- TypeScript strict pour la sécurité des types
- Tailwind CSS avec design system glassmorphism
- PostgreSQL + Drizzle ORM (type-safe queries)
- NextAuth.js v5 pour l'authentification OAuth
- Vercel pour le déploiement serverless
- Supabase pour l'hébergement PostgreSQL

// 🔌 Intégrations API externes
- Spotify Web API (OAuth 2.0) - Top tracks + user profile
- Discogs API (OAuth 1.0a) - Collection synchronization
- Amazon Affiliate Links - Liens de recherche avec tag d'affiliation (pas d'API PAAPI)
- Resend pour les emails transactionnels
- Firebase pour les notifications push (PWA)

// 🎨 UI/UX Framework
- Radix UI pour les composants accessibles
- Lucide React pour les icônes cohérentes
- Framer Motion pour les animations fluides
- Next-intl pour l'internationalisation (FR/EN)
- Zustand pour la gestion d'état client
- Sonner pour les notifications toast
```

### 📊 Architecture des Données

```mermaid
graph TD
    A[Spotify API] --> B[Top Tracks Analysis]
    C[Discogs API] --> D[Collection Sync]
    B --> E[Scoring Algorithm]
    D --> F[Duplicate Detection]
    E --> G[Recommendations DB]
    F --> G
    G --> H[Amazon Search Links]
    H --> I[Affiliate Links]
    I --> J[User Interface]
    G --> K[Email Notifications]
    G --> L[Push Notifications]
```

### Structure du Projet

```
Stream2Spin/
├── app/                        # App Router Next.js 15
│   ├── (auth)/                # Groupe de routes d'authentification
│   │   └── login/
│   ├── account/               # Gestion du compte utilisateur
│   ├── api/                   # Routes API et endpoints
│   │   ├── auth/             # NextAuth.js callbacks
│   │   ├── cron/             # Tâches automatisées
│   │   ├── discogs/          # Intégration Discogs
│   │   ├── recommendations/  # Gestion des recommandations
│   │   └── user/             # Actions utilisateur
│   ├── recommendations/       # Page principale des recommandations
│   ├── collection/           # Collection Discogs de l'utilisateur
│   ├── wishlist/             # Liste d'envies
│   ├── generating/           # Page de génération interactive
│   ├── public/               # Profils publics (/public/[id])
│   ├── u/                    # Profils publics (/u/[id])
│   └── error/                # Pages d'erreur
├── components/               # Composants React réutilisables
│   ├── account/             # Composants de gestion de compte
│   ├── auth/                # Composants d'authentification
│   ├── audio/               # Lecteur audio global
│   ├── error/               # Gestion d'erreurs
│   ├── generating/          # Expérience de génération
│   ├── layout/              # Layout et navigation
│   ├── public/              # Composants pour profils publics
│   ├── recommendations/     # Composants de recommandations
│   ├── ui/                  # Composants UI de base
│   └── wishlist/            # Composants de wishlist
├── lib/                     # Utilitaires et logique métier
│   ├── db/                  # Configuration et schémas DB
│   ├── album-matching.ts    # Algorithme de matching albums
│   ├── spotify.ts           # Intégration Spotify API
│   ├── auth.ts              # Utilitaires d'authentification
│   ├── amazon-phase1.ts     # Génération liens de recherche Amazon
│   ├── resend.ts            # Gestion des emails
│   └── analytics.ts         # Analytics pour profils publics
├── migrations/              # Scripts de migration DB
├── messages/               # Fichiers de traduction i18n
├── emails/                 # Templates d'email React
│   ├── welcome-email.tsx       # Email de bienvenue
│   └── recommendations-email.tsx  # Email de recommandations
├── components/emails/      # Composants d'email partagés
│   └── EmailHeader.tsx         # Header standard pour tous les emails
└── scripts/                # Scripts de maintenance et sécurité
```

## Base de Données (PostgreSQL + Drizzle ORM)

### 🗄️ Schéma Principal

```typescript
// 📋 Table users - Utilisateurs principaux
export const users = pgTable('users', {
  id: text('id').primaryKey().$defaultFn(() => crypto.randomUUID()),
  name: text('name'),                    // Nom depuis Spotify
  email: text('email').unique(),         // Email depuis Spotify
  emailVerified: timestamp('emailVerified', { mode: 'date' }),
  image: text('image'),                  // Photo de profil Spotify
  preferredLanguage: text('preferredLanguage').default('fr').notNull(),
  emailFrequency: text('emailFrequency').default('weekly').notNull(), // 'weekly' | 'bi-weekly' | 'monthly' | 'never'
  pushFrequency: text('pushFrequency').default('weekly').notNull(),
  firstRecommendationEmailSent: boolean('firstRecommendationEmailSent').default(false),
  
  // 🌐 Epic 16: Partage public des recommandations
  publicListEnabled: boolean('publicListEnabled').default(false).notNull(),
  publicListId: text('publicListId').unique().$defaultFn(() => crypto.randomUUID()),
  
  // 👤 Epic 17: Profil public granulaire
  publicProfileEnabled: boolean('publicProfileEnabled').default(true).notNull(),
  publicRecommendationsEnabled: boolean('publicRecommendationsEnabled').default(true).notNull(),
  publicWishlistEnabled: boolean('publicWishlistEnabled').default(false).notNull(),
  publicCollectionEnabled: boolean('publicCollectionEnabled').default(false).notNull(),
  
  // 📧 Tracking de délivrabilité des emails
  emailNotificationsEnabled: boolean('email_notifications_enabled').default(true),
  lastEmailSent: timestamp('last_email_sent', { mode: 'date' }),
  lastEmailBounce: timestamp('last_email_bounce', { mode: 'date' }),
  emailBounceReason: text('email_bounce_reason'),
  emailDeliverabilityScore: integer('email_deliverability_score').default(100),
  
  timezone: text('timezone').default('Europe/Paris'),
  createdAt: timestamp('createdAt', { mode: 'date' }).defaultNow(),
  updatedAt: timestamp('updatedAt', { mode: 'date' }).defaultNow(),
});

// 🎵 Table recommendations - Recommandations d'albums
export const recommendations = pgTable('recommendations', {
  id: bigserial('id', { mode: 'number' }).primaryKey(),
  userId: text('userId').notNull().references(() => users.id, { onDelete: 'cascade' }),
  artistName: text('artistName').notNull(),
  albumTitle: text('albumTitle').notNull(),
  albumCoverUrl: text('albumCoverUrl'),
  discogsReleaseId: bigint('discogsReleaseId', { mode: 'number' }),
  spotifyAlbumId: text('spotifyAlbumId'),
  listenScore: integer('listenScore').notNull(),        // Score calculé par l'algorithme
  estimatedPlays: integer('estimatedPlays'),            // Nombre d'écoutes estimé basé sur la position
  timeframe: text('timeframe').notNull(),               // 'short_term' | 'medium_term' | 'long_term'
  generatedAt: timestamp('generatedAt', { mode: 'date' }).notNull().defaultNow(),
  isOwned: boolean('isOwned').notNull().default(false), // Marqué via croisement Discogs
  affiliateLinks: jsonb('affiliateLinks'),              // [{vendor, url, price, currency, merchantId}]
  
  // 🎧 US 3.6: Informations du titre phare pour l'extrait audio
  topTrackName: text('topTrackName'),
  topTrackId: text('topTrackId'),                      // ID Spotify pour l'embed
  topTrackPreviewUrl: text('topTrackPreviewUrl'),      // URL de preview 30s
  topTrackListenScore: integer('topTrackListenScore'), // Score du titre phare
}, (table) => ({
  userAlbumTimeframeUnique: unique().on(table.userId, table.spotifyAlbumId, table.timeframe),
}));

// 💿 Table userDiscogsCollection - Collections Discogs synchronisées
export const userDiscogsCollection = pgTable('user_discogs_collection', {
  userId: text('userId').notNull().references(() => users.id, { onDelete: 'cascade' }),
  discogsReleaseId: bigint('discogsReleaseId', { mode: 'number' }).notNull(),
  artistName: text('artistName').notNull(),
  albumTitle: text('albumTitle').notNull(),
  year: integer('year'),
  format: text('format'),                              // 'Vinyl', 'CD', etc.
  notes: text('notes'),                                // Notes utilisateur Discogs
  folder: text('folder'),                              // ID du dossier Discogs
  syncedAt: timestamp('syncedAt', { mode: 'date' }).notNull().defaultNow(),
}, (table) => ({
  pk: primaryKey({ columns: [table.userId, table.discogsReleaseId] }),
}));

// ❤️ Table wishlistItems - Liste d'envies PERSISTANTE (Janvier 2025)
// ⚠️ CRITIQUE: INDÉPENDANTE des recommandations - Données complètes sauvegardées
export const wishlistItems = pgTable('wishlist_items', {
  userId: text('userId').notNull().references(() => users.id, { onDelete: 'cascade' }),
  artistName: text('artistName').notNull(),
  albumTitle: text('albumTitle').notNull(),
  albumCoverUrl: text('album_cover_url'),                  // ← PERSISTANT: Pochette d'album
  spotifyAlbumId: text('spotify_album_id'),                // ← PERSISTANT: ID Spotify
  discogsReleaseId: bigint('discogs_release_id', { mode: 'number' }),
  affiliateLinks: jsonb('affiliate_links'),               // ← PERSISTANT: Liens d'achat Amazon
  topTrackName: text('top_track_name'),                   // ← PERSISTANT: Player Spotify
  topTrackId: text('top_track_id'),                       // ← PERSISTANT: Player Spotify
  topTrackPreviewUrl: text('top_track_preview_url'),      // ← PERSISTANT: Player Spotify
  topTrackListenScore: integer('top_track_listen_score'), // ← PERSISTANT: Score de la track
  originalUserName: text('original_user_name'),          // Pour les ajouts depuis profils publics
  createdAt: timestamp('createdAt', { mode: 'date' }).notNull().defaultNow(),
}, (table) => ({
  // ⚠️ PAS de foreign key vers recommendations - Totalement indépendant !
  pk: primaryKey({ columns: [table.userId, table.artistName, table.albumTitle] }),
}));

// 📊 Table publicListAnalytics - Analytics pour profils publics
export const publicListAnalytics = pgTable('public_list_analytics', {
  id: text('id').primaryKey().$defaultFn(() => crypto.randomUUID()),
  userId: text('userId').notNull().references(() => users.id, { onDelete: 'cascade' }),
  publicListId: text('publicListId').notNull().references(() => users.publicListId),
  eventType: text('eventType').notNull(),              // 'view' | 'share' | 'signup_click' | 'signup_conversion'
  eventData: jsonb('eventData'),                       // Données supplémentaires (tab, timeframe, etc.)
  ipAddress: text('ipAddress'),                        // Pour éviter les doublons de vues
  userAgent: text('userAgent'),
  referrer: text('referrer'),
  timestamp: timestamp('timestamp', { mode: 'date' }).defaultNow().notNull(),
});

// 👥 Epic Social V1: Fonctionnalités sociales (OPÉRATIONNEL)
export const followers = pgTable('followers', {
  followerId: text('follower_id').notNull().references(() => users.id, { onDelete: 'cascade' }),
  followingId: text('following_id').notNull().references(() => users.id, { onDelete: 'cascade' }),
  createdAt: timestamp('created_at', { mode: 'date' }).defaultNow().notNull(),
}, (table) => ({
  pk: primaryKey({ columns: [table.followerId, table.followingId] }),
}));

export const notifications = pgTable('notifications', {
  id: text('id').primaryKey().$defaultFn(() => crypto.randomUUID()),
  recipientId: text('recipient_id').notNull().references(() => users.id, { onDelete: 'cascade' }),
  actorId: text('actor_id').notNull().references(() => users.id, { onDelete: 'cascade' }),
  type: notificationTypeEnum('type').notNull(),  // 'new_follower'
  isRead: boolean('is_read').default(false).notNull(),
  createdAt: timestamp('created_at', { mode: 'date' }).defaultNow().notNull(),
});

export const recommendationHistory = pgTable('recommendation_history', {
  id: bigserial('id', { mode: 'number' }).primaryKey(),
  userId: text('user_id').notNull().references(() => users.id, { onDelete: 'cascade' }),
  artistName: text('artist_name').notNull(),
  albumTitle: text('album_title').notNull(),
  spotifyAlbumId: text('spotify_album_id'),
  listenScore: integer('listen_score').notNull(),
  timeframe: text('timeframe').notNull(),
  generatedAt: timestamp('generated_at', { mode: 'date' }).notNull().defaultNow(),
});
```

## 👥 Fonctionnalités Sociales (Epic Social V1)

### 🎯 Vue d'ensemble

Les fonctionnalités sociales transforment Stream2Spin en plateforme communautaire où les utilisateurs peuvent se connecter, partager leurs découvertes musicales et découvrir de nouveaux profils.

### 🏗️ Architecture Sociale

```typescript
// Tables principales
- followers: Relations de suivi entre utilisateurs
- notifications: Notifications in-app et email  
- recommendation_history: Archive pour algorithme de matching
- users: Colonnes de visibilité (profile_visibility, share_*)
```

### 🔧 Composants Disponibles

```typescript
// app/actions/social.ts - Actions serveur
- followUser(targetUserId): Promise<Result>
- unfollowUser(targetUserId): Promise<Result>
- getFollowers(targetUserId): Promise<User[]>
- getFollowing(targetUserId): Promise<User[]>
- getSocialFeed(page?: number): Promise<FeedItem[]>
- getProfileSuggestions(): Promise<ProfileSuggestion[]>

// components/social/ - Composants UI
- FollowButton.tsx: Bouton de suivi avec optimistic UI
- SocialStats.tsx: Affichage des compteurs followers/following
- SocialListModal.tsx: Modal avec listes de followers/following
- SocialFeed.tsx: Feed des recommandations suivies
```

### 📊 Paramètres de Visibilité

```typescript
// Niveaux de visibilité du profil
profile_visibility: 'private' | 'users_only' | 'public'

// Contrôles granulaires de partage
share_recommendations: boolean    // Feed social
share_wishlist: boolean          // Profil public  
share_collection: boolean        // Profil public
```

### 🚀 Fonctionnalités Implémentées

- ✅ **Suivi d'utilisateurs** : Follow/unfollow avec optimistic UI
- ✅ **Modales sociales** : Listes followers/following interactives
- ✅ **Feed social** : Recommandations des utilisateurs suivis
- ✅ **Profils publics** : Pages `/u/[id]` avec paramètres de visibilité
- ✅ **Notifications** : System in-app et email pour nouveaux followers
- ✅ **Suggestions** : Algorithme de recommandation de profils
- ✅ **Base de données** : Tables créées et opérationnelles
- ✅ **Filtrage intelligent** : Les utilisateurs non connectés ne voient que les profils publics dans les modales
- ✅ **Gestion des non-connectés** : Bouton "Suivre" ouvre la modale de connexion appropriée
- ✅ **Correction du chargement** : Les modales se chargent correctement pour tous les utilisateurs
- ✅ **Migration de données** : Synchronisation `profileVisibility` avec `publicListEnabled`
- ✅ **🐛 Correction du contexte de la modale** : La modale d'authentification affiche les bonnes informations selon l'action pour la cohérence

### 🔧 Migrations Critiques

**Migration 0003** : Synchronisation des données de visibilité
- **Fichier** : `lib/db/migrations/0003_sync_profile_visibility.sql`
- **Objectif** : Synchroniser `profileVisibility` avec `publicListEnabled`
- **Logique** : `publicListEnabled: true → profileVisibility: 'public'` et `publicListEnabled: false → profileVisibility: 'internal'`
- **Impact** : Résout l'incohérence entre l'interface utilisateur et la base de données
- **Critique** : Doit être exécutée avant le déploiement en staging/production

### 🐛 Corrections de Bugs

- **Bug de contexte de la modale** : La modale d'authentification affichait les informations du mauvais utilisateur
- **Solution** : Store Zustand global avec données contextuelles (`store/auth-modal.ts`)
- **Cas supportés** : Wishlist (album), Follow principal (propriétaire), Follow imbriqué (utilisateur ciblé)

### 📋 Routes Sociales

```typescript
// Pages
/social                    // Feed social principal
/u/[publicListId]         // Profils publics

// API
/api/social/follow        // Actions de suivi
/api/social/notifications // Gestion notifications
/api/social/feed          // Récupération feed paginé
```

## 🧠 Algorithme de Recommandation

### 🎯 Vue d'ensemble de l'Algorithme

L'algorithme de recommandation de Stream2Spin fonctionne en 4 étapes principales :

1. **Récupération des données Spotify** (3 timeframes)
2. **Calcul des scores par position** (track #1 = 50 points)
3. **Croisement avec collection Discogs** (évitement des doublons)
4. **Enrichissement avec liens d'achat** (Amazon uniquement)

### 📊 Formule de Scoring

```typescript
// Score = 51 - position (track #1 = 50 points, track #50 = 1 point)
const trackScore = 51 - position;

// Estimation des écoutes basée sur la position
const estimatedPlaysForTrack = Math.max(1, Math.round((51 - position) * 2.5));

// Score total d'un album = somme des scores de tous ses tracks
// Albums avec plusieurs tracks du même artiste ont un score plus élevé
```

### 1. Récupération des Données Spotify

```typescript
/**
 * Récupère les top tracks d'un utilisateur via l'API Spotify
 * 3 timeframes: short_term (4 semaines), medium_term (6 mois), long_term (toute la vie)
 */
export async function fetchUserTopTracks(
  accessToken: string,
  timeRange: "short_term" | "medium_term" | "long_term" = "short_term",
  limit: number = 50
): Promise<SpotifyTrack[] | null> {
  const url = `https://api.spotify.com/v1/me/top/tracks?limit=${limit}&time_range=${timeRange}`;
  
  const response = await fetch(url, {
    headers: {
      "Authorization": `Bearer ${accessToken}`,
      "Content-Type": "application/json",
    },
  });

  if (!response.ok) {
    throw new Error(`Erreur API Spotify: ${response.status}`);
  }

  const data: SpotifyTopTracksResponse = await response.json();
  return data.items || [];
}

### 2. Calcul des Scores d'Albums

```typescript
/**
 * 🧮 Algorithme de scoring des albums basé sur les top tracks Spotify
 * 
 * Principe: Plus un track est haut dans le classement, plus il contribue au score de l'album
 * - Track #1 = 50 points, Track #50 = 1 point
 * - Albums avec plusieurs tracks du même artiste ont un score cumulé
 * - Sélection du "titre phare" pour l'extrait audio
 */
export function analyzeTracksAndCalculateScores(tracks: SpotifyTrack[], timeframe: string) {
  const albumScores = new Map<string, any>();

  tracks.forEach((track, index) => {
    const album = track.album;
    if (!album) return;

    const albumKey = album.id;
    const position = index + 1; // Position dans le classement (1-50)

    // 🎯 Calcul du score : plus la position est haute, plus le score est élevé
    const trackScore = 51 - position; // Track #1 = 50 points, Track #50 = 1 point

    // 📊 Estimation du nombre d'écoutes basée sur la position
    const estimatedPlaysForTrack = Math.max(1, Math.round((51 - position) * 2.5));

    if (albumScores.has(albumKey)) {
      // Album déjà présent, additionner le score et les écoutes estimées
      const existingAlbum = albumScores.get(albumKey);
      existingAlbum.listenScore += trackScore;
      existingAlbum.estimatedPlays += estimatedPlaysForTrack;

      // 🎧 Stratégie pour le titre phare (meilleur score + preview URL)
      const shouldUpdateTopTrack =
        // Cas 1: Ce track a un meilleur score
        trackScore > (existingAlbum.topTrackListenScore || 0) ||
        // Cas 2: Score égal mais ce track a une preview_url et l'actuel n'en a pas
        (trackScore === (existingAlbum.topTrackListenScore || 0) &&
         track.preview_url && !existingAlbum.topTrackPreviewUrl) ||
        // Cas 3: L'actuel n'a pas de preview_url et ce track en a une
        (!existingAlbum.topTrackPreviewUrl && track.preview_url);

      if (shouldUpdateTopTrack) {
        existingAlbum.topTrackName = track.name;
        existingAlbum.topTrackId = track.id;
        existingAlbum.topTrackPreviewUrl = track.preview_url;
        existingAlbum.topTrackListenScore = trackScore;
      }
    } else {
      // Nouvel album
      albumScores.set(albumKey, {
        spotifyAlbumId: album.id,
        artistName: album.artists[0]?.name || "Unknown Artist",
        albumTitle: album.name,
        albumCoverUrl: album.images[0]?.url || null,
        listenScore: trackScore,
        estimatedPlays: estimatedPlaysForTrack,
        timeframe: timeframe,
        // 🎧 Titre phare (premier track de cet album)
        topTrackName: track.name,
        topTrackId: track.id,
        topTrackPreviewUrl: track.preview_url,
        topTrackListenScore: trackScore,
      });
    }
  });

  // 🏆 Retourner les 50 meilleurs albums triés par score décroissant
  return Array.from(albumScores.values())
    .sort((a, b) => b.listenScore - a.listenScore)
    .slice(0, 50);
}
```

### 3. Croisement avec les Collections Discogs

```typescript
/**
 * Marque les recommandations comme possédées en croisant avec la collection Discogs
 */
export function markOwnedAlbums(
  recommendations: SpotifyRecommendation[],
  discogsCollection: DiscogsCollectionItem[]
): (SpotifyRecommendation & { isOwned: boolean })[] {
  // Créer un Set de clés normalisées pour la collection Discogs (recherche O(1))
  const normalizedCollection = new Set<string>();
  
  discogsCollection.forEach(item => {
    const normalizedArtist = normalizeArtistName(item.artistName);
    const normalizedAlbum = normalizeAlbumTitle(item.albumTitle);
    const exactKey = `${normalizedArtist}|${normalizedAlbum}`;
    normalizedCollection.add(exactKey);
  });

  // Marquer chaque recommandation comme possédée ou non
  return recommendations.map(recommendation => {
    const normalizedSpotifyArtist = normalizeArtistName(recommendation.artistName);
    const normalizedSpotifyAlbum = normalizeAlbumTitle(recommendation.albumTitle);
    const exactKey = `${normalizedSpotifyArtist}|${normalizedSpotifyAlbum}`;
    
    return {
      ...recommendation,
      isOwned: normalizedCollection.has(exactKey)
    };
  });
}
```

## 🔐 Système d'Authentification

### 🎯 Stratégie d'Authentification

Stream2Spin utilise **Spotify OAuth uniquement** comme méthode d'authentification :
- **Pas de mots de passe** - sécurité maximale
- **Données utilisateur** récupérées automatiquement depuis Spotify
- **Tokens gérés** automatiquement par NextAuth.js
- **Sessions JWT** pour les performances

### 🎵 Authentification Spotify Standard

**Approche** : Authentification OAuth standard via NextAuth.js conforme à la documentation Spotify officielle.

**Fonctionnalités** :
- **Flux unique** : Authorization Code avec PKCE pour toutes les plateformes
- **Expérience unifiée** : Même flux sur desktop et mobile via navigateur
- **Sécurité optimale** : Implémentation selon les meilleures pratiques OAuth 2.0
- **Support complet** : Compatible avec tous les navigateurs et appareils

**Architecture** :
- NextAuth.js gère automatiquement l'OAuth avec Spotify
- `SpotifyProvider` configuré avec les paramètres recommandés
- Callbacks automatiques via `/api/auth/callback/spotify`
- Session JWT pour les performances

### Configuration NextAuth.js

```typescript
// auth.ts
export const authOptions: AuthOptions = {
  adapter: DrizzleAdapter(db, {
    usersTable: users,
    accountsTable: accounts,
    sessionsTable: sessions,
    verificationTokensTable: verificationTokens,
  }),
  session: { strategy: "jwt" },
  pages: {
    signIn: "/login",
  },
  providers: [
    SpotifyProvider({
      clientId: process.env.AUTH_SPOTIFY_ID!,
      clientSecret: process.env.AUTH_SPOTIFY_SECRET!,
      authorization: {
        params: {
          scope: "user-read-email user-top-read user-read-private",
        },
      },
      allowDangerousEmailAccountLinking: true, // Permet la liaison automatique des comptes
      profile(profile) {
        return {
          id: profile.id,
          name: profile.display_name,
          email: profile.email,
          image: profile.images?.[0]?.url || null,
        };
      },
    }),
  ],
  callbacks: {
    async jwt({ token, user }) {
      if (user) {
        token.sub = user.id;
        token.name = user.name;
        token.email = user.email;
        token.picture = user.image;
      }
      return token;
    },
    async session({ session, token }) {
      if (token && session.user) {
        session.user.id = token.sub!;
        
        // 🚀 Récupérer les données fraîches depuis la DB avec cache optimisé
        const userData = getCachedUserQuery(token.sub!, 'profile');
        if (userData) {
          session.user.name = userData.name;
          session.user.email = userData.email;
          session.user.image = userData.image;
        }
      }
      return session;
    },
  },
};
```

### Gestion des Tokens Spotify

```typescript
/**
 * Obtient un access token Spotify valide pour un utilisateur
 * Rafraîchit automatiquement le token si nécessaire
 */
export async function getValidSpotifyToken(userId: string): Promise<string | null> {
  const spotifyAccount = await db.query.accounts.findFirst({
    where: and(
      eq(accounts.userId, userId),
      eq(accounts.provider, "spotify")
    ),
  });

  if (!spotifyAccount?.refresh_token) {
    return null;
  }

  // Vérifier si le token actuel est encore valide
  const now = Math.floor(Date.now() / 1000);
  if (spotifyAccount.expires_at && spotifyAccount.expires_at > now && spotifyAccount.access_token) {
    return spotifyAccount.access_token;
  }

  // Token expiré, le rafraîchir
  const refreshedTokens = await refreshSpotifyToken(spotifyAccount.refresh_token);
  if (!refreshedTokens) {
    return null;
  }

  // Mettre à jour les tokens en base
  await db.update(accounts)
    .set({
      access_token: refreshedTokens.access_token,
      expires_at: refreshedTokens.expires_at,
      refresh_token: refreshedTokens.refresh_token || spotifyAccount.refresh_token,
    })
    .where(and(
      eq(accounts.userId, userId),
      eq(accounts.provider, "spotify")
    ));

  return refreshedTokens.access_token;
}
```

## Intégrations API Externes

### 1. Discogs API (OAuth 1.0a)

```typescript
/**
 * Synchronise la collection Discogs d'un utilisateur
 */
export async function syncDiscogsCollection(userId: string): Promise<SyncResult> {
  const discogsAccount = await db.query.accounts.findFirst({
    where: and(
      eq(accounts.userId, userId),
      eq(accounts.provider, "discogs")
    ),
  });

  if (!discogsAccount) {
    return { success: false, error: "Compte Discogs non trouvé" };
  }

  // Récupérer l'identité utilisateur Discogs
  const identityResponse = await fetch("https://api.discogs.com/oauth/identity", {
    headers: {
      "Authorization": `OAuth ${buildOAuthHeader(discogsAccount)}`,
      "User-Agent": "Stream2Spin/1.0",
    },
  });

  const identity = await identityResponse.json();
  
  // Récupérer la collection complète
  const collection = await fetchFullDiscogsCollection(identity.username, discogsAccount);

  // Nettoyer l'ancienne collection
  await db.delete(userDiscogsCollection).where(eq(userDiscogsCollection.userId, userId));

  // Insérer la nouvelle collection
  if (collection.length > 0) {
    await db.insert(userDiscogsCollection).values(
      collection.map(item => ({
        userId,
        discogsReleaseId: item.id,
        artistName: item.basic_information.artists[0]?.name || "Unknown Artist",
        albumTitle: item.basic_information.title,
        year: item.basic_information.year,
        format: item.basic_information.formats[0]?.name || "Unknown",
        notes: item.notes,
        folder: item.folder_id.toString(),
      }))
    );
  }

  return { success: true, itemCount: collection.length };
}
```

### 2. Liens de Recherche Amazon (Phase 1)

```typescript
/**
 * 🛒 Génération de liens de recherche Amazon avec tag d'affiliation
 * 
 * Note: Pas d'utilisation de l'API Amazon PAAPI actuellement
 * Les liens redirigent vers la page de recherche Amazon avec le tag d'affiliation
 */
export function buildAmazonSearchLink(artistName: string, albumTitle: string): string | null {
  const partnerTag = process.env.AMAZON_AFFILIATE_TAG;
  
  if (!partnerTag) {
    console.error("❌ Amazon Partner Tag is not configured");
    return null;
  }

  try {
    // Construire le mot-clé de recherche optimisé pour les vinyles
    const keyword = `${artistName} ${albumTitle} vinyl`;
    
    // Encoder le mot-clé pour l'URL
    const encodedKeyword = encodeURIComponent(keyword).replace(/%20/g, '+');
    
    // Construire l'URL de recherche Amazon avec le tag d'affiliation
    const searchUrl = `https://www.amazon.fr/s?k=${encodedKeyword}&tag=${partnerTag}`;
    
    console.log(`🔗 Lien de recherche Amazon généré: "${keyword}" -> ${searchUrl}`);
    
    return searchUrl;
    
  } catch (error) {
    console.error("❌ Erreur lors de la construction du lien Amazon:", error);
    return null;
  }
}
```

### 3. Rakuten (Désactivé)

```typescript
/**
 * ⚠️ Rakuten est actuellement désactivé dans l'application
 * 
 * Le code Rakuten existe mais n'est pas utilisé :
 * - lib/rakuten.ts contient l'implémentation
 * - lib/rakuten-auth.ts pour l'authentification
 * - Peut être supprimé si non nécessaire
 */
export async function searchVinylOffers(artistName: string, albumTitle: string): Promise<RakutenOffer[]> {
  // Rakuten désactivé - retourner un tableau vide
  console.log(`🚫 Rakuten désactivé pour: "${artistName}" - "${albumTitle}"`);
  return [];
}
```

## Fonctionnalités Principales

### 1. Génération Interactive des Recommandations

```typescript
/**
 * API Route SSE pour la génération interactive de recommandations
 * Streaming en temps réel avec Server-Sent Events
 */
export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const userId = searchParams.get("userId");

  const headers = {
    'Content-Type': 'text/event-stream',
    'Cache-Control': 'no-cache',
    'Connection': 'keep-alive',
    'Access-Control-Allow-Origin': '*',
  };

  const stream = new ReadableStream({
    start(controller) {
      const encoder = new TextEncoder();
      
      const sendEvent = (data: any) => {
        const formattedData = `data: ${JSON.stringify(data)}\n\n`;
        controller.enqueue(encoder.encode(formattedData));
      };

      const generateRecommendations = async () => {
        try {
          // Étape 1: Analyse des écoutes
          sendEvent({
            type: 'step_update',
            step: 'analyzing',
            status: 'in_progress',
            message: 'Analyse de vos habitudes d\'écoute...'
          });

          const validAccessToken = await getValidSpotifyToken(userId);
          const timeframes = ['short_term', 'medium_term', 'long_term'];
          let allRecommendations: any[] = [];

          // Traiter chaque timeframe
          for (const timeframe of timeframes) {
            const tracks = await fetchUserTopTracks(validAccessToken, timeframe);
            if (tracks) {
              const albumRecommendations = analyzeTracksAndCalculateScores(tracks, timeframe);
              
              // Envoyer les albums découverts en temps réel
              albumRecommendations.forEach(album => {
                sendEvent({
                  type: 'album_art_discovered',
                  payload: {
                    artist: album.artistName,
                    album: album.albumTitle,
                    coverUrl: album.albumCoverUrl
                  }
                });
              });

              allRecommendations.push(...albumRecommendations);
            }
          }

          // Étape 2: Croisement avec collection Discogs
          sendEvent({
            type: 'step_update',
            step: 'collection',
            status: 'in_progress',
            message: 'Croisement avec votre collection Discogs...'
          });

          const discogsCollection = await getUserDiscogsCollection(userId, db);
          const markedRecommendations = markOwnedAlbums(allRecommendations, discogsCollection);

          // Étape 3: Recherche d'offres
          sendEvent({
            type: 'step_update',
            step: 'offers',
            status: 'in_progress',
            message: 'Recherche des meilleures offres...'
          });

          // Enrichir avec les liens d'achat Amazon
          const enrichedRecommendations = await Promise.all(
            markedRecommendations.map(async (rec) => {
              const amazonSearchLink = buildAmazonSearchLink(rec.artistName, rec.albumTitle);
              
              return {
                ...rec,
                affiliateLinks: amazonSearchLink ? [{
                  vendor: 'Amazon',
                  url: amazonSearchLink,
                  price: null, // Pas de prix disponible sans API
                  currency: 'EUR',
                  productName: `${rec.artistName} - ${rec.albumTitle}`,
                  inStock: null // Pas d'info de stock sans API
                }] : []
              };
            })
          );

          // Sauvegarder en base de données
          await db.delete(recommendations).where(eq(recommendations.userId, userId));
          await db.insert(recommendations).values(enrichedRecommendations);

          sendEvent({
            type: 'complete',
            message: 'Recommandations générées avec succès!'
          });

        } catch (error) {
          sendEvent({
            type: 'error',
            message: 'Erreur lors de la génération des recommandations'
          });
        }
      };

      generateRecommendations();
    }
  });

  return new Response(stream, { headers });
}
```

### 2. Profils Publics et Partage

```typescript
/**
 * Récupération des données pour un profil public
 */
export async function getPublicUserData(publicListId: string) {
  const user = await db.query.users.findFirst({
    where: eq(users.publicListId, publicListId),
    columns: {
      id: true,
      name: true,
      image: true,
      publicListEnabled: true,
      publicProfileEnabled: true,
      publicRecommendationsEnabled: true,
      publicWishlistEnabled: true,
      publicCollectionEnabled: true,
    },
  });

  if (!user || !user.publicListEnabled) {
    return null;
  }

  // Récupérer les recommandations publiques
  const publicRecommendations = user.publicRecommendationsEnabled
    ? await db.query.recommendations.findMany({
        where: eq(recommendations.userId, user.id),
        orderBy: [desc(recommendations.listenScore)],
        limit: 100,
      })
    : [];

  // Récupérer la wishlist publique
  const publicWishlist = user.publicWishlistEnabled
    ? await db.query.wishlistItems.findMany({
        where: eq(wishlistItems.userId, user.id),
        orderBy: [desc(wishlistItems.createdAt)],
      })
    : [];

  // Récupérer la collection publique
  const publicCollection = user.publicCollectionEnabled
    ? await db.query.userDiscogsCollection.findMany({
        where: eq(userDiscogsCollection.userId, user.id),
        orderBy: [desc(userDiscogsCollection.syncedAt)],
      })
    : [];

  return {
    user,
    recommendations: publicRecommendations,
    wishlist: publicWishlist,
    collection: publicCollection,
  };
}
```

### 3. Système de Notifications

```typescript
/**
 * Envoi d'emails de recommandations personnalisés
 */
export async function sendRecommendationsEmail(
  user: EmailUser,
  recommendations: EmailRecommendation[]
): Promise<EmailResult> {
  const { recommendations: filteredRecommendations, totalCount } = 
    await filterRecommendationsForEmail(recommendations, user.id);

  if (filteredRecommendations.length === 0) {
    return { success: false, error: "Aucune recommandation à envoyer" };
  }

  const RecommendationsEmail = await import('@/emails/recommendations-email');
  const emailHtml = await render(RecommendationsEmail.default({
    userName: user.name || "Mélomane",
    recommendations: filteredRecommendations,
    totalCount,
    locale: user.preferredLanguage || 'fr',
  }));

  const result = await resend.emails.send({
    from: 'Stream2Spin <<EMAIL>>',
    to: user.email,
    subject: `🎵 ${filteredRecommendations.length} nouvelles recommandations vinyles`,
    html: emailHtml,
    headers: {
      'X-User-ID': user.id,
      'X-Email-Type': 'recommendations',
    },
  });

  return { success: true, messageId: result.data?.id };
}
```

## Tâches Automatisées (Cron Jobs)

### 1. Génération Hebdomadaire des Recommandations

```typescript
/**
 * Cron job Vercel - Génération hebdomadaire des recommandations
 * Déclenché tous les jeudis à 00:30 UTC
 */
export async function POST(request: NextRequest) {
  // Vérification de sécurité
  const authHeader = request.headers.get("authorization");
  const expectedSecret = `Bearer ${process.env.CRON_SECRET}`;
  
  if (!authHeader || authHeader !== expectedSecret) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  // Étape 1: Synchronisation des collections Discogs
  const discogsResult = await syncAllDiscogsCollections();

  // Étape 2: Génération des recommandations Spotify
  const usersWithSpotify = await db
    .select({
      userId: users.id,
      userName: users.name,
      userEmail: users.email,
    })
    .from(users)
    .innerJoin(accounts, eq(accounts.userId, users.id))
    .where(eq(accounts.provider, "spotify"));

  const results = await Promise.allSettled(
    usersWithSpotify.map(async (user) => {
      return await generateRecommendationsForUser(user.userId);
    })
  );

  let successCount = 0;
  let errorCount = 0;
  
  results.forEach((result) => {
    if (result.status === "fulfilled" && result.value.status === "success") {
      successCount++;
    } else {
      errorCount++;
    }
  });

  return NextResponse.json({
    success: true,
    message: "Job hebdomadaire terminé",
    discogs: discogsResult,
    recommendations: { successCount, errorCount },
  });
}
```

### 2. Envoi des Notifications

```typescript
/**
 * Cron job Vercel - Envoi des notifications
 * Déclenché tous les jeudis à 09:30 UTC
 */
export async function POST(request: NextRequest) {
  // Traitement des notifications email
  const usersForEmail = await db.query.users.findMany({
    where: and(
      ne(users.emailFrequency, 'never'),
      eq(users.firstRecommendationEmailSent, true)
    ),
  });

  const emailResults = await Promise.allSettled(
    usersForEmail.map(async (user) => {
      // Vérifier les nouvelles recommandations selon la fréquence
      const sinceDate = calculateSinceDate(user.emailFrequency);
      const newRecommendations = await getNewRecommendations(user.id, sinceDate);
      
      if (newRecommendations.length > 0) {
        return await sendRecommendationsEmail(user, newRecommendations);
      }
      
      return { status: "no_recommendations" };
    })
  );

  // Traitement des notifications push
  const pushResults = await processPushNotifications();

  return NextResponse.json({
    success: true,
    email: emailResults,
    push: pushResults,
  });
}
```

## Optimisations de Performance

### 1. Cache de Requêtes DB

```typescript
/**
 * Cache intelligent pour les requêtes utilisateur
 */
export class QueryCache {
  private cache = new Map<string, { data: any; timestamp: number; ttl: number }>();
  
  get(key: string): any | null {
    const cached = this.cache.get(key);
    if (!cached) return null;
    
    const now = Date.now();
    if (now - cached.timestamp > cached.ttl) {
      this.cache.delete(key);
      return null;
    }
    
    return cached.data;
  }
  
  set(key: string, data: any, ttl: number = 300000): void { // 5 minutes par défaut
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl
    });
  }
}

// Cache global pour les sessions utilisateur
export function getCachedUserQuery(userId: string, queryType: string): any | null {
  const cacheKey = `user:${userId}:${queryType}`;
  return queryCache.get(cacheKey);
}
```

### 2. Optimisation des Connexions DB

```typescript
/**
 * Configuration ultra-optimisée pour Drizzle + PostgreSQL
 */
function getDbClient() {
  if (!globalClient) {
    globalClient = postgres(dbUrl, {
      prepare: false,
      ssl: 'require',
      max: process.env.NODE_ENV === 'production' ? 5 : 10,
      idle_timeout: process.env.NODE_ENV === 'production' ? 60 : 120,
      max_lifetime: 60 * 60, // 1 heure
      connect_timeout: 3,
      transform: { undefined: null },
      fetch_types: false, // Éviter les requêtes de métadonnées
    });
  }
  return globalClient;
}

export const db = drizzle(getDbClient(), {
  schema: {
    users,
    accounts,
    sessions,
    recommendations,
    userDiscogsCollection,
    wishlistItems,
    publicListAnalytics,
    userFCMTokens,
  },
});
```

## Sécurité et Gestion des Erreurs

### 1. Middleware de Sécurité

```typescript
/**
 * Middleware NextAuth.js pour la protection des routes
 */
export default withAuth(
  async function middleware(req) {
    const { nextUrl } = req;
    const token = req.nextauth.token;
    const isLoggedIn = !!token;

    const isProtectedRoute = nextUrl.pathname.startsWith('/recommendations') ||
                            nextUrl.pathname.startsWith('/account') ||
                            nextUrl.pathname.startsWith('/collection') ||
                            nextUrl.pathname.startsWith('/wishlist');

    // Protéger les routes qui nécessitent une connexion
    if (isProtectedRoute && !isLoggedIn) {
      return NextResponse.redirect(new URL('/login', nextUrl));
    }

    // Rediriger les utilisateurs connectés loin de la page de login
    if (nextUrl.pathname === '/login' && isLoggedIn) {
      return NextResponse.redirect(new URL('/recommendations', nextUrl));
    }

    return NextResponse.next();
  },
  {
    callbacks: {
      authorized: ({ token, req }) => {
        const { pathname } = req.nextUrl;
        const isPublicRoute = pathname === '/' || 
                             pathname.startsWith('/public/') ||
                             pathname.startsWith('/u/');
        return isPublicRoute || !!token;
      },
    },
  }
);
```

### 2. Sécurité de la Recherche Utilisateurs

```typescript
/**
 * Protection contre la découverte d'adresses email via la recherche
 * 🔒 Mesures de sécurité implémentées dans app/actions/social.ts
 */
export async function searchUsers(query: string): Promise<UserSearchResult[]> {
  // 🔒 SÉCURITÉ : Bloquer la recherche sur "@" pour éviter la découverte d'adresses email
  if (query.includes('@')) {
    console.warn(`🚨 Tentative de recherche avec "@" bloquée`);
    return [];
  }

  // 🔒 SÉCURITÉ : Validation supplémentaire - bloquer les tentatives de domaines communs
  const suspiciousDomains = ['gmail', 'hotmail', 'yahoo', 'outlook', 'icloud', '.com', '.fr', '.org'];
  if (suspiciousDomains.some(domain => query.toLowerCase().includes(domain))) {
    console.warn(`🚨 Tentative de recherche de domaine bloquée`);
    return [];
  }

  // 🔒 SÉCURITÉ : Recherche uniquement sur la partie locale de l'email (avant @)
  const searchResults = await db
    .where(
      or(
        ilike(users.name, `%${query}%`),
        ilike(sql`split_part(${users.email}, '@', 1)`, `%${query}%`)
      )
    );
}
```

**Protections mises en place :**
- ❌ **Blocage du caractère "@"** : Empêche la recherche sur les domaines email
- ❌ **Blocage des domaines courants** : Empêche les tentatives de découverte par domaine
- 🔒 **Recherche email limitée** : Seule la partie locale (avant @) est recherchable
- 📝 **Logging de sécurité** : Toutes les tentatives suspectes sont loggées
- 🚫 **Profils privés exclus** : Les utilisateurs avec `profileVisibility: 'private'` ne sont jamais retournés

### 3. Gestion Globale des Erreurs

```typescript
/**
 * Boundary d'erreur global pour React
 */
export class GlobalErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    return {
      hasError: true,
      error,
      fromUrl: typeof window !== "undefined" ? window.location.pathname : undefined,
    };
  }

  componentDidCatch(error: Error, errorInfo: any) {
    console.error("Global Error Boundary caught an error:", {
      message: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      timestamp: new Date().toISOString(),
    });
  }

  render() {
    if (this.state.hasError) {
      return (
        <ErrorStatePage
          illustration={<ServerErrorIllustration />}
          title="Ça patine un peu..."
          message="Il y a un petit souci technique de notre côté."
          ctaText="Retourner aux recommandations"
          ctaLink="/recommendations"
        />
      );
    }

    return this.props.children;
  }
}
```

## Internationalisation (i18n)

### 1. Configuration Next-intl

```typescript
/**
 * Configuration i18n avec next-intl
 */
export const locales = ['fr', 'en'] as const;
export type Locale = (typeof locales)[number];

export const routing = defineRouting({
  locales,
  defaultLocale: 'fr',
  localeDetection: false,
  localePrefix: 'never' // Pas de préfixe dans les URLs
});

export default getRequestConfig(async () => {
  let locale: Locale = 'fr';

  try {
    const session = await getSession();
    if (session?.user?.id) {
      const userData = await db.query.users.findFirst({
        where: eq(users.id, session.user.id),
      });

      if (userData?.preferredLanguage && locales.includes(userData.preferredLanguage as Locale)) {
        locale = userData.preferredLanguage as Locale;
      }
    }
  } catch (error) {
    console.error('Erreur lors de la récupération de la langue utilisateur:', error);
  }

  return {
    locale,
    messages: (await import(`./messages/${locale}.json`)).default
  };
});
```

### 2. Fichiers de Traduction

```json
// messages/fr.json
{
  "navigation": {
    "recommendations": "Recommandations",
    "wishlist": "Mes Envies",
    "collection": "Ma Collection"
  },
  "recommendations": {
    "title": "Mes Recommandations",
    "empty": "Aucune recommandation trouvée",
    "generate": "Générer de nouvelles recommandations"
  },
  "generating": {
    "initializing": "Initialisation...",
    "steps": {
      "analyzing": "Analyse des écoutes",
      "collection": "Collection Discogs",
      "offers": "Recherche d'offres",
      "finalizing": "Finalisation"
    }
  }
}
```

## Configuration de Déploiement

### 1. Vercel.json

```json
{
  "crons": [
    {
      "path": "/api/cron/generate-recommendations",
      "schedule": "30 0 * * 4"
    },
    {
      "path": "/api/cron/send-notifications",
      "schedule": "30 9 * * 4"
    }
  ],
  "functions": {
    "app/api/cron/generate-recommendations/route.ts": {
      "maxDuration": 300
    },
    "app/api/cron/send-notifications/route.ts": {
      "maxDuration": 300
    }
  }
}
```

### 2. Variables d'Environnement

```bash
# Base de données
DATABASE_URL="postgresql://..."

# NextAuth.js
NEXTAUTH_URL="https://stream2spin.com"
NEXTAUTH_SECRET="your-secret-key"

# Spotify API
AUTH_SPOTIFY_ID="your-spotify-client-id"
AUTH_SPOTIFY_SECRET="your-spotify-client-secret"

# Discogs API
AUTH_DISCOGS_KEY="your-discogs-consumer-key"
AUTH_DISCOGS_SECRET="your-discogs-consumer-secret"

# Amazon Affiliate (Phase 1 - liens de recherche uniquement)
AMAZON_AFFILIATE_TAG="your-amazon-affiliate-tag"

# Rakuten (désactivé - peut être supprimé)
# RAKUTEN_CLIENT_ID="your-rakuten-client-id"
# RAKUTEN_CLIENT_SECRET="your-rakuten-client-secret"

# Email
RESEND_API_KEY="your-resend-api-key"

# Cron Jobs
CRON_SECRET="your-cron-secret"

# Firebase (Push Notifications)
FIREBASE_PROJECT_ID="your-firebase-project-id"
FIREBASE_PRIVATE_KEY="your-firebase-private-key"
FIREBASE_CLIENT_EMAIL="your-firebase-client-email"
```

## Templates d'Email (React Email)

### 🎨 Architecture des Templates

Les templates d'email utilisent **React Email** avec un système de composants partagés pour garantir la cohérence visuelle :

```typescript
// components/emails/EmailHeader.tsx - Header standardisé
export function EmailHeader() {
  return (
    <Section style={headerStyle}>
      <Img
        src={EMAIL_ASSETS.logo}
        width="180"
        height="40"
        alt="Stream2Spin"
        style={logoStyle}
      />
    </Section>
  );
}
```

### 📧 Templates Disponibles

1. **Email de Bienvenue** (`emails/welcome-email.tsx`)
   - Accueil des nouveaux utilisateurs
   - Conseils de démarrage (Discogs, wishlist)
   - Réutilise le composant `<EmailHeader />`

2. **Email de Recommandations** (`emails/recommendations-email.tsx`)
   - Recommandations hebdomadaires personnalisées
   - Multi-langue (FR/EN)
   - Liens d'achat affiliés
   - Réutilise le composant `<EmailHeader />`

### 🔧 Styles Email Harmonisés

```typescript
// Styles CSS-in-JS optimisés pour la délivrabilité
const main = {
  backgroundColor: '#f6f9fc',
  padding: '24px 0',  // Espace au-dessus du conteneur
  fontFamily: '-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto...',
};

const container = {
  backgroundColor: '#ffffff',
  padding: '0 0 48px',  // Pas d'espace en haut (header collé)
  borderRadius: '12px',
  border: '1px solid #e6e6e6',
};

const headerStyle = {
  padding: '24px',
  backgroundColor: '#6236FF',
  borderRadius: '12px 12px 0 0',  // Coins arrondis en haut
  textAlign: 'center',
};
```

### 🎯 Bonnes Pratiques

- **Composants partagés** : Utiliser `<EmailHeader />` pour la cohérence
- **Responsive design** : Styles compatibles avec tous les clients email
- **Délivrabilité** : Meta tags et structure optimisée
- **Bordures arrondies** : Header parfaitement aligné avec le conteneur

## Structure des Composants UI

### 1. Design System

```typescript
// components/ui/button.tsx
const buttonVariants = cva(
  "inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",
  {
    variants: {
      variant: {
        default: "bg-primary text-primary-foreground hover:bg-primary/90",
        destructive: "bg-destructive text-destructive-foreground hover:bg-destructive/90",
        outline: "border border-input bg-background hover:bg-accent hover:text-accent-foreground",
        secondary: "bg-secondary text-secondary-foreground hover:bg-secondary/80",
        ghost: "hover:bg-accent hover:text-accent-foreground",
        link: "text-primary underline-offset-4 hover:underline",
      },
      size: {
        default: "h-10 px-4 py-2",
        sm: "h-9 rounded-md px-3",
        lg: "h-11 rounded-md px-8",
        icon: "h-10 w-10",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
);
```

### 2. Layout Authentifié

```typescript
// components/layout/authenticated-layout.tsx
export function AuthenticatedLayout({ children }: AuthenticatedLayoutProps) {
  const { data: session } = useSession();
  const pathname = usePathname();

  const isLoginPage = pathname === "/login";
  const isGeneratingPage = pathname === "/generating";
  const isPublicPage = pathname.startsWith('/u/');

  return (
    <div className="min-h-screen bg-background">
      {/* Sidebar - toujours rendue, gère sa propre visibilité */}
      <Sidebar />

      {/* Header - toujours rendu, gère sa propre visibilité */}
      <Header />

      {/* Contenu principal avec marge conditionnelle */}
      <main className={
        isLoginPage || isGeneratingPage || isPublicPage
          ? ""
          : session?.user
            ? "md:ml-64 pt-16"
            : "pt-16"
      }>
        {children}
      </main>
    </div>
  );
}
```

## Workflow de Développement

### 1. Scripts Disponibles

```json
{
  "scripts": {
    "dev": "next dev -p 3000 --turbo",
    "build": "next build",
    "start": "next start",
    "lint": "next lint",
    "security:cleanup": "node scripts/cleanup-debug-routes.js",
    "security:validate": "node scripts/validate-production-ready.js",
    "security:deploy": "./scripts/complete-security-deployment.sh"
  }
}
```

### 2. Processus de Déploiement

```bash
# 1. Nettoyage et sécurisation
npm run security:cleanup

# 2. Validation de sécurité
npm run security:validate

# 3. Build de production
npm run build

# 4. Déploiement automatisé
npm run security:deploy
```

## Monitoring et Analytics

### 1. Analytics des Profils Publics

```typescript
/**
 * Enregistrement des événements analytics
 */
export async function trackPublicListEvent(
  publicListId: string,
  eventType: 'view' | 'share' | 'signup_click' | 'signup_conversion',
  eventData?: any
) {
  const user = await db.query.users.findFirst({
    where: eq(users.publicListId, publicListId),
  });

  if (!user?.publicListEnabled) return null;

  const headersList = await headers();
  const ipAddress = headersList.get('x-forwarded-for') || 'unknown';
  const userAgent = headersList.get('user-agent') || 'unknown';
  const referrer = headersList.get('referer') || null;

  // Éviter les doublons de vues (même IP dans les 5 dernières minutes)
  if (eventType === 'view') {
    const recentView = await db.query.publicListAnalytics.findFirst({
      where: and(
        eq(publicListAnalytics.publicListId, publicListId),
        eq(publicListAnalytics.eventType, 'view'),
        eq(publicListAnalytics.ipAddress, ipAddress),
        sql`${publicListAnalytics.timestamp} > NOW() - INTERVAL '5 minutes'`
      ),
    });

    if (recentView) return null;
  }

  return await db.insert(publicListAnalytics).values({
    userId: user.id,
    publicListId,
    eventType,
    eventData: eventData ? JSON.stringify(eventData) : null,
    ipAddress,
    userAgent,
    referrer,
  });
}
```

### 2. Métriques de Performance

```typescript
/**
 * Métriques de performance pour l'admin
 */
export async function getPerformanceMetrics() {
  const totalUsers = await db.select({ count: count() }).from(users);
  const activeUsers = await db.select({ count: count() })
    .from(users)
    .where(sql`${users.updatedAt} > NOW() - INTERVAL '30 days'`);
  
  const totalRecommendations = await db.select({ count: count() }).from(recommendations);
  const recentRecommendations = await db.select({ count: count() })
    .from(recommendations)
    .where(sql`${recommendations.generatedAt} > NOW() - INTERVAL '7 days'`);

  return {
    users: { total: totalUsers[0].count, active: activeUsers[0].count },
    recommendations: { total: totalRecommendations[0].count, recent: recentRecommendations[0].count },
  };
}
```

## ⚡ Dernières Mises à Jour Critiques (Janvier 2025)

### 🚨 BUG MAJEUR CORRIGÉ - Wishlist Persistante
**Date:** 3 janvier 2025  
**Impact:** Critique - Fonctionnalité principale cassée  
**Statut:** ✅ Corrigé et déployé en staging

#### 🐛 Problème Identifié
- **Symptôme**: Albums disparaissaient de la wishlist avec affichage dégradé (sans pochette, sans liens, sans player)
- **Cause**: Dépendance de la wishlist aux recommandations temporaires
- **Impact utilisateur**: Expérience frustrante, perte de données wishlist

#### ✅ Solution Implémentée
```typescript
// ❌ AVANT (bugué)
await db.insert(wishlistItems).values({
  userId,
  artistName,
  albumTitle,
  // Pas de données enrichies - dépendait des recommandations
});

// ✅ APRÈS (corrigé)
await db.insert(wishlistItems).values({
  userId,
  artistName,
  albumTitle,
  albumCoverUrl,     // Pochette persistante
  affiliateLinks,    // Liens d'achat persistants
  topTrackName,      // Player persistant
  topTrackId,        // Player persistant
  // TOUTES les données enrichies sauvegardées !
});
```

#### 🔧 Fichiers Modifiés
- `app/actions/wishlist.ts` - Fonction `addToWishlist()` enrichie
- `app/actions/user.ts` - Synchronisation automatique ajoutée  
- `app/api/generation-stream/route.ts` - Sync dans génération streaming
- `migrations/add-wishlist-enriched-data.sql` - Nouvelles colonnes DB
- `docs/test-wishlist-persistence-guide.md` - Guide de validation

#### 📋 Tests de Validation
1. Ajout album à wishlist → ✅ Toutes données sauvegardées
2. Suppression des recommandations → ✅ Album reste intact
3. Réapparition avec nouvelle topTrack → ✅ Synchronisation auto

**Migration DB appliquée avec succès en staging** ✅

### 🎯 Règles Critiques pour IA
1. **JAMAIS** modifier `addToWishlist()` sans sauvegarder TOUTES les données enrichies
2. **TOUJOURS** appeler `syncWishlistWithRecommendations()` après génération
3. **La wishlist est INDÉPENDANTE des recommandations** - pas de foreign key
4. Tests obligatoires: scénario de disparition/réapparition d'albums

---

## Conclusion

Stream2Spin est une application web complexe qui combine :
- **Analyse de données musicales** via l'API Spotify
- **Algorithmes de recommandation** personnalisés
- **Wishlist persistante** totalement indépendante (2025)
- **Intégrations e-commerce** pour la monétisation
- **Expérience utilisateur** moderne et interactive
- **Architecture scalable** avec Next.js et PostgreSQL
- **Sécurité robuste** avec NextAuth.js et middleware
- **Automatisation** via des cron jobs Vercel
- **Notifications multi-canal** (email + push)
- **Profils publics** avec analytics intégrés

L'application privilégie les performances, la sécurité et l'expérience utilisateur tout en maintenant une architecture claire et maintenable.

## 🚀 Guide de Développement pour IA

### 🎯 Patterns de Code Importants

#### 1. Gestion des Erreurs
```typescript
// Pattern standard pour les API routes
try {
  // Logique métier
  return NextResponse.json({ success: true, data });
} catch (error) {
  console.error("Erreur:", error);
  return NextResponse.json(
    { success: false, error: "Erreur interne" },
    { status: 500 }
  );
}
```

#### 2. Cache et Performance
```typescript
// Cache DB avec TTL automatique
const userData = getCachedUserQuery(userId, 'profile');
if (!userData) {
  // Requête DB + mise en cache
  cacheUserQuery(userId, 'profile', userData, 30 * 60 * 1000); // 30min
}
```

#### 3. Validation des Données
```typescript
// Validation avec Zod
const schema = z.object({
  userId: z.string().uuid(),
  timeframe: z.enum(['short_term', 'medium_term', 'long_term']),
});
const validatedData = schema.parse(requestData);
```

### 🔧 Scripts de Maintenance

```bash
# Nettoyage et sécurisation
npm run security:cleanup

# Validation avant déploiement
npm run security:validate

# Tests de sécurité
npm run security:test

# Déploiement complet
npm run security:deploy
```

### 📊 Métriques Clés à Surveiller

- **Performance** : Temps de génération des recommandations (< 30s)
- **Sécurité** : Tentatives d'accès non autorisées aux cron jobs
- **Utilisateur** : Taux de conversion Spotify → recommandations
- **Monétisation** : Clics sur les liens affiliés
- **Email** : Taux de délivrabilité et d'ouverture

### 🐛 Debugging Commun

#### Problème : Tokens Spotify expirés
```typescript
// Solution : Refresh automatique dans getValidSpotifyToken()
const now = Math.floor(Date.now() / 1000);
if (spotifyAccount.expires_at && spotifyAccount.expires_at > now) {
  return spotifyAccount.access_token; // Token valide
}
// Sinon : refresh automatique
```

#### Problème : Recommandations vides
```typescript
// Vérifier : Scope Spotify correct
scope: "user-read-email user-top-read user-read-private"

// Vérifier : Timeframes disponibles
const timeframes = ['short_term', 'medium_term', 'long_term'];
```

#### Problème : Emails non envoyés
```typescript
// Vérifier : Variables d'environnement Resend
RESEND_API_KEY="re_..."

// Vérifier : Permissions utilisateur
emailNotificationsEnabled: true
firstRecommendationEmailSent: true
```

#### 🧪 Route API de Debug pour Emails

**Endpoint** : `/api/debug/send-test-emails?email=<EMAIL>`
**Environnement** : Développement uniquement (`NODE_ENV === 'development'`)
**Sécurité** : Retourne 404 en production

**Fonctionnalités** :
- Utilise les vraies données utilisateur de la base de données
- Récupère les 5 dernières recommandations de l'utilisateur
- Envoie 4 emails : bienvenue et recommandations en français ET anglais
- Emails envoyés à l'adresse email réelle de l'utilisateur
- Sujets d'emails localisés automatiquement
- Tous les textes utilisent les traductions (aucun texte en dur)
- Gère le rate limiting avec délais appropriés (1s entre envois)

**Exemple d'utilisation** :
```bash
# Démarrer le serveur de développement
pnpm dev

# Tester les emails avec de vraies données
curl -X GET "http://localhost:3000/api/debug/send-test-emails?email=<EMAIL>"

# Réponse attendue
{
  "success": true,
  "message": "Emails de test envoyés avec succès",
  "user": {
    "id": "fa88e12e-ad5b-4092-82f9-0927540d0040",
    "name": "Simon Gavelle",
    "email": "<EMAIL>"
  },
  "recommendationsCount": 5,
  "sentEmails": [
    {"type": "welcome-fr", "id": "17d839c8-99a7-4e9b-a49d-45d7268de9ca", "success": true},
    {"type": "recommendations-fr", "id": "445def79-598c-4b54-aaa4-4d5afb2a1a46", "success": true},
    {"type": "welcome-en", "id": "fd79434f-b0cd-42ea-9bdc-4a59bdf687ce", "success": true},
    {"type": "recommendations-en", "id": "7fb83393-cc84-4af3-a065-c39e414e5988", "success": true}
  ]
}
```

**Avantages** :
- Test du rendu complet des emails dans un client de messagerie
- Validation de la localisation française et anglaise complète
- Utilisation de vraies données utilisateur pour des tests réalistes
- Sujets d'emails correctement localisés avec nombres pluriels ICU
- Aucun texte en dur, tout est traduit dynamiquement
- Gestion robuste des pluriels ICU MessageFormat imbriqués
- Sécurité garantie (impossible d'accéder en production)

**Corrections apportées** :
- ✅ Structure JSON corrigée (suppression de la section emails dupliquée)
- ✅ Fonction de traduction ICU robuste pour les pluriels complexes
- ✅ Sujets d'emails sans préfixes TEST-FR/TEST-EN
- ✅ Variables dynamiques correctement substituées
- ✅ Emails envoyés aux vraies adresses utilisateur

#### 🔧 Correction des Templates d'Email avec Support Complet des Traductions

**Problème résolu** : Les templates d'email `WelcomeEmail` et `RecommendationsEmailTemplate` avaient été mis à jour pour accepter une prop `t` pour les traductions, mais les fonctions `sendWelcomeEmail` et `sendRecommendationsEmail` ne passaient pas cette prop, causant des erreurs de build TypeScript.

**Solution implémentée** :
1. **Ajout de `preferredLanguage` à `WelcomeEmailData`** dans `lib/email.ts`
2. **Support des traductions dans `sendWelcomeEmail`** et `sendRecommendationsEmail`
3. **Fonction de traduction `t` avec support des pluriels ICU**
4. **Mise à jour d'`auth.ts`** pour passer `preferredLanguage`

**Fonctionnalités** :
- Chargement automatique des traductions selon `preferredLanguage`
- Support complet des pluriels ICU MessageFormat imbriqués
- Substitution dynamique des variables dans les traductions
- Fallback vers 'fr' si `preferredLanguage` n'est pas défini
- Suppression des textes en dur dans les templates

**Exemple de traduction ICU** :
```typescript
// messages/fr.json
{
  "emails": {
    "recommendations": {
      "subject": "{totalCount, plural, =1 {🎵 Nouvelle recommandation vinyle pour vous !} other {🎵 # nouvelles recommandations vinyles pour vous !}}"
    }
  }
}

// Utilisation dans le template
const subject = t('emails.recommendations.subject', { totalCount: 5 });
// Résultat : "🎵 5 nouvelles recommandations vinyles pour vous !"
```

**Intégration dans les templates** :
```typescript
// emails/welcome-email.tsx
interface WelcomeEmailProps {
  name: string;
  userEmail: string;
  t: (key: string, values?: Record<string, any>) => string;
}

// emails/recommendations-email.tsx  
interface RecommendationsEmailProps {
  user: EmailUser;
  recommendations: EmailRecommendation[];
  totalCount: number;
  unsubscribeUrl: string;
  viewRecommendationsUrl: string;
  t: (key: string, values?: Record<string, any>) => string;
}
```

**Build et déploiement** :
- ✅ Build TypeScript réussi sans erreurs
- ✅ Prêt pour le déploiement Vercel
- ✅ Emails fonctionnels en français et anglais
- ✅ Harmonisation des styles avec bordures arrondies cohérentes
- ✅ Icône cœur corrigée (rouge au lieu du filtre violet)

### �� Conventions UI/UX

#### Design System
- **Couleurs** : Palette violet/bleu avec glassmorphism
- **Animations** : Framer Motion pour les transitions fluides
- **Responsive** : Mobile-first avec breakpoints Tailwind
- **Accessibilité** : Radix UI pour les composants

#### États de Chargement
```typescript
// Pattern standard pour les états de chargement
const [isLoading, setIsLoading] = useState(false);
const [error, setError] = useState<string | null>(null);

// Affichage conditionnel
{isLoading && <LoadingSpinner />}
{error && <ErrorMessage message={error} />}
```

### 🔄 Workflow de Développement

1. **Feature Branch** : `feature/nouvelle-fonctionnalite`
2. **Tests** : Validation locale avec données de test
3. **Sécurité** : Vérification avec `npm run security:validate`
4. **Déploiement** : Staging → Production via Vercel
5. **Monitoring** : Surveillance des métriques et erreurs

### 📚 Ressources Utiles

- **Documentation Spotify API** : https://developer.spotify.com/documentation
- **Documentation Discogs API** : https://www.discogs.com/developers/
- **Documentation Amazon Associates** : https://affiliate-program.amazon.com/
- **Documentation NextAuth.js** : https://next-auth.js.org/
- **Documentation Drizzle ORM** : https://orm.drizzle.team/

### 🎯 Checklist de Démarrage IA

- [ ] **Comprendre l'algorithme** : Scoring basé sur position des tracks
- [ ] **Authentification** : Spotify OAuth uniquement
- [ ] **Base de données** : Schémas avec relations et contraintes
- [ ] **APIs externes** : Spotify, Discogs, Amazon, Rakuten
- [ ] **Performance** : Cache DB + optimisations requêtes
- [ ] **Sécurité** : Middleware + protection des routes
- [ ] **Déploiement** : Vercel avec cron jobs
- [ ] **Monitoring** : Analytics + métriques de performance

Cette documentation complète permet à une IA de comprendre rapidement l'architecture, les patterns de code, et les bonnes pratiques du projet Stream2Spin. 