/**
 * Firebase Cloud Messaging Service Worker
 * Epic 6 - Notifications Utilisateur
 * 
 * Ce service worker gère les notifications push en arrière-plan
 * et les interactions utilisateur avec les notifications.
 */

// Import Firebase scripts
importScripts('https://www.gstatic.com/firebasejs/10.7.1/firebase-app-compat.js');
importScripts('https://www.gstatic.com/firebasejs/10.7.1/firebase-messaging-compat.js');

// Configuration Firebase (sera injectée dynamiquement)
const firebaseConfig = {
  apiKey: "your-api-key",
  authDomain: "your-project.firebaseapp.com",
  projectId: "your-project-id",
  storageBucket: "your-project.appspot.com",
  messagingSenderId: "your-sender-id",
  appId: "your-app-id"
};

// Initialiser Firebase
firebase.initializeApp(firebaseConfig);

// Initialiser Firebase Messaging
const messaging = firebase.messaging();

// Gérer les messages en arrière-plan
messaging.onBackgroundMessage((payload) => {
  console.log('[firebase-messaging-sw.js] Message reçu en arrière-plan:', payload);

  const notificationTitle = payload.notification?.title || 'Stream2Spin';
  const notificationOptions = {
    body: payload.notification?.body || 'Nouvelles recommandations disponibles',
    icon: payload.notification?.icon || '/Stream2Spin_icon.svg',
    badge: '/Stream2Spin_icon.svg',
    image: payload.notification?.image,
    data: {
      ...payload.data,
      click_action: payload.fcmOptions?.link || '/recommendations'
    },
    actions: [
      {
        action: 'view',
        title: 'Voir les recommandations',
        icon: '/icons/view.png'
      },
      {
        action: 'dismiss',
        title: 'Fermer',
        icon: '/icons/close.png'
      }
    ],
    requireInteraction: true,
    tag: 'stream2spin-recommendations'
  };

  // Afficher la notification
  return self.registration.showNotification(notificationTitle, notificationOptions);
});

// Gérer les clics sur les notifications
self.addEventListener('notificationclick', (event) => {
  console.log('[firebase-messaging-sw.js] Clic sur notification:', event);

  event.notification.close();

  const action = event.action;
  const data = event.notification.data;

  if (action === 'dismiss') {
    // Ne rien faire, juste fermer la notification
    return;
  }

  // Action 'view' ou clic sur la notification principale
  const urlToOpen = data?.click_action || '/recommendations';

  event.waitUntil(
    clients.matchAll({
      type: 'window',
      includeUncontrolled: true
    }).then((clientList) => {
      // Vérifier si une fenêtre de l'app est déjà ouverte
      for (const client of clientList) {
        if (client.url.includes(self.location.origin) && 'focus' in client) {
          // Naviguer vers la page des recommandations et donner le focus
          client.postMessage({
            type: 'NOTIFICATION_CLICK',
            url: urlToOpen,
            data: data
          });
          return client.focus();
        }
      }

      // Aucune fenêtre ouverte, en ouvrir une nouvelle
      if (clients.openWindow) {
        return clients.openWindow(self.location.origin + urlToOpen);
      }
    })
  );
});

// Gérer les erreurs de notification
self.addEventListener('notificationerror', (event) => {
  console.error('[firebase-messaging-sw.js] Erreur de notification:', event);
});

// Gérer la fermeture des notifications
self.addEventListener('notificationclose', (event) => {
  console.log('[firebase-messaging-sw.js] Notification fermée:', event);
  
  // Optionnel: envoyer des analytics sur la fermeture
  const data = event.notification.data;
  if (data?.type === 'recommendations') {
    // Ici on pourrait envoyer des stats sur les notifications fermées
    console.log('Notification de recommandations fermée sans interaction');
  }
});

// Gérer l'installation du service worker
self.addEventListener('install', (event) => {
  console.log('[firebase-messaging-sw.js] Service Worker installé');
  self.skipWaiting();
});

// Gérer l'activation du service worker
self.addEventListener('activate', (event) => {
  console.log('[firebase-messaging-sw.js] Service Worker activé');
  event.waitUntil(self.clients.claim());
});

// Gérer les messages du client principal
self.addEventListener('message', (event) => {
  console.log('[firebase-messaging-sw.js] Message reçu du client:', event.data);

  if (event.data && event.data.type === 'SKIP_WAITING') {
    self.skipWaiting();
  }

  if (event.data && event.data.type === 'UPDATE_CONFIG') {
    // Mettre à jour la configuration Firebase si nécessaire
    console.log('Mise à jour de la configuration Firebase');
  }
});

// Fonction utilitaire pour créer des notifications personnalisées
function createCustomNotification(title, options = {}) {
  const defaultOptions = {
    icon: '/Stream2Spin_icon.svg',
    badge: '/Stream2Spin_icon.svg',
    requireInteraction: true,
    tag: 'stream2spin-custom'
  };

  return self.registration.showNotification(title, {
    ...defaultOptions,
    ...options
  });
}

// Gérer les notifications de synchronisation en arrière-plan
self.addEventListener('sync', (event) => {
  console.log('[firebase-messaging-sw.js] Événement de synchronisation:', event.tag);

  if (event.tag === 'background-sync-recommendations') {
    event.waitUntil(
      // Ici on pourrait implémenter une synchronisation en arrière-plan
      // pour vérifier s'il y a de nouvelles recommandations
      console.log('Synchronisation des recommandations en arrière-plan')
    );
  }
});

// Gérer les mises à jour du service worker
self.addEventListener('updatefound', () => {
  console.log('[firebase-messaging-sw.js] Mise à jour du service worker détectée');
});

// Fonction pour nettoyer les anciennes notifications
function cleanupOldNotifications() {
  self.registration.getNotifications().then((notifications) => {
    const now = Date.now();
    const maxAge = 24 * 60 * 60 * 1000; // 24 heures

    notifications.forEach((notification) => {
      const notificationTime = notification.timestamp || 0;
      if (now - notificationTime > maxAge) {
        notification.close();
      }
    });
  });
}

// Nettoyer les anciennes notifications périodiquement
setInterval(cleanupOldNotifications, 60 * 60 * 1000); // Toutes les heures
