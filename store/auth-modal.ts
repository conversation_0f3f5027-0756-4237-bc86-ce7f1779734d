/**
 * Store global pour la modale d'authentification
 * Gère l'état de la modale et les informations contextuelles de l'entité cible
 */

import { create } from 'zustand';

interface AlbumData {
  artistName: string;
  albumTitle: string;
  albumCoverUrl?: string | null;
  spotifyAlbumId?: string | null;
  discogsReleaseId?: number | null;
}

interface UserData {
  id: string;
  name: string | null;
  image: string | null;
  publicListId: string | null;
}

interface AuthModalState {
  // État de la modale
  isOpen: boolean;
  loginReason: 'wishlist' | 'follow';
  
  // Données contextuelles
  targetData: {
    type: 'album' | 'user';
    payload: AlbumData | UserData;
  } | null;
  
  // Informations du profil principal (pour les callbacks)
  publicListId: string;
  mainUserName?: string;
  mainUserImage?: string | null;
  
  // Actions
  openModal: (params: {
    loginReason: 'wishlist' | 'follow';
    targetData: {
      type: 'album' | 'user';
      payload: AlbumData | UserData;
    };
    publicListId: string;
    mainUserName?: string;
    mainUserImage?: string | null;
  }) => void;
  closeModal: () => void;
  reset: () => void;
}

export const useAuthModal = create<AuthModalState>((set) => ({
  // État initial
  isOpen: false,
  loginReason: 'wishlist',
  targetData: null,
  publicListId: '',
  mainUserName: undefined,
  mainUserImage: undefined,

  // Actions
  openModal: ({ loginReason, targetData, publicListId, mainUserName, mainUserImage }) => {
    set({
      isOpen: true,
      loginReason,
      targetData,
      publicListId,
      mainUserName,
      mainUserImage,
    });
  },

  closeModal: () => {
    set({
      isOpen: false,
      targetData: null,
    });
  },

  reset: () => {
    set({
      isOpen: false,
      loginReason: 'wishlist',
      targetData: null,
      publicListId: '',
      mainUserName: undefined,
      mainUserImage: undefined,
    });
  },
})); 