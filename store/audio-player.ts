/**
 * Store global pour le lecteur audio
 * US 3.6: Gestion de l'état du lecteur audio pour les extraits de titres phares
 */

import { create } from 'zustand';

interface AudioPlayerState {
  // État actuel du lecteur
  currentTrackUrl: string | null;
  currentTrackName: string | null;
  currentAlbumTitle: string | null;
  currentArtistName: string | null;
  isPlaying: boolean;
  isLoading: boolean;
  
  // Actions du lecteur
  playTrack: (url: string, trackName: string, albumTitle: string, artistName: string) => void;
  pauseTrack: () => void;
  stopTrack: () => void;
  togglePlayPause: (url: string, trackName: string, albumTitle: string, artistName: string) => void;
  setLoading: (loading: boolean) => void;
  
  // Méthodes utilitaires
  isCurrentTrack: (url: string) => boolean;
  reset: () => void;
}

export const useAudioPlayer = create<AudioPlayerState>((set, get) => ({
  // État initial
  currentTrackUrl: null,
  currentTrackName: null,
  currentAlbumTitle: null,
  currentArtistName: null,
  isPlaying: false,
  isLoading: false,

  // Actions
  playTrack: (url, trackName, albumTitle, artistName) => {
    const state = get();
    
    // Si c'est le même track, juste reprendre la lecture
    if (state.currentTrackUrl === url) {
      set({ isPlaying: true });
    } else {
      // Nouveau track, arrêter l'ancien et démarrer le nouveau
      set({
        currentTrackUrl: url,
        currentTrackName: trackName,
        currentAlbumTitle: albumTitle,
        currentArtistName: artistName,
        isPlaying: true,
        isLoading: true
      });
    }
  },

  pauseTrack: () => {
    set({ isPlaying: false });
  },

  stopTrack: () => {
    set({
      isPlaying: false,
      currentTrackUrl: null,
      currentTrackName: null,
      currentAlbumTitle: null,
      currentArtistName: null,
      isLoading: false
    });
  },

  togglePlayPause: (url, trackName, albumTitle, artistName) => {
    const state = get();
    
    if (state.currentTrackUrl === url && state.isPlaying) {
      // Même track en cours de lecture, mettre en pause
      set({ isPlaying: false });
    } else if (state.currentTrackUrl === url && !state.isPlaying) {
      // Même track en pause, reprendre la lecture
      set({ isPlaying: true });
    } else {
      // Nouveau track, arrêter l'ancien et démarrer le nouveau
      set({
        currentTrackUrl: url,
        currentTrackName: trackName,
        currentAlbumTitle: albumTitle,
        currentArtistName: artistName,
        isPlaying: true,
        isLoading: true
      });
    }
  },

  setLoading: (loading) => {
    set({ isLoading: loading });
  },

  // Méthodes utilitaires
  isCurrentTrack: (url) => {
    return get().currentTrackUrl === url;
  },

  reset: () => {
    set({
      currentTrackUrl: null,
      currentTrackName: null,
      currentAlbumTitle: null,
      currentArtistName: null,
      isPlaying: false,
      isLoading: false
    });
  }
}));

// Hook personnalisé pour obtenir l'état d'un track spécifique
export const useTrackState = (url: string) => {
  const { currentTrackUrl, isPlaying, isLoading } = useAudioPlayer();
  
  return {
    isCurrentTrack: currentTrackUrl === url,
    isPlaying: currentTrackUrl === url && isPlaying,
    isLoading: currentTrackUrl === url && isLoading
  };
};
