const { drizzle } = require('drizzle-orm/postgres-js');
const postgres = require('postgres');
require('dotenv').config({ path: '.env.local' });

async function testDbQuery() {
  const sql = postgres(process.env.DATABASE_URL, {
    ssl: 'require',
    prepare: false
  });
  
  const db = drizzle(sql);
  
  try {
    console.log('🔍 Test de la requête problématique...');
    
    // Test de la requête exacte qui échoue
    const result = await sql`
      SELECT 
        "accounts"."userId", 
        "accounts"."type", 
        "accounts"."provider", 
        "accounts"."providerAccountId",
        "accounts"."refresh_token",
        "accounts"."access_token",
        "accounts"."access_token_secret",
        "accounts"."expires_at",
        "accounts"."token_type",
        "accounts"."scope",
        "accounts"."id_token",
        "accounts"."session_state",
        "users"."id",
        "users"."name",
        "users"."email",
        "users"."emailVerified",
        "users"."image",
        "users"."preferredLanguage",
        "users"."emailFrequency",
        "users"."pushFrequency",
        "users"."firstRecommendationEmailSent",
        "users"."publicListEnabled",
        "users"."publicListId",
        "users"."publicProfileEnabled",
        "users"."publicRecommendationsEnabled",
        "users"."publicWishlistEnabled",
        "users"."publicCollectionEnabled",
        "users"."email_notifications_enabled",
        "users"."last_email_sent",
        "users"."last_email_bounce",
        "users"."email_bounce_reason",
        "users"."last_email_complaint",
        "users"."email_complaint_reason",
        "users"."last_email_delivered",
        "users"."last_email_opened",
        "users"."last_email_clicked",
        "users"."email_deliverability_score",
        "users"."timezone",
        "users"."createdAt",
        "users"."updatedAt"
      FROM "accounts" 
      INNER JOIN "users" ON "accounts"."userId" = "users"."id" 
      WHERE ("accounts"."provider" = 'spotify' AND "accounts"."providerAccountId" = '**********')
    `;
    
    console.log('✅ Requête réussie !');
    console.log('Résultats:', result);
    
    if (result.length > 0) {
      console.log('✅ Utilisateur trouvé:', {
        userId: result[0].userId,
        email: result[0].email,
        name: result[0].name
      });
    } else {
      console.log('❌ Aucun utilisateur trouvé');
    }
    
  } catch (error) {
    console.error('❌ Erreur lors de la requête:', error);
  } finally {
    await sql.end();
  }
}

testDbQuery(); 