import { getRequestConfig } from 'next-intl/server';
import { defineRouting } from 'next-intl/routing';
import { getSession } from '@/lib/auth';
import { db } from '@/lib/db';
import { users } from '@/lib/db/schema';
import { eq } from 'drizzle-orm';
import { headers } from 'next/headers';
import { cookies } from 'next/headers';

// Can be imported from a shared config
export const locales = ['fr', 'en'] as const;
export type Locale = (typeof locales)[number];

// Configuration de routage pour éviter les redirections automatiques
export const routing = defineRouting({
  locales,
  defaultLocale: 'fr',
  localeDetection: false,
  localePrefix: 'never'
});

// Fonction pour détecter la langue du navigateur
async function detectBrowserLanguage(): Promise<Locale> {
  try {
    const headersList = await headers();
    const acceptLanguage = headersList.get('accept-language');
    
    if (acceptLanguage) {
      // Parse Accept-Language header
      const languages = acceptLanguage
        .split(',')
        .map((lang: string) => lang.trim().split(';')[0])
        .map((lang: string) => lang.toLowerCase());
      
      // Chercher d'abord une correspondance exacte
      for (const lang of languages) {
        if (lang === 'fr' || lang === 'fr-fr' || lang === 'fr-ca') {
          return 'fr';
        }
        if (lang === 'en' || lang.startsWith('en-')) {
          return 'en';
        }
      }
      
      // Si aucune correspondance exacte, chercher les préfixes
      for (const lang of languages) {
        if (lang.startsWith('fr')) {
          return 'fr';
        }
        if (lang.startsWith('en')) {
          return 'en';
        }
      }
    }
  } catch (error) {
    console.error('Erreur lors de la détection de la langue du navigateur:', error);
  }
  
  // Langue par défaut
  return 'fr';
}



export default getRequestConfig(async () => {
  let locale: Locale = 'fr'; // Langue par défaut

  try {
    const session = await getSession();
    const cookieStore = await cookies();
    
    // 1. Vérifier d'abord les cookies (préférence temporaire)
    const languageCookie = cookieStore.get('preferred-language');
    if (languageCookie && locales.includes(languageCookie.value as Locale)) {
      locale = languageCookie.value as Locale;
    } else if (session?.user?.id) {
      // 2. Utilisateur connecté : utiliser sa préférence depuis la base de données
      const userResult = await db
        .select({ preferredLanguage: users.preferredLanguage })
        .from(users)
        .where(eq(users.id, session.user.id))
        .limit(1);

      const userData = userResult[0];

      if (userData?.preferredLanguage && locales.includes(userData.preferredLanguage as Locale)) {
        locale = userData.preferredLanguage as Locale;
      }
    } else {
      // 3. Utilisateur non connecté : détecter la langue du navigateur
      locale = await detectBrowserLanguage();
    }
  } catch (error) {
    console.error('Erreur lors de la récupération de la langue utilisateur:', error);
    // En cas d'erreur, essayer la détection du navigateur
    locale = await detectBrowserLanguage();
  }

  return {
    locale,
    messages: (await import(`./messages/${locale}.json`)).default
  };
});
