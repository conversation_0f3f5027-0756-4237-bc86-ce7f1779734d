{"name": "stream2spin", "version": "0.1.0", "private": true, "scripts": {"build": "next build", "dev": "next dev -p 3000 --turbo", "dev:fast": "next dev -p 3000 --turbo --experimental-https", "lint": "next lint", "start": "next start", "security:cleanup": "node scripts/cleanup-debug-routes.js", "security:validate": "node scripts/validate-production-ready.js", "security:test": "node scripts/run-security-tests.js", "security:deploy": "./scripts/complete-security-deployment.sh", "deploy:staging": "./scripts/deploy-staging.sh", "clean:logs": "node scripts/clean-console-logs.js", "test:cron": "node scripts/test-cron-jobs.js", "diagnose:cron": "node scripts/diagnose-cron-issues.js", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build"}, "dependencies": {"@auth/drizzle-adapter": "^1.9.1", "@hookform/resolvers": "^5.1.1", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@react-email/components": "^0.1.0", "@react-email/render": "^1.1.2", "@upstash/qstash": "^2.8.1", "@upstash/redis": "^1.35.1", "@vercel/kv": "^3.0.0", "accept-language-parser": "^1.5.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "disconnect": "^1.2.2", "dotenv": "^16.5.0", "drizzle-orm": "^0.44.2", "firebase": "^11.9.1", "firebase-admin": "^13.4.0", "lucide-react": "^0.515.0", "next": "^15.3.3", "next-auth": "^4.24.11", "next-intl": "^4.1.0", "pg": "^8.16.3", "postgres": "^3.4.7", "prettier": "^3.6.2", "react": "^19.1.0", "react-dom": "^19.1.0", "react-email": "^4.0.16", "react-hook-form": "^7.58.1", "resend": "^4.6.0", "sonner": "^2.0.5", "tailwind-merge": "^3.3.1", "tailwindcss": "^3.4.17", "tailwindcss-animate": "^1.0.7", "zod": "^3.25.64", "zustand": "^5.0.5"}, "devDependencies": {"@chromatic-com/storybook": "^4.0.1", "@storybook/addon-a11y": "^9.0.16", "@storybook/addon-docs": "^9.0.16", "@storybook/addon-essentials": "^8.6.14", "@storybook/addon-links": "^9.0.16", "@storybook/addon-onboarding": "^9.0.16", "@storybook/addon-vitest": "^9.0.16", "@storybook/nextjs-vite": "^9.0.16", "@types/accept-language-parser": "^1.5.8", "@types/node": "24.0.1", "@types/react": "19.1.8", "@types/react-dom": "^19.1.0", "@vitest/browser": "^3.2.4", "@vitest/coverage-v8": "^3.2.4", "autoprefixer": "^10.4.21", "drizzle-kit": "^0.31.2", "eslint": "^8.57.0", "eslint-config-next": "^15.3.3", "eslint-plugin-storybook": "^9.0.16", "playwright": "^1.54.1", "storybook": "^9.0.16", "tsx": "^4.20.3", "typescript": "5.8.3", "vitest": "^3.2.4"}, "eslintConfig": {"extends": ["plugin:storybook/recommended"]}}