# Stream2Spin 🎵➡️💿

**Transformez vos habitudes d'écoute Spotify en recommandations de vinyles personnalisées**

Stream2Spin est une application web qui analyse vos données d'écoute Spotify pour vous recommander des vinyles qui correspondent parfaitement à vos goûts musicaux. En croisant vos habitudes d'écoute avec votre collection Discogs existante, l'application évite les doublons et vous propose uniquement des albums que vous n'avez pas encore dans votre collection.

## ✨ Fonctionnalités principales

### 🎯 Recommandations intelligentes
- **Analyse des données Spotify** : Utilise vos titres et artistes les plus écoutés
- **Évitement des doublons** : Croise avec votre collection Discogs pour éviter les suggestions redondantes
- **Scoring personnalisé** : Algorithme de recommandation basé sur vos habitudes d'écoute réelles

### 🔐 Authentification simplifiée
- **Connexion Spotify uniquement** : Authentification en un clic via Spotify Connect
- **Intégration Discogs optionnelle** : Connectez votre compte Discogs pour améliorer les recommandations
- **Sécurité renforcée** : Utilisation d'Auth.js avec tokens sécurisés

### 🌍 Interface multilingue
- **Français** (langue par défaut)
- **Anglais**
- **Changement de langue en temps réel** dans les paramètres du compte

### 🔄 Synchronisation automatique
- **Synchronisation hebdomadaire** : Mise à jour automatique tous les dimanches
- **Synchronisation à la connexion** : Actualisation des données à chaque connexion
- **Synchronisation manuelle** : Boutons de contrôle dans les paramètres du compte

## 🛠️ Stack technique

### Frontend & Backend
- **Next.js 15** avec App Router
- **React 19** pour l'interface utilisateur
- **TypeScript** pour la sécurité des types
- **Tailwind CSS** pour le design system
- **Glassmorphism UI** avec effets de verre dépoli

### Base de données & Authentification
- **PostgreSQL** hébergé sur Supabase
- **Drizzle ORM** pour les requêtes typées
- **Auth.js (NextAuth v5)** pour l'authentification
- **Row-Level Security (RLS)** pour la sécurité des données

### APIs externes
- **Spotify Web API** : Récupération des données d'écoute
- **Discogs API** : Gestion des collections de vinyles
- **Awin API** : Liens d'affiliation pour la monétisation

### Déploiement & Infrastructure
- **Vercel** pour l'hébergement et les fonctions serverless
- **Cron jobs** pour les tâches automatisées
- **Variables d'environnement** sécurisées

## 🚀 Installation et développement

### Prérequis
- Node.js 18+
- pnpm (gestionnaire de paquets recommandé)
- Compte Spotify Developer
- Compte Discogs Developer (optionnel)
- Base de données PostgreSQL (Supabase recommandé)

### Installation

1. **Cloner le repository**
```bash
git clone https://github.com/votre-username/stream2spin.git
cd stream2spin
```

2. **Installer les dépendances**
```bash
pnpm install
```

3. **Configuration des variables d'environnement**
```bash
cp .env.example .env.local
```

Remplissez les variables suivantes dans `.env.local` :
```env
# Base de données
DATABASE_URL="postgresql://..."

# Auth.js
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="your-secret-key"

# Spotify API
AUTH_SPOTIFY_ID="your-spotify-client-id"
AUTH_SPOTIFY_SECRET="your-spotify-client-secret"

# Discogs API (optionnel)
AUTH_DISCOGS_KEY="your-discogs-consumer-key"
AUTH_DISCOGS_SECRET="your-discogs-consumer-secret"

# Awin API (optionnel)
AWIN_API_TOKEN="your-awin-api-token"
```

4. **Initialiser la base de données**
```bash
# Exécuter les migrations
pnpm run db:migrate

# (Optionnel) Seed avec des données de test
pnpm run db:seed
```

5. **Lancer le serveur de développement**
```bash
pnpm dev
```

L'application sera accessible sur [http://localhost:3000](http://localhost:3000)

## 📁 Structure du projet

```
stream2spin/
├── app/                    # Pages et routes (App Router)
│   ├── (auth)/            # Pages d'authentification
│   ├── account/           # Gestion du compte utilisateur
│   ├── api/               # Routes API et webhooks
│   ├── recommendations/   # Page des recommandations
│   └── layout.tsx         # Layout principal
├── components/            # Composants React réutilisables
│   ├── auth/             # Composants d'authentification
│   ├── layout/           # Composants de mise en page
│   └── ui/               # Composants UI de base
├── lib/                  # Utilitaires et configurations
│   ├── db/               # Configuration base de données
│   ├── schemas/          # Schémas de validation
│   └── utils.ts          # Fonctions utilitaires
├── messages/             # Fichiers de traduction i18n
├── public/               # Assets statiques
├── specs/                # Documentation technique
└── types/                # Définitions TypeScript
```

## 🔧 Scripts disponibles

```bash
# Développement
pnpm dev              # Lancer le serveur de développement
pnpm build            # Construire l'application pour la production
pnpm start            # Lancer l'application en mode production
pnpm lint             # Vérifier le code avec ESLint

# Base de données
pnpm db:generate      # Générer les migrations Drizzle
pnpm db:migrate       # Exécuter les migrations
pnpm db:studio        # Ouvrir Drizzle Studio (interface graphique)

# Tests et debugging
pnpm test             # Lancer les tests (si configurés)
pnpm type-check       # Vérifier les types TypeScript
```

## 🔐 Configuration des APIs

### Spotify Developer
1. Créez une application sur [Spotify Developer Dashboard](https://developer.spotify.com/dashboard)
2. Ajoutez `http://localhost:3000/api/auth/callback/spotify` dans les Redirect URIs
3. Notez votre Client ID et Client Secret

### Discogs Developer (Optionnel)
1. Créez une application sur [Discogs Developer](https://www.discogs.com/settings/developers)
2. Configurez l'URL de callback : `http://localhost:3000/api/discogs/callback`
3. Récupérez votre Consumer Key et Consumer Secret

### Supabase
1. Créez un projet sur [Supabase](https://supabase.com)
2. Récupérez l'URL de connexion PostgreSQL
3. Activez Row-Level Security (RLS) sur toutes les tables

## 🚀 Déploiement

### Vercel (Recommandé)
1. **Connecter le repository**
```bash
vercel --prod
```

2. **Configurer les variables d'environnement**
Dans le dashboard Vercel, ajoutez toutes les variables d'environnement de production.

3. **Configurer les cron jobs**
Le fichier `vercel.json` contient déjà la configuration pour les tâches automatisées :
```json
{
  "crons": [
    {
      "path": "/api/cron/weekly-sync",
      "schedule": "0 3 * * 0"
    }
  ]
}
```

### Variables d'environnement de production
Assurez-vous de configurer ces variables dans Vercel :
- `DATABASE_URL` : URL de la base de données PostgreSQL
- `NEXTAUTH_URL` : URL de production (ex: `https://stream2spin.vercel.app`)
- `NEXTAUTH_SECRET` : Secret fort pour la signature des tokens
- `AUTH_SPOTIFY_ID` et `AUTH_SPOTIFY_SECRET` : Credentials Spotify
- `AUTH_DISCOGS_KEY` et `AUTH_DISCOGS_SECRET` : Credentials Discogs
- `AWIN_API_TOKEN` : Token API Awin pour l'affiliation

## 📊 Fonctionnalités avancées

### Algorithme de recommandation
L'algorithme analyse plusieurs facteurs :
- **Fréquence d'écoute** : Nombre de fois qu'un titre a été écouté
- **Récence** : Période d'écoute (derniers mois privilégiés)
- **Diversité** : Équilibre entre artistes familiers et découvertes
- **Disponibilité vinyle** : Vérification de l'existence en format vinyle

### Synchronisation Discogs
- **Détection automatique** des nouveaux ajouts à la collection
- **Matching intelligent** entre les données Spotify et Discogs
- **Gestion des variantes** (rééditions, pressages spéciaux)
- **Historique des synchronisations** avec logs détaillés

### Système de notifications
- **Emails personnalisés** pour les nouvelles recommandations
- **Notifications push** (PWA) pour les utilisateurs actifs
- **Préférences granulaires** de notification par utilisateur

## 🧪 Tests et qualité

### Tests unitaires
```bash
# Lancer les tests
pnpm test

# Tests en mode watch
pnpm test:watch

# Coverage des tests
pnpm test:coverage
```

### Linting et formatage
```bash
# Vérifier le code
pnpm lint

# Corriger automatiquement
pnpm lint:fix

# Formater le code
pnpm format
```

## 📚 Documentation

- **[Spécifications techniques](./specs/)** : Documentation complète du projet
- **[Guide utilisateur](./docs/)** : Documentation pour les utilisateurs finaux
- **[API Reference](./docs/api.md)** : Documentation des endpoints API

## 🤝 Contribution

1. **Fork** le repository
2. **Créer une branche** pour votre fonctionnalité (`git checkout -b feature/nouvelle-fonctionnalite`)
3. **Commiter** vos changements (`git commit -m 'Ajout nouvelle fonctionnalité'`)
4. **Pusher** vers la branche (`git push origin feature/nouvelle-fonctionnalite`)
5. **Ouvrir une Pull Request**

### Standards de code
- Utiliser **TypeScript** pour tous les nouveaux fichiers
- Suivre les conventions **ESLint** configurées
- Ajouter des **tests** pour les nouvelles fonctionnalités
- Documenter les **APIs publiques**

## 📄 Licence

Ce projet est sous licence MIT. Voir le fichier [LICENSE](./LICENSE) pour plus de détails.

## 🆘 Support

- **Issues GitHub** : [Signaler un bug](https://github.com/votre-username/stream2spin/issues)
- **Discussions** : [Forum communautaire](https://github.com/votre-username/stream2spin/discussions)
- **Email** : <EMAIL>

---

**Stream2Spin** - Transformez votre passion musicale en collection de vinyles personnalisée 🎵💿
