"use client";

import { useState } from 'react';
import { Play, Pause } from 'lucide-react';

interface SpotifyEmbedProps {
  trackId: string;
  trackName: string;
  artistName: string;
  albumName: string;
  compact?: boolean;
  autoPlay?: boolean;
}

/**
 * Composant Spotify Embed pour l'US 3.6
 * Utilise le lecteur Spotify officiel intégré via iframe
 * Résout le problème des preview_url manquantes
 */
export function SpotifyEmbed({ 
  trackId, 
  trackName, 
  artistName, 
  albumName,
  compact = false,
  autoPlay = false 
}: SpotifyEmbedProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  // URL de l'embed Spotify
  const embedUrl = `https://open.spotify.com/embed/track/${trackId}?utm_source=generator&theme=0${autoPlay ? '&autoplay=1' : ''}`;
  
  // URL pour ouvrir dans Spotify
  const spotifyUrl = `https://open.spotify.com/track/${trackId}`;

  const handleTogglePlayer = () => {
    setIsExpanded(!isExpanded);
  };

  const handleIframeLoad = () => {
    setIsLoading(false);
  };

  if (compact && !isExpanded) {
    // Mode compact : bouton play qui révèle le lecteur
    return (
      <div className="flex items-center space-x-2 p-2 bg-slate-50 dark:bg-slate-700/50 rounded-lg">
        <button
          onClick={handleTogglePlayer}
          className="flex-shrink-0 w-8 h-8 bg-[#1DB954] hover:bg-[#1ed760] text-white rounded-full flex items-center justify-center transition-colors"
          title="Écouter sur Spotify"
        >
          <Play className="w-4 h-4 ml-0.5" />
        </button>
        
        <div className="flex-1 min-w-0">
          <p className="text-sm font-medium text-slate-900 dark:text-slate-100 truncate">
            {trackName}
          </p>
          <p className="text-xs text-slate-600 dark:text-slate-400 truncate">
            {artistName}
          </p>
        </div>


      </div>
    );
  }

  // Mode étendu : lecteur Spotify complet
  return (
    <div className="space-y-2">
      {compact && (
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <button
              onClick={handleTogglePlayer}
              className="flex-shrink-0 w-6 h-6 bg-slate-200 dark:bg-slate-600 hover:bg-slate-300 dark:hover:bg-slate-500 rounded-full flex items-center justify-center transition-colors"
              title="Masquer le lecteur"
            >
              <Pause className="w-3 h-3" />
            </button>
            <span className="text-sm font-medium text-slate-700 dark:text-slate-300">
              Titre phare
            </span>
          </div>
          

        </div>
      )}

      <div className="relative">
        {isLoading && (
          <div className="absolute inset-0 flex items-center justify-center bg-slate-100 dark:bg-slate-800 rounded-lg">
            <div className="flex items-center space-x-2 text-slate-600 dark:text-slate-400">
              <div className="w-4 h-4 border-2 border-[#1DB954] border-t-transparent rounded-full animate-spin" />
              <span className="text-sm">Chargement du lecteur Spotify...</span>
            </div>
          </div>
        )}
        
        <iframe
          src={embedUrl}
          width="100%"
          height={compact ? "152" : "352"}
          frameBorder="0"
          allowTransparency={true}
          allow="autoplay; clipboard-write; encrypted-media; fullscreen; picture-in-picture"
          loading="lazy"
          onLoad={handleIframeLoad}
          className={`rounded-lg ${isLoading ? 'opacity-0' : 'opacity-100'} transition-opacity duration-300`}
          title={`Lecteur Spotify pour ${trackName} par ${artistName}`}
        />
      </div>

      {!compact && (
        <div className="text-center">
          <p className="text-sm font-medium text-slate-900 dark:text-slate-100">
            {trackName}
          </p>
          <p className="text-xs text-slate-600 dark:text-slate-400">
            {artistName} • {albumName}
          </p>
        </div>
      )}
    </div>
  );
}
