"use client";

import { useEffect, useRef } from 'react';
import { useAudioPlayer } from '@/store/audio-player';

/**
 * Composant AudioPlayer global
 * US 3.6: Gère la lecture audio des extraits de titres phares
 * 
 * Ce composant contient une balise audio invisible qui est contrôlée
 * par le store global useAudioPlayer. Il assure qu'une seule piste
 * joue à la fois dans toute l'application.
 */
export function GlobalAudioPlayer() {
  const audioRef = useRef<HTMLAudioElement>(null);
  const {
    currentTrackUrl,
    isPlaying,
    isLoading,
    setLoading,
    pauseTrack,
    stopTrack
  } = useAudioPlayer();

  // Effet pour gérer les changements d'état du lecteur
  useEffect(() => {
    const audio = audioRef.current;
    if (!audio) return;

    const handleCanPlay = () => {
      setLoading(false);
      if (isPlaying) {
        audio.play().catch((error) => {
          console.error('Erreur lors de la lecture audio:', error);
          setLoading(false);
          pauseTrack();
        });
      }
    };

    const handleLoadStart = () => {
      setLoading(true);
    };

    const handleError = (error: Event) => {
      // Ne pas logger d'erreur si il n'y a pas de source audio
      if (currentTrackUrl) {
        console.error('Erreur de chargement audio:', error);
      }
      setLoading(false);
      pauseTrack();
    };

    const handleEnded = () => {
      stopTrack();
    };

    const handlePause = () => {
      // Ne pas mettre à jour l'état si c'est nous qui avons déclenché la pause
      if (isPlaying) {
        pauseTrack();
      }
    };

    // Ajouter les event listeners
    audio.addEventListener('canplay', handleCanPlay);
    audio.addEventListener('loadstart', handleLoadStart);
    audio.addEventListener('error', handleError);
    audio.addEventListener('ended', handleEnded);
    audio.addEventListener('pause', handlePause);

    return () => {
      // Nettoyer les event listeners
      audio.removeEventListener('canplay', handleCanPlay);
      audio.removeEventListener('loadstart', handleLoadStart);
      audio.removeEventListener('error', handleError);
      audio.removeEventListener('ended', handleEnded);
      audio.removeEventListener('pause', handlePause);
    };
  }, [isPlaying, setLoading, pauseTrack, stopTrack, currentTrackUrl]);

  // Effet pour gérer les changements d'URL de track
  useEffect(() => {
    const audio = audioRef.current;
    if (!audio) return;

    if (currentTrackUrl) {
      // Nouveau track à charger
      audio.src = currentTrackUrl;
      audio.load(); // Force le rechargement
      
      if (isPlaying) {
        setLoading(true);
        // La lecture se déclenchera dans handleCanPlay
      }
    } else {
      // Pas de track, arrêter et vider la source
      audio.pause();
      // Vider la source sans déclencher d'erreur
      audio.removeAttribute('src');
      audio.load();
      setLoading(false);
    }
  }, [currentTrackUrl, isPlaying, setLoading]);

  // Effet pour gérer play/pause
  useEffect(() => {
    const audio = audioRef.current;
    if (!audio || !currentTrackUrl) return;

    if (isPlaying && audio.paused) {
      // Démarrer la lecture si elle n'est pas déjà en cours
      audio.play().catch((error) => {
        console.error('Erreur lors de la lecture audio:', error);
        pauseTrack();
      });
    } else if (!isPlaying && !audio.paused) {
      // Mettre en pause si elle est en cours
      audio.pause();
    }
  }, [isPlaying, currentTrackUrl, pauseTrack]);

  return (
    <audio
      ref={audioRef}
      preload="none"
      className="hidden"
      // Propriétés pour optimiser la lecture des extraits
      crossOrigin="anonymous"
    />
  );
}
