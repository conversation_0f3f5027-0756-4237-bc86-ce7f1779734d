import { Meta } from '@storybook/blocks';

<Meta title="Design System/Tokens" />

# Design System Stream2Spin

Cette page documente les tokens de design utilisés dans Stream2Spin, incluant les couleurs, la typographie et les espacements.

## Couleurs

Notre palette de couleurs est basée sur un système de variables CSS HSL avec support du mode sombre et clair.

### Couleurs Principales

<div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '1rem', marginTop: '1rem' }}>
  <div style={{ display: 'flex', flexDirection: 'column', gap: '0.5rem' }}>
    <div style={{ backgroundColor: 'hsl(254 100% 60%)', height: '80px', borderRadius: '0.5rem', display: 'flex', alignItems: 'center', justifyContent: 'center', color: 'hsl(210 40% 98%)', fontWeight: 'bold' }}>
      Primary
    </div>
    <div style={{ fontSize: '0.875rem', color: 'hsl(var(--muted-foreground))' }}>
      <strong>Light:</strong> hsl(254 100% 60%)<br />
      <strong>Dark:</strong> hsl(254 100% 70%)<br />
      <strong>Usage:</strong> Boutons principaux, liens actifs
    </div>
  </div>

  <div style={{ display: 'flex', flexDirection: 'column', gap: '0.5rem' }}>
    <div style={{ backgroundColor: 'hsl(210 40% 96%)', height: '80px', borderRadius: '0.5rem', display: 'flex', alignItems: 'center', justifyContent: 'center', color: 'hsl(222.2 84% 4.9%)', fontWeight: 'bold' }}>
      Secondary
    </div>
    <div style={{ fontSize: '0.875rem', color: 'hsl(var(--muted-foreground))' }}>
      <strong>Light:</strong> hsl(210 40% 96%)<br />
      <strong>Dark:</strong> hsl(217.2 32.6% 17.5%)<br />
      <strong>Usage:</strong> Boutons secondaires, backgrounds
    </div>
  </div>

  <div style={{ display: 'flex', flexDirection: 'column', gap: '0.5rem' }}>
    <div style={{ backgroundColor: 'hsl(0 84.2% 60.2%)', height: '80px', borderRadius: '0.5rem', display: 'flex', alignItems: 'center', justifyContent: 'center', color: 'hsl(210 40% 98%)', fontWeight: 'bold' }}>
      Destructive
    </div>
    <div style={{ fontSize: '0.875rem', color: 'hsl(var(--muted-foreground))' }}>
      <strong>Light:</strong> hsl(0 84.2% 60.2%)<br />
      <strong>Dark:</strong> hsl(0 62.8% 30.6%)<br />
      <strong>Usage:</strong> Erreurs, suppressions
    </div>
  </div>

  <div style={{ display: 'flex', flexDirection: 'column', gap: '0.5rem' }}>
    <div style={{ backgroundColor: 'hsl(210 40% 96%)', height: '80px', borderRadius: '0.5rem', display: 'flex', alignItems: 'center', justifyContent: 'center', color: 'hsl(222.2 84% 4.9%)', fontWeight: 'bold' }}>
      Muted
    </div>
    <div style={{ fontSize: '0.875rem', color: 'hsl(var(--muted-foreground))' }}>
      <strong>Light:</strong> hsl(210 40% 96%)<br />
      <strong>Dark:</strong> hsl(217.2 32.6% 17.5%)<br />
      <strong>Usage:</strong> Backgrounds subtils, textes secondaires
    </div>
  </div>
</div>

### Couleurs de Surface

<div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '1rem', marginTop: '1rem' }}>
  <div style={{ display: 'flex', flexDirection: 'column', gap: '0.5rem' }}>
    <div style={{ backgroundColor: 'hsl(0 0% 100%)', height: '80px', borderRadius: '0.5rem', display: 'flex', alignItems: 'center', justifyContent: 'center', color: 'hsl(222.2 84% 4.9%)', fontWeight: 'bold', border: '1px solid hsl(214.3 31.8% 91.4%)' }}>
      Background
    </div>
    <div style={{ fontSize: '0.875rem', color: 'hsl(var(--muted-foreground))' }}>
      <strong>Light:</strong> hsl(0 0% 100%)<br />
      <strong>Dark:</strong> hsl(222.2 84% 4.9%)<br />
      <strong>Usage:</strong> Arrière-plan principal
    </div>
  </div>

  <div style={{ display: 'flex', flexDirection: 'column', gap: '0.5rem' }}>
    <div style={{ backgroundColor: 'hsl(0 0% 100%)', height: '80px', borderRadius: '0.5rem', display: 'flex', alignItems: 'center', justifyContent: 'center', color: 'hsl(222.2 84% 4.9%)', fontWeight: 'bold', border: '1px solid hsl(214.3 31.8% 91.4%)' }}>
      Card
    </div>
    <div style={{ fontSize: '0.875rem', color: 'hsl(var(--muted-foreground))' }}>
      <strong>Light:</strong> hsl(0 0% 100%)<br />
      <strong>Dark:</strong> hsl(222.2 84% 4.9%)<br />
      <strong>Usage:</strong> Cartes, conteneurs
    </div>
  </div>

  <div style={{ display: 'flex', flexDirection: 'column', gap: '0.5rem' }}>
    <div style={{ backgroundColor: 'hsl(214.3 31.8% 91.4%)', height: '80px', borderRadius: '0.5rem', display: 'flex', alignItems: 'center', justifyContent: 'center', color: 'hsl(222.2 84% 4.9%)', fontWeight: 'bold' }}>
      Border
    </div>
    <div style={{ fontSize: '0.875rem', color: 'hsl(var(--muted-foreground))' }}>
      <strong>Light:</strong> hsl(214.3 31.8% 91.4%)<br />
      <strong>Dark:</strong> hsl(217.2 32.6% 17.5%)<br />
      <strong>Usage:</strong> Bordures, séparateurs
    </div>
  </div>
</div>

## Typographie

Notre typographie utilise les classes Tailwind CSS standard avec des tailles et poids personnalisés.

### Hiérarchie des Titres

<div style={{ display: 'flex', flexDirection: 'column', gap: '1.5rem', marginTop: '1rem' }}>
  <div>
    <h1 style={{ fontSize: '2.25rem', fontWeight: 800, lineHeight: 1.2, marginBottom: '0.5rem' }}>
      Heading 1 - 36px / 800
    </h1>
    <code style={{ fontSize: '0.875rem', color: 'hsl(var(--muted-foreground))' }}>
      className="text-4xl font-extrabold"
    </code>
  </div>

  <div>
    <h2 style={{ fontSize: '1.875rem', fontWeight: 700, lineHeight: 1.3, marginBottom: '0.5rem' }}>
      Heading 2 - 30px / 700
    </h2>
    <code style={{ fontSize: '0.875rem', color: 'hsl(var(--muted-foreground))' }}>
      className="text-3xl font-bold"
    </code>
  </div>

  <div>
    <h3 style={{ fontSize: '1.5rem', fontWeight: 600, lineHeight: 1.4, marginBottom: '0.5rem' }}>
      Heading 3 - 24px / 600
    </h3>
    <code style={{ fontSize: '0.875rem', color: 'hsl(var(--muted-foreground))' }}>
      className="text-2xl font-semibold"
    </code>
  </div>

  <div>
    <h4 style={{ fontSize: '1.25rem', fontWeight: 500, lineHeight: 1.5, marginBottom: '0.5rem' }}>
      Heading 4 - 20px / 500
    </h4>
    <code style={{ fontSize: '0.875rem', color: 'hsl(var(--muted-foreground))' }}>
      className="text-xl font-medium"
    </code>
  </div>
</div>

### Texte de Corps

<div style={{ display: 'flex', flexDirection: 'column', gap: '1rem', marginTop: '1rem' }}>
  <div>
    <p style={{ fontSize: '1rem', lineHeight: 1.5, marginBottom: '0.5rem' }}>
      Body Large - 16px / 400 - Texte principal des interfaces
    </p>
    <code style={{ fontSize: '0.875rem', color: 'hsl(var(--muted-foreground))' }}>
      className="text-base"
    </code>
  </div>

  <div>
    <p style={{ fontSize: '0.875rem', lineHeight: 1.5, marginBottom: '0.5rem' }}>
      Body Medium - 14px / 400 - Texte secondaire et descriptions
    </p>
    <code style={{ fontSize: '0.875rem', color: 'hsl(var(--muted-foreground))' }}>
      className="text-sm"
    </code>
  </div>

  <div>
    <p style={{ fontSize: '0.75rem', lineHeight: 1.4, marginBottom: '0.5rem' }}>
      Body Small - 12px / 400 - Labels, légendes et métadonnées
    </p>
    <code style={{ fontSize: '0.875rem', color: 'hsl(var(--muted-foreground))' }}>
      className="text-xs"
    </code>
  </div>
</div>

## Espacements

Notre système d'espacement suit l'échelle Tailwind CSS basée sur des multiples de 0.25rem (4px).

### Échelle d'Espacement

<div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '1rem', marginTop: '1rem' }}>
  {[
    { name: 'xs', value: '0.25rem', px: '4px', class: 'space-1' },
    { name: 'sm', value: '0.5rem', px: '8px', class: 'space-2' },
    { name: 'md', value: '1rem', px: '16px', class: 'space-4' },
    { name: 'lg', value: '1.5rem', px: '24px', class: 'space-6' },
    { name: 'xl', value: '2rem', px: '32px', class: 'space-8' },
    { name: '2xl', value: '3rem', px: '48px', class: 'space-12' },
  ].map((space, index) => (
    <div key={index} style={{ border: '1px solid hsl(214.3 31.8% 91.4%)', padding: '1rem', borderRadius: '0.5rem' }}>
      <div style={{ fontWeight: 600, marginBottom: '0.5rem' }}>{space.name}</div>
      <div style={{ backgroundColor: 'hsl(254 100% 60%)', height: space.value, borderRadius: '0.25rem', marginBottom: '0.5rem' }}></div>
      <div style={{ fontSize: '0.875rem', color: 'hsl(var(--muted-foreground))' }}>
        <div>{space.value} ({space.px})</div>
        <code>{space.class}</code>
      </div>
    </div>
  ))}
</div>

### Utilisation des Espacements

<div style={{ marginTop: '1.5rem' }}>
  <h4 style={{ fontSize: '1.125rem', fontWeight: 600, marginBottom: '1rem' }}>Recommandations d'Usage</h4>
  
  <div style={{ display: 'flex', flexDirection: 'column', gap: '0.75rem' }}>
    <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>
      <div style={{ backgroundColor: 'hsl(254 100% 60%)', width: '0.25rem', height: '0.25rem', borderRadius: '50%' }}></div>
      <span><strong>xs (4px):</strong> Espacement minimal entre éléments très proches</span>
    </div>
    
    <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>
      <div style={{ backgroundColor: 'hsl(254 100% 60%)', width: '0.5rem', height: '0.5rem', borderRadius: '50%' }}></div>
      <span><strong>sm (8px):</strong> Espacement dans les boutons et badges</span>
    </div>
    
    <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>
      <div style={{ backgroundColor: 'hsl(254 100% 60%)', width: '1rem', height: '1rem', borderRadius: '50%' }}></div>
      <span><strong>md (16px):</strong> Espacement standard entre éléments</span>
    </div>
    
    <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>
      <div style={{ backgroundColor: 'hsl(254 100% 60%)', width: '1.5rem', height: '1.5rem', borderRadius: '50%' }}></div>
      <span><strong>lg (24px):</strong> Espacement entre sections dans les cartes</span>
    </div>
    
    <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>
      <div style={{ backgroundColor: 'hsl(254 100% 60%)', width: '2rem', height: '2rem', borderRadius: '50%' }}></div>
      <span><strong>xl (32px):</strong> Espacement entre grandes sections</span>
    </div>
    
    <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>
      <div style={{ backgroundColor: 'hsl(254 100% 60%)', width: '3rem', height: '3rem', borderRadius: '50%' }}></div>
      <span><strong>2xl (48px):</strong> Espacement entre pages ou sections majeures</span>
    </div>
  </div>
</div>

## Rayons de Bordure

Notre système utilise une valeur de rayon personnalisée définie dans les variables CSS.

<div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(150px, 1fr))', gap: '1rem', marginTop: '1rem' }}>
  <div style={{ textAlign: 'center' }}>
    <div style={{ 
      backgroundColor: 'hsl(254 100% 60%)', 
      height: '80px', 
      borderRadius: '0.5rem',
      marginBottom: '0.5rem'
    }}></div>
    <div style={{ fontSize: '0.875rem', fontWeight: 600 }}>Default</div>
    <div style={{ fontSize: '0.75rem', color: 'hsl(var(--muted-foreground))' }}>
      0.5rem (8px)<br />
      <code>rounded</code>
    </div>
  </div>

  <div style={{ textAlign: 'center' }}>
    <div style={{ 
      backgroundColor: 'hsl(254 100% 60%)', 
      height: '80px', 
      borderRadius: '0.25rem',
      marginBottom: '0.5rem'
    }}></div>
    <div style={{ fontSize: '0.875rem', fontWeight: 600 }}>Small</div>
    <div style={{ fontSize: '0.75rem', color: 'hsl(var(--muted-foreground))' }}>
      0.25rem (4px)<br />
      <code>rounded-sm</code>
    </div>
  </div>

  <div style={{ textAlign: 'center' }}>
    <div style={{ 
      backgroundColor: 'hsl(254 100% 60%)', 
      height: '80px', 
      borderRadius: '0.75rem',
      marginBottom: '0.5rem'
    }}></div>
    <div style={{ fontSize: '0.875rem', fontWeight: 600 }}>Large</div>
    <div style={{ fontSize: '0.75rem', color: 'hsl(var(--muted-foreground))' }}>
      0.75rem (12px)<br />
      <code>rounded-lg</code>
    </div>
  </div>

  <div style={{ textAlign: 'center' }}>
    <div style={{ 
      backgroundColor: 'hsl(254 100% 60%)', 
      height: '80px', 
      borderRadius: '9999px',
      marginBottom: '0.5rem'
    }}></div>
    <div style={{ fontSize: '0.875rem', fontWeight: 600 }}>Full</div>
    <div style={{ fontSize: '0.75rem', color: 'hsl(var(--muted-foreground))' }}>
      9999px<br />
      <code>rounded-full</code>
    </div>
  </div>
</div>

## Ombres

Les ombres utilisent les valeurs par défaut de Tailwind CSS pour créer de la profondeur.

<div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '1rem', marginTop: '1rem' }}>
  {[
    { name: 'Small', class: 'shadow-sm', shadow: '0 1px 2px 0 rgb(0 0 0 / 0.05)' },
    { name: 'Medium', class: 'shadow-md', shadow: '0 4px 6px -1px rgb(0 0 0 / 0.1)' },
    { name: 'Large', class: 'shadow-lg', shadow: '0 10px 15px -3px rgb(0 0 0 / 0.1)' },
    { name: 'XL', class: 'shadow-xl', shadow: '0 20px 25px -5px rgb(0 0 0 / 0.1)' },
  ].map((shadow, index) => (
    <div key={index} style={{ textAlign: 'center' }}>
      <div style={{ 
        backgroundColor: 'hsl(0 0% 100%)', 
        height: '80px', 
        borderRadius: '0.5rem',
        marginBottom: '0.5rem',
        boxShadow: shadow.shadow,
        border: '1px solid hsl(214.3 31.8% 91.4%)'
      }}></div>
      <div style={{ fontSize: '0.875rem', fontWeight: 600 }}>{shadow.name}</div>
      <div style={{ fontSize: '0.75rem', color: 'hsl(var(--muted-foreground))' }}>
        <code>{shadow.class}</code>
      </div>
    </div>
  ))}
</div>

---

*Cette documentation est générée automatiquement à partir des tokens de design définis dans notre configuration Tailwind et nos variables CSS.* 