"use client";

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { Languages, CheckCircle } from "lucide-react";
import { updateLanguage } from "@/app/actions/user";
import { useTransition, useState } from "react";
import { useRouter } from "next/navigation";
import { useTranslations } from 'next-intl';

interface LanguageSettingsCardProps {
  currentLanguage: string;
}

const languageOptions = [
  { value: "fr", label: "Français", flag: "🇫🇷" },
  { value: "en", label: "English", flag: "🇺🇸" },
];

export function LanguageSettingsCard({ currentLanguage }: LanguageSettingsCardProps) {
  const [isPending, startTransition] = useTransition();
  const [showSuccess, setShowSuccess] = useState(false);
  const router = useRouter();
  const t = useTranslations('account.language');

  const handleLanguageChange = (newLanguage: string) => {
    startTransition(async () => {
      try {
        await updateLanguage(newLanguage);
        setShowSuccess(true);
        setTimeout(() => setShowSuccess(false), 3000);

        // Recharger la page pour appliquer la nouvelle langue
        router.refresh();
      } catch (error) {
        console.error("Erreur lors de la mise à jour de la langue:", error);
      }
    });
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Languages className="w-5 h-5" />
          {t('title')}
        </CardTitle>
        <CardDescription>
          {t('description')}
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <div className="flex items-center gap-2">
            <Label htmlFor="language-select" className="text-sm font-medium">
              {t('label')}
            </Label>
            {showSuccess && (
              <CheckCircle className="w-4 h-4 text-green-500 animate-in fade-in duration-300" />
            )}
          </div>
          <Select
            value={currentLanguage}
            onValueChange={handleLanguageChange}
            disabled={isPending}
          >
            <SelectTrigger id="language-select" className="w-full">
              <SelectValue placeholder={t('placeholder')} />
            </SelectTrigger>
            <SelectContent>
              {languageOptions.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  <div className="flex items-center gap-2">
                    <span>{option.flag}</span>
                    <span>{option.label}</span>
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {isPending && (
          <div className="text-sm text-muted-foreground">
            {t('updating')}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
