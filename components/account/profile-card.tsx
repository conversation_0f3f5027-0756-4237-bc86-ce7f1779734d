import { <PERSON>, CardContent, Card<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { User, Mail } from "lucide-react";

interface ProfileCardProps {
  user: {
    name?: string | null;
    email?: string | null;
    image?: string | null;
  };
  translations: {
    title: string;
    description: string;
    username: string;
    email: string;
    profilePicture: string;
    syncedFromSpotify: string;
    notDefined: string;
  };
}

export function ProfileCard({ user, translations }: ProfileCardProps) {

  // Générer les initiales à partir du nom ou de l'email
  const getInitials = () => {
    if (user.name) {
      return user.name
        .split(" ")
        .map((n) => n[0])
        .join("")
        .toUpperCase()
        .slice(0, 2);
    }
    if (user.email) {
      return user.email.slice(0, 2).toUpperCase();
    }
    return "U";
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <User className="w-5 h-5" />
          {translations.title}
        </CardTitle>
        <CardDescription>
          {translations.description}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium text-muted-foreground">
                {translations.username}
              </label>
              <div className="mt-1 p-3 bg-muted rounded-md border">
                {user.name || translations.notDefined}
              </div>
            </div>

            <div>
              <label className="text-sm font-medium text-muted-foreground">
                {translations.email}
              </label>
              <div className="mt-1 p-3 bg-muted rounded-md border flex items-center gap-2">
                <Mail className="w-4 h-4 text-muted-foreground" />
                {user.email || translations.notDefined}
              </div>
            </div>
          </div>
          
          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium text-muted-foreground">
                {translations.profilePicture}
              </label>
              <div className="mt-1 flex items-center gap-4">
                <Avatar className="h-16 w-16">
                  <AvatarImage
                    src={user.image || undefined}
                    alt={user.name || user.email || "User"}
                  />
                  <AvatarFallback className="bg-gradient-to-br from-primary to-primary/80 text-primary-foreground font-semibold text-lg">
                    {getInitials()}
                  </AvatarFallback>
                </Avatar>
                <div className="text-sm text-muted-foreground">
                  {translations.syncedFromSpotify}
                </div>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
