"use client";

import { useEffect } from "react";
import { useSearchParams } from "next/navigation";
import { toast } from "sonner";
import { useTranslations } from 'next-intl';

export function SuccessToastHandler() {
  const searchParams = useSearchParams();
  const tMessages = useTranslations('messages');

  useEffect(() => {
    const success = searchParams.get('success');
    
    if (success === 'discogs_connected') {
      toast.success(tMessages('discogsConnected'));
      
      // Nettoyer l'URL sans recharger la page
      const url = new URL(window.location.href);
      url.searchParams.delete('success');
      window.history.replaceState({}, '', url.toString());
    }
  }, [searchParams, tMessages]);

  return null;
}
