"use client";

import { useState, useEffect } from "react";
import { Clock, CheckCircle, AlertCircle, Loader2 } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { useTranslations } from 'next-intl';

interface SyncStatusInfoProps {
  type: "spotify" | "discogs";
  isConnected: boolean;
  isSyncing: boolean;
}

interface SyncInfo {
  lastSync?: Date;
  status: "never" | "recent" | "old" | "syncing";
  count?: number;
}

export function SyncStatusInfo({ type, isConnected, isSyncing }: SyncStatusInfoProps) {
  const [syncInfo, setSyncInfo] = useState<SyncInfo>({ status: "never" });
  const [isLoading, setIsLoading] = useState(true);
  const t = useTranslations('account.connectedAccounts.syncStatus');

  useEffect(() => {
    if (!isConnected) {
      setSyncInfo({ status: "never" });
      setIsLoading(false);
      return;
    }

    if (isSyncing) {
      setSyncInfo(prev => ({ ...prev, status: "syncing" }));
      return;
    }

    // Récupérer les vraies informations de synchronisation depuis l'API
    const fetchSyncInfo = async () => {
      try {
        setIsLoading(true);

        const response = await fetch(`/api/sync-status?type=${type}`);
        if (!response.ok) {
          throw new Error(`HTTP ${response.status}`);
        }

        const result = await response.json();

        if (result.success && result.data) {
          const data = result.data;
          const lastSync = data.lastSync ? new Date(data.lastSync) : undefined;

          setSyncInfo({
            lastSync,
            status: data.status,
            count: data.count
          });
        } else {
          setSyncInfo({ status: "never" });
        }
      } catch (error) {
        console.error("Erreur lors de la récupération des infos de sync:", error);
        setSyncInfo({ status: "never" });
      } finally {
        setIsLoading(false);
      }
    };

    fetchSyncInfo();
  }, [type, isConnected, isSyncing]);

  if (!isConnected) {
    return null;
  }

  if (isLoading) {
    return (
      <div className="flex items-center gap-2 text-xs text-muted-foreground">
        <Loader2 className="w-3 h-3 animate-spin" />
        Vérification...
      </div>
    );
  }

  const getStatusInfo = () => {
    switch (syncInfo.status) {
      case "syncing":
        return {
          icon: <Loader2 className="w-3 h-3 animate-spin" />,
          text: t('refreshing'),
          variant: "secondary" as const,
          color: "text-primary"
        };
      case "recent":
        return {
          icon: <CheckCircle className="w-3 h-3" />,
          text: formatLastSync(syncInfo.lastSync!, t),
          variant: "secondary" as const,
          color: "text-green-600"
        };
      case "old":
        return {
          icon: <Clock className="w-3 h-3" />,
          text: formatLastSync(syncInfo.lastSync!, t),
          variant: "outline" as const,
          color: "text-orange-600"
        };
      case "never":
      default:
        return {
          icon: <AlertCircle className="w-3 h-3" />,
          text: t('never'),
          variant: "outline" as const,
          color: "text-muted-foreground"
        };
    }
  };

  const statusInfo = getStatusInfo();

  return (
    <div className="flex flex-col sm:flex-row sm:items-center gap-1 sm:gap-2 mt-1">
      <Badge variant={statusInfo.variant} className="text-xs w-fit">
        <span className={`flex items-center gap-1 ${statusInfo.color}`}>
          {statusInfo.icon}
          <span className="truncate">{statusInfo.text}</span>
        </span>
      </Badge>
      {syncInfo.count && (
        <span className="text-xs text-muted-foreground truncate">
          {type === "spotify" ? t('recommendations', { count: syncInfo.count }) : `${syncInfo.count} albums`}
        </span>
      )}
    </div>
  );
}

function formatLastSync(date: Date, t: any): string {
  const now = new Date();
  const diffMs = now.getTime() - date.getTime();
  const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
  const diffDays = Math.floor(diffHours / 24);

  if (diffHours < 1) {
    return t('justNow');
  } else if (diffHours < 24) {
    return t('hoursAgo', { hours: diffHours });
  } else if (diffDays === 1) {
    return t('daysAgo', { days: 1 });
  } else if (diffDays < 7) {
    return t('daysAgo', { days: diffDays });
  } else {
    return date.toLocaleDateString('fr-FR', {
      day: 'numeric',
      month: 'short'
    });
  }
}
