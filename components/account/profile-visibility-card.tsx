"use client";

import { useState, useTransition, useEffect, useRef, useCallback } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Globe, Users, Lock, ListMusic, Heart, Disc, Loader2, Check } from "lucide-react";
import { updateVisibilitySettings, type VisibilitySettings } from "@/app/actions/profile";
import { toast } from "sonner";
import { useTranslations } from 'next-intl';

const formSchema = z.object({
  profileVisibility: z.enum(["public", "users_only", "private"]),
  shareRecommendations: z.boolean(),
  shareWishlist: z.boolean(),
  shareCollection: z.boolean(),
});

export function ProfileVisibilityCard({ initialSettings }: { initialSettings: VisibilitySettings }) {
  const t = useTranslations('account.visibility');
  const [isPending, startTransition] = useTransition();
  const [showSavedIndicator, setShowSavedIndicator] = useState(false);
  const saveTimeoutRef = useRef<NodeJS.Timeout | undefined>(undefined);
  const lastSavedDataRef = useRef<VisibilitySettings>(initialSettings);
  
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: initialSettings,
  });

  const currentData = form.watch();

  // Fonction de sauvegarde automatique
  const autoSave = useCallback(async (data: VisibilitySettings) => {
    // Comparer avec les dernières données sauvegardées
    if (JSON.stringify(data) === JSON.stringify(lastSavedDataRef.current)) {
      return; // Pas de changement
    }

    startTransition(async () => {
      const result = await updateVisibilitySettings(data);
      if (result.success) {
        lastSavedDataRef.current = data;
        setShowSavedIndicator(true);
        
        // Masquer l'indicateur après 2 secondes
        setTimeout(() => {
          setShowSavedIndicator(false);
        }, 2000);
        
        form.reset(data);
      } else {
        toast.error(result.error || t('updateError'));
      }
    });
  }, [form, t]);

  // Effet pour déclencher la sauvegarde automatique
  useEffect(() => {
    // Ne pas sauvegarder si les données n'ont pas changé depuis l'initialisation
    if (JSON.stringify(currentData) === JSON.stringify(initialSettings)) {
      return;
    }

    // Annuler le timeout précédent
    if (saveTimeoutRef.current) {
      clearTimeout(saveTimeoutRef.current);
    }

    // Programmer une nouvelle sauvegarde après 1 seconde
    saveTimeoutRef.current = setTimeout(() => {
      autoSave(currentData);
    }, 1000);

    // Nettoyer le timeout
    return () => {
      if (saveTimeoutRef.current) {
        clearTimeout(saveTimeoutRef.current);
      }
    };
  }, [currentData, initialSettings, autoSave]);

  // Nettoyer les timeouts au démontage
  useEffect(() => {
    return () => {
      if (saveTimeoutRef.current) {
        clearTimeout(saveTimeoutRef.current);
      }
    };
  }, []);

  const visibilityLevel = form.watch("profileVisibility");

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Globe className="w-5 h-5" />
          {t('title')}
          {/* Indicateur de sauvegarde */}
          <div className="ml-auto flex items-center gap-2">
            {isPending && (
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <Loader2 className="h-4 w-4 animate-spin" />
                Sauvegarde...
              </div>
            )}
            {showSavedIndicator && !isPending && (
              <div className="flex items-center gap-2 text-sm text-green-600">
                <Check className="h-4 w-4" />
                Sauvegardé
              </div>
            )}
          </div>
        </CardTitle>
        <CardDescription>{t('description')}</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-8">
          <RadioGroup
            name="profileVisibility"
            value={visibilityLevel}
            onValueChange={(value) => form.setValue("profileVisibility", value as any, { shouldDirty: true })}
            disabled={isPending}
            className="space-y-4"
          >
            <Label className="text-base font-semibold">{t('level.title')}</Label>
            <div className="flex items-start space-x-3">
              <RadioGroupItem value="public" id="public" />
              <div className="grid gap-1.5 leading-none">
                <Label htmlFor="public" className="flex items-center gap-2"><Globe className="w-4 h-4" />{t('level.public.label')}</Label>
                <p className="text-xs text-muted-foreground">{t('level.public.description')}</p>
              </div>
            </div>
            <div className="flex items-start space-x-3">
              <RadioGroupItem value="users_only" id="users_only" />
              <div className="grid gap-1.5 leading-none">
                <Label htmlFor="users_only" className="flex items-center gap-2"><Users className="w-4 h-4" />{t('level.users_only.label')}</Label>
                <p className="text-xs text-muted-foreground">{t('level.users_only.description')}</p>
              </div>
            </div>
            <div className="flex items-start space-x-3">
              <RadioGroupItem value="private" id="private" />
              <div className="grid gap-1.5 leading-none">
                <Label htmlFor="private" className="flex items-center gap-2"><Lock className="w-4 h-4" />{t('level.private.label')}</Label>
                <p className="text-xs text-muted-foreground">{t('level.private.description')}</p>
              </div>
            </div>
          </RadioGroup>

          <div className={`space-y-4 transition-opacity ${visibilityLevel === 'private' ? 'opacity-50' : ''}`}>
            <Label className="text-base font-semibold">{t('sharing.title')}</Label>
            <div className="flex items-center justify-between rounded-lg border p-4">
              <div className="space-y-0.5">
                <Label htmlFor="shareRecommendations" className="flex items-center gap-2"><ListMusic className="w-4 h-4" />{t('sharing.recommendations')}</Label>
              </div>
              <Switch
                id="shareRecommendations"
                checked={form.watch("shareRecommendations")}
                onCheckedChange={(checked) => form.setValue("shareRecommendations", checked, { shouldDirty: true })}
                disabled={isPending || visibilityLevel === 'private'}
              />
            </div>
            <div className="flex items-center justify-between rounded-lg border p-4">
              <div className="space-y-0.5">
                <Label htmlFor="shareWishlist" className="flex items-center gap-2"><Heart className="w-4 h-4" />{t('sharing.wishlist')}</Label>
              </div>
              <Switch
                id="shareWishlist"
                checked={form.watch("shareWishlist")}
                onCheckedChange={(checked) => form.setValue("shareWishlist", checked, { shouldDirty: true })}
                disabled={isPending || visibilityLevel === 'private'}
              />
            </div>
            <div className="flex items-center justify-between rounded-lg border p-4">
              <div className="space-y-0.5">
                <Label htmlFor="shareCollection" className="flex items-center gap-2"><Disc className="w-4 h-4" />{t('sharing.collection')}</Label>
              </div>
              <Switch
                id="shareCollection"
                checked={form.watch("shareCollection")}
                onCheckedChange={(checked) => form.setValue("shareCollection", checked, { shouldDirty: true })}
                disabled={isPending || visibilityLevel === 'private'}
              />
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
