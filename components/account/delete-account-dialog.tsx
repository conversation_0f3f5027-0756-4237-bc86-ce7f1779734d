"use client";

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { deleteAccount } from "@/app/actions/user";
import { signOut } from "next-auth/react";
import { useTransition, useState } from "react";
import { AlertTriangle } from "lucide-react";
import { useTranslations } from 'next-intl';

interface DeleteAccountDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function DeleteAccountDialog({ open, onOpenChange }: DeleteAccountDialogProps) {
  const [isPending, startTransition] = useTransition();
  const [confirmationText, setConfirmationText] = useState("");
  const t = useTranslations('account.dangerZone.deleteAccount.dialog');
  const tAccount = useTranslations('account');

  const CONFIRMATION_TEXT = t('placeholder');
  const isConfirmationValid = confirmationText === CONFIRMATION_TEXT;

  const handleDelete = () => {
    if (!isConfirmationValid) return;

    startTransition(async () => {
      try {
        await deleteAccount();
        // Déconnecter l'utilisateur et rediriger vers la page d'accueil
        await signOut({ callbackUrl: "/" });
      } catch (error) {
        console.error("Erreur lors de la suppression du compte:", error);
        // En cas d'erreur, fermer le dialog
        onOpenChange(false);
        setConfirmationText("");
      }
    });
  };

  const handleOpenChange = (open: boolean) => {
    if (!open) {
      setConfirmationText("");
    }
    onOpenChange(open);
  };

  return (
    <AlertDialog open={open} onOpenChange={handleOpenChange}>
      <AlertDialogContent className="max-w-md">
        <AlertDialogHeader>
          <div className="flex items-center gap-2">
            <AlertTriangle className="w-6 h-6 text-destructive" />
            <AlertDialogTitle className="text-destructive">
              {t('title')}
            </AlertDialogTitle>
          </div>
          <div className="space-y-3 text-sm text-muted-foreground">
            <div>
              {t('description')}
            </div>
            <ul className="list-disc list-inside space-y-1 text-sm">
              {(() => {
                try {
                  const dataList = t.raw('dataList');
                  if (Array.isArray(dataList)) {
                    return dataList.map((item: string, index: number) => (
                      <li key={index}>{item}</li>
                    ));
                  }
                  return null;
                } catch (error) {
                  console.error('Error accessing dataList:', error);
                  return null;
                }
              })()}
            </ul>
            <div className="font-medium">
              {t('confirmText')} "<span className="font-mono bg-muted px-1 rounded">{CONFIRMATION_TEXT}</span>"
            </div>
          </div>
        </AlertDialogHeader>
        
        <div className="space-y-2">
          <Label htmlFor="confirmation-input" className="sr-only">
            {tAccount('deleteConfirmationLabel')}
          </Label>
          <Input
            id="confirmation-input"
            value={confirmationText}
            onChange={(e) => setConfirmationText(e.target.value)}
            placeholder={CONFIRMATION_TEXT}
            disabled={isPending}
            className="font-mono"
          />
        </div>

        <AlertDialogFooter>
          <AlertDialogCancel disabled={isPending}>
            {t('cancel')}
          </AlertDialogCancel>
          <AlertDialogAction
            onClick={handleDelete}
            disabled={!isConfirmationValid || isPending}
            className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
          >
            {isPending ? t('deleting') : t('confirm')}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
