"use client";

import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { AlertTriangle } from "lucide-react";
import { DeleteAccountDialog } from "./delete-account-dialog";
import { useState } from "react";
import { useTranslations } from 'next-intl';

export function DangerZoneCard() {
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const t = useTranslations('account.dangerZone');

  return (
    <Card className="border-destructive/50">
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-destructive">
          <AlertTriangle className="w-5 h-5" />
          {t('title')}
        </CardTitle>
        <CardDescription>
          {t('description')}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="p-4 bg-destructive/5 rounded-lg border border-destructive/20">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div className="min-w-0 flex-1">
              <div className="font-medium text-destructive">
                {t('deleteAccount.title')}
              </div>
              <div className="text-sm text-muted-foreground">
                {t('deleteAccount.description')}
              </div>
            </div>
            <div className="flex-shrink-0">
              <Button
                variant="destructive"
                size="sm"
                onClick={() => setShowDeleteDialog(true)}
                className="w-full sm:w-auto"
              >
                {t('deleteAccount.button')}
              </Button>
            </div>
          </div>
        </div>

        <DeleteAccountDialog 
          open={showDeleteDialog}
          onOpenChange={setShowDeleteDialog}
        />
      </CardContent>
    </Card>
  );
}
