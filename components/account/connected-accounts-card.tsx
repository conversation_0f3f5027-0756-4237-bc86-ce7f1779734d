"use client";

import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Shield, Info, RefreshCw } from "lucide-react";
import { signIn } from "next-auth/react";
import { DiscogsDisconnectDialog } from "./discogs-disconnect-dialog";
import { useState, useTransition } from "react";
import { useTranslations } from 'next-intl';
import Image from "next/image";
import { manualSyncSpotify, manualSyncDiscogs } from "@/app/actions/user";
import { toast } from "sonner";
import { SyncStatusInfo } from "./sync-status-info";

interface ConnectedAccountsCardProps {
  isDiscogsConnected: boolean;
}

export function ConnectedAccountsCard({ isDiscogsConnected }: ConnectedAccountsCardProps) {
  const [showDisconnectDialog, setShowDisconnectDialog] = useState(false);
  const [isSpotifySyncing, startSpotifySync] = useTransition();
  const [isDiscogsSyncing, startDiscogsSync] = useTransition();
  const t = useTranslations('account.connectedAccounts');

  const handleConnectDiscogs = async () => {
    try {
      // Appeler l'API de connexion Discogs avec le paramètre returnTo pour revenir sur account
      const response = await fetch("/api/discogs/connect?returnTo=/account");
      const data = await response.json();

      if (data.authorizeUrl) {
        window.location.href = data.authorizeUrl;
      } else {
        console.error("Erreur lors de la connexion Discogs:", data.error);
      }
    } catch (error) {
      console.error("Erreur lors de la connexion Discogs:", error);
    }
  };

  const handleSyncSpotify = () => {
    // Rediriger vers /generating pour régénérer les recommandations
    window.location.href = '/generating';
  };

  const handleSyncDiscogs = () => {
    startDiscogsSync(async () => {
      try {
        const result = await manualSyncDiscogs();
        if (result.success) {
          toast.success(`Discogs synchronisé ! ${result.message}`);

          // Déclencher un événement pour notifier les autres composants
          window.dispatchEvent(new CustomEvent('discogs-sync-completed', {
            detail: { syncedCount: result.syncedCount }
          }));
        } else {
          toast.error(`Erreur Discogs: ${result.error}`);
        }
      } catch (error) {
        toast.error("Erreur lors de la synchronisation Discogs");
      }
    });
  };



  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Shield className="w-5 h-5" />
          {t('title')}
        </CardTitle>
        <CardDescription>
          {t('description')}
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Ligne Spotify */}
        <div className="p-4 bg-green-50 dark:bg-green-950 rounded-lg border border-green-200 dark:border-green-800">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div className="flex items-center gap-3 min-w-0 flex-1">
              <div className="w-8 h-8 flex items-center justify-center flex-shrink-0">
                <Image
                  src="/Spotify_icon.svg"
                  alt="Spotify"
                  width={32}
                  height={32}
                  className="w-8 h-8"
                />
              </div>
              <div className="flex-1 min-w-0">
                <div className="font-medium text-green-800 dark:text-green-200">
                  {t('spotify.name')}
                </div>
                <div className="text-sm text-green-600 dark:text-green-400">
                  {t('spotify.connected')}
                </div>
                <SyncStatusInfo
                  type="spotify"
                  isConnected={true}
                  isSyncing={isSpotifySyncing}
                />
              </div>
            </div>
            <div className="flex items-center gap-2 flex-shrink-0">
              <Button
                variant="outline"
                size="sm"
                onClick={handleSyncSpotify}
                className="min-w-[120px]"
              >
                <RefreshCw className="w-4 h-4 mr-2" />
                {t('syncStatus.refresh')}
              </Button>
              <div className="group relative">
                <Info className="w-4 h-4 text-muted-foreground cursor-help" />
                <div className="absolute bottom-full right-0 mb-2 px-3 py-2 bg-popover text-popover-foreground text-xs rounded-md border shadow-md opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none z-10 max-w-[200px] sm:max-w-[250px] whitespace-normal">
                  {t('syncStatus.refreshDescription')}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Ligne Discogs */}
        <div className="p-4 bg-muted rounded-lg border">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div className="flex items-center gap-3 min-w-0 flex-1">
              <div className="w-8 h-8 flex items-center justify-center flex-shrink-0">
                <Image
                  src="/Discogs_record_icon.svg"
                  alt="Discogs"
                  width={32}
                  height={32}
                  className="w-8 h-8"
                />
              </div>
              <div className="flex-1 min-w-0">
                <div className="font-medium">
                  {t('discogs.name')}
                </div>
                <div className="text-sm text-muted-foreground">
                  {isDiscogsConnected ? t('discogs.connected') : t('discogs.notConnected')}
                </div>
                <SyncStatusInfo
                  type="discogs"
                  isConnected={isDiscogsConnected}
                  isSyncing={isDiscogsSyncing}
                />
              </div>
            </div>
            <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-2 flex-shrink-0">
              {isDiscogsConnected ? (
                <>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleSyncDiscogs}
                    disabled={isDiscogsSyncing}
                    className="min-w-[120px]"
                  >
                    {isDiscogsSyncing ? (
                      <>
                        <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                        {t('syncStatus.refreshing')}
                      </>
                    ) : (
                      <>
                        <RefreshCw className="w-4 h-4 mr-2" />
                        {t('syncStatus.refresh')}
                      </>
                    )}
                  </Button>
                  <div className="flex items-center gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setShowDisconnectDialog(true)}
                      className="text-destructive hover:text-destructive"
                    >
                      {t('discogs.disconnect')}
                    </Button>
                    <div className="group relative">
                      <Info className="w-4 h-4 text-muted-foreground cursor-help" />
                      <div className="absolute bottom-full right-0 mb-2 px-3 py-2 bg-popover text-popover-foreground text-xs rounded-md border shadow-md opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none z-10 max-w-[200px] sm:max-w-[250px] whitespace-normal">
                        {t('discogs.refreshTooltip')}
                      </div>
                    </div>
                  </div>
                </>
              ) : (
                <div className="flex items-center gap-2">
                  <Button
                    variant="default"
                    size="sm"
                    onClick={handleConnectDiscogs}
                  >
                    {t('discogs.connect')}
                  </Button>
                  <div className="group relative">
                    <Info className="w-4 h-4 text-muted-foreground cursor-help" />
                    <div className="absolute bottom-full right-0 mb-2 px-3 py-2 bg-popover text-popover-foreground text-xs rounded-md border shadow-md opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none z-10 max-w-[200px] sm:max-w-[250px] whitespace-normal">
                      {t('discogs.tooltip')}
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Description Discogs */}
        {!isDiscogsConnected && (
          <div className="text-sm text-muted-foreground bg-muted/50 p-3 rounded-md">
            {t('discogs.description')}
          </div>
        )}

        {/* Dialog de déconnexion Discogs */}
        <DiscogsDisconnectDialog 
          open={showDisconnectDialog}
          onOpenChange={setShowDisconnectDialog}
        />
      </CardContent>
    </Card>
  );
}
