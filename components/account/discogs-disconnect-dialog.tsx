"use client";

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { disconnectDiscogs } from "@/app/actions/user";
import { useTransition } from "react";
import { toast } from "sonner";
import { useTranslations } from 'next-intl';

interface DiscogsDisconnectDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function DiscogsDisconnectDialog({ open, onOpenChange }: DiscogsDisconnectDialogProps) {
  const [isPending, startTransition] = useTransition();
  const t = useTranslations('account.connectedAccounts.discogs.disconnectDialog');
  const tMessages = useTranslations('messages');

  const handleDisconnect = () => {
    startTransition(async () => {
      try {
        await disconnectDiscogs();
        toast.success(tMessages('discogsDisconnected'));
        onOpenChange(false);
      } catch (error) {
        toast.error(tMessages('discogsDisconnectError'));
      }
    });
  };

  return (
    <AlertDialog open={open} onOpenChange={onOpenChange}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>{t('title')}</AlertDialogTitle>
          <AlertDialogDescription>
            {t('description')}
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel disabled={isPending}>
            {t('cancel')}
          </AlertDialogCancel>
          <AlertDialogAction
            onClick={handleDisconnect}
            disabled={isPending}
            className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
          >
            {isPending ? t('disconnecting') : t('confirm')}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
