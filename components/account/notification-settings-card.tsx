"use client";

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Bell, Mail, Smartphone, CheckCircle } from "lucide-react";
import { updateNotificationPreferences, updateNewFollowerNotification } from "@/app/actions/user";
import { useTransition, useState } from "react";
import { useTranslations } from 'next-intl';

interface NotificationSettingsCardProps {
  emailFrequency: string;
  pushFrequency: string;
  emailOnNewFollower: boolean;
}

export function NotificationSettingsCard({ emailFrequency, pushFrequency, emailOnNewFollower }: NotificationSettingsCardProps) {
  const [isPending, startTransition] = useTransition();
  const [currentEmailFreq, setCurrentEmailFreq] = useState(emailFrequency);
  const [currentPushFreq, setCurrentPushFreq] = useState(pushFrequency);
  const [showEmailSuccess, setShowEmailSuccess] = useState(false);
  const [showPushSuccess, setShowPushSuccess] = useState(false);
  const [currentEmailOnNewFollower, setCurrentEmailOnNewFollower] = useState(emailOnNewFollower);
  const t = useTranslations('account.notifications');

  const handleNewFollowerChange = (checked: boolean) => {
    setCurrentEmailOnNewFollower(checked);
    startTransition(async () => {
      try {
        await updateNewFollowerNotification(checked);
        // Idéalement, afficher un toast ici
      } catch (error) {
        console.error("Erreur lors de la mise à jour:", error);
        setCurrentEmailOnNewFollower(!checked);
      }
    });
  };

  const frequencyOptions = [
    { value: "weekly", label: t('frequency.weekly') },
    { value: "bi-weekly", label: t('frequency.biweekly') },
    { value: "monthly", label: t('frequency.monthly') },
    { value: "never", label: t('frequency.never') },
  ];

  const handleEmailFrequencyChange = (value: string) => {
    setCurrentEmailFreq(value);
    
    startTransition(async () => {
      try {
        const formData = new FormData();
        formData.append("channel", "email");
        formData.append("frequency", value);
        
        await updateNotificationPreferences(formData);
        setShowEmailSuccess(true);
        setTimeout(() => setShowEmailSuccess(false), 3000);
      } catch (error) {
        console.error("Erreur lors de la mise à jour:", error);
        // Revenir à l'ancienne valeur en cas d'erreur
        setCurrentEmailFreq(emailFrequency);
      }
    });
  };

  const handlePushFrequencyChange = (value: string) => {
    setCurrentPushFreq(value);
    
    startTransition(async () => {
      try {
        const formData = new FormData();
        formData.append("channel", "push");
        formData.append("frequency", value);
        
        await updateNotificationPreferences(formData);
        setShowPushSuccess(true);
        setTimeout(() => setShowPushSuccess(false), 3000);
      } catch (error) {
        console.error("Erreur lors de la mise à jour:", error);
        // Revenir à l'ancienne valeur en cas d'erreur
        setCurrentPushFreq(pushFrequency);
      }
    });
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Bell className="w-5 h-5" />
          {t('title')}
        </CardTitle>
        <CardDescription>
          {t('description')}
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Notifications de nouveau follower */}
        <div className="flex items-center justify-between rounded-lg border p-4">
          <div>
            <Label htmlFor="new-follower-switch" className="font-medium">{t('newFollower.label')}</Label>
            <p className="text-sm text-muted-foreground">{t('newFollower.description')}</p>
          </div>
          <Switch
            id="new-follower-switch"
            checked={currentEmailOnNewFollower}
            onCheckedChange={handleNewFollowerChange}
            disabled={isPending}
          />
        </div>

        {/* Notifications par Email */}
        <div className="space-y-3">
          <div className="flex items-center gap-2">
            <Mail className="w-4 h-4" />
            <Label className="text-base font-medium">{t('email')}</Label>
            {showEmailSuccess && (
              <CheckCircle className="w-4 h-4 text-green-500 animate-in fade-in duration-300" />
            )}
          </div>
          <RadioGroup
            value={currentEmailFreq}
            onValueChange={handleEmailFrequencyChange}
            disabled={isPending}
            className="grid grid-cols-2 gap-4"
          >
            {frequencyOptions.map((option) => (
              <div key={`email-${option.value}`} className="flex items-center space-x-2">
                <RadioGroupItem value={option.value} id={`email-${option.value}`} />
                <Label htmlFor={`email-${option.value}`} className="text-sm">
                  {option.label}
                </Label>
              </div>
            ))}
          </RadioGroup>
        </div>

        {/* Notifications Push */}
        <div className="space-y-3">
          <div className="flex items-center gap-2">
            <Smartphone className="w-4 h-4" />
            <Label className="text-base font-medium">{t('push')}</Label>
            {showPushSuccess && (
              <CheckCircle className="w-4 h-4 text-green-500 animate-in fade-in duration-300" />
            )}
          </div>
          <RadioGroup
            value={currentPushFreq}
            onValueChange={handlePushFrequencyChange}
            disabled={isPending}
            className="grid grid-cols-2 gap-4"
          >
            {frequencyOptions.map((option) => (
              <div key={`push-${option.value}`} className="flex items-center space-x-2">
                <RadioGroupItem value={option.value} id={`push-${option.value}`} />
                <Label htmlFor={`push-${option.value}`} className="text-sm">
                  {option.label}
                </Label>
              </div>
            ))}
          </RadioGroup>
        </div>
      </CardContent>
    </Card>
  );
}
