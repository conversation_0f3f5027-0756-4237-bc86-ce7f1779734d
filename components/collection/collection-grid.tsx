import { CollectionAlbumCard } from "./collection-album-card";
import { CollectionEmptyState } from "./collection-empty-state";

interface CollectionAlbum {
  id: number;
  discogsReleaseId: number;
  artistName: string;
  albumTitle: string;
  albumCoverUrl?: string | null;
  year?: number | null;
  format?: string | null;
  syncedAt: Date;
}

interface CollectionGridProps {
  albums: CollectionAlbum[];
}

export function CollectionGrid({ albums }: CollectionGridProps) {
  if (albums.length === 0) {
    return <CollectionEmptyState />;
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
      {albums.map((album) => (
        <CollectionAlbumCard
          key={album.id}
          album={album}
        />
      ))}
    </div>
  );
}
