"use client";

import { Music } from "lucide-react";
import Image from "next/image";

interface CollectionAlbum {
  id: number;
  discogsReleaseId: number;
  artistName: string;
  albumTitle: string;
  albumCoverUrl?: string | null;
  year?: number | null;
  format?: string | null;
  syncedAt: Date;
}

interface CollectionAlbumCardProps {
  album: CollectionAlbum;
}

export function CollectionAlbumCard({ album }: CollectionAlbumCardProps) {
  // Utiliser l'URL de couverture depuis la base de données
  const albumCoverUrl = album.albumCoverUrl;

  return (
    <div className="bg-white dark:bg-slate-800 rounded-xl shadow-sm border border-slate-200 dark:border-slate-700 overflow-hidden hover:shadow-md transition-all duration-200">
      {/* Image de l'album */}
      <div className="aspect-square relative">
        {albumCoverUrl ? (
          <Image
            src={albumCoverUrl}
            alt={`${album.albumTitle} by ${album.artistName}`}
            fill
            className="object-cover"
            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 25vw"
          />
        ) : (
          <div className="w-full h-full bg-slate-200 dark:bg-slate-700 flex items-center justify-center">
            <Music className="w-12 h-12 text-slate-400" />
          </div>
        )}
        
        {/* Badge format si disponible */}
        {album.format && (
          <div className="absolute top-2 right-2 bg-slate-800/80 text-white text-xs px-2 py-1 rounded-full">
            {album.format}
          </div>
        )}
      </div>
      
      {/* Informations de l'album */}
      <div className="p-4">
        <h3 className="font-semibold text-slate-900 dark:text-slate-100 line-clamp-2 mb-1">
          {album.albumTitle}
        </h3>
        <p className="text-slate-600 dark:text-slate-400 text-sm line-clamp-1 mb-2">
          {album.artistName}
        </p>
        
        {/* Année de sortie si disponible */}
        {album.year && (
          <p className="text-slate-500 dark:text-slate-500 text-xs">
            {album.year}
          </p>
        )}
      </div>
    </div>
  );
}
