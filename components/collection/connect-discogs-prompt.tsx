"use client";

import { useState } from "react";
import { useTranslations } from "next-intl";
import { Button } from "@/components/ui/button";
import { Disc3, ExternalLink } from "lucide-react";

export function ConnectDiscogsPrompt() {
  const t = useTranslations('collection');
  const [isConnecting, setIsConnecting] = useState(false);

  const handleConnect = async () => {
    setIsConnecting(true);
    try {
      // Appeler l'API de connexion Discogs avec le paramètre returnTo pour revenir sur la collection
      const response = await fetch('/api/discogs/connect?returnTo=/collection');
      const data = await response.json();

      if (data.authorizeUrl) {
        // Rediriger vers l'URL d'autorisation Discogs
        window.location.href = data.authorizeUrl;
      } else {
        console.error('Erreur lors de la connexion Discogs:', data.error);
        setIsConnecting(false);
      }
    } catch (error) {
      console.error('Erreur lors de la connexion Discogs:', error);
      setIsConnecting(false);
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="text-center py-16 space-y-8">
        {/* Illustration */}
        <div className="relative mx-auto w-24 h-24 flex items-center justify-center">
          <div className="absolute inset-0 bg-gradient-to-br from-orange-100 to-red-100 dark:from-orange-900/20 dark:to-red-900/20 rounded-full"></div>
          <div className="relative flex items-center justify-center">
            <Disc3 className="w-12 h-12 text-orange-400 dark:text-orange-500" />
          </div>
        </div>

        {/* Contenu */}
        <div className="space-y-4 max-w-md mx-auto">
          <h2 className="text-2xl font-semibold text-slate-900 dark:text-slate-100">
            {t('connectPrompt.title')}
          </h2>
          <p className="text-slate-600 dark:text-slate-400 leading-relaxed">
            {t('connectPrompt.description')}
          </p>
        </div>

        {/* Appel à l'action */}
        <div className="flex flex-col sm:flex-row gap-3 justify-center">
          <Button
            onClick={handleConnect}
            disabled={isConnecting}
            className="bg-primary hover:bg-primary/90"
          >
            {isConnecting ? (
              <>
                <Disc3 className="mr-2 h-4 w-4 animate-spin" />
                {t('connectPrompt.connecting')}
              </>
            ) : (
              <>
                <ExternalLink className="mr-2 h-4 w-4" />
                {t('connectPrompt.button')}
              </>
            )}
          </Button>
        </div>

        {/* Conseil */}
        <div className="bg-slate-50 dark:bg-slate-800/50 rounded-lg p-6 max-w-lg mx-auto">
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0 w-6 h-6 bg-orange-100 dark:bg-orange-900/30 rounded-full flex items-center justify-center mt-0.5">
              <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
            </div>
            <div className="text-left">
              <h3 className="text-sm font-medium text-slate-900 dark:text-slate-100 mb-1">
                {t('connectPrompt.whyConnect.title')}
              </h3>
              <p className="text-xs text-slate-600 dark:text-slate-400">
                {t('connectPrompt.whyConnect.description')}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
