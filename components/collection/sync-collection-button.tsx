"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import { RefreshCw, CheckCircle, AlertCircle } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { useTranslations } from 'next-intl';

interface SyncCollectionButtonProps {
  variant?: "default" | "outline" | "ghost";
  size?: "default" | "sm" | "lg";
  className?: string;
}

export function SyncCollectionButton({ 
  variant = "outline", 
  size = "default",
  className = ""
}: SyncCollectionButtonProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [lastSyncStatus, setLastSyncStatus] = useState<'idle' | 'success' | 'error'>('idle');
  const router = useRouter();
  const { toast } = useToast();
  const t = useTranslations('collection.sync');

  const handleSync = async () => {
    setIsLoading(true);
    setLastSyncStatus('idle');

    try {
      const response = await fetch('/api/discogs/sync', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const data = await response.json();

      if (response.ok) {
        setLastSyncStatus('success');
        toast({
          title: t('successTitle'),
          description: t('successDescription', { count: data.syncedCount || 0 }),
        });

        // Rafraîchir la page pour afficher les nouveaux albums
        router.refresh();

        // Déclencher un événement pour notifier les autres composants (comme les recommandations)
        window.dispatchEvent(new CustomEvent('discogs-sync-completed', {
          detail: { syncedCount: data.syncedCount }
        }));
      } else {
        setLastSyncStatus('error');
        toast({
          title: t('errorTitle'),
          description: data.error || t('errorDescription'),
          variant: "destructive",
        });
      }
    } catch (error) {
      setLastSyncStatus('error');
      toast({
        title: t('errorTitle'),
        description: t('connectionError'),
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
      
      // Réinitialiser le statut après 3 secondes
      setTimeout(() => {
        setLastSyncStatus('idle');
      }, 3000);
    }
  };

  const getIcon = () => {
    if (isLoading) {
      return <RefreshCw className="h-4 w-4 animate-spin" />;
    }
    
    switch (lastSyncStatus) {
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'error':
        return <AlertCircle className="h-4 w-4 text-red-600" />;
      default:
        return <RefreshCw className="h-4 w-4" />;
    }
  };

  const getButtonText = () => {
    if (isLoading) {
      return t('syncing');
    }

    switch (lastSyncStatus) {
      case 'success':
        return t('synced');
      case 'error':
        return t('error');
      default:
        return t('button');
    }
  };

  return (
    <Button
      onClick={handleSync}
      disabled={isLoading}
      variant={variant}
      size={size}
      className={className}
    >
      {getIcon()}
      <span className="ml-2">{getButtonText()}</span>
    </Button>
  );
}
