"use client";

import { Disc3, Plus } from "lucide-react";
import Link from "next/link";
import { useTranslations } from 'next-intl';
import { Button } from "@/components/ui/button";

export function CollectionEmptyState() {
  const t = useTranslations('collection');

  return (
    <div className="text-center py-16 space-y-8">
      {/* Illustration */}
      <div className="relative mx-auto w-24 h-24 flex items-center justify-center">
        <div className="absolute inset-0 bg-gradient-to-br from-blue-100 to-purple-100 dark:from-blue-900/20 dark:to-purple-900/20 rounded-full"></div>
        <div className="relative flex items-center justify-center">
          <Disc3 className="w-12 h-12 text-blue-400 dark:text-blue-500" />
        </div>
      </div>

      {/* Contenu */}
      <div className="space-y-4 max-w-md mx-auto">
        <h2 className="text-2xl font-semibold text-slate-900 dark:text-slate-100">
          {t('empty.title')}
        </h2>
        <p className="text-slate-600 dark:text-slate-400 leading-relaxed">
          {t('empty.description')}
        </p>
      </div>

      {/* Appel à l'action */}
      <div className="flex flex-col sm:flex-row gap-3 justify-center">
        <Button asChild className="bg-primary hover:bg-primary/90">
          <Link href="/recommendations">
            <Plus className="w-4 h-4 mr-2" />
            {t('empty.cta')}
          </Link>
        </Button>
      </div>

      {/* Conseil */}
      <div className="bg-slate-50 dark:bg-slate-800/50 rounded-lg p-6 max-w-lg mx-auto">
        <div className="flex items-start space-x-3">
          <div className="flex-shrink-0 w-6 h-6 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center mt-0.5">
            <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
          </div>
          <div className="text-left">
            <h3 className="text-sm font-medium text-slate-900 dark:text-slate-100 mb-1">
              {t('empty.tip.title')}
            </h3>
            <p className="text-xs text-slate-600 dark:text-slate-400">
              {t('empty.tip.description')}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
