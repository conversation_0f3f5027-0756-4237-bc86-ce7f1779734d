"use client";

import { useState, useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { Search } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useDebounce } from "@/hooks/use-debounce";
import { useTranslations } from 'next-intl';

interface CollectionControlsProps {
  currentQuery: string;
  currentSortBy: string;
  totalCount: number;
}

export function CollectionControls({ currentQuery, currentSortBy, totalCount }: CollectionControlsProps) {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [searchQuery, setSearchQuery] = useState(currentQuery);
  const t = useTranslations('collection.filters');

  // Debounce la recherche pour éviter trop de requêtes
  const debouncedSearchQuery = useDebounce(searchQuery, 300);

  // Options de tri disponibles
  const sortOptions = [
    { value: "artistName_asc", label: t('sortOptions.artistName_asc') },
    { value: "artistName_desc", label: t('sortOptions.artistName_desc') },
    { value: "albumTitle_asc", label: t('sortOptions.albumTitle_asc') },
    { value: "albumTitle_desc", label: t('sortOptions.albumTitle_desc') },
    { value: "year_desc", label: t('sortOptions.year_desc') },
    { value: "year_asc", label: t('sortOptions.year_asc') },
    { value: "syncedAt_desc", label: t('sortOptions.syncedAt_desc') },
    { value: "syncedAt_asc", label: t('sortOptions.syncedAt_asc') },
  ];

  // Effet pour déclencher la navigation quand la recherche change
  useEffect(() => {
    if (debouncedSearchQuery !== currentQuery) {
      updateUrl({ q: debouncedSearchQuery });
    }
  }, [debouncedSearchQuery, currentQuery]);

  // Fonction pour mettre à jour l'URL en préservant les autres paramètres
  const updateUrl = (updates: { q?: string; sortBy?: string; page?: string }) => {
    const params = new URLSearchParams(searchParams.toString());
    
    // Mettre à jour les paramètres
    Object.entries(updates).forEach(([key, value]) => {
      if (value === undefined || value === "") {
        params.delete(key);
      } else {
        params.set(key, value);
      }
    });

    // Réinitialiser la page à 1 si on change la recherche ou le tri
    if (updates.q !== undefined || updates.sortBy !== undefined) {
      params.delete("page");
    }

    // Naviguer vers la nouvelle URL
    const newUrl = params.toString() ? `/collection?${params.toString()}` : "/collection";
    router.push(newUrl);
  };

  const handleSortChange = (newSortBy: string) => {
    updateUrl({ sortBy: newSortBy });
  };

  return (
    <div className="flex flex-col sm:flex-row gap-4">
      {/* Champ de recherche */}
      <div className="relative flex-1">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
        <Input
          type="text"
          placeholder={t('search')}
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="pl-10"
        />
      </div>

      {/* Sélecteur de tri */}
      <div className="sm:w-64">
        <Select value={currentSortBy} onValueChange={handleSortChange}>
          <SelectTrigger>
            <SelectValue placeholder="Trier par..." />
          </SelectTrigger>
          <SelectContent>
            {sortOptions.map((option) => (
              <SelectItem key={option.value} value={option.value}>
                {option.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Compteur de résultats */}
      <div className="flex items-center text-sm text-muted-foreground whitespace-nowrap">
        {totalCount} album{totalCount > 1 ? 's' : ''}
      </div>
    </div>
  );
}
