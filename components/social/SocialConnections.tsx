'use client';

import { useState } from 'react';
import { useTranslations } from 'next-intl';
import Link from 'next/link';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { User } from 'lucide-react';
import { FollowButton } from './FollowButton';
import { SocialListModal } from './SocialListModal'; // Assumant que ce composant existe

type SocialUser = {
  id: string;
  name: string | null;
  image: string | null;
  publicListId: string | null;
  isFollowing: boolean;
};

interface SocialConnectionsProps {
  currentUserId: string;
  followers: SocialUser[];
  following: SocialUser[];
  followersCount: number;
  followingCount: number;
}

export function SocialConnections({
  currentUserId,
  followers,
  following,
  followersCount,
  followingCount,
}: SocialConnectionsProps) {
  const t = useTranslations('Social');
  const [modalState, setModalState] = useState<{
    isOpen: boolean;
    defaultTab: 'followers' | 'following';
  }>({ isOpen: false, defaultTab: 'followers' });

  const UserRow = ({ user, isFollower }: { user: SocialUser; isFollower: boolean }) => (
    <div key={user.id} className="flex items-center justify-between gap-2 py-2">
      <Link href={`/u/${user.publicListId}`} className="flex items-center gap-3 min-w-0">
        <Avatar className="h-10 w-10">
          <AvatarImage src={user.image || undefined} alt={user.name || 'Avatar'} />
          <AvatarFallback><User className="h-5 w-5" /></AvatarFallback>
        </Avatar>
        <span className="font-medium text-sm truncate">{user.name}</span>
      </Link>
      <FollowButton
        targetUserId={user.id}
        isFollowingInitial={user.isFollowing}
        showFollowBack={isFollower && !user.isFollowing}
        size="sm"
        className="shrink-0"
      />
    </div>
  );

  return (
    <>
      <div className="space-y-6">
        {/* Followers List */}
        <div className="rounded-lg border bg-card p-4">
          <div className="flex justify-between items-center mb-4">
            <h3 className="font-semibold">{t('followers')}</h3>
            <span className="text-sm font-medium text-muted-foreground">{followersCount}</span>
          </div>
          <div className="space-y-2">
            {followers.map((user) => <UserRow key={user.id} user={user} isFollower={true} />)}
          </div>
          {followersCount > 5 && (
            <Button
              variant="link"
              className="p-0 h-auto mt-4"
              onClick={() => setModalState({ isOpen: true, defaultTab: 'followers' })}
            >
              {t('seeMore')}
            </Button>
          )}
        </div>

        {/* Following List */}
        <div className="rounded-lg border bg-card p-4">
          <div className="flex justify-between items-center mb-4">
            <h3 className="font-semibold">{t('following')}</h3>
            <span className="text-sm font-medium text-muted-foreground">{followingCount}</span>
          </div>
          <div className="space-y-2">
            {following.map((user) => <UserRow key={user.id} user={user} isFollower={false} />)}
          </div>
          {followingCount > 5 && (
            <Button
              variant="link"
              className="p-0 h-auto mt-4"
              onClick={() => setModalState({ isOpen: true, defaultTab: 'following' })}
            >
              {t('seeMore')}
            </Button>
          )}
        </div>
      </div>

      {modalState.isOpen && (
        <SocialListModal
          targetUserId={currentUserId}
          activeTab={modalState.defaultTab}
          isOpen={modalState.isOpen}
          onClose={() => setModalState({ isOpen: false, defaultTab: 'followers' })}
          userName="Utilisateur"
        />
      )}
    </>
  );
}
