'use client';

import { useState, useTransition } from 'react';
import { useSession } from 'next-auth/react';
import { Button } from '@/components/ui/button';
import { followUser, unfollowUser } from '@/app/actions/social';
import { toast } from 'sonner';
import { useTranslations } from 'next-intl';
import { UserPlus } from 'lucide-react';
import { useAuthModal } from '@/store/auth-modal';

interface FollowButtonProps {
  targetUserId: string;
  isFollowingInitial: boolean;
  showFollowBack?: boolean; // Nouvelle prop
  size?: 'sm' | 'default' | 'lg';
  className?: string;
  onFollowAction?: () => void;
  onLoginRequired?: () => void;
  userData?: {
    id: string;
    name: string | null;
    image: string | null;
    publicListId: string | null;
  };
  publicListId?: string;
  mainUserName?: string;
  mainUserImage?: string | null;
}

export function FollowButton({ 
  targetUserId, 
  isFollowingInitial, 
  showFollowBack = false, // Valeur par défaut
  size = 'sm',
  className,
  onFollowAction,
  userData,
  publicListId,
  mainUserName,
  mainUserImage
}: FollowButtonProps) {
  const { data: session } = useSession();
  const t = useTranslations('Social');
  const [isFollowing, setIsFollowing] = useState(isFollowingInitial);
  const [isPending, startTransition] = useTransition();
  const { openModal } = useAuthModal();

  const handleFollow = () => {
    if (!session?.user) {
      if (userData && publicListId) {
        openModal({
          loginReason: 'follow',
          targetData: { type: 'user', payload: userData },
          publicListId,
          mainUserName,
          mainUserImage
        });
      }
      return;
    }

    startTransition(async () => {
      const previousState = isFollowing;
      setIsFollowing(!previousState);
      try {
        const action = previousState ? unfollowUser : followUser;
        const result = await action(targetUserId);
        if (result.error) {
          setIsFollowing(previousState);
          toast.error(result.error);
        } else {
          toast.success(previousState ? t('unfollowSuccess') : t('followSuccess'));
          if (onFollowAction) onFollowAction();
        }
      } catch (error) {
        setIsFollowing(previousState);
        toast.error(t('errors.generic'));
        console.error('Erreur dans FollowButton:', error);
      }
    });
  };

  const getButtonText = () => {
    if (isPending) return t('loading');
    if (isFollowing) return t('unfollow');
    if (showFollowBack) return t('followBack');
    return t('follow');
  }

  return (
    <Button
      onClick={handleFollow}
      disabled={isPending}
      variant={isFollowing ? 'secondary' : 'default'}
      size={size}
      className={className}
    >
      <UserPlus className="w-4 h-4 mr-2" />
      {getButtonText()}
    </Button>
  );
} 