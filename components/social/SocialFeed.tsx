'use client';

import { useState, useTransition, useEffect, useRef, useCallback } from 'react';
import { useTranslations } from 'next-intl';
import { getSocialFeed, type SocialFeedItem } from '@/app/actions/social';
import { AlbumCard } from '@/components/recommendations/album-card';
import { Loader2 } from 'lucide-react';

interface SocialFeedProps {
  initialItems: SocialFeedItem[];
}

export function SocialFeed({ initialItems }: SocialFeedProps) {
  const t = useTranslations('Social.feed');
  const [items, setItems] = useState<SocialFeedItem[]>(initialItems);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(initialItems.length > 0);
  const [isPending, startTransition] = useTransition();
  const observerRef = useRef<IntersectionObserver | null>(null);

  const loadMoreItems = useCallback(() => {
    if (isPending || !hasMore) return;

    startTransition(async () => {
      const nextPage = page + 1;
      const newItems = await getSocialFeed({ page: nextPage });
      
      if (newItems.length > 0) {
        setItems((prevItems) => [...prevItems, ...newItems]);
        setPage(nextPage);
      } else {
        setHasMore(false);
      }
    });
  }, [isPending, hasMore, page]);

  const lastItemRef = useCallback((node: HTMLDivElement) => {
    if (isPending) return;
    if (observerRef.current) observerRef.current.disconnect();

    observerRef.current = new IntersectionObserver(entries => {
      if (entries[0].isIntersecting && hasMore) {
        loadMoreItems();
      }
    }, { rootMargin: '200px' });

    if (node) observerRef.current.observe(node);
  }, [isPending, hasMore, loadMoreItems]);

  return (
    <div>
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
        {items.map((item, index) => {
          // Attacher la référence au dernier élément de la liste
          if (items.length === index + 1) {
            return (
              <div ref={lastItemRef} key={`${item.author.id}-${item.id}`}>
                <AlbumCard
                  recommendation={item}
                  author={item.author}
                  isWishlisted={item.isWishlisted}
                />
              </div>
            );
          }
          return (
            <AlbumCard
              key={`${item.author.id}-${item.id}`}
              recommendation={item}
              author={item.author}
              isWishlisted={item.isWishlisted}
            />
          );
        })}
      </div>

      <div className="flex justify-center items-center h-20">
        {isPending && <Loader2 className="w-6 h-6 animate-spin text-muted-foreground" />}
        {!hasMore && items.length > 0 && (
          <p className="text-sm text-muted-foreground">{t('endOfFeed')}</p>
        )}
      </div>
    </div>
  );
}
