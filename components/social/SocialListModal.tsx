'use client';

import { useState, useEffect } from 'react';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { useTranslations } from 'next-intl';
import { FollowButton } from './FollowButton';
import Link from 'next/link';

interface User {
  id: string;
  name: string | null;
  image: string | null;
  publicListId: string | null;
  isFollowing: boolean;
}

interface SocialListModalProps {
  isOpen: boolean;
  onClose: () => void;
  targetUserId: string;
  activeTab: 'followers' | 'following';
  userName: string;
  currentUserId?: string | null; // ID de l'utilisateur connecté
  onFollowAction?: () => void; // Callback pour mettre à jour les compteurs
  onLoginRequired?: () => void; // Callback pour ouvrir la modale de connexion (legacy)
  publicListId?: string; // ID de la liste publique pour les callbacks
  mainUserName?: string; // Nom de l'utilisateur principal pour les fallbacks
  mainUserImage?: string | null; // Image de l'utilisateur principal pour les fallbacks
}

export function SocialListModal({ 
  isOpen, 
  onClose, 
  targetUserId, 
  activeTab, 
  userName, 
  currentUserId, 
  onFollowAction, 
  onLoginRequired,
  publicListId,
  mainUserName,
  mainUserImage
}: SocialListModalProps) {
  const t = useTranslations('Social');
  const [followers, setFollowers] = useState<User[]>([]);
  const [following, setFollowing] = useState<User[]>([]);
  const [loading, setLoading] = useState({ followers: false, following: false });
  const [error, setError] = useState<string | null>(null);
  const [loadedTabs, setLoadedTabs] = useState<Set<string>>(new Set());
  
  // ✅ CORRECTION: Ajouter un état local pour gérer le changement d'onglet
  const [currentTab, setCurrentTab] = useState<'followers' | 'following'>(activeTab);

  // Réinitialiser les états quand la modale s'ouvre
  useEffect(() => {
    if (isOpen) {
      setFollowers([]);
      setFollowing([]);
      setLoading({ followers: false, following: false });
      setError(null);
      setLoadedTabs(new Set());
      setCurrentTab(activeTab); // Réinitialiser l'onglet actuel
    }
  }, [isOpen, activeTab]);

  // Charger les données quand l'onglet devient actif
  useEffect(() => {
    if (!isOpen) return;

    const loadData = async () => {
      if (currentTab === 'followers' && !loadedTabs.has('followers')) {
        // Vérifier si déjà en cours de chargement pour éviter les appels multiples
        if (loading.followers) return;
        
        setLoading(prev => ({ ...prev, followers: true }));
        setError(null);
        
        try {
          console.log('🔄 Chargement des followers pour:', targetUserId);
          const response = await fetch(`/api/social/followers?userId=${targetUserId}`);
          const result = await response.json();
          console.log('📊 Résultat API followers:', result);
          
          if (result.success && result.followers) {
            setFollowers(result.followers);
            setLoadedTabs(prev => new Set([...prev, 'followers']));
          } else {
            setError(result.error || 'Erreur lors du chargement des followers');
          }
        } catch (err) {
          console.error('❌ Erreur lors du chargement des followers:', err);
          setError('Erreur lors du chargement des followers');
        } finally {
          setLoading(prev => ({ ...prev, followers: false }));
        }
      }

      if (currentTab === 'following' && !loadedTabs.has('following')) {
        // Vérifier si déjà en cours de chargement pour éviter les appels multiples
        if (loading.following) return;
        
        setLoading(prev => ({ ...prev, following: true }));
        setError(null);
        
        try {
          console.log('🔄 Chargement des following pour:', targetUserId);
          const response = await fetch(`/api/social/following?userId=${targetUserId}`);
          const result = await response.json();
          console.log('📊 Résultat API following:', result);
          
          if (result.success && result.following) {
            setFollowing(result.following);
            setLoadedTabs(prev => new Set([...prev, 'following']));
          } else {
            setError(result.error || 'Erreur lors du chargement des following');
          }
        } catch (err) {
          console.error('❌ Erreur lors du chargement des following:', err);
          setError('Erreur lors du chargement des following');
        } finally {
          setLoading(prev => ({ ...prev, following: false }));
        }
      }
    };

    loadData();
  }, [isOpen, currentTab, targetUserId]); // ✅ CORRECTION: Utiliser currentTab au lieu de activeTab

  const handleTabChange = (value: string) => {
    // ✅ CORRECTION: Mettre à jour l'onglet actuel
    setCurrentTab(value as 'followers' | 'following');
  };

  const renderUserList = (users: User[], isLoading: boolean) => {
    if (isLoading) {
      return (
        <div className="flex items-center justify-center py-8">
          <div className="text-muted-foreground">Chargement...</div>
        </div>
      );
    }

    if (error) {
      return (
        <div className="flex items-center justify-center py-8">
          <div className="text-destructive">{error}</div>
        </div>
      );
    }

    if (users.length === 0) {
      return (
        <div className="flex items-center justify-center py-8">
          <div className="text-muted-foreground">
            {currentTab === 'followers' ? 'Aucun follower' : 'Ne suit personne'}
          </div>
        </div>
      );
    }

    return (
      <div className="space-y-3 max-h-96 overflow-y-auto">
        {users.map((user) => (
          <div key={user.id} className="flex items-center justify-between p-3 rounded-lg hover:bg-muted/50">
            <Link href={user.publicListId ? `/u/${user.publicListId}` : '#'} className="flex items-center space-x-3 flex-1">
              <Avatar className="h-10 w-10">
                <AvatarImage src={user.image || undefined} alt={user.name || 'Utilisateur'} />
                <AvatarFallback>
                  {user.name?.charAt(0).toUpperCase() || 'U'}
                </AvatarFallback>
              </Avatar>
              <div className="flex-1 min-w-0">
                <p className="font-medium text-sm truncate">{user.name || 'Utilisateur'}</p>
              </div>
            </Link>
            {currentUserId === user.id ? (
              <span className="text-sm text-muted-foreground px-4">{t('you')}</span>
            ) : (
              <FollowButton
                targetUserId={user.id}
                isFollowingInitial={user.isFollowing}
                onFollowAction={onFollowAction}
                onLoginRequired={onLoginRequired}
                userData={{
                  id: user.id,
                  name: user.name,
                  image: user.image,
                  publicListId: user.publicListId
                }}
                publicListId={publicListId}
                mainUserName={mainUserName}
                mainUserImage={mainUserImage}
              />
            )}
          </div>
        ))}
      </div>
    );
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>
            {t('modalTitle', { name: userName })}
          </DialogTitle>
        </DialogHeader>
        
        <Tabs value={currentTab} onValueChange={handleTabChange} className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="followers">
              {t('followersTab')}
            </TabsTrigger>
            <TabsTrigger value="following">
              {t('followingTab')}
            </TabsTrigger>
          </TabsList>
          
          <TabsContent value="followers" className="mt-4">
            {renderUserList(followers, loading.followers)}
          </TabsContent>
          
          <TabsContent value="following" className="mt-4">
            {renderUserList(following, loading.following)}
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
} 