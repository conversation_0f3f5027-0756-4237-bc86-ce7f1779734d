'use client';

import Link from 'next/link';
import { useState, useEffect, useRef } from 'react';
import { useTranslations } from 'next-intl';
import { Search, User } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { useDebounce } from '@/hooks/use-debounce';
import { searchUsers, type UserSearchResult } from '@/app/actions/social';
import { FollowButton } from './FollowButton';

interface UserSearchProps {
  className?: string;
}

export function UserSearch({ className }: UserSearchProps) {
  const t = useTranslations('Social');
  const [query, setQuery] = useState('');
  const [results, setResults] = useState<UserSearchResult[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [showResults, setShowResults] = useState(false);
  const searchContainerRef = useRef<HTMLDivElement>(null);

  const debouncedQuery = useDebounce(query, 300);

  const performSearch = async (currentQuery: string) => {
    if (currentQuery.trim().length < 2) {
      setResults([]);
      return;
    }
    setIsLoading(true);
    setShowResults(true);
    try {
      const searchResults = await searchUsers(currentQuery);
      setResults(searchResults);
    } catch (error) {
      console.error('Erreur de recherche:', error);
      setResults([]);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    performSearch(debouncedQuery);
  }, [debouncedQuery]);

  // Gère la fermeture de la dropdown de manière robuste
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (searchContainerRef.current && !searchContainerRef.current.contains(event.target as Node)) {
        setShowResults(false);
      }
    };
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  return (
    <div ref={searchContainerRef} className={`relative ${className}`}>
      <div className="relative">
        <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
        <Input
          type="text"
          placeholder={t('searchPlaceholder')}
          value={query}
          onChange={(e) => setQuery(e.target.value)}
          onFocus={() => {
            if (query.trim().length >= 2) setShowResults(true);
          }}
          className="pl-10 text-sm sm:text-base"
        />
      </div>

      {showResults && (
        <div className="absolute top-full z-50 mt-1 w-full rounded-md border bg-popover p-0 shadow-md min-w-0 left-0 right-0">
          {isLoading ? (
            <div className="flex items-center justify-center p-4">
              <div className="h-4 w-4 animate-spin rounded-full border-2 border-primary border-t-transparent" />
            </div>
          ) : results.length > 0 ? (
            <div className="max-h-64 overflow-y-auto">
              {results.map((user) => (
                <div
                  key={user.id}
                  className="flex items-center gap-2 sm:gap-3 p-2 sm:p-3"
                >
                  <Link
                    href={`/u/${user.publicListId}`}
                    className={`flex items-center gap-2 sm:gap-3 flex-1 min-w-0 transition-opacity ${
                      user.publicListId ? 'hover:opacity-80' : 'opacity-50 cursor-not-allowed'
                    }`}
                    onClick={(e) => {
                      if (!user.publicListId) e.preventDefault();
                      else {
                        setShowResults(false);
                        setQuery('');
                      }
                    }}
                  >
                    <Avatar className="h-6 w-6 sm:h-8 sm:w-8 shrink-0">
                      <AvatarImage src={user.image || ''} alt={user.name || ''} />
                      <AvatarFallback>
                        <User className="h-3 w-3 sm:h-4 sm:w-4" />
                      </AvatarFallback>
                    </Avatar>
                    <div className="flex-1 min-w-0">
                      <p className="text-xs sm:text-sm font-medium truncate">
                        {user.name || 'Utilisateur anonyme'}
                      </p>
                    </div>
                  </Link>
                  <FollowButton
                    targetUserId={user.id}
                    isFollowingInitial={user.isFollowing}
                    size="sm"
                    className="shrink-0"
                    onFollowAction={() => performSearch(query)}
                  />
                </div>
              ))}
            </div>
          ) : debouncedQuery.trim().length >= 2 ? (
            <div className="p-3 sm:p-4 text-center text-xs sm:text-sm text-muted-foreground">
              {t('searchNoResults')}
            </div>
          ) : null}
        </div>
      )}
    </div>
  );
}
 