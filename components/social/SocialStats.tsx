'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { useTranslations } from 'next-intl';
import { SocialListModal } from './SocialListModal';

interface SocialStatsProps {
  targetUserId: string;
  followersCount: number;
  followingCount: number;
  userName: string;
  currentUserId?: string | null; // ID de l'utilisateur connecté
  onFollowAction?: () => void; // Callback pour mettre à jour les compteurs
  onLoginRequired?: () => void; // Callback pour ouvrir la modale de connexion (legacy)
  publicListId?: string; // ID de la liste publique pour les callbacks
  mainUserName?: string; // Nom de l'utilisateur principal pour les fallbacks
  mainUserImage?: string | null; // Image de l'utilisateur principal pour les fallbacks
}

export function SocialStats({ 
  targetUserId, 
  followersCount, 
  followingCount, 
  userName, 
  currentUserId, 
  onFollowAction, 
  onLoginRequired,
  publicListId,
  mainUserName,
  mainUserImage
}: SocialStatsProps) {
  const t = useTranslations('Social');
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [activeTab, setActiveTab] = useState<'followers' | 'following'>('followers');

  const handleFollowersClick = () => {
    setActiveTab('followers');
    setIsModalOpen(true);
  };

  const handleFollowingClick = () => {
    setActiveTab('following');
    setIsModalOpen(true);
  };

  return (
    <>
      <div className="flex items-center gap-4 text-sm text-muted-foreground">
        <Button
          variant="link"
          className="h-auto p-0 text-muted-foreground hover:text-foreground"
          onClick={handleFollowersClick}
        >
          <span className="font-semibold text-foreground">{followersCount.toLocaleString()}</span>
          <span className="ml-1">
            {t('followersLabel', { count: followersCount })}
          </span>
        </Button>
        
        <span className="text-muted-foreground">|</span>
        
        <Button
          variant="link"
          className="h-auto p-0 text-muted-foreground hover:text-foreground"
          onClick={handleFollowingClick}
        >
          <span className="font-semibold text-foreground">{followingCount.toLocaleString()}</span>
          <span className="ml-1">
            {t('followingLabel', { count: followingCount })}
          </span>
        </Button>
      </div>

      <SocialListModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        targetUserId={targetUserId}
        activeTab={activeTab}
        userName={userName}
        currentUserId={currentUserId}
        onFollowAction={onFollowAction}
        onLoginRequired={onLoginRequired}
        publicListId={publicListId}
        mainUserName={mainUserName}
        mainUserImage={mainUserImage}
      />
    </>
  );
} 