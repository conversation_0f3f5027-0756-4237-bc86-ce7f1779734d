'use client';

import { useEffect, useState } from 'react';
import { useTranslations } from 'next-intl';
import Link from 'next/link';
import { User, UserPlus } from 'lucide-react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { FollowButton } from './FollowButton';
import { getProfileSuggestions, type ProfileSuggestion } from '@/app/actions/social';

export function ProfileSuggestions() {
  const t = useTranslations('Social');
  const [suggestions, setSuggestions] = useState<ProfileSuggestion[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchSuggestions = async () => {
      try {
        setIsLoading(true);
        const result = await getProfileSuggestions();
        setSuggestions(result);
      } catch (error) {
        console.error("Erreur lors de la récupération des suggestions:", error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchSuggestions();
  }, []);

  if (isLoading) {
    return (
      <div className="p-4">
        <div className="h-4 w-1/2 bg-muted rounded-md animate-pulse mb-4"></div>
        <div className="flex items-center gap-3 mb-3">
            <div className="h-10 w-10 bg-muted rounded-full animate-pulse"></div>
            <div className="flex-1 space-y-2">
                <div className="h-3 w-3/4 bg-muted rounded-md animate-pulse"></div>
                <div className="h-3 w-1/2 bg-muted rounded-md animate-pulse"></div>
            </div>
        </div>
      </div>
    );
  }

  if (suggestions.length === 0) {
    return null; // Ne rien afficher si pas de suggestions
  }

  return (
    <div className="p-4 border rounded-lg bg-background">
      <h3 className="font-semibold text-sm mb-4">{t('suggestions.title')}</h3>
      <div className="space-y-4">
        {suggestions.map((user) => (
          <div key={user.id} className="flex items-center gap-3">
            <Avatar className="h-10 w-10">
              <AvatarImage src={user.image || ''} alt={user.name || 'Avatar'} />
              <AvatarFallback>
                <User className="h-5 w-5" />
              </AvatarFallback>
            </Avatar>
            <div className="flex-1 min-w-0">
              <Link href={`/u/${user.publicListId}`} className="font-semibold text-sm truncate hover:underline">
                {user.name}
              </Link>
              <p className="text-xs text-muted-foreground truncate">
                {user.mutualFollowers > 0 
                  ? t('suggestions.mutualFollowers', { count: user.mutualFollowers })
                  : t('suggestions.suggestion')}
              </p>
            </div>
            <FollowButton targetUserId={user.id} isFollowingInitial={false} size="sm" />
          </div>
        ))}
      </div>
    </div>
  );
}
