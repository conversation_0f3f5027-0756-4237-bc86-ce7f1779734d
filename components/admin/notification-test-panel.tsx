"use client";

import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { 
  Mail, 
  Smartphone, 
  CheckCircle, 
  XCircle, 
  AlertTriangle, 
  Settings,
  Play,
  RefreshCw
} from "lucide-react";

interface TestResult {
  success: boolean;
  message?: string;
  messageId?: string;
  error?: string;
}

interface ConfigurationStatus {
  isValid: boolean;
  message: string;
}

export function NotificationTestPanel() {
  const [emailTest, setEmailTest] = useState<TestResult | null>(null);
  const [pushTest, setPushTest] = useState<TestResult | null>(null);
  const [configurations, setConfigurations] = useState<{
    resend?: ConfigurationStatus;
    firebase?: ConfigurationStatus;
    firebaseClient?: ConfigurationStatus;
  } | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [testEmail, setTestEmail] = useState("");
  const [fcmToken, setFcmToken] = useState("");

  // Vérifier les configurations
  const checkConfigurations = async () => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/test/notifications');
      const data = await response.json();
      
      if (data.success) {
        setConfigurations(data.configurations);
      }
    } catch (error) {
      console.error('Erreur lors de la vérification des configurations:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Test d'email
  const testEmailNotification = async () => {
    setIsLoading(true);
    setEmailTest(null);
    
    try {
      const response = await fetch('/api/test/notifications', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          type: 'email', 
          email: testEmail || undefined 
        })
      });
      
      const data = await response.json();
      setEmailTest(data);
    } catch (error) {
      setEmailTest({ 
        success: false, 
        error: 'Erreur lors du test d\'email' 
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Test de notification push
  const testPushNotification = async () => {
    setIsLoading(true);
    setPushTest(null);
    
    try {
      const response = await fetch('/api/test/notifications', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          type: 'push', 
          fcmToken 
        })
      });
      
      const data = await response.json();
      setPushTest(data);
    } catch (error) {
      setPushTest({ 
        success: false, 
        error: 'Erreur lors du test de notification push' 
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Test complet
  const runCompleteTest = async () => {
    setIsLoading(true);
    setEmailTest(null);
    setPushTest(null);
    
    try {
      const response = await fetch('/api/test/notifications', {
        method: 'PUT'
      });
      
      const data = await response.json();
      
      if (data.success && data.results) {
        setEmailTest(data.results.email);
        setPushTest(data.results.push);
        setConfigurations(data.results.configurations);
      }
    } catch (error) {
      console.error('Erreur lors du test complet:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Initialiser les notifications push
  const initializePushNotifications = async () => {
    setIsLoading(true);
    
    try {
      const { initializePushNotifications } = await import('@/lib/firebase-client');
      const result = await initializePushNotifications();
      
      if (result.success && result.token) {
        setFcmToken(result.token);
        setPushTest({ 
          success: true, 
          message: 'Notifications push initialisées avec succès' 
        });
      } else {
        setPushTest({ 
          success: false, 
          error: result.error || 'Erreur lors de l\'initialisation' 
        });
      }
    } catch (error) {
      setPushTest({ 
        success: false, 
        error: 'Erreur lors de l\'initialisation des notifications push' 
      });
    } finally {
      setIsLoading(false);
    }
  };

  const renderConfigurationStatus = (config: ConfigurationStatus | undefined, name: string) => {
    if (!config) return null;
    
    return (
      <div className="flex items-center gap-2 p-2 rounded border">
        {config.isValid ? (
          <CheckCircle className="w-4 h-4 text-green-500" />
        ) : (
          <XCircle className="w-4 h-4 text-red-500" />
        )}
        <div>
          <div className="font-medium">{name}</div>
          <div className="text-sm text-gray-600">{config.message}</div>
        </div>
      </div>
    );
  };

  const renderTestResult = (result: TestResult | null, type: string) => {
    if (!result) return null;
    
    return (
      <Alert className={result.success ? "border-green-200" : "border-red-200"}>
        <div className="flex items-center gap-2">
          {result.success ? (
            <CheckCircle className="w-4 h-4 text-green-500" />
          ) : (
            <XCircle className="w-4 h-4 text-red-500" />
          )}
          <AlertDescription>
            <div className="font-medium">
              {result.success ? `Test ${type} réussi` : `Test ${type} échoué`}
            </div>
            <div className="text-sm">
              {result.message || result.error}
            </div>
            {result.messageId && (
              <div className="text-xs text-gray-500 mt-1">
                ID: {result.messageId}
              </div>
            )}
          </AlertDescription>
        </div>
      </Alert>
    );
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Settings className="w-5 h-5" />
          Panel de Test des Notifications
        </CardTitle>
        <CardDescription>
          Testez et vérifiez le système de notifications email et push
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="config" className="space-y-4">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="config">Configuration</TabsTrigger>
            <TabsTrigger value="email">Email</TabsTrigger>
            <TabsTrigger value="push">Push</TabsTrigger>
            <TabsTrigger value="complete">Test Complet</TabsTrigger>
          </TabsList>

          <TabsContent value="config" className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-medium">État des Configurations</h3>
              <Button 
                onClick={checkConfigurations} 
                disabled={isLoading}
                variant="outline"
                size="sm"
              >
                <RefreshCw className={`w-4 h-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
                Vérifier
              </Button>
            </div>
            
            <div className="space-y-3">
              {renderConfigurationStatus(configurations?.resend, "Resend (Email)")}
              {renderConfigurationStatus(configurations?.firebase, "Firebase (Push Server)")}
              {renderConfigurationStatus(configurations?.firebaseClient, "Firebase (Push Client)")}
            </div>
          </TabsContent>

          <TabsContent value="email" className="space-y-4">
            <div className="space-y-3">
              <div>
                <Label htmlFor="test-email">Email de test (optionnel)</Label>
                <Input
                  id="test-email"
                  type="email"
                  placeholder="Laisser vide pour utiliser votre email"
                  value={testEmail}
                  onChange={(e) => setTestEmail(e.target.value)}
                />
              </div>
              
              <Button 
                onClick={testEmailNotification} 
                disabled={isLoading}
                className="w-full"
              >
                <Mail className="w-4 h-4 mr-2" />
                {isLoading ? 'Envoi en cours...' : 'Envoyer Email de Test'}
              </Button>
              
              {renderTestResult(emailTest, "email")}
            </div>
          </TabsContent>

          <TabsContent value="push" className="space-y-4">
            <div className="space-y-3">
              <div>
                <Label htmlFor="fcm-token">Token FCM</Label>
                <Input
                  id="fcm-token"
                  placeholder="Token FCM pour le test"
                  value={fcmToken}
                  onChange={(e) => setFcmToken(e.target.value)}
                />
              </div>
              
              <div className="flex gap-2">
                <Button 
                  onClick={initializePushNotifications} 
                  disabled={isLoading}
                  variant="outline"
                  className="flex-1"
                >
                  <Smartphone className="w-4 h-4 mr-2" />
                  Initialiser Push
                </Button>
                
                <Button 
                  onClick={testPushNotification} 
                  disabled={isLoading || !fcmToken}
                  className="flex-1"
                >
                  <Play className="w-4 h-4 mr-2" />
                  Test Push
                </Button>
              </div>
              
              {renderTestResult(pushTest, "push")}
            </div>
          </TabsContent>

          <TabsContent value="complete" className="space-y-4">
            <div className="text-center">
              <Button 
                onClick={runCompleteTest} 
                disabled={isLoading}
                size="lg"
                className="w-full"
              >
                <Play className="w-4 h-4 mr-2" />
                {isLoading ? 'Test en cours...' : 'Lancer Test Complet'}
              </Button>
            </div>
            
            <div className="space-y-3">
              {renderTestResult(emailTest, "email")}
              {renderTestResult(pushTest, "push")}
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}
