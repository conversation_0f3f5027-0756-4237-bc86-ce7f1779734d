"use client";

import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { 
  Mail, 
  User, 
  CheckCircle, 
  XCircle, 
  Settings,
  Play,
  RefreshCw,
  UserPlus
} from "lucide-react";

interface TestResult {
  success: boolean;
  message?: string;
  messageId?: string;
  error?: string;
  user?: any;
  welcomeEmail?: any;
}

interface ConfigurationStatus {
  isValid: boolean;
  message: string;
}

export function US13TestPanel() {
  const [manualTest, setManualTest] = useState<TestResult | null>(null);
  const [simulationTest, setSimulationTest] = useState<TestResult | null>(null);
  const [configuration, setConfiguration] = useState<ConfigurationStatus | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [testEmail, setTestEmail] = useState("<EMAIL>");
  const [testName, setTestName] = useState("Simon");

  // Vérifier la configuration
  const checkConfiguration = async () => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/test/welcome-email');
      const data = await response.json();
      
      if (data.success) {
        setConfiguration(data.configuration);
      }
    } catch (error) {
      console.error('Erreur lors de la vérification de la configuration:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Test manuel d'email de bienvenue
  const testManualWelcomeEmail = async () => {
    setIsLoading(true);
    setManualTest(null);
    
    try {
      const response = await fetch('/api/test/welcome-email', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          email: testEmail,
          name: testName
        })
      });
      
      const data = await response.json();
      setManualTest(data);
    } catch (error) {
      setManualTest({ 
        success: false, 
        error: 'Erreur lors du test manuel' 
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Simulation complète de création d'utilisateur
  const simulateUserCreation = async () => {
    setIsLoading(true);
    setSimulationTest(null);
    
    try {
      const response = await fetch('/api/test/simulate-user-creation', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          email: testEmail,
          name: testName
        })
      });
      
      const data = await response.json();
      setSimulationTest(data);
    } catch (error) {
      setSimulationTest({ 
        success: false, 
        error: 'Erreur lors de la simulation' 
      });
    } finally {
      setIsLoading(false);
    }
  };

  const renderTestResult = (result: TestResult | null, type: string) => {
    if (!result) return null;
    
    return (
      <Alert className={result.success ? "border-green-200" : "border-red-200"}>
        <div className="flex items-center gap-2">
          {result.success ? (
            <CheckCircle className="w-4 h-4 text-green-500" />
          ) : (
            <XCircle className="w-4 h-4 text-red-500" />
          )}
          <div className="flex-1">
            <div className="font-medium">
              {result.success ? `Test ${type} réussi` : `Test ${type} échoué`}
            </div>
            <div className="text-sm mt-1">
              {result.message || result.error}
            </div>
            {result.messageId && (
              <div className="text-xs text-gray-500 mt-1">
                Email ID: {result.messageId}
              </div>
            )}
            {result.user && (
              <div className="text-xs text-gray-500 mt-1">
                Utilisateur: {result.user.name} ({result.user.email})
              </div>
            )}
          </div>
        </div>
      </Alert>
    );
  };

  const renderConfigurationStatus = () => {
    if (!configuration) return null;
    
    return (
      <div className="flex items-center gap-2 p-3 rounded border">
        {configuration.isValid ? (
          <CheckCircle className="w-4 h-4 text-green-500" />
        ) : (
          <XCircle className="w-4 h-4 text-red-500" />
        )}
        <div>
          <div className="font-medium">Configuration Email de Bienvenue</div>
          <div className="text-sm text-gray-600">{configuration.message}</div>
        </div>
      </div>
    );
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Mail className="w-5 h-5" />
          Panel de Test US 1.3 - Email de Bienvenue
        </CardTitle>
        <CardDescription>
          Testez l'envoi automatique d'emails de bienvenue lors de la création d'un compte
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="config" className="space-y-4">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="config">Configuration</TabsTrigger>
            <TabsTrigger value="manual">Test Manuel</TabsTrigger>
            <TabsTrigger value="simulation">Simulation</TabsTrigger>
            <TabsTrigger value="info">Informations</TabsTrigger>
          </TabsList>

          <TabsContent value="config" className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-medium">État de la Configuration</h3>
              <Button 
                onClick={checkConfiguration} 
                disabled={isLoading}
                variant="outline"
                size="sm"
              >
                <RefreshCw className={`w-4 h-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
                Vérifier
              </Button>
            </div>
            
            {renderConfigurationStatus()}
          </TabsContent>

          <TabsContent value="manual" className="space-y-4">
            <div className="space-y-3">
              <div className="grid grid-cols-2 gap-3">
                <div>
                  <Label htmlFor="test-email">Email de test</Label>
                  <Input
                    id="test-email"
                    type="email"
                    value={testEmail}
                    onChange={(e) => setTestEmail(e.target.value)}
                  />
                </div>
                <div>
                  <Label htmlFor="test-name">Nom de test</Label>
                  <Input
                    id="test-name"
                    value={testName}
                    onChange={(e) => setTestName(e.target.value)}
                  />
                </div>
              </div>
              
              <Button 
                onClick={testManualWelcomeEmail} 
                disabled={isLoading || !testEmail || !testName}
                className="w-full"
              >
                <Mail className="w-4 h-4 mr-2" />
                {isLoading ? 'Envoi en cours...' : 'Envoyer Email de Bienvenue'}
              </Button>
              
              {renderTestResult(manualTest, "manuel")}
            </div>
          </TabsContent>

          <TabsContent value="simulation" className="space-y-4">
            <div className="space-y-3">
              <div className="p-4 bg-blue-50 rounded-lg">
                <h4 className="font-medium text-blue-900 mb-2">Simulation Complète</h4>
                <p className="text-sm text-blue-800">
                  Cette simulation reproduit exactement le processus de création d'un utilisateur 
                  et déclenche l'email de bienvenue comme le ferait NextAuth.
                </p>
              </div>
              
              <div className="grid grid-cols-2 gap-3">
                <div>
                  <Label htmlFor="sim-email">Email utilisateur</Label>
                  <Input
                    id="sim-email"
                    type="email"
                    value={testEmail}
                    onChange={(e) => setTestEmail(e.target.value)}
                  />
                </div>
                <div>
                  <Label htmlFor="sim-name">Nom utilisateur</Label>
                  <Input
                    id="sim-name"
                    value={testName}
                    onChange={(e) => setTestName(e.target.value)}
                  />
                </div>
              </div>
              
              <Button 
                onClick={simulateUserCreation} 
                disabled={isLoading || !testEmail || !testName}
                className="w-full"
                variant="default"
              >
                <UserPlus className="w-4 h-4 mr-2" />
                {isLoading ? 'Simulation en cours...' : 'Simuler Création d\'Utilisateur'}
              </Button>
              
              {renderTestResult(simulationTest, "simulation")}
            </div>
          </TabsContent>

          <TabsContent value="info" className="space-y-4">
            <div className="space-y-4">
              <div className="p-4 bg-gray-50 rounded-lg">
                <h4 className="font-medium mb-2">Flux de l'US 1.3</h4>
                <ol className="text-sm space-y-1 list-decimal list-inside">
                  <li>Utilisateur se connecte avec Spotify (première fois)</li>
                  <li>NextAuth déclenche l'événement <code>createUser</code></li>
                  <li>Le système envoie automatiquement un email de bienvenue</li>
                  <li>L'utilisateur reçoit l'email avec un CTA vers les recommandations</li>
                </ol>
              </div>
              
              <div className="p-4 bg-green-50 rounded-lg">
                <h4 className="font-medium text-green-900 mb-2">Fichiers impliqués</h4>
                <ul className="text-sm text-green-800 space-y-1">
                  <li>• <code>auth.ts</code> - Configuration NextAuth avec événement createUser</li>
                  <li>• <code>lib/email.ts</code> - Module d'envoi d'emails</li>
                  <li>• <code>emails/welcome-email.tsx</code> - Template React Email</li>
                  <li>• <code>app/api/test/welcome-email/</code> - API de test</li>
                </ul>
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}
