// components/emails/EmailHeader.tsx
import { Section, Img } from '@react-email/components';
import { EMAIL_ASSETS } from '@/lib/email-assets';

const headerStyle = {
  padding: '24px', // Padding interne pour l'espacement du logo
  textAlign: 'center' as const,
  backgroundColor: '#6236FF',
  color: '#ffffff',
  // Les coins supérieurs seront arrondis pour correspondre au container
  borderRadius: '12px 12px 0 0', 
};

const logoStyle = {
  margin: '0 auto',
  display: 'block',
};

export function EmailHeader() {
  return (
    <Section style={headerStyle}>
      <Img
        src={EMAIL_ASSETS.logo}
        width="180" // Largeur standard pour le logo
        height="40"
        alt="Stream2Spin"
        style={logoStyle}
      />
    </Section>
  );
} 