"use client";

import { signIn } from "next-auth/react";
import { But<PERSON> } from "@/components/ui/button";
import { useState } from "react";
import { Loader2 } from "lucide-react";
import { useTranslations } from 'next-intl';

export function SpotifySignInButton() {
  const [isLoading, setIsLoading] = useState(false);
  const t = useTranslations('login.modal');

  const handleSpotifySignIn = async () => {
    setIsLoading(true);
    try {
      // 🎯 Flux NextAuth.js standard - seule méthode supportée par Spotify
      console.log('🎵 Connexion Spotify via NextAuth.js');
      await signIn("spotify", {
        callbackUrl: "/login-redirect",
        redirect: true
      });
    } catch (error) {
      console.error("Erreur lors de la connexion Spotify:", error);
      setIsLoading(false);
    }
  };

  return (
    <Button
      type="button"
      disabled={isLoading}
      onClick={handleSpotifySignIn}
      className="w-full h-12 flex items-center justify-center gap-3 bg-primary hover:bg-primary/90 text-primary-foreground font-semibold shadow-lg hover:shadow-xl transition-all duration-200 border-0"
    >
      {isLoading ? (
        <>
          <Loader2 className="w-5 h-5 animate-spin" />
          <span>{t('connecting')}</span>
        </>
      ) : (
        <>
          <svg className="w-5 h-5" viewBox="0 0 24 24" fill="currentColor">
            <path d="M12 0C5.4 0 0 5.4 0 12s5.4 12 12 12 12-5.4 12-12S18.66 0 12 0zm5.521 17.34c-.24.359-.66.48-1.021.24-2.82-1.74-6.36-2.101-10.561-1.141-.418.122-.779-.179-.899-.539-.12-.421.18-.78.54-.9 4.56-1.021 8.52-.6 11.64 1.32.42.18.479.659.301 1.02zm1.44-3.3c-.301.42-.841.6-1.262.3-3.239-1.98-8.159-2.58-11.939-1.38-.479.12-1.02-.12-1.14-.6-.12-.48.12-1.021.6-1.141C9.6 9.9 15 10.561 18.72 12.84c.361.181.54.78.241 1.2zm.12-3.36C15.24 8.4 8.82 8.16 5.16 9.301c-.6.179-1.2-.181-1.38-.721-.18-.601.18-1.2.72-1.381 4.26-1.26 11.28-1.02 15.721 1.621.539.3.719 1.02.42 1.56-.297.539-1.02.718-1.559.42z"/>
          </svg>
          <span>{t('continueWith')}</span>
        </>
      )}
    </Button>
  );
}
