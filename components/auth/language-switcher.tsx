"use client";

import { Globe } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useRouter } from "next/navigation";
import { useTranslations } from 'next-intl';

export function LanguageSwitcher() {
  const router = useRouter();
  const t = useTranslations('common');

  const handleLanguageChange = (locale: string) => {
    // Stocker la préférence de langue dans un cookie
    document.cookie = `preferred-language=${locale}; path=/; max-age=2592000`; // 30 jours
    
    // Recharger la page pour appliquer la nouvelle langue
    window.location.reload();
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          size="sm"
          className="h-8 w-8 p-0 text-white/80 hover:text-white hover:bg-white/10"
        >
          <Globe className="h-4 w-4" />
          <span className="sr-only">Changer la langue</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-40">
        <DropdownMenuItem onClick={() => handleLanguageChange('fr')}>
          <span className="mr-2">🇫🇷</span>
          Français
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => handleLanguageChange('en')}>
          <span className="mr-2">🇺🇸</span>
          English
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
} 