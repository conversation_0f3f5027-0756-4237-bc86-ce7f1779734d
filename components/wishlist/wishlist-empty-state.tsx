"use client";

import { Heart } from "lucide-react";
import Link from "next/link";
import { useTranslations } from 'next-intl';
import { Button } from "@/components/ui/button";

export function WishlistEmptyState() {
  const t = useTranslations('wishlist');

  return (
    <div className="text-center py-16 space-y-8">
      {/* Illustration */}
      <div className="relative mx-auto w-24 h-24 flex items-center justify-center">
        <div className="absolute inset-0 bg-gradient-to-br from-red-100 to-pink-100 dark:from-red-900/20 dark:to-pink-900/20 rounded-full"></div>
        <div className="relative flex items-center justify-center">
          <Heart className="w-12 h-12 text-red-400 dark:text-red-500" />
        </div>
      </div>

      {/* Contenu */}
      <div className="space-y-4 max-w-md mx-auto">
        <h2 className="text-2xl font-semibold text-slate-900 dark:text-slate-100">
          {t('empty.title')}
        </h2>
        <p className="text-slate-600 dark:text-slate-400 leading-relaxed">
          {t('empty.description')}
        </p>
      </div>

      {/* Appel à l'action */}
      <div className="flex flex-col sm:flex-row gap-3 justify-center">
        <Button asChild className="bg-primary hover:bg-primary/90">
          <Link href="/recommendations">
            {t('empty.cta')}
          </Link>
        </Button>
      </div>

      {/* Conseil */}
      <div className="bg-slate-50 dark:bg-slate-800/50 rounded-lg p-6 max-w-lg mx-auto">
        <div className="flex items-start space-x-3">
          <div className="flex-shrink-0 w-6 h-6 bg-red-100 dark:bg-red-900/30 rounded-full flex items-center justify-center mt-0.5">
            <div className="w-2 h-2 bg-red-500 rounded-full"></div>
          </div>
          <div className="text-left">
            <h3 className="text-sm font-medium text-slate-900 dark:text-slate-100 mb-1">
              {t('empty.tip.title')}
            </h3>
            <p className="text-xs text-slate-600 dark:text-slate-400">
              {t('empty.tip.description')}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
