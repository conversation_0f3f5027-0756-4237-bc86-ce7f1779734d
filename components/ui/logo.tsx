import { cn } from "@/lib/utils";
import Image from "next/image";

interface LogoProps {
  size?: "sm" | "md" | "lg" | "xl";
  showText?: boolean;
  className?: string;
  logoClassName?: string;
  textClassName?: string;
  animated?: boolean;
}

const sizeConfig = {
  sm: {
    logo: "h-6",
    text: "text-lg",
    container: "space-x-1.5"
  },
  md: {
    logo: "h-8",
    text: "text-xl",
    container: "space-x-2"
  },
  lg: {
    logo: "h-10",
    text: "text-2xl",
    container: "space-x-2.5"
  },
  xl: {
    logo: "h-12",
    text: "text-3xl",
    container: "space-x-3"
  }
};

export function Logo({
  size = "md",
  showText = true,
  className,
  logoClassName,
  textClassName,
  animated = true
}: LogoProps) {
  const config = sizeConfig[size];
  
  return (
    <div className={cn("flex items-center", config.container, className)}>
      {/* Logo SVG officiel */}
      <div className="relative">
        <Image
          src="/stream2spin-logo.svg"
          alt="Stream2Spin"
          width={200}
          height={75}
          className={cn(
            config.logo,
            "w-auto object-contain",
            animated && "transition-transform hover:scale-105",
            logoClassName
          )}
          priority
        />
      </div>

      {/* Texte optionnel (masqué par défaut car le logo contient déjà le texte) */}
      {showText && (
        <span
          className={cn(
            config.text,
            "font-bold bg-gradient-to-r from-primary to-primary/80 bg-clip-text text-transparent sr-only",
            textClassName
          )}
        >
          Stream2Spin
        </span>
      )}
    </div>
  );
}
