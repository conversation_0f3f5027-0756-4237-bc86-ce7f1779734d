// components/ui/badge.stories.tsx
import type { Meta, StoryObj } from '@storybook/nextjs-vite';
import { Badge } from './badge';
import { Check, X, AlertTriangle, Star } from 'lucide-react';

// Définition des métadonnées pour la documentation et les contrôles
const meta = {
  title: 'Design System/UI/Badge',
  component: Badge,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  // Définition des contrôles interactifs dans l'interface Storybook
  argTypes: {
    variant: {
      control: 'select',
      options: ['default', 'secondary', 'destructive', 'outline'],
      description: 'La variante visuelle du badge',
    },
    className: {
      control: 'text',
      description: 'Classes CSS personnalisées pour le composant',
    },
    children: {
      control: 'text',
      description: 'Le contenu textuel du badge',
    }
  },
} satisfies Meta<typeof Badge>;

export default meta;
type Story = StoryObj<typeof meta>;

// Histoire pour le badge par défaut
export const Default: Story = {
  args: {
    variant: 'default',
    children: 'Badge par défaut',
  },
};

// Histoire pour le badge secondaire
export const Secondary: Story = {
  args: {
    variant: 'secondary',
    children: 'Badge secondaire',
  },
};

// Histoire pour le badge destructif
export const Destructive: Story = {
  args: {
    variant: 'destructive',
    children: 'Badge destructif',
  },
};

// Histoire pour le badge outline
export const Outline: Story = {
  args: {
    variant: 'outline',
    children: 'Badge outline',
  },
};

// Histoire pour le badge avec icône
export const WithIcon: Story = {
  args: {
    variant: 'default',
    children: (
      <>
        <Check className="mr-1 h-3 w-3" />
        Validé
      </>
    ),
  },
};

// Histoire pour le badge d'erreur avec icône
export const ErrorWithIcon: Story = {
  args: {
    variant: 'destructive',
    children: (
      <>
        <X className="mr-1 h-3 w-3" />
        Erreur
      </>
    ),
  },
};

// Histoire pour le badge d'avertissement avec icône
export const WarningWithIcon: Story = {
  args: {
    variant: 'outline',
    children: (
      <>
        <AlertTriangle className="mr-1 h-3 w-3" />
        Attention
      </>
    ),
  },
};

// Histoire pour le badge premium avec icône
export const PremiumWithIcon: Story = {
  args: {
    variant: 'secondary',
    children: (
      <>
        <Star className="mr-1 h-3 w-3" />
        Premium
      </>
    ),
  },
};

// Histoire pour différents états dans la collection
export const StatusBadges: Story = {
  render: () => (
    <div className="flex flex-wrap gap-2">
      <Badge variant="default">
        <Check className="mr-1 h-3 w-3" />
        Dans la collection
      </Badge>
      <Badge variant="secondary">En cours</Badge>
      <Badge variant="outline">Souhaité</Badge>
      <Badge variant="destructive">Non disponible</Badge>
    </div>
  ),
}; 