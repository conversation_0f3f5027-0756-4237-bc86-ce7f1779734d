// components/ui/avatar.stories.tsx
import type { Meta, StoryObj } from '@storybook/nextjs-vite';
import { Avatar, AvatarImage, AvatarFallback } from './avatar';

// Définition des métadonnées pour la documentation et les contrôles
const meta = {
  title: 'Design System/UI/Avatar',
  component: Avatar,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  // Définition des contrôles interactifs dans l'interface Storybook
  argTypes: {
    className: {
      control: 'text',
      description: 'Classes CSS personnalisées pour le composant',
    },
    children: {
      control: false,
      description: 'Le contenu de l\'avatar (AvatarImage et AvatarFallback)',
    }
  },
} satisfies Meta<typeof Avatar>;

export default meta;
type Story = StoryObj<typeof meta>;

// Histoire pour l'avatar avec une image
export const WithImage: Story = {
  args: {
    children: (
      <>
        <AvatarImage src="https://github.com/shadcn.png" alt="Avatar" />
        <AvatarFallback>CN</AvatarFallback>
      </>
    ),
  },
};

// Histoire pour l'avatar avec fallback (initiales)
export const Fallback: Story = {
  args: {
    children: (
      <>
        <AvatarImage src="https://invalid-url.com/image.jpg" alt="Avatar" />
        <AvatarFallback>JD</AvatarFallback>
      </>
    ),
  },
};

// Histoire pour l'avatar avec fallback uniquement
export const FallbackOnly: Story = {
  args: {
    children: (
      <AvatarFallback>SG</AvatarFallback>
    ),
  },
};

// Histoire pour l'avatar avec image locale
export const WithLocalImage: Story = {
  args: {
    children: (
      <>
        <AvatarImage src="/erik-mclean-9y1cTVKe1IY-unsplash.jpg" alt="Avatar utilisateur" />
        <AvatarFallback>US</AvatarFallback>
      </>
    ),
  },
};

// Histoire pour l'avatar avec taille personnalisée
export const LargeAvatar: Story = {
  args: {
    className: "h-20 w-20",
    children: (
      <>
        <AvatarImage src="https://github.com/shadcn.png" alt="Avatar large" />
        <AvatarFallback className="text-lg">LG</AvatarFallback>
      </>
    ),
  },
};

// Histoire pour l'avatar avec taille petite
export const SmallAvatar: Story = {
  args: {
    className: "h-6 w-6",
    children: (
      <>
        <AvatarImage src="https://github.com/shadcn.png" alt="Avatar petit" />
        <AvatarFallback className="text-xs">SM</AvatarFallback>
      </>
    ),
  },
}; 