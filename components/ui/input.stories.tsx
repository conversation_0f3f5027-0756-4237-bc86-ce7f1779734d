// components/ui/input.stories.tsx
import type { Meta, StoryObj } from '@storybook/nextjs-vite';
import { Input } from './input';
import { Search, Lock, Mail } from 'lucide-react';

// Définition des métadonnées pour la documentation et les contrôles
const meta = {
  title: 'Design System/UI/Input',
  component: Input,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  // Définition des contrôles interactifs dans l'interface Storybook
  argTypes: {
    type: {
      control: 'select',
      options: ['text', 'email', 'password', 'search', 'number', 'tel', 'url'],
      description: 'Le type d\'input HTML',
    },
    placeholder: {
      control: 'text',
      description: 'Le texte de placeholder',
    },
    disabled: {
      control: 'boolean',
      description: 'Si l\'input est désactivé',
    },
    className: {
      control: 'text',
      description: 'Classes CSS personnalisées pour le composant',
    },
  },
} satisfies Meta<typeof Input>;

export default meta;
type Story = StoryObj<typeof meta>;

// Histoire pour l'input par défaut
export const Default: Story = {
  args: {
    type: 'text',
    placeholder: 'Tapez votre texte ici...',
  },
};

// Histoire pour l'input désactivé
export const Disabled: Story = {
  args: {
    type: 'text',
    placeholder: 'Input désactivé',
    disabled: true,
    value: 'Valeur désactivée',
  },
};

// Histoire pour l'input email
export const Email: Story = {
  args: {
    type: 'email',
    placeholder: '<EMAIL>',
  },
};

// Histoire pour l'input mot de passe
export const Password: Story = {
  args: {
    type: 'password',
    placeholder: 'Votre mot de passe',
  },
};

// Histoire pour l'input de recherche
export const SearchInput: Story = {
  args: {
    type: 'search',
    placeholder: 'Rechercher...',
  },
};

// Histoire pour l'input avec icône (simulé avec un wrapper)
export const WithIcon: Story = {
  render: (args) => (
    <div className="relative">
      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
      <Input {...args} className="pl-10" />
    </div>
  ),
  args: {
    type: 'search',
    placeholder: 'Rechercher dans Stream2Spin...',
  },
};

// Histoire pour l'input avec icône à droite
export const WithRightIcon: Story = {
  render: (args) => (
    <div className="relative">
      <Input {...args} className="pr-10" />
      <Lock className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
    </div>
  ),
  args: {
    type: 'password',
    placeholder: 'Mot de passe sécurisé',
  },
};

// Histoire pour l'input avec label
export const WithLabel: Story = {
  render: (args) => (
    <div className="space-y-2">
      <label htmlFor="email-input" className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
        Adresse email
      </label>
      <Input {...args} id="email-input" />
    </div>
  ),
  args: {
    type: 'email',
    placeholder: 'Entrez votre email',
  },
};

// Histoire pour l'input avec erreur
export const WithError: Story = {
  render: (args) => (
    <div className="space-y-2">
      <label htmlFor="error-input" className="text-sm font-medium leading-none">
        Nom d'utilisateur
      </label>
      <Input {...args} id="error-input" className="border-red-500 focus-visible:ring-red-500" />
      <p className="text-sm text-red-500">Ce nom d'utilisateur est déjà pris</p>
    </div>
  ),
  args: {
    type: 'text',
    placeholder: 'Nom d\'utilisateur',
    value: 'user123',
  },
}; 