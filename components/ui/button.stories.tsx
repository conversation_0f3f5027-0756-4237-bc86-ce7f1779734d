// components/ui/button.stories.tsx
import type { Meta, StoryObj } from '@storybook/nextjs-vite';
import { But<PERSON> } from './button';
import { Rocket } from 'lucide-react';

// Définition des métadonnées pour la documentation et les contrôles
const meta = {
  title: 'Design System/UI/Button',
  component: Button,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  // Définition des contrôles interactifs dans l'interface Storybook
  argTypes: {
    variant: {
      control: 'select',
      options: ['default', 'destructive', 'outline', 'secondary', 'ghost', 'link'],
      description: 'Le style visuel du bouton',
    },
    size: {
      control: 'select',
      options: ['default', 'sm', 'lg', 'icon'],
      description: 'La taille du bouton',
    },
    children: {
      control: 'text',
      description: 'Le contenu textuel ou JSX du bouton',
    }
  },
} satisfies Meta<typeof Button>;

export default meta;
type Story = StoryObj<typeof meta>;

// Histoire pour le bouton primaire (défaut)
export const Primary: Story = {
  args: {
    variant: 'default',
    children: 'Primary Action',
  },
};

// Histoire pour le bouton destructif
export const Destructive: Story = {
  args: {
    variant: 'destructive',
    children: 'Delete Item',
  },
};

// Histoire pour un bouton avec une icône
export const WithIcon: Story = {
  args: {
    variant: 'outline',
    children: (
      <>
        <Rocket className="mr-2 h-4 w-4" /> Launch Mission
      </>
    ),
  },
};

// Histoire pour un bouton contenant uniquement une icône
export const IconButton: Story = {
    args: {
        variant: 'secondary',
        size: 'icon',
        children: <Rocket className="h-4 w-4" />,
    },
}; 