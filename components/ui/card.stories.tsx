// components/ui/card.stories.tsx
import type { Meta, StoryObj } from '@storybook/nextjs-vite';
import { Card, CardHeader, CardTitle, CardDescription, CardContent, CardFooter } from './card';
import { Button } from './button';
import { Set<PERSON><PERSON>, <PERSON> } from 'lucide-react';

// Définition des métadonnées pour la documentation et les contrôles
const meta = {
  title: 'Design System/UI/Card',
  component: Card,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  // Définition des contrôles interactifs dans l'interface Storybook
  argTypes: {
    className: {
      control: 'text',
      description: 'Classes CSS personnalisées pour le composant',
    },
    children: {
      control: false,
      description: 'Le contenu de la carte',
    }
  },
} satisfies Meta<typeof Card>;

export default meta;
type Story = StoryObj<typeof meta>;

// Histoire pour la carte de base avec header, content et footer
export const Default: Story = {
  args: {
    children: (
      <>
        <CardHeader>
          <CardTitle>Titre de la carte</CardTitle>
          <CardDescription>Description de la carte qui explique son contenu</CardDescription>
        </CardHeader>
        <CardContent>
          <p>Voici le contenu principal de la carte. Il peut contenir du texte, des images, ou d'autres éléments.</p>
        </CardContent>
        <CardFooter>
          <Button variant="outline">Annuler</Button>
          <Button>Confirmer</Button>
        </CardFooter>
      </>
    ),
  },
};

// Histoire pour une carte avec icône dans le header
export const WithIcon: Story = {
  args: {
    children: (
      <>
        <CardHeader>
          <div className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            <CardTitle>Paramètres</CardTitle>
          </div>
          <CardDescription>Configurez vos préférences d'application</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <span>Notifications</span>
              <Button variant="outline" size="sm">Activer</Button>
            </div>
            <div className="flex items-center justify-between">
              <span>Mode sombre</span>
              <Button variant="outline" size="sm">Désactiver</Button>
            </div>
          </div>
        </CardContent>
      </>
    ),
  },
};

// Histoire pour une carte simple sans footer
export const WithoutFooter: Story = {
  args: {
    children: (
      <>
        <CardHeader>
          <CardTitle>Information</CardTitle>
          <CardDescription>Carte d'information simple</CardDescription>
        </CardHeader>
        <CardContent>
          <p>Cette carte ne contient pas de footer, seulement un header et du contenu.</p>
        </CardContent>
      </>
    ),
  },
};

// Histoire pour une carte avec action dans le header
export const WithHeaderAction: Story = {
  args: {
    children: (
      <>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Ma Liste</CardTitle>
              <CardDescription>Gérez vos éléments favoris</CardDescription>
            </div>
            <Button variant="ghost" size="icon">
              <Heart className="h-4 w-4" />
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <ul className="space-y-2">
            <li>• Élément 1</li>
            <li>• Élément 2</li>
            <li>• Élément 3</li>
          </ul>
        </CardContent>
      </>
    ),
  },
}; 