"use client";

import { useSearchParams, useRouter } from "next/navigation";
import { useState } from "react";
import { Link as LinkIcon } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { useTranslations } from 'next-intl';

interface ToggleOwnedFilterProps {
  hasDiscogsAccount: boolean;
  hideOwned: boolean;
}

export function ToggleOwnedFilter({ hasDiscogsAccount, hideOwned }: ToggleOwnedFilterProps) {
  const searchParams = useSearchParams();
  const router = useRouter();
  const t = useTranslations('recommendations.filters');
  const [isConnecting, setIsConnecting] = useState(false);

  // Fonction pour naviguer avec le nouveau état hideOwned en préservant les autres paramètres
  const handleToggleOwned = (newHideOwned: boolean) => {
    const params = new URLSearchParams(searchParams.toString());

    if (newHideOwned) {
      // Masquer les vinyles possédés (hideOwned=true)
      params.set('hideOwned', 'true');
    } else {
      // Afficher les vinyles possédés (valeur par défaut, supprimer le paramètre)
      params.delete('hideOwned');
    }

    const queryString = params.toString();
    const newUrl = `/recommendations${queryString ? `?${queryString}` : ''}`;
    router.push(newUrl);
  };

  // Fonction pour gérer la connexion Discogs
  const handleDiscogsConnect = async () => {
    setIsConnecting(true);
    try {
      const currentUrl = `/recommendations${searchParams.toString() ? `?${searchParams.toString()}` : ''}`;
      // Appeler l'API de connexion Discogs avec le paramètre returnTo pour revenir sur la page des recommandations
      const response = await fetch(`/api/discogs/connect?returnTo=${encodeURIComponent(currentUrl)}`);
      const data = await response.json();

      if (data.authorizeUrl) {
        // Rediriger vers l'URL d'autorisation Discogs
        window.location.href = data.authorizeUrl;
      } else {
        console.error('Erreur lors de la connexion Discogs:', data.error);
        setIsConnecting(false);
      }
    } catch (error) {
      console.error('Erreur lors de la connexion Discogs:', error);
      setIsConnecting(false);
    }
  };

  // Si Discogs n'est pas connecté, afficher le bouton de connexion
  if (!hasDiscogsAccount) {
    return (
      <div className="flex flex-col space-y-3">
        <div className="flex items-center space-x-2">
          <h3 className="text-sm font-medium text-muted-foreground">
            {t('owned.title')}
          </h3>
        </div>
        
        <Button
          onClick={handleDiscogsConnect}
          disabled={isConnecting}
          variant="outline"
          size="sm"
          className="inline-flex items-center space-x-2 w-fit"
        >
          <LinkIcon className="w-4 h-4" />
          <span>{isConnecting ? 'Connexion...' : t('owned.connectDiscogs')}</span>
        </Button>
        
        <p className="text-xs text-muted-foreground">
          {t('owned.connectDescription')}
        </p>
      </div>
    );
  }

  // Si Discogs est connecté, afficher le toggle
  return (
    <div className="flex flex-col space-y-3">
      <div className="flex items-center space-x-2">
        <h3 className="text-sm font-medium text-muted-foreground">
          {t('owned.title')}
        </h3>
      </div>

      <div className="flex items-center space-x-3">
        <Switch
          id="hide-owned-toggle"
          checked={hideOwned}
          onCheckedChange={handleToggleOwned}
          className="data-[state=checked]:bg-primary"
        />
        <Label
          htmlFor="hide-owned-toggle"
          className="text-sm font-medium cursor-pointer"
        >
          {t('owned.hideOwned')}
        </Label>
      </div>

      <p className="text-xs text-muted-foreground">
        {hideOwned ? t('owned.hidingOwnedDescription') : t('owned.showingOwnedDescription')}
      </p>
    </div>
  );
}
