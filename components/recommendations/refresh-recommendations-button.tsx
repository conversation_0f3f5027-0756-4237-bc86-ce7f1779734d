"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import { RefreshCw, CheckCircle, AlertCircle } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { useTranslations } from 'next-intl';

interface RefreshRecommendationsButtonProps {
  variant?: "default" | "outline" | "ghost";
  size?: "default" | "sm" | "lg";
  className?: string;
}

export function RefreshRecommendationsButton({ 
  variant = "outline", 
  size = "default",
  className = ""
}: RefreshRecommendationsButtonProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [lastRefreshStatus, setLastRefreshStatus] = useState<'idle' | 'success' | 'error'>('idle');
  const router = useRouter();
  const { toast } = useToast();
  const t = useTranslations('recommendations.refresh');

  const handleRefresh = async () => {
    setIsLoading(true);
    setLastRefreshStatus('idle');

    try {
      // Rediriger vers la page de génération
      router.push('/generating');

      setLastRefreshStatus('success');

    } catch (error) {
      setLastRefreshStatus('error');
      toast({
        title: t('errorTitle'),
        description: t('errorDescription'),
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);

      // Réinitialiser le statut après 3 secondes
      setTimeout(() => {
        setLastRefreshStatus('idle');
      }, 3000);
    }
  };

  const getIcon = () => {
    if (isLoading) {
      return <RefreshCw className="h-4 w-4 animate-spin" />;
    }
    
    switch (lastRefreshStatus) {
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'error':
        return <AlertCircle className="h-4 w-4 text-red-600" />;
      default:
        return <RefreshCw className="h-4 w-4" />;
    }
  };

  const getButtonText = () => {
    if (isLoading) {
      return t('refreshing');
    }

    switch (lastRefreshStatus) {
      case 'success':
        return t('refreshed');
      case 'error':
        return t('error');
      default:
        return t('button');
    }
  };

  return (
    <Button
      onClick={handleRefresh}
      disabled={isLoading}
      variant={variant}
      size={size}
      className={className}
    >
      {getIcon()}
      <span className="ml-2">{getButtonText()}</span>
    </Button>
  );
}
