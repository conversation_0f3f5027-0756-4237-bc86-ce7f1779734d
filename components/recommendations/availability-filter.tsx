"use client";

import { useSearchParams, useRouter } from "next/navigation";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { ShoppingCart } from "lucide-react";
import { useTranslations } from 'next-intl';

interface AvailabilityFilterProps {
  withOffers: boolean;
}

export function AvailabilityFilter({ withOffers }: AvailabilityFilterProps) {
  const searchParams = useSearchParams();
  const router = useRouter();
  const t = useTranslations('recommendations.filters');

  // Fonction pour gérer le changement d'état du switch
  const handleToggle = (checked: boolean) => {
    const params = new URLSearchParams(searchParams.toString());
    
    if (checked) {
      params.set('withOffers', 'true');
    } else {
      // Supprimer le paramètre withOffers si false (valeur par défaut)
      params.delete('withOffers');
    }
    
    const queryString = params.toString();
    const newUrl = `/recommendations${queryString ? `?${queryString}` : ''}`;
    
    router.push(newUrl);
  };

  return (
    <div className="flex flex-col space-y-3">
      <div className="flex items-center space-x-2">
        <h3 className="text-sm font-medium text-muted-foreground">
          {t('availability.title')}
        </h3>
      </div>
      
      <div className="flex items-center space-x-3">
        <Switch
          id="with-offers"
          checked={withOffers}
          onCheckedChange={handleToggle}
        />
        <Label 
          htmlFor="with-offers" 
          className="flex items-center space-x-2 text-sm font-medium cursor-pointer"
        >
          <ShoppingCart className="w-4 h-4" />
          <span>{t('availability.label')}</span>
        </Label>
      </div>
      
      <p className="text-xs text-muted-foreground">
        {withOffers 
          ? t('availability.enabledDescription')
          : t('availability.disabledDescription')
        }
      </p>
    </div>
  );
}
