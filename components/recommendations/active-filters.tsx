"use client";

import { useSearchParams, useRouter } from "next/navigation";
import { X } from "lucide-react";
import { useTranslations } from 'next-intl';

interface ActiveFiltersProps {
  currentTimeframe: string;
  hideOwned: boolean;
  withOffers: boolean;
  hasDiscogsAccount: boolean;
}

export function ActiveFilters({ 
  currentTimeframe, 
  hideOwned, 
  withOffers, 
  hasDiscogsAccount 
}: ActiveFiltersProps) {
  const searchParams = useSearchParams();
  const router = useRouter();
  const t = useTranslations('recommendations.filters');

  // Fonction pour supprimer un filtre en préservant les autres paramètres
  const removeFilter = (filterType: 'timeframe' | 'hideOwned' | 'withOffers') => {
    const params = new URLSearchParams(searchParams.toString());
    
    switch (filterType) {
      case 'timeframe':
        params.delete('timeframe');
        break;
      case 'hideOwned':
        params.delete('hideOwned');
        break;
      case 'withOffers':
        params.delete('withOffers');
        break;
    }
    
    const queryString = params.toString();
    const newUrl = `/recommendations${queryString ? `?${queryString}` : ''}`;
    router.push(newUrl);
  };

  // Déterminer quels filtres sont actifs
  const activeFilters = [];

  // Filtre timeframe - Afficher le tag seulement si :
  // 1. C'est short_term ou medium_term (toujours affichés)
  // 2. C'est long_term ET il y a un paramètre timeframe dans l'URL (explicitement sélectionné)
  const timeframeParam = searchParams.get('timeframe');
  const shouldShowTimeframeTag = 
    currentTimeframe === 'short_term' || 
    currentTimeframe === 'medium_term' || 
    (currentTimeframe === 'long_term' && timeframeParam === 'long_term');

  if (shouldShowTimeframeTag) {
    let timeframeLabel;
    switch (currentTimeframe) {
      case 'medium_term':
        timeframeLabel = t('activeTags.mediumTerm');
        break;
      case 'long_term':
        timeframeLabel = t('activeTags.longTerm');
        break;
      case 'short_term':
      default:
        timeframeLabel = t('activeTags.shortTerm');
    }
    
    activeFilters.push({
      id: 'timeframe',
      label: timeframeLabel,
      onRemove: () => removeFilter('timeframe')
    });
  }

  // Filtre hideOwned (si activé et si Discogs est connecté)
  if (hideOwned && hasDiscogsAccount) {
    activeFilters.push({
      id: 'hideOwned',
      label: t('activeTags.inCollection'),
      onRemove: () => removeFilter('hideOwned')
    });
  }

  // Filtre withOffers (si activé)
  if (withOffers) {
    activeFilters.push({
      id: 'withOffers',
      label: t('activeTags.withOffers'),
      onRemove: () => removeFilter('withOffers')
    });
  }

  // Si aucun filtre actif, ne rien afficher
  if (activeFilters.length === 0) {
    return null;
  }

  return (
    <div className="mb-6">
      <div className="flex flex-wrap gap-2">
        {activeFilters.map((filter) => (
          <div
            key={filter.id}
            className="inline-flex items-center gap-1 px-3 py-1.5 bg-primary/10 text-primary rounded-full text-sm font-medium border border-primary/20"
          >
            <span>{filter.label}</span>
            <button
              onClick={filter.onRemove}
              className="ml-1 p-0.5 hover:bg-primary/20 rounded-full transition-colors"
              title={t('activeTags.removeFilter')}
            >
              <X className="w-3 h-3" />
            </button>
          </div>
        ))}
      </div>
    </div>
  );
} 