"use client";

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogTitle } from "@/components/ui/dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ExternalLink, Music, ShoppingCart, Star } from "lucide-react";
import Image from "next/image";
import { AmazonPhase1Display } from "@/components/common/amazon-phase1-display";

interface AffiliateLink {
  vendor: string;
  url: string;
  price: number;
  currency: string;
  merchantId: string;
  productName: string;
  inStock: boolean;
  asin?: string; // Amazon-specific identifier
}

interface Recommendation {
  id: number;
  artistName: string;
  albumTitle: string;
  albumCoverUrl: string | null;
  listenScore: number;
  isOwned: boolean;
  affiliateLinks?: AffiliateLink[] | null;
}

interface OfferModalProps {
  recommendation: Recommendation;
  isOpen: boolean;
  onClose: () => void;
}

export function OfferModal({ recommendation, isOpen, onClose }: OfferModalProps) {
  // Phase 1: Filtrer les offres Amazon (sans prix) pour ne garder que les vraies offres
  const offers = (recommendation.affiliateLinks || []).filter(link =>
    link.vendor !== 'Amazon' || link.price !== null
  );

  // Trier les offres par prix croissant
  const sortedOffers = [...offers].sort((a, b) => a.price - b.price);

  const handleOfferClick = async (offer: AffiliateLink) => {
    try {
      // Optionnel: Tracker le clic pour les analytics
      // await fetch('/api/track-click', {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify({
      //     recommendationId: recommendation.id,
      //     vendor: offer.vendor,
      //     price: offer.price
      //   })
      // });

      // Ouvrir le lien dans un nouvel onglet
      window.open(offer.url, '_blank', 'noopener,noreferrer');
    } catch (error) {
      console.error('Erreur lors du tracking du clic:', error);
      // Ouvrir le lien même en cas d'erreur de tracking
      window.open(offer.url, '_blank', 'noopener,noreferrer');
    }
  };

  const getVendorLogo = (vendor: string) => {
    // Retourner le logo du vendeur si disponible
    const logos: Record<string, string> = {
      'FNAC': '/vendors/fnac-logo.svg',
      'CULTURA': '/vendors/cultura-logo.svg',
      'AMAZON': '/vendors/amazon-logo.svg',
    };
    return logos[vendor] || null;
  };

  const formatPrice = (price: number, currency: string) => {
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: currency || 'EUR'
    }).format(price);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-xl font-bold">
            Offres disponibles
          </DialogTitle>
        </DialogHeader>

        {/* Informations de l'album */}
        <div className="flex gap-4 mb-6 p-4 bg-slate-50 dark:bg-slate-800 rounded-lg">
          <div className="w-20 h-20 flex-shrink-0">
            {recommendation.albumCoverUrl ? (
              <Image
                src={recommendation.albumCoverUrl}
                alt={`${recommendation.albumTitle} by ${recommendation.artistName}`}
                width={80}
                height={80}
                className="w-20 h-20 object-cover rounded-lg"
              />
            ) : (
              <div className="w-20 h-20 bg-slate-200 dark:bg-slate-700 rounded-lg flex items-center justify-center">
                <Music className="w-8 h-8 text-slate-400" />
              </div>
            )}
          </div>
          <div className="flex-1">
            <h3 className="font-semibold text-lg text-slate-900 dark:text-slate-100 mb-1">
              {recommendation.albumTitle}
            </h3>
            <p className="text-slate-600 dark:text-slate-400 mb-2">
              {recommendation.artistName}
            </p>
            <div className="flex items-center space-x-1">
              <Star className="w-4 h-4 text-yellow-500" />
              <span className="text-sm text-slate-500 dark:text-slate-400">
                Score d'écoute: {recommendation.listenScore}
              </span>
            </div>
          </div>
        </div>

        {/* Affichage des offres (hors Amazon) */}
        <div>
          <h4 className="font-medium text-slate-900 dark:text-slate-100 mb-4">
            Autres offres disponibles
          </h4>

          {offers.length > 0 ? (
            <div className="space-y-3">
              {offers.map((offer, index) => (
                <div
                  key={`offer-${index}`}
                  className="flex items-center justify-between p-3 border border-slate-200 dark:border-slate-700 rounded-lg hover:bg-slate-50 dark:hover:bg-slate-800 transition-colors"
                >
                  <div className="flex items-center gap-3 flex-1">
                    {/* Logo du vendeur */}
                    <div className="w-10 h-10 flex items-center justify-center bg-white dark:bg-slate-700 rounded-lg border border-slate-200 dark:border-slate-600">
                      {getVendorLogo(offer.vendor) ? (
                        <Image
                          src={getVendorLogo(offer.vendor)!}
                          alt={offer.vendor}
                          width={24}
                          height={24}
                          className="w-6 h-6"
                        />
                      ) : (
                        <span className="text-xs font-medium text-slate-600 dark:text-slate-400">
                          {offer.vendor.substring(0, 3)}
                        </span>
                      )}
                    </div>

                    {/* Informations de l'offre */}
                    <div className="flex-1">
                      <div className="font-medium text-slate-900 dark:text-slate-100">
                        {offer.vendor}
                      </div>
                      <div className="text-sm text-slate-500 dark:text-slate-400 line-clamp-1">
                        {offer.productName}
                      </div>
                      {!offer.inStock && (
                        <div className="text-xs text-red-500 mt-1">
                          Stock limité
                        </div>
                      )}
                    </div>

                    {/* Prix */}
                    <div className="text-right">
                      <div className="text-lg font-bold text-slate-900 dark:text-slate-100">
                        {offer.price && offer.currency ? formatPrice(offer.price, offer.currency) : 'Prix non disponible'}
                      </div>
                    </div>
                  </div>

                  {/* Bouton d'achat */}
                  <Button
                    onClick={() => handleOfferClick(offer)}
                    className="ml-4 bg-primary hover:bg-primary/90 text-primary-foreground"
                    size="sm"
                  >
                    Acheter
                    <ExternalLink className="w-3 h-3 ml-2" />
                  </Button>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-6">
              <ShoppingCart className="w-8 h-8 text-slate-400 mx-auto mb-3" />
              <h5 className="font-medium text-slate-900 dark:text-slate-100 mb-2">
                Aucune autre offre
              </h5>
              <p className="text-slate-500 dark:text-slate-400 text-sm">
                Seul Amazon propose cet album pour le moment.
              </p>
            </div>
          )}
        </div>

        {/* Note de confiance */}
        {sortedOffers.length > 0 && (
          <div className="mt-6 p-3 bg-blue-50 dark:bg-blue-950 rounded-lg border border-blue-200 dark:border-blue-800">
            <p className="text-xs text-blue-700 dark:text-blue-300 text-center">
              Les prix sont fournis à titre indicatif par nos partenaires et peuvent varier. 
              En cliquant sur "Acheter", vous serez redirigé vers le site du vendeur.
            </p>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
}
