"use client";

import { useSearchParams, useRouter } from "next/navigation";
import { cn } from "@/lib/utils";
import { useTranslations } from 'next-intl';

interface TimeframeOption {
  value: string;
  labelKey: string;
  description: string;
}

const timeframeOptions: TimeframeOption[] = [
  {
    value: 'short_term',
    labelKey: 'shortTerm',
    description: 'shortTermDescription'
  },
  {
    value: 'medium_term',
    labelKey: 'mediumTerm',
    description: 'mediumTermDescription'
  },
  {
    value: 'long_term',
    labelKey: 'longTerm',
    description: 'longTermDescription'
  }
];

interface TimeframeFilterProps {
  currentTimeframe: string;
  onTimeframeChange?: (timeframe: string) => void;
  disabled?: boolean;
  availableTimeframes?: {
    short_term: boolean;
    medium_term: boolean;
    long_term: boolean;
  };
  customDescription?: string;
  timeframeCounts?: Record<string, number>;
}

export function TimeframeFilter({
  currentTimeframe,
  onTimeframeChange,
  disabled = false,
  availableTimeframes,
  customDescription,
  timeframeCounts = {}
}: TimeframeFilterProps) {
  const searchParams = useSearchParams();
  const router = useRouter();
  const t = useTranslations('recommendations.filters');
  const tTimeframes = useTranslations('recommendations.timeframes');

  // Fonction pour naviguer vers le nouveau timeframe en préservant les autres paramètres
  const handleTimeframeChange = (newTimeframe: string) => {
    // Si une fonction personnalisée est fournie, l'utiliser (pour la page publique)
    if (onTimeframeChange) {
      onTimeframeChange(newTimeframe);
      return;
    }

    // Sinon, utiliser la navigation par défaut
    const params = new URLSearchParams(searchParams.toString());

    if (newTimeframe === 'short_term') {
      // Supprimer le paramètre timeframe si c'est la valeur par défaut
      params.delete('timeframe');
    } else {
      params.set('timeframe', newTimeframe);
    }

    const queryString = params.toString();
    const newUrl = `/recommendations${queryString ? `?${queryString}` : ''}`;
    router.push(newUrl);
  };

  return (
    <div className="flex flex-col space-y-3">
      <div className="flex items-center space-x-2">
        <h3 className="text-sm font-medium text-muted-foreground">
          {t('timeframe.title')}
        </h3>
      </div>
      
      <div className="flex flex-wrap gap-2">
        {timeframeOptions.map((option) => {
          const isActive = currentTimeframe === option.value;
          const isAvailable = availableTimeframes ? availableTimeframes[option.value as keyof typeof availableTimeframes] : true;
          const count = timeframeCounts[option.value] || 0;
          const isEmpty = count === 0;

          return (
            <button
              key={option.value}
              onClick={() => handleTimeframeChange(option.value)}
              disabled={disabled || !isAvailable || isEmpty}
              className={cn(
                "inline-flex items-center px-4 py-2 rounded-full text-sm font-medium transition-all duration-200",
                "border border-border hover:border-primary/50",
                isActive
                  ? "bg-primary text-primary-foreground border-primary shadow-sm"
                  : "bg-background text-muted-foreground hover:text-foreground hover:bg-accent",
                (disabled || !isAvailable || isEmpty) && "opacity-50 cursor-not-allowed"
              )}
              title={isEmpty ? `${tTimeframes('noRecommendationsFor')} ${tTimeframes(option.description).toLowerCase()}` : undefined}
            >
              <span>{t(`timeframe.${option.labelKey}`)}</span>
              {count > 0 && (
                <span className={cn(
                  "ml-2 px-2 py-0.5 text-xs rounded-full",
                  isActive
                    ? "bg-primary-foreground/20 text-primary-foreground"
                    : "bg-primary/20 text-primary"
                )}>
                  {count}
                </span>
              )}
            </button>
          );
        })}
      </div>
      
      <p className="text-xs text-muted-foreground">
        {customDescription || t('timeframe.description')}
      </p>
    </div>
  );
}