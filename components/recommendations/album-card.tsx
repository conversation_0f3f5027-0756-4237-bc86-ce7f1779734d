"use client";

import { useState, useTransition, useEffect } from "react";
import { Music, Play, Pause, Heart, User } from "lucide-react";
import Image from "next/image";
import { OfferModal } from "./offer-modal";
import { useAudioPlayer, useTrackState } from "@/store/audio-player";
import { SpotifyEmbed } from "@/components/audio/spotify-embed";
import { addToWishlist, removeFromWishlist, removeFromWishlistFromPublic, addToWishlistFromPublic } from "@/app/actions/wishlist";
import { RemoveConfirmationDialog } from "@/components/wishlist/remove-confirmation-dialog";
import { toast } from "sonner";
import { useTranslations } from 'next-intl';
import Link from 'next/link';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';

// --- Helper Sub-component for Social Feed ---
function AuthorDisplay({ author }: { author: Author }) {
  const tSocial = useTranslations('Social.feed');
  if (!author.publicListId) return null;

  return (
    <div className="p-3 border-b border-slate-200 dark:border-slate-700">
      <Link href={`/u/${author.publicListId}`} className="flex items-center gap-2 group">
        <Avatar className="h-6 w-6">
          <AvatarImage src={author.image || ''} alt={author.name || 'Avatar'} />
          <AvatarFallback className="text-xs">
            <User className="h-3 w-3" />
          </AvatarFallback>
        </Avatar>
        <p className="text-xs text-slate-500 dark:text-slate-400">
          {tSocial.rich('recommendedBy', {
            name: author.name || 'Utilisateur',
            b: (chunks) => <span className="font-semibold text-slate-700 dark:text-slate-300 group-hover:underline">{chunks}</span>
          })}
        </p>
      </Link>
    </div>
  );
}

interface AffiliateLink {
  vendor: string;
  url: string;
  price: number;
  currency: string;
  merchantId: string;
  productName: string;
  inStock: boolean;
  asin?: string; // Amazon-specific identifier
}

interface Author {
  id: string;
  name: string | null;
  image: string | null;
  publicListId: string | null;
}

interface Recommendation {
  id: number;
  artistName: string;
  albumTitle: string;
  albumCoverUrl: string | null;
  listenScore: number;
  estimatedPlays?: number | null; // Nombre d'écoutes estimé
  isOwned: boolean;
  affiliateLinks?: AffiliateLink[] | null;
  // US 3.6: Informations du titre phare
  topTrackName?: string | null;
  topTrackId?: string | null;
  topTrackPreviewUrl?: string | null;
  topTrackListenScore?: number | null;
}

interface AlbumCardProps {
  recommendation: Recommendation;
  isWishlisted?: boolean;
  author?: Author; // Nouvelle prop pour l'auteur
}

export function AlbumCard({ recommendation, isWishlisted = false, author }: AlbumCardProps) {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isRemoveDialogOpen, setIsRemoveDialogOpen] = useState(false);
  const [isWishlistedLocal, setIsWishlistedLocal] = useState(isWishlisted);
  const [isPending, startTransition] = useTransition();
  const { togglePlayPause } = useAudioPlayer();
  const t = useTranslations('recommendations.offers');

  const trackState = useTrackState(recommendation.topTrackPreviewUrl || '');

  useEffect(() => {
    setIsWishlistedLocal(isWishlisted);
  }, [isWishlisted]);

  const handlePlayClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (recommendation.topTrackPreviewUrl && recommendation.topTrackName) {
      togglePlayPause(
        recommendation.topTrackPreviewUrl,
        recommendation.topTrackName,
        recommendation.albumTitle,
        recommendation.artistName
      );
    }
  };

  const handleAddToWishlist = () => {
    setIsWishlistedLocal(true);
    startTransition(async () => {
      try {
        const result = await addToWishlist(recommendation.id);
        if (result.success) {
          toast.success(t('wishlist.added', { albumTitle: recommendation.albumTitle, artistName: recommendation.artistName }));
        } else {
          setIsWishlistedLocal(false);
          toast.error(result.error || t('wishlist.addError'));
        }
      } catch (error) {
        setIsWishlistedLocal(false);
        toast.error(t('wishlist.addErrorUnexpected'));
      }
    });
  };

  const handleRemoveFromWishlist = () => {
    setIsRemoveDialogOpen(false);
    setIsWishlistedLocal(false);
    startTransition(async () => {
      try {
        const result = await removeFromWishlist(recommendation.id);
        if (result.success) {
          toast.success(t('wishlist.removed', { albumTitle: recommendation.albumTitle, artistName: recommendation.artistName }));
        } else {
          setIsWishlistedLocal(true);
          toast.error(result.error || t('wishlist.removeError'));
        }
      } catch (error) {
        setIsWishlistedLocal(true);
        toast.error(t('wishlist.removeErrorUnexpected'));
      }
    });
  };

  const handleWishlistClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (isWishlistedLocal) {
      setIsRemoveDialogOpen(true);
    } else {
      handleAddToWishlist();
    }
  };

  const amazonLink = recommendation.affiliateLinks?.find(link => link.vendor === 'Amazon');
  const otherOffers = recommendation.affiliateLinks?.filter(link => link.vendor !== 'Amazon') || [];
  const hasPreview = recommendation.topTrackPreviewUrl && recommendation.topTrackName;
  const hasSpotifyEmbed = recommendation.topTrackId && recommendation.topTrackName;

  return (
    <>
      <div
        className="bg-white dark:bg-slate-800 rounded-xl shadow-sm border border-slate-200 dark:border-slate-700 overflow-hidden hover:shadow-md transition-all duration-200 flex flex-col h-full"
      >
        {author && <AuthorDisplay author={author} />}

        <div className="aspect-square relative">
          {recommendation.albumCoverUrl ? (
            <Image
              src={recommendation.albumCoverUrl}
              alt={`${recommendation.albumTitle} by ${recommendation.artistName}`}
              fill
              className="object-cover"
              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 25vw"
            />
          ) : (
            <div className="w-full h-full bg-slate-200 dark:bg-slate-700 flex items-center justify-center">
              <Music className="w-12 h-12 text-slate-400" />
            </div>
          )}
          
          {/* Badge "Dans votre collection" si possédé */}
          {recommendation.isOwned && (
            <div className="absolute top-2 right-2 bg-green-500 text-white text-xs px-2 py-1 rounded-full">
              Dans votre collection
            </div>
          )}

          {/* Bouton cœur pour la wishlist */}
          <button
            onClick={handleWishlistClick}
            disabled={isPending}
            className={`absolute top-2 left-2 w-8 h-8 rounded-full flex items-center justify-center transition-all duration-200 ${
              isWishlistedLocal
                ? 'bg-red-500 text-white shadow-lg'
                : 'bg-white/80 text-gray-600 hover:bg-white hover:text-red-500'
            } ${isPending ? 'opacity-50 cursor-not-allowed' : 'hover:scale-110'}`}
            title={isWishlistedLocal ? "Retirer de mes envies" : "Ajouter à mes envies"}
          >
            <Heart
              className={`w-4 h-4 transition-all duration-200 ${
                isWishlistedLocal ? 'fill-current' : ''
              }`}
            />
          </button>

          {/* Pas de badge Amazon - design simplifié */}
        </div>
        
        {/* Informations de l'album */}
        <div className="p-4">
          <h3 className="font-semibold text-slate-900 dark:text-slate-100 line-clamp-2 mb-1">
            {recommendation.albumTitle}
          </h3>
          <p className="text-slate-600 dark:text-slate-400 text-sm line-clamp-1 mb-2">
            {recommendation.artistName}
          </p>

          {/* US 3.6: Section du titre phare avec Spotify Embed */}
          {hasSpotifyEmbed && (
            <div className="mb-3">
              <div className="mb-2">
                <p className="text-xs text-slate-600 dark:text-slate-400">
                  {author
                    ? t('topTrackForUser', { userName: author.name || 'Utilisateur' })
                    : t('topTrack')}
                </p>
              </div>
              <SpotifyEmbed
                trackId={recommendation.topTrackId!}
                trackName={recommendation.topTrackName!}
                artistName={recommendation.artistName}
                albumName={recommendation.albumTitle}
                compact={true}
              />
            </div>
          )}

          {/* Fallback: Section du titre phare avec lecteur audio personnalisé */}
          {!hasSpotifyEmbed && hasPreview && (
            <div className="flex items-center space-x-2 mb-3 p-2 bg-slate-50 dark:bg-slate-700/50 rounded-lg">
              <button
                onClick={handlePlayClick}
                className="flex-shrink-0 w-8 h-8 bg-primary hover:bg-primary/90 text-primary-foreground rounded-full flex items-center justify-center transition-colors"
                title={trackState.isPlaying ? "Mettre en pause" : "Écouter l'extrait"}
              >
                {trackState.isLoading ? (
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                ) : trackState.isPlaying ? (
                  <Pause className="w-4 h-4" />
                ) : (
                  <Play className="w-4 h-4 ml-0.5" />
                )}
              </button>
              <div className="flex-1 min-w-0">
                <p className="text-xs text-slate-600 dark:text-slate-400 mb-0.5">
                  {author
                    ? t('topTrackForUser', { userName: author.name || 'Utilisateur' })
                    : t('topTrack')}
                </p>
                <p className="text-sm font-medium text-slate-900 dark:text-slate-100 truncate">
                  {recommendation.topTrackName}
                </p>
              </div>
            </div>
          )}
          
          {/* Boutons d'action directs - Phase 1 */}
          <div className="flex justify-end">
            <div className="flex flex-col gap-2 items-end">
              {/* Bouton Amazon si disponible */}
              {amazonLink && (
                <button
                  className="text-xs bg-primary hover:bg-primary/90 text-primary-foreground px-3 py-1.5 rounded-full transition-colors flex items-center gap-1.5"
                  onClick={(e) => {
                    e.stopPropagation();
                    window.open(amazonLink.url, '_blank', 'noopener,noreferrer');
                  }}
                >
                  <Image
                    src="/vendors/Amazon_icon_white.svg"
                    alt="Amazon"
                    width={14}
                    height={14}
                    className="w-3.5 h-3.5"
                  />
                  {t('buy')}
                </button>
              )}

              {/* Bouton pour autres offres si disponibles */}
              {otherOffers.length > 0 && (
                <button
                  className="text-xs bg-primary hover:bg-primary/90 text-primary-foreground px-3 py-1.5 rounded-full transition-colors"
                  onClick={(e) => {
                    e.stopPropagation();
                    setIsModalOpen(true);
                  }}
                >
                  {t('otherOffers', { count: otherOffers.length })}
                </button>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Modale des offres (uniquement pour les offres non-Amazon) */}
      {isModalOpen && otherOffers.length > 0 && (
        <OfferModal
          recommendation={recommendation}
          isOpen={isModalOpen}
          onClose={() => setIsModalOpen(false)}
        />
      )}

      {/* Modale de confirmation pour retirer de la wishlist */}
      <RemoveConfirmationDialog
        isOpen={isRemoveDialogOpen}
        onClose={() => setIsRemoveDialogOpen(false)}
        onConfirm={handleRemoveFromWishlist}
        albumTitle={recommendation.albumTitle}
        artistName={recommendation.artistName}
        isLoading={isPending}
      />
    </>
  );
}
