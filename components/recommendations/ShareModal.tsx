"use client";

import { useState, useEffect, useTransition } from "react";
import { Button } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogClose,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Share2, Copy, Check, Loader2, X } from "lucide-react";
import { cn } from "@/lib/utils";
import { useToast } from "@/hooks/use-toast";
import { useTranslations } from 'next-intl';
import { getProfileSharingSettings, updateProfileVisibilityLevel } from "@/app/actions/profile";

type VisibilityLevel = 'public' | 'users_only' | 'private';

export function ShareModal({ className }: { className?: string }) {
  const [isOpen, setIsOpen] = useState(false);
  const [isCopied, setIsCopied] = useState(false);
  const [isPending, startTransition] = useTransition();
  const [settings, setSettings] = useState<{ publicListId: string | null, profileVisibility: VisibilityLevel } | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  const { toast } = useToast();
  const t = useTranslations('recommendations.share');
  const tAccount = useTranslations('account.visibility');

  useEffect(() => {
    if (isOpen) {
      setIsLoading(true);
      getProfileSharingSettings().then(data => {
        if (data) {
          setSettings(data);
        }
        setIsLoading(false);
      });
    }
  }, [isOpen]);

  const publicUrl = (typeof window !== 'undefined' && settings?.publicListId)
    ? `${window.location.origin}/u/${settings.publicListId}`
    : '';

  const handleCopyLink = async () => {
    if (!publicUrl) return;
    try {
      await navigator.clipboard.writeText(publicUrl);
      setIsCopied(true);
      toast({
        title: t('linkCopied'),
      });
      setTimeout(() => setIsCopied(false), 2000);
    } catch (error) {
      console.error("Erreur lors de la copie:", error);
      toast({
        title: t('copyError'),
        variant: "destructive",
      });
    }
  };

  const handleVisibilityChange = (value: string) => {
    const newVisibility = value as VisibilityLevel;
    if (settings) {
        setSettings({...settings, profileVisibility: newVisibility});
    }

    startTransition(async () => {
      const result = await updateProfileVisibilityLevel(newVisibility);
      if (result.success) {
        toast({ title: tAccount('updateSuccess') });
      } else {
        toast({ title: tAccount('updateError'), variant: 'destructive' });
        // Revert on failure
        getProfileSharingSettings().then(data => data && setSettings(data));
      }
    });
  };

  const visibilityOptions = [
    {
      value: "public",
      label: tAccount('level.public.label'),
      description: tAccount('level.public.description'),
    },
    {
      value: "users_only",
      label: tAccount('level.users_only.label'),
      description: tAccount('level.users_only.description'),
    },
  ];

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button
          variant="outline"
          className={cn(className)}
        >
          <Share2 className="h-4 w-4 mr-2" />
          {t('button')}
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>{t('title')}</DialogTitle>
        </DialogHeader>
        {isLoading ? (
          <div className="flex items-center justify-center h-40">
            <Loader2 className="h-6 w-6 animate-spin text-muted-foreground" />
          </div>
        ) : (
          <div className="space-y-4 pt-4">
            <p className="text-sm text-muted-foreground">
              {t('description')}
            </p>

            {settings?.profileVisibility === 'private' ? (
                 <div className="p-3 text-sm text-center text-orange-700 bg-orange-50 border border-orange-200 rounded-md">
                    <p>{tAccount('level.privateWarning')}</p>
                 </div>
            ) : (
                <div className="space-y-2">
                    <Label htmlFor="share-url">{t('shareLink')}</Label>
                    <div className="flex gap-2">
                    <Input
                        id="share-url"
                        value={publicUrl}
                        readOnly
                        className="text-sm"
                    />
                    <Button
                        size="sm"
                        onClick={handleCopyLink}
                        className={cn(
                        "shrink-0 transition-all duration-200",
                        isCopied && "bg-green-600 hover:bg-green-700"
                        )}
                    >
                        {isCopied ? (
                        <Check className="h-4 w-4" />
                        ) : (
                        <Copy className="h-4 w-4" />
                        )}
                    </Button>
                    </div>
                </div>
            )}

            <div className="space-y-3">
              <Label className="font-medium">{tAccount('level.title')}</Label>
              <RadioGroup
                value={settings?.profileVisibility}
                onValueChange={handleVisibilityChange}
                disabled={isPending}
              >
                {visibilityOptions.map(option => (
                  <div key={option.value} className="flex items-start p-3 space-x-3 transition-colors border rounded-md hover:bg-accent">
                    <RadioGroupItem value={option.value} id={option.value} className="mt-1" />
                    <div className="space-y-1">
                      <Label htmlFor={option.value} className="font-normal cursor-pointer">
                        {option.label}
                      </Label>
                      <p className="text-xs text-muted-foreground">
                        {option.description}
                      </p>
                    </div>
                  </div>
                ))}
              </RadioGroup>
               {isPending && <Loader2 className="h-4 w-4 animate-spin mr-2" />}
            </div>
          </div>
        )}
        <DialogClose className="absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground">
          <X className="h-4 w-4" />
          <span className="sr-only">Close</span>
        </DialogClose>
      </DialogContent>
    </Dialog>
  );
}
