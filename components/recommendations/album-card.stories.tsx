// components/recommendations/album-card.stories.tsx
import type { <PERSON><PERSON>, StoryObj } from '@storybook/nextjs-vite';
import { Music, Play, Pause, Heart } from 'lucide-react';
import { useState } from 'react';

// Mock du composant Image de Next.js
const ImageMock = ({ src, alt, fill, className, sizes }: any) => {
  if (fill) {
    return (
      <img
        src={src}
        alt={alt}
        className={`absolute inset-0 w-full h-full ${className}`}
      />
    );
  }
  return <img src={src} alt={alt} className={className} />;
};

// Composant AlbumCard mock fidèle au vrai composant
const AlbumCardMock = ({ recommendation, isWishlisted = false }: any) => {
  const [isWishlistedLocal, setIsWishlistedLocal] = useState(isWishlisted);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isPlaying, setIsPlaying] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const handleWishlistClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    setIsWishlistedLocal(!isWishlistedLocal);
  };

  const handlePlayClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    setIsLoading(true);
    setTimeout(() => {
      setIsLoading(false);
      setIsPlaying(!isPlaying);
    }, 500);
  };

  // Trouver le lien Amazon
  const amazonLink = recommendation.affiliateLinks?.find((link: any) =>
    link.vendor === 'Amazon' && link.price === null
  );

  // Autres offres avec prix
  const otherOffers = recommendation.affiliateLinks?.filter((link: any) =>
    link.vendor !== 'Amazon' || link.price !== null
  ) || [];

  const hasPreview = recommendation.topTrackPreviewUrl && recommendation.topTrackName;
  const hasSpotifyEmbed = recommendation.topTrackId && recommendation.topTrackName;

  return (
    <div className="bg-white dark:bg-slate-800 rounded-xl shadow-sm border border-slate-200 dark:border-slate-700 overflow-hidden hover:shadow-md transition-all duration-200">
      {/* Image de l'album */}
      <div className="aspect-square relative">
        {recommendation.albumCoverUrl ? (
          <ImageMock
            src={recommendation.albumCoverUrl}
            alt={`${recommendation.albumTitle} by ${recommendation.artistName}`}
            fill
            className="object-cover"
            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 25vw"
          />
        ) : (
          <div className="w-full h-full bg-slate-200 dark:bg-slate-700 flex items-center justify-center">
            <Music className="w-12 h-12 text-slate-400" />
          </div>
        )}
        
        {/* Badge "Dans votre collection" si possédé */}
        {recommendation.isOwned && (
          <div className="absolute top-2 right-2 bg-green-500 text-white text-xs px-2 py-1 rounded-full">
            Dans votre collection
          </div>
        )}

        {/* Bouton cœur pour la wishlist */}
        <button
          onClick={handleWishlistClick}
          className={`absolute top-2 left-2 w-8 h-8 rounded-full flex items-center justify-center transition-all duration-200 ${
            isWishlistedLocal
              ? 'bg-red-500 text-white shadow-lg'
              : 'bg-white/80 text-gray-600 hover:bg-white hover:text-red-500'
          } hover:scale-110`}
          title={isWishlistedLocal ? "Retirer de mes envies" : "Ajouter à mes envies"}
        >
          <Heart
            className={`w-4 h-4 transition-all duration-200 ${
              isWishlistedLocal ? 'fill-current' : ''
            }`}
          />
        </button>
      </div>
      
      {/* Informations de l'album */}
      <div className="p-4">
        <h3 className="font-semibold text-slate-900 dark:text-slate-100 line-clamp-2 mb-1">
          {recommendation.albumTitle}
        </h3>
        <p className="text-slate-600 dark:text-slate-400 text-sm line-clamp-1 mb-2">
          {recommendation.artistName}
        </p>

        {/* Section du titre phare avec SpotifyEmbed simulé */}
        {hasSpotifyEmbed && (
          <div className="mb-3">
            <div className="mb-2">
              <p className="text-xs text-slate-600 dark:text-slate-400">
                Titre phare
              </p>
            </div>
            {/* Mock du SpotifyEmbed - Interface Spotify complète */}
            <div className="bg-black text-white rounded-lg p-3 relative">
              <div className="flex items-center space-x-3 mb-2">
                <div className="w-12 h-12 bg-slate-700 rounded flex items-center justify-center flex-shrink-0">
                  <img 
                    src={recommendation.albumCoverUrl} 
                    alt="Album" 
                    className="w-full h-full object-cover rounded"
                  />
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-white truncate">
                    {recommendation.topTrackName}
                  </p>
                  <div className="flex items-center space-x-1">
                    <span className="bg-slate-600 text-white text-xs px-1 rounded">E</span>
                    <p className="text-xs text-slate-400 truncate">
                      {recommendation.artistName}
                    </p>
                  </div>
                </div>
                <div className="absolute top-3 right-3">
                  <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                    <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M12 0C5.4 0 0 5.4 0 12s5.4 12 12 12 12-5.4 12-12S18.66 0 12 0zm5.521 17.34c-.24.359-.66.48-1.021.24-2.82-1.74-6.36-2.101-10.561-1.141-.418.122-.84-.179-.84-.66 0-.359.24-.66.54-.78 4.56-1.021 8.52-.6 11.64 1.32.42.18.479.659.242 1.021zm1.44-3.3c-.301.42-.841.6-1.262.3-3.239-1.98-8.159-2.58-11.939-1.38-.479.12-1.02-.12-1.14-.6-.12-.48.12-1.021.6-1.141C9.6 9.9 15 10.561 18.72 12.84c.361.181.54.78.241 1.2zm.12-3.36C15.24 8.4 8.82 8.16 5.16 9.301c-.6.179-1.2-.181-1.38-.721-.18-.601.18-1.2.72-1.381 4.26-1.26 11.28-1.02 15.721 1.621.539.3.719 1.02.42 1.56-.301.421-1.02.599-1.559.3z"/>
                    </svg>
                  </div>
                </div>
              </div>
              
              <div className="flex items-center space-x-2">
                <div className="w-8 h-8 bg-white rounded-full flex items-center justify-center">
                  <span className="text-lg">+</span>
                </div>
                <span className="text-xs text-slate-400 flex-1">Ajouter sur S...</span>
              </div>
              
              <div className="flex items-center justify-between mt-3">
                <div className="w-16 h-1 bg-slate-600 rounded"></div>
                <span className="text-xs text-slate-400">03:50</span>
                <div className="flex space-x-1">
                  <div className="w-1 h-1 bg-slate-400 rounded-full"></div>
                  <div className="w-1 h-1 bg-slate-400 rounded-full"></div>
                  <div className="w-1 h-1 bg-slate-400 rounded-full"></div>
                </div>
                <button className="w-8 h-8 bg-white rounded-full flex items-center justify-center">
                  <Play className="w-4 h-4 text-black ml-0.5" />
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Fallback: Section du titre phare avec lecteur audio personnalisé */}
        {!hasSpotifyEmbed && hasPreview && (
          <div className="mb-3">
            <div className="mb-2">
              <p className="text-xs text-slate-600 dark:text-slate-400">
                Votre titre phare :
              </p>
            </div>
            <div className="flex items-center space-x-3">
              <button
                onClick={handlePlayClick}
                className="flex-shrink-0 w-8 h-8 bg-green-500 hover:bg-green-600 text-white rounded-full flex items-center justify-center transition-colors"
                title={isPlaying ? "Mettre en pause" : "Écouter l'extrait"}
              >
                {isLoading ? (
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                ) : isPlaying ? (
                  <Pause className="w-4 h-4" />
                ) : (
                  <Play className="w-4 h-4 ml-0.5" />
                )}
              </button>
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-slate-900 dark:text-slate-100 truncate">
                  {recommendation.topTrackName}
                </p>
                <p className="text-xs text-slate-600 dark:text-slate-400 truncate">
                  {recommendation.artistName}
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Affichage du score d'écoute */}
        {recommendation.estimatedPlays && (
          <div className="text-xs text-slate-500 dark:text-slate-400 mb-3">
            Basé sur {recommendation.estimatedPlays} écoutes
          </div>
        )}

        {/* Boutons d'action directs */}
        <div className="flex justify-end">
          <div className="flex flex-col gap-2 items-end">
            {/* Bouton Amazon si disponible */}
            {amazonLink && (
              <button
                className="text-xs bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-full transition-colors flex items-center gap-2"
                onClick={(e) => {
                  e.stopPropagation();
                  window.open(amazonLink.url, '_blank', 'noopener,noreferrer');
                }}
              >
                <div className="w-4 h-4 bg-white rounded-sm flex items-center justify-center">
                  <span className="text-xs text-black font-bold">a</span>
                </div>
                Acheter
              </button>
            )}

            {/* Bouton pour autres offres si disponibles */}
            {otherOffers.length > 0 && (
              <button
                className="text-xs bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-full transition-colors"
                onClick={(e) => {
                  e.stopPropagation();
                  setIsModalOpen(true);
                }}
              >
                {otherOffers.length === 1 ? '1 autre offre' : `${otherOffers.length} autres offres`}
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

// Données mock pour les stories
const mockRecommendation = {
  id: 1,
  artistName: 'Limp Bizkit',
  albumTitle: 'Chocolate Starfish And The Hot Dog Flavored Water',
  albumCoverUrl: '/erik-mclean-9y1cTVKe1IY-unsplash.jpg',
  listenScore: 85,
  estimatedPlays: 127,
  isOwned: false,
  affiliateLinks: [
    {
      vendor: 'Amazon',
      url: 'https://amazon.fr/example',
      price: null,
      currency: 'EUR',
      merchantId: 'AMZ-123',
      productName: 'Chocolate Starfish And The Hot Dog Flavored Water - Vinyl',
      inStock: true,
      asin: 'B000001234'
    },
    {
      vendor: 'Fnac',
      url: 'https://fnac.com/example',
      price: 29.99,
      currency: 'EUR',
      merchantId: 'FNAC-456',
      productName: 'Chocolate Starfish And The Hot Dog Flavored Water - Vinyl',
      inStock: true
    }
  ],
  topTrackName: 'Hot Dog',
  topTrackId: '43PuMrRfbyyuz4QpZ3oAwN',
  topTrackPreviewUrl: 'https://example.com/preview.mp3',
  topTrackListenScore: 95
};

// Définition des métadonnées
const meta: Meta<typeof AlbumCardMock> = {
  title: 'Design System/Components/AlbumCard',
  component: AlbumCardMock,
  parameters: {
    layout: 'centered',
    backgrounds: {
      default: 'light',
      values: [
        { name: 'light', value: '#f8fafc' },
        { name: 'dark', value: '#0f172a' }
      ]
    }
  },
  decorators: [
    (Story) => (
      <div className="w-80 p-4">
        <Story />
      </div>
    ),
  ],
};

export default meta;
type Story = StoryObj<typeof meta>;

// Stories
export const Default: Story = {
  args: {
    recommendation: mockRecommendation,
    isWishlisted: false,
  },
};

export const Owned: Story = {
  args: {
    recommendation: {
      ...mockRecommendation,
      isOwned: true,
    },
    isWishlisted: false,
  },
};

export const NoLinks: Story = {
  args: {
    recommendation: {
      ...mockRecommendation,
      affiliateLinks: [],
    },
    isWishlisted: false,
  },
};

export const LongTitles: Story = {
  args: {
    recommendation: {
      ...mockRecommendation,
      artistName: 'The Super Long Artist Name That Goes On And On And Should Test Text Overflow',
      albumTitle: 'This Is An Extremely Long Album Title That Should Test How The Component Handles Very Long Text Content',
      topTrackName: 'This Is A Very Long Track Name That Should Also Test Text Overflow Behavior',
    },
    isWishlisted: false,
  },
};

export const Wishlisted: Story = {
  args: {
    recommendation: mockRecommendation,
    isWishlisted: true,
  },
};

export const NoImage: Story = {
  args: {
    recommendation: {
      ...mockRecommendation,
      albumCoverUrl: null,
    },
    isWishlisted: false,
  },
};

export const NoPreview: Story = {
  args: {
    recommendation: {
      ...mockRecommendation,
      topTrackName: null,
      topTrackId: null,
      topTrackPreviewUrl: null,
    },
    isWishlisted: false,
  },
};

export const WithSpotifyEmbed: Story = {
  args: {
    recommendation: {
      ...mockRecommendation,
      topTrackId: '43PuMrRfbyyuz4QpZ3oAwN',
      topTrackName: 'Hot Dog',
      topTrackPreviewUrl: null, // Pas de preview pour forcer l'embed Spotify
      estimatedPlays: null, // Pas d'affichage du score pour cette version
    },
    isWishlisted: false,
  },
};

export const WithSpotifyEmbedAndPlays: Story = {
  args: {
    recommendation: {
      ...mockRecommendation,
      topTrackId: '43PuMrRfbyyuz4QpZ3oAwN',
      topTrackName: 'Hot Dog',
      topTrackPreviewUrl: null,
      estimatedPlays: 127, // Version avec "Basé sur 127 écoutes"
    },
    isWishlisted: false,
  },
};

export const WithAudioPreview: Story = {
  args: {
    recommendation: {
      ...mockRecommendation,
      topTrackId: null, // Pas d'ID Spotify pour forcer le lecteur audio
      topTrackName: 'Money',
      topTrackPreviewUrl: 'https://example.com/preview.mp3',
      estimatedPlays: 127, // Version avec "Basé sur 127 écoutes"
    },
    isWishlisted: false,
  },
};

export const OnlyAmazon: Story = {
  args: {
    recommendation: {
      ...mockRecommendation,
      affiliateLinks: [
        {
          vendor: 'Amazon',
          url: 'https://amazon.fr/example',
          price: null,
          currency: 'EUR',
          merchantId: 'AMZ-123',
          productName: 'The Dark Side of the Moon - Vinyl',
          inStock: true,
          asin: 'B000001234'
        }
      ],
    },
    isWishlisted: false,
  },
};

export const MultipleOffers: Story = {
  args: {
    recommendation: {
      ...mockRecommendation,
      affiliateLinks: [
        {
          vendor: 'Amazon',
          url: 'https://amazon.fr/example',
          price: null,
          currency: 'EUR',
          merchantId: 'AMZ-123',
          productName: 'The Dark Side of the Moon - Vinyl',
          inStock: true,
          asin: 'B000001234'
        },
        {
          vendor: 'Fnac',
          url: 'https://fnac.com/example',
          price: 29.99,
          currency: 'EUR',
          merchantId: 'FNAC-456',
          productName: 'The Dark Side of the Moon - Vinyl',
          inStock: true
        },
        {
          vendor: 'Cultura',
          url: 'https://cultura.com/example',
          price: 32.50,
          currency: 'EUR',
          merchantId: 'CULT-789',
          productName: 'The Dark Side of the Moon - Vinyl',
          inStock: false
        }
      ],
    },
    isWishlisted: false,
  },
}; 