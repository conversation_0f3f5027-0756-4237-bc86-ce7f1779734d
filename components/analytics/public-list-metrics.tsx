"use client";

import { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Eye, Share2, UserPlus, TrendingUp, ExternalLink, Calendar } from "lucide-react";
import { Button } from "@/components/ui/button";

interface PublicListMetrics {
  totalViews: number;
  totalShares: number;
  signupClicks: number;
  signupConversions: number;
  conversionRate: number;
  recentViews: Array<{ date: string; count: number }>;
  topReferrers: Array<{ referrer: string; count: number }>;
}

interface PublicListMetricsProps {
  publicListId: string;
  isPublic: boolean;
}

export function PublicListMetrics({ publicListId, isPublic }: PublicListMetricsProps) {
  const [metrics, setMetrics] = useState<PublicListMetrics | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchMetrics = async () => {
    if (!isPublic || !publicListId) return;

    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/analytics/metrics?publicListId=${publicListId}`);
      if (response.ok) {
        const data = await response.json();
        setMetrics(data);
      } else {
        setError("Impossible de charger les métriques");
      }
    } catch (err) {
      setError("Erreur lors du chargement des métriques");
      console.error("Erreur métriques:", err);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchMetrics();
  }, [publicListId, isPublic]);

  if (!isPublic) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5" />
            Métriques de partage
          </CardTitle>
          <CardDescription>
            Rendez votre liste publique pour voir les statistiques de partage
          </CardDescription>
        </CardHeader>
      </Card>
    );
  }

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5" />
            Métriques de partage
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5" />
            Métriques de partage
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-muted-foreground">{error}</p>
          <Button variant="outline" size="sm" onClick={fetchMetrics} className="mt-2">
            Réessayer
          </Button>
        </CardContent>
      </Card>
    );
  }

  if (!metrics) return null;

  const publicUrl = `${window.location.origin}/u/${publicListId}`;

  return (
    <div className="space-y-6">
      {/* Métriques principales */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Eye className="h-4 w-4 text-blue-600" />
              <div>
                <p className="text-2xl font-bold">{metrics.totalViews}</p>
                <p className="text-xs text-muted-foreground">Vues</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Share2 className="h-4 w-4 text-green-600" />
              <div>
                <p className="text-2xl font-bold">{metrics.totalShares}</p>
                <p className="text-xs text-muted-foreground">Partages</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <UserPlus className="h-4 w-4 text-purple-600" />
              <div>
                <p className="text-2xl font-bold">{metrics.signupClicks}</p>
                <p className="text-xs text-muted-foreground">Clics inscription</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <TrendingUp className="h-4 w-4 text-orange-600" />
              <div>
                <p className="text-2xl font-bold">{metrics.conversionRate}%</p>
                <p className="text-xs text-muted-foreground">Conversion</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Lien public */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <ExternalLink className="h-5 w-5" />
            Lien public
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-2">
            <code className="flex-1 p-2 bg-muted rounded text-sm font-mono">
              {publicUrl}
            </code>
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                navigator.clipboard.writeText(publicUrl);
                // TODO: Ajouter une notification toast
              }}
            >
              Copier
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Vues récentes */}
      {metrics.recentViews.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Calendar className="h-5 w-5" />
              Vues des 7 derniers jours
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {metrics.recentViews.map((view, index) => (
                <div key={index} className="flex justify-between items-center">
                  <span className="text-sm">{new Date(view.date).toLocaleDateString('fr-FR')}</span>
                  <Badge variant="secondary">{view.count} vues</Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Top referrers */}
      {metrics.topReferrers.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Sources de trafic</CardTitle>
            <CardDescription>
              D'où viennent vos visiteurs
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {metrics.topReferrers.map((referrer, index) => (
                <div key={index} className="flex justify-between items-center">
                  <span className="text-sm truncate flex-1">
                    {referrer.referrer || 'Accès direct'}
                  </span>
                  <Badge variant="outline">{referrer.count} vues</Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
