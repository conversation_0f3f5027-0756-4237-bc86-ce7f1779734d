"use client";

import { Button } from "@/components/ui/button";
import { ExternalLink, Search } from "lucide-react";
import Image from "next/image";

interface AffiliateLink {
  vendor: string;
  url: string;
  price: number | null;
  currency: string | null;
  merchantId: string;
  productName: string;
  inStock: boolean;
  asin?: string;
  searchKeyword?: string; // Phase 1 specific
}

interface AmazonPhase1DisplayProps {
  affiliateLinks: AffiliateLink[];
  albumTitle: string;
  artistName: string;
  isOwned: boolean;
}

export function AmazonPhase1Display({ 
  affiliateLinks, 
  albumTitle, 
  artistName, 
  isOwned 
}: AmazonPhase1DisplayProps) {
  // Séparer les offres Amazon Phase 1 (sans prix) des autres offres
  const amazonLinks = affiliateLinks.filter(link => 
    link.vendor === 'Amazon' && link.price === null
  );
  const otherOffers = affiliateLinks.filter(link => 
    link.vendor !== 'Amazon' || link.price !== null
  );

  const handleAmazonSearchClick = (amazonLink: AffiliateLink) => {
    try {
      // Ouvrir le lien de recherche Amazon dans un nouvel onglet
      window.open(amazonLink.url, '_blank', 'noopener,noreferrer');
    } catch (error) {
      console.error('Erreur lors de l\'ouverture du lien Amazon:', error);
    }
  };

  const formatPrice = (price: number, currency: string) => {
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: currency || 'EUR'
    }).format(price);
  };

  return (
    <div className="space-y-4">
      {/* Section Amazon Phase 1 */}
      {amazonLinks.length > 0 && (
        <div className="space-y-3">
          <h5 className="text-sm font-medium text-slate-700 dark:text-slate-300">
            Rechercher sur Amazon
          </h5>
          
          {amazonLinks.map((amazonLink, index) => (
            <div
              key={`amazon-${index}`}
              className="flex items-center justify-between p-3 border border-orange-200 dark:border-orange-800 rounded-lg bg-orange-50 dark:bg-orange-950/30"
            >
              <div className="flex items-center gap-3 flex-1">
                {/* Logo Amazon */}
                <div className="w-10 h-10 flex items-center justify-center bg-orange-500 rounded-lg">
                  <Image
                    src="/vendors/Amazon_icon_white.svg"
                    alt="Amazon"
                    width={24}
                    height={24}
                    className="w-6 h-6"
                  />
                </div>

                {/* Informations */}
                <div className="flex-1">
                  <div className="font-medium text-slate-900 dark:text-slate-100">
                    Amazon.fr
                  </div>
                  <div className="text-sm text-slate-600 dark:text-slate-400">
                    {isOwned ? 'Voir les prix disponibles' : 'Rechercher cet album'}
                  </div>
                  {amazonLink.searchKeyword && (
                    <div className="text-xs text-slate-500 dark:text-slate-500 mt-1">
                      Recherche: "{amazonLink.searchKeyword}"
                    </div>
                  )}
                </div>

                {/* Indicateur Phase 1 */}
                <div className="text-right">
                  <div className="text-sm font-medium text-orange-600 dark:text-orange-400">
                    Phase 1
                  </div>
                  <div className="text-xs text-slate-500 dark:text-slate-400">
                    Lien de recherche
                  </div>
                </div>
              </div>

              {/* Bouton de recherche */}
              <Button
                onClick={() => handleAmazonSearchClick(amazonLink)}
                className="ml-4 bg-primary hover:bg-primary/90 text-primary-foreground"
                size="sm"
              >
                <Search className="w-4 h-4 mr-2" />
                {isOwned ? 'Voir prix' : 'Acheter'}
                <ExternalLink className="w-3 h-3 ml-2" />
              </Button>
            </div>
          ))}
          
          {/* Note explicative Phase 1 */}
          <div className="p-3 bg-blue-50 dark:bg-blue-950/30 rounded-lg border border-blue-200 dark:border-blue-800">
            <p className="text-xs text-blue-700 dark:text-blue-300">
              <strong>Phase 1 :</strong> Ce lien vous amène directement à la recherche Amazon pour cet album. 
              Les prix détaillés seront disponibles dans la Phase 2 après validation du programme partenaire.
            </p>
          </div>
        </div>
      )}

      {/* Section autres offres (Rakuten, etc.) avec prix */}
      {otherOffers.length > 0 && (
        <div className="space-y-3">
          <h5 className="text-sm font-medium text-slate-700 dark:text-slate-300">
            Offres avec prix
          </h5>
          
          {otherOffers.map((offer, index) => (
            <div
              key={`offer-${index}`}
              className="flex items-center justify-between p-3 border border-slate-200 dark:border-slate-700 rounded-lg hover:bg-slate-50 dark:hover:bg-slate-800 transition-colors"
            >
              <div className="flex items-center gap-3 flex-1">
                {/* Logo du vendeur */}
                <div className="w-10 h-10 flex items-center justify-center bg-white dark:bg-slate-700 rounded-lg border border-slate-200 dark:border-slate-600">
                  <span className="text-xs font-medium text-slate-600 dark:text-slate-400">
                    {offer.vendor.substring(0, 3)}
                  </span>
                </div>

                {/* Informations de l'offre */}
                <div className="flex-1">
                  <div className="font-medium text-slate-900 dark:text-slate-100">
                    {offer.vendor}
                  </div>
                  <div className="text-sm text-slate-500 dark:text-slate-400 line-clamp-1">
                    {offer.productName}
                  </div>
                  {!offer.inStock && (
                    <div className="text-xs text-red-500 mt-1">
                      Stock limité
                    </div>
                  )}
                </div>

                {/* Prix */}
                <div className="text-right">
                  <div className="text-lg font-bold text-slate-900 dark:text-slate-100">
                    {offer.price && offer.currency ? formatPrice(offer.price, offer.currency) : 'Prix non disponible'}
                  </div>
                </div>
              </div>

              {/* Bouton d'achat */}
              <Button
                onClick={() => window.open(offer.url, '_blank', 'noopener,noreferrer')}
                className="ml-4 bg-primary hover:bg-primary/90 text-primary-foreground"
                size="sm"
              >
                Acheter
                <ExternalLink className="w-3 h-3 ml-2" />
              </Button>
            </div>
          ))}
        </div>
      )}

      {/* Message si aucune offre */}
      {affiliateLinks.length === 0 && (
        <div className="text-center py-6">
          <Search className="w-8 h-8 text-slate-400 mx-auto mb-3" />
          <h5 className="font-medium text-slate-900 dark:text-slate-100 mb-2">
            Aucune offre disponible
          </h5>
          <p className="text-slate-500 dark:text-slate-400 text-sm">
            Nous n'avons pas trouvé d'offre pour cet album pour le moment.
          </p>
        </div>
      )}
    </div>
  );
}
