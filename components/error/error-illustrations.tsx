"use client";

// Illustration pour la page 404 - Disque cassé/sauté
export function NotFoundIllustration() {
  return (
    <div className="relative mx-auto w-24 h-24 flex items-center justify-center">
      <div className="absolute inset-0 bg-gradient-to-br from-orange-100 to-red-100 dark:from-orange-900/20 dark:to-red-900/20 rounded-full"></div>
      <div className="relative flex items-center justify-center">
        <svg
          className="w-12 h-12 text-orange-400 dark:text-orange-500"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
          xmlns="http://www.w3.org/2000/svg"
        >
          {/* Disque avec une fissure */}
          <circle cx="12" cy="12" r="9" strokeWidth="2" />
          <circle cx="12" cy="12" r="3" strokeWidth="2" />
          {/* Fissure en zigzag */}
          <path
            d="M8 8l1 2-1 2 1 2-1 2"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
          <path
            d="M16 8l-1 2 1 2-1 2 1 2"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
        </svg>
      </div>
    </div>
  );
}

// Illustration pour les erreurs serveur 500 - Engrenages cassés
export function ServerErrorIllustration() {
  return (
    <div className="relative mx-auto w-24 h-24 flex items-center justify-center">
      <div className="absolute inset-0 bg-gradient-to-br from-yellow-100 to-orange-100 dark:from-yellow-900/20 dark:to-orange-900/20 rounded-full"></div>
      <div className="relative flex items-center justify-center">
        <svg
          className="w-12 h-12 text-yellow-500 dark:text-yellow-400"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
          xmlns="http://www.w3.org/2000/svg"
        >
          {/* Engrenage principal */}
          <path
            d="M12 15a3 3 0 100-6 3 3 0 000 6z"
            strokeWidth="2"
          />
          <path
            d="M19.4 15a1.65 1.65 0 00.33 1.82l.06.06a2 2 0 010 2.83 2 2 0 01-2.83 0l-.06-.06a1.65 1.65 0 00-1.82-.33 1.65 1.65 0 00-1 1.51V21a2 2 0 01-2 2 2 2 0 01-2-2v-.09A1.65 1.65 0 009 19.4a1.65 1.65 0 00-1.82.33l-.06.06a2 2 0 01-2.83 0 2 2 0 010-2.83l.06-.06a1.65 1.65 0 00.33-1.82 1.65 1.65 0 00-1.51-1H3a2 2 0 01-2-2 2 2 0 012-2h.09A1.65 1.65 0 004.6 9a1.65 1.65 0 00-.33-1.82l-.06-.06a2 2 0 010-2.83 2 2 0 012.83 0l.06.06a1.65 1.65 0 001.82.33H9a1.65 1.65 0 001-1.51V3a2 2 0 012-2 2 2 0 012 2v.09a1.65 1.65 0 001 1.51 1.65 1.65 0 001.82-.33l.06-.06a2 2 0 012.83 0 2 2 0 010 2.83l-.06.06a1.65 1.65 0 00-.33 1.82V9a1.65 1.65 0 001.51 1H21a2 2 0 012 2 2 2 0 01-2 2h-.09a1.65 1.65 0 00-1.51 1z"
            strokeWidth="2"
          />

        </svg>
      </div>
    </div>
  );
}

// Illustration pour les erreurs de permission 401/403 - Cadenas
export function PermissionErrorIllustration() {
  return (
    <div className="relative mx-auto w-24 h-24 flex items-center justify-center">
      <div className="absolute inset-0 bg-gradient-to-br from-purple-100 to-indigo-100 dark:from-purple-900/20 dark:to-indigo-900/20 rounded-full"></div>
      <div className="relative flex items-center justify-center">
        <svg
          className="w-12 h-12 text-purple-400 dark:text-purple-500"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
          xmlns="http://www.w3.org/2000/svg"
        >
          {/* Cadenas fermé */}
          <rect x="3" y="11" width="18" height="11" rx="2" ry="2" strokeWidth="2" />
          <path d="M7 11V7a5 5 0 0110 0v4" strokeWidth="2" />
          <circle cx="12" cy="16" r="1" strokeWidth="2" fill="currentColor" />
        </svg>
      </div>
    </div>
  );
}
