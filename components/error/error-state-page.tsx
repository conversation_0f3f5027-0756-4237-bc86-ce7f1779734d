"use client";

import { ReactNode } from "react";
import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";

interface ErrorStatePageProps {
  illustration: ReactNode;
  title: string;
  message: string;
  ctaText: string;
  ctaLink: string;
}

export function ErrorStatePage({
  illustration,
  title,
  message,
  ctaText,
  ctaLink,
}: ErrorStatePageProps) {
  return (
    <div className="text-center py-16 space-y-8">
      {/* Illustration */}
      <div className="relative mx-auto w-24 h-24 flex items-center justify-center">
        {illustration}
      </div>

      {/* Contenu */}
      <div className="space-y-4 max-w-md mx-auto">
        <h2 className="text-2xl font-semibold text-slate-900 dark:text-slate-100">
          {title}
        </h2>
        <p className="text-slate-600 dark:text-slate-400 leading-relaxed">
          {message}
        </p>
      </div>

      {/* Appel à l'action */}
      <div className="flex flex-col sm:flex-row gap-3 justify-center">
        <Button asChild className="bg-primary hover:bg-primary/90">
          <Link href={ctaLink}>
            {ctaText}
          </Link>
        </Button>
      </div>
    </div>
  );
}
