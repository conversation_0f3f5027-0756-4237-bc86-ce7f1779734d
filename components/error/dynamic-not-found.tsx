"use client";

import { ErrorStatePage } from "./error-state-page";
import { NotFoundIllustration } from "./error-illustrations";
import { useErrorRedirect } from "@/hooks/use-error-redirect";
import { useTranslations } from 'next-intl';

interface DynamicNotFoundProps {
  fromUrl?: string;
}

export function DynamicNotFound({ fromUrl }: DynamicNotFoundProps) {
  const { ctaText, ctaLink } = useErrorRedirect(fromUrl);
  const t = useTranslations('errors.notFound');

  return (
    <div className="container py-12">
      <ErrorStatePage
        illustration={<NotFoundIllustration />}
        title={t('title')}
        message={t('description')}
        ctaText={ctaText}
        ctaLink={ctaLink}
      />
    </div>
  );
}
