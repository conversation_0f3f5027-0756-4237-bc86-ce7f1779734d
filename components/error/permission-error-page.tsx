"use client";

import { ErrorStatePage } from "./error-state-page";
import { PermissionErrorIllustration } from "./error-illustrations";
import { useRouter } from "next/navigation";
import { useEffect } from "react";
import { signOut } from "next-auth/react";
import { useErrorRedirect } from "@/hooks/use-error-redirect";

interface PermissionErrorPageProps {
  fromUrl?: string;
}

export function PermissionErrorPage({ fromUrl }: PermissionErrorPageProps) {
  const router = useRouter();
  const { ctaText, ctaLink } = useErrorRedirect(fromUrl);

  useEffect(() => {
    // Pour une erreur 401, nettoyer la session locale
    const handleUnauthorized = async () => {
      try {
        // Nettoyer les tokens locaux et déconnecter l'utilisateur
        await signOut({ redirect: false });

        // Rediriger vers la page de connexion après un court délai
        setTimeout(() => {
          router.push("/login");
        }, 2000);
      } catch (error) {
        console.error("Erreur lors de la déconnexion:", error);
      }
    };

    // Déclencher la déconnexion automatique
    handleUnauthorized();
  }, [router]);

  return (
    <div className="container py-12">
      <ErrorStatePage
        illustration={<PermissionErrorIllustration />}
        title="Accès réservé"
        message="On dirait que vous n'avez pas l'invitation pour accéder à cette section. Assurez-vous d'être bien connecté(e)."
        ctaText={ctaText}
        ctaLink={ctaLink}
      />
    </div>
  );
}
