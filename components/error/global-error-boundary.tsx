"use client";

import { Component, ReactNode } from "react";
import { ErrorStatePage } from "./error-state-page";
import { ServerErrorIllustration } from "./error-illustrations";

interface Props {
  children: ReactNode;
}

interface State {
  hasError: boolean;
  error?: Error;
  fromUrl?: string;
}

export class GlobalErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    // Récupérer l'URL actuelle si possible
    const fromUrl = typeof window !== "undefined" ? window.location.pathname : undefined;
    
    return {
      hasError: true,
      error,
      fromUrl,
    };
  }

  componentDidCatch(error: Error, errorInfo: any) {
    // Log l'erreur sans données sensibles
    console.error("Global Error Boundary caught an error:", {
      message: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      timestamp: new Date().toISOString(),
    });

    // TODO: Envoyer l'erreur à un service de monitoring (Sentry, etc.)
    // en s'assurant d'anonymiser les données personnelles
  }

  private getCtaProps = () => {
    // Logique de redirection conditionnelle selon US-128
    if (this.state.fromUrl === "/recommendations") {
      return {
        ctaText: "Voir mes envies",
        ctaLink: "/wishlist"
      };
    }

    return {
      ctaText: "Retourner aux recommandations",
      ctaLink: "/recommendations"
    };
  };

  render() {
    if (this.state.hasError) {
      const { ctaText, ctaLink } = this.getCtaProps();

      return (
        <div className="container py-12">
          <ErrorStatePage
            illustration={<ServerErrorIllustration />}
            title="Ça patine un peu..."
            message="Il y a un petit souci technique de notre côté. Notre équipe est déjà sur le coup. Réessayez dans un instant."
            ctaText={ctaText}
            ctaLink={ctaLink}
          />
        </div>
      );
    }

    return this.props.children;
  }
}
