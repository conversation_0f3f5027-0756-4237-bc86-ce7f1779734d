"use client";

import { useState } from "react";
import { Session } from "next-auth";
import { PublicHeader } from "./public-header";
import { PublicUserProfile } from "./public-user-profile";
import { PublicTabs } from "./public-tabs";
import { PublicRecommendationsTab } from "./public-recommendations-tab";
import { PublicWishlistTab } from "./public-wishlist-tab";
import { PublicCollectionTab } from "./public-collection-tab";
import { useTranslations } from 'next-intl';
import { useAuthModal } from "@/store/auth-modal";

interface AffiliateLink {
  vendor: string;
  url: string;
  price: number;
  currency: string;
  merchantId: string;
  productName: string;
  inStock: boolean;
  asin?: string;
}

interface Recommendation {
  id: number;
  artistName: string;
  albumTitle: string;
  albumCoverUrl?: string | null;
  listenScore: number;
  estimatedPlays: number | null;
  timeframe: string;
  isOwned: boolean;
  affiliateLinks?: unknown;
  topTrackName?: string | null;
  topTrackId?: string | null;
  topTrackPreviewUrl?: string | null;
  topTrackListenScore?: number | null;
  generatedAt?: Date;
}

interface WishlistItem {
  id: number;
  artistName: string;
  albumTitle: string;
  albumCoverUrl?: string | null;
  listenScore: number;
  estimatedPlays: number | null;
  isOwned: boolean;
  isWishlisted: boolean;
  affiliateLinks?: unknown;
  topTrackName?: string | null;
  topTrackId?: string | null;
  topTrackPreviewUrl?: string | null;
  topTrackListenScore?: number | null;
  timeframe: string;
  createdAt: Date;
}

interface CollectionItem {
  id: number;
  discogsReleaseId: number;
  artistName: string;
  albumTitle: string;
  albumCoverUrl?: string | null;
  year?: number | null;
  format?: string | null;
  syncedAt: Date;
}

interface PublicUser {
  id: string;
  name?: string | null;
  image?: string | null;
  publicListEnabled: boolean;
  publicProfileEnabled: boolean;
  publicRecommendationsEnabled: boolean;
  publicWishlistEnabled: boolean;
  publicCollectionEnabled: boolean;
}

interface PublicData {
  user: PublicUser;
  recommendations: Recommendation[];
  wishlist: WishlistItem[];
  collection: CollectionItem[];
}

interface PublicProfileClientProps {
  publicData: PublicData;
  publicListId: string;
  initialTab: string;
  initialTimeframe: string;
  session: Session | null;
  visitorWishlistSet: Set<string>;
  originalUserFirstName: string; // Prénom de l'utilisateur d'origine
  isFollowingInitial: boolean; // État de suivi initial pour le FollowButton (US-02)
  isFollowingBackAvailable: boolean; // L'utilisateur consulté nous suit-il ?
}

interface AlbumData {
  artistName: string;
  albumTitle: string;
  albumCoverUrl?: string | null;
  spotifyAlbumId?: string | null;
  discogsReleaseId?: number | null;
}

export function PublicProfileClient({
  publicData,
  publicListId,
  initialTab,
  initialTimeframe,
  session,
  visitorWishlistSet,
  originalUserFirstName,
  isFollowingInitial,
  isFollowingBackAvailable
}: PublicProfileClientProps) {
  const [currentTab, setCurrentTab] = useState(initialTab);
  const [currentTimeframe, setCurrentTimeframe] = useState(initialTimeframe);

  const t = useTranslations('public');
  const { openModal } = useAuthModal();

  const userName = publicData.user.name || t('meta.fallbackUser');

  // Gérer le clic sur le cœur pour les visiteurs non connectés
  const handleHeartClick = (albumData: AlbumData) => {
    if (!session) {
      // Utiliser le nouveau système de modale
      openModal({
        loginReason: 'wishlist',
        targetData: {
          type: 'album',
          payload: albumData
        },
        publicListId,
        mainUserName: userName,
        mainUserImage: publicData.user.image
      });
    }
    // Si connecté, la logique sera gérée par les composants enfants
  };

  // Gérer le clic sur "Suivre" pour les visiteurs non connectés
  const handleFollowClick = () => {
    if (!session) {
      // Utiliser le nouveau système de modale
      openModal({
        loginReason: 'follow',
        targetData: {
          type: 'user',
          payload: {
            id: publicData.user.id,
            name: publicData.user.name || null,
            image: publicData.user.image || null,
            publicListId: (publicData.user as any).publicListId || null
          }
        },
        publicListId,
        mainUserName: userName,
        mainUserImage: publicData.user.image
      });
    }
  };

  // Déterminer quels onglets afficher
  const availableTabs = [];
  
  if (publicData.user.publicRecommendationsEnabled) {
    availableTabs.push({
      id: "recommendations",
      label: t('tabs.recommendations'),
      count: publicData.recommendations.length
    });
  }

  if (publicData.user.publicWishlistEnabled) {
    availableTabs.push({
      id: "wishlist",
      label: t('tabs.wishlist'),
      count: publicData.wishlist.length
    });
  }

  if (publicData.user.publicCollectionEnabled) {
    availableTabs.push({
      id: "collection",
      label: t('tabs.collection'),
      count: publicData.collection.length
    });
  }

  // Si aucun onglet n'est disponible, afficher un message
  if (availableTabs.length === 0) {
    return (
      <>
        {/* Header public uniquement pour les visiteurs non connectés */}
        {!session?.user && <PublicHeader />}
        
        <div className="container py-12">
          <div className="text-center">
            <h1 className="text-2xl font-bold mb-4">Profil privé</h1>
            <p className="text-muted-foreground">
              Ce profil n'a pas de contenu public à afficher.
            </p>
          </div>
        </div>
      </>
    );
  }

  // S'assurer que l'onglet actuel est disponible
  const validTab = availableTabs.find(tab => tab.id === currentTab) 
    ? currentTab 
    : availableTabs[0].id;

  if (validTab !== currentTab) {
    setCurrentTab(validTab);
  }

  return (
    <>
      {/* Header public uniquement pour les visiteurs non connectés */}
      {!session?.user && <PublicHeader />}
      
      <div className="container py-8">
        {/* Profil utilisateur */}
        <PublicUserProfile
          user={publicData.user}
          userName={userName}
          session={session}
          isFollowingInitial={isFollowingInitial}
          onFollowClick={handleFollowClick}
          isFollowingBackAvailable={isFollowingBackAvailable}
        />

        {/* Onglets */}
        <PublicTabs
          tabs={availableTabs}
          currentTab={currentTab}
          onTabChange={setCurrentTab}
        />

        {/* Contenu des onglets */}
        <div className="mt-8">
          {currentTab === "recommendations" && publicData.user.publicRecommendationsEnabled && (
            <PublicRecommendationsTab
              recommendations={publicData.recommendations}
              publicListId={publicListId}
              currentTimeframe={currentTimeframe}
              onTimeframeChange={setCurrentTimeframe}
              session={session}
              visitorWishlistSet={visitorWishlistSet}
              onHeartClick={handleHeartClick}
              userName={userName}
              originalUserFirstName={originalUserFirstName}
              showCollectionTag={publicData.user.publicCollectionEnabled}
              hasDiscogsAccount={publicData.collection.length > 0}
              publicCollectionEnabled={publicData.user.publicCollectionEnabled}
            />
          )}

          {currentTab === "wishlist" && publicData.user.publicWishlistEnabled && (
            <PublicWishlistTab
              wishlist={publicData.wishlist}
              publicListId={publicListId}
              session={session}
              visitorWishlistSet={visitorWishlistSet}
              onHeartClick={handleHeartClick}
              userName={userName}
              originalUserFirstName={originalUserFirstName}
              showCollectionTag={publicData.user.publicCollectionEnabled}
            />
          )}

          {currentTab === "collection" && publicData.user.publicCollectionEnabled && (
            <PublicCollectionTab
              collection={publicData.collection}
              session={session}
              visitorWishlistSet={visitorWishlistSet}
              onHeartClick={handleHeartClick}
              userName={userName}
            />
          )}
        </div>
      </div>
    </>
  );
}
