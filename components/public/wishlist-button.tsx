"use client";

import { useState, useTransition } from "react";
import { Heart, Loader2 } from "lucide-react";
import { cn } from "@/lib/utils";
import { addToWishlistFromPublic, removeFromWishlistFromPublic } from "@/app/actions/wishlist";
import { LoginModal } from "./login-modal";
import { useTranslations } from 'next-intl';

interface AlbumData {
  artistName: string;
  albumTitle: string;
  albumCoverUrl?: string | null;
  spotifyAlbumId?: string | null;
  discogsReleaseId?: number | null;
}

interface WishlistButtonProps {
  albumData: AlbumData;
  isAuthenticated: boolean;
  isWishlisted: boolean;
  publicListId: string;
  className?: string;
}

export function WishlistButton({
  albumData,
  isAuthenticated,
  isWishlisted: initialIsWishlisted,
  publicListId,
  className
}: WishlistButtonProps) {
  const [isWishlisted, setIsWishlisted] = useState(initialIsWishlisted);
  const [showLoginModal, setShowLoginModal] = useState(false);
  const [isPending, startTransition] = useTransition();
  const t = useTranslations('public.wishlistButton');

  const handleClick = () => {
    if (!isAuthenticated) {
      // Ouvrir la modale de connexion pour les visiteurs non connectés
      setShowLoginModal(true);
      return;
    }

    // Pour les utilisateurs connectés, gérer l'ajout/suppression
    startTransition(async () => {
      try {
        if (isWishlisted) {
          // Retirer de la wishlist
          const result = await removeFromWishlistFromPublic(
            albumData.artistName,
            albumData.albumTitle
          );
          
          if (result.success) {
            setIsWishlisted(false);
          } else {
            console.error("Erreur lors de la suppression:", result.error);
          }
        } else {
          // Ajouter à la wishlist
          const result = await addToWishlistFromPublic(albumData);
          
          if (result.success) {
            setIsWishlisted(true);
          } else {
            console.error("Erreur lors de l'ajout:", result.error);
          }
        }
      } catch (error) {
        console.error("Erreur wishlist:", error);
      }
    });
  };

  return (
    <>
      <button
        onClick={handleClick}
        disabled={isPending}
        className={cn(
          "absolute top-2 left-2 w-8 h-8 rounded-full flex items-center justify-center transition-all duration-200",
          isWishlisted
            ? "bg-red-500 text-white shadow-lg hover:bg-red-600"
            : "bg-white/80 text-gray-600 hover:bg-white hover:text-red-500",
          isPending && "opacity-50 cursor-not-allowed",
          !isPending && "hover:scale-110",
          className
        )}
        title={
          !isAuthenticated
            ? t('loginToAdd')
            : isWishlisted
            ? t('removeFromWishlist')
            : t('addToWishlist')
        }
      >
        {isPending ? (
          <Loader2 className="w-4 h-4 animate-spin" />
        ) : (
          <Heart
            className={cn(
              "w-4 h-4 transition-all duration-200",
              isWishlisted && "fill-current"
            )}
          />
        )}
      </button>

      {/* Modale de connexion pour visiteurs non connectés */}
      <LoginModal
        open={showLoginModal}
        onOpenChange={setShowLoginModal}
        albumData={albumData}
        publicListId={publicListId}
        loginReason="wishlist"
      />
    </>
  );
}
