"use client";

import Image from "next/image";
import { Button } from "@/components/ui/button";
import { UserPlus, Music } from "lucide-react";
import { useTranslations } from 'next-intl';
import { FollowButton } from "@/components/social/FollowButton";
import { SocialStats } from "@/components/social/SocialStats";
import { getFollowStats } from "@/app/actions/social";
import { useEffect, useState } from "react";

interface PublicUser {
  id: string;
  name?: string | null;
  image?: string | null;
  publicListId?: string | null;
  publicListEnabled: boolean;
  publicProfileEnabled: boolean;
  publicRecommendationsEnabled: boolean;
  publicWishlistEnabled: boolean;
  publicCollectionEnabled: boolean;
}

interface PublicUserProfileProps {
  user: PublicUser;
  userName: string;
  session?: any; // Session de l'utilisateur connecté
  isFollowingInitial?: boolean; // État de suivi initial
  isFollowingBackAvailable?: boolean; // L'utilisateur consulté nous suit-il ?
  onFollowClick?: () => void; // Callback pour le clic sur "Suivre" par un visiteur
}

export function PublicUserProfile({ user, userName, session, isFollowingInitial = false, isFollowingBackAvailable = false, onFollowClick }: PublicUserProfileProps) {
  const t = useTranslations('public.profile');
  const [followStats, setFollowStats] = useState<{ followersCount: number; followingCount: number } | null>(null);
  const [loading, setLoading] = useState(true);

  // Charger les statistiques de followers/following
  const loadFollowStats = async () => {
    try {
      const result = await getFollowStats(user.id);
      setFollowStats({
        followersCount: result.followers,
        followingCount: result.following
      });
    } catch (error) {
      console.error('Erreur lors du chargement des statistiques:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadFollowStats();
  }, [user.id]);

  // Mettre à jour les compteurs après une action follow/unfollow
  const handleFollowAction = async () => {
    // Recharger les statistiques après un délai pour laisser le temps à la DB de se mettre à jour
    setTimeout(() => {
      loadFollowStats();
    }, 500);
  };

  return (
    <div className="bg-white/60 dark:bg-slate-800/60 backdrop-blur-sm rounded-2xl border border-slate-200 dark:border-slate-700 p-8 mb-8 mt-8">
      <div className="flex flex-col sm:flex-row items-center sm:items-start gap-6">
        {/* Photo de profil */}
        <div className="relative">
          {user.image ? (
            <Image
              src={user.image}
              alt={userName}
              width={80}
              height={80}
              className="rounded-full border-4 border-white dark:border-slate-700 shadow-lg"
            />
          ) : (
            <div className="w-20 h-20 rounded-full bg-gradient-to-br from-primary/20 to-primary/40 border-4 border-white dark:border-slate-700 shadow-lg flex items-center justify-center">
              <Music className="w-8 h-8 text-primary" />
            </div>
          )}
        </div>

        {/* Informations utilisateur */}
        <div className="flex-1 text-center sm:text-left">
          <h1 className="text-2xl font-bold text-slate-900 dark:text-white mb-4">
            {userName}
          </h1>
          
          {/* Statistiques sociales et bouton follow sur la même ligne */}
          {!loading && followStats && (
            <div className="flex items-center gap-4">
              {/* Statistiques sociales (US-03) */}
              <SocialStats
                targetUserId={user.id}
                followersCount={followStats.followersCount}
                followingCount={followStats.followingCount}
                userName={userName}
                currentUserId={session?.user?.id}
                onFollowAction={handleFollowAction}
                onLoginRequired={onFollowClick}
                publicListId={user.publicListId || undefined}
                mainUserName={userName}
                mainUserImage={user.image}
              />
              
              {/* Bouton Suivre pour l'Epic Social V1 - US-02 */}
              {session?.user?.id && session.user.id !== user.id ? (
                <FollowButton
                  targetUserId={user.id}
                  isFollowingInitial={isFollowingInitial}
                  onFollowAction={handleFollowAction}
                  showFollowBack={isFollowingBackAvailable && !isFollowingInitial}
                />
              ) : !session?.user?.id ? (
                <Button
                  variant="outline"
                  className="bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm border-primary/20 hover:bg-primary/5"
                  onClick={onFollowClick}
                >
                  <UserPlus className="w-4 h-4 mr-2" />
                  {t('follow')}
                </Button>
              ) : null}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
