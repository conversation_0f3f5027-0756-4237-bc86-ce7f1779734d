"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { useRouter, useSearchParams } from "next/navigation";
import { Music, Heart, Disc3, LucideIcon } from "lucide-react";

interface Tab {
  id: string;
  label: string;
  count: number;
  icon?: LucideIcon;
}

interface PublicTabsProps {
  tabs: Tab[];
  currentTab: string;
  onTabChange: (tabId: string) => void;
}

export function PublicTabs({ tabs, currentTab, onTabChange }: PublicTabsProps) {
  const router = useRouter();
  const searchParams = useSearchParams();

  // Fonction pour obtenir l'icône appropriée selon l'ID de l'onglet
  const getTabIcon = (tabId: string): LucideIcon => {
    switch (tabId) {
      case 'recommendations':
        return Music;
      case 'wishlist':
        return Heart;
      case 'collection':
        return Disc3;
      default:
        return Music;
    }
  };

  const handleTabChange = (tabId: string) => {
    // Mettre à jour l'état local
    onTabChange(tabId);

    // Mettre à jour l'URL avec le nouveau paramètre tab
    const params = new URLSearchParams(searchParams);
    params.set('tab', tabId);

    // Conserver le timeframe s'il existe
    const currentTimeframe = searchParams.get('timeframe');
    if (currentTimeframe) {
      params.set('timeframe', currentTimeframe);
    }

    // Naviguer vers la nouvelle URL
    router.push(`?${params.toString()}`, { scroll: false });
  };

  return (
    <div className="sticky top-20 z-10 bg-white/60 dark:bg-slate-800/60 backdrop-blur-sm rounded-2xl border border-slate-200 dark:border-slate-700 p-2 mb-8">
      <div className="flex gap-2 overflow-x-auto md:scrollbar-hide">
        {tabs.map((tab) => {
          const IconComponent = getTabIcon(tab.id);
          return (
            <Button
              key={tab.id}
              variant={currentTab === tab.id ? "default" : "ghost"}
              onClick={() => handleTabChange(tab.id)}
              className={`flex items-center space-x-2 whitespace-nowrap flex-shrink-0 ${
                currentTab === tab.id
                  ? "bg-primary text-primary-foreground shadow-sm"
                  : "hover:bg-slate-100 dark:hover:bg-slate-700"
              }`}
            >
              <IconComponent className="w-4 h-4" />
              <span>{tab.label}</span>
              <Badge
                variant={currentTab === tab.id ? "secondary" : "outline"}
                className={`ml-2 ${
                  currentTab === tab.id
                    ? "bg-primary-foreground/20 text-primary-foreground"
                    : "bg-slate-100 dark:bg-slate-700"
                }`}
              >
                {tab.count}
              </Badge>
            </Button>
          );
        })}
      </div>
    </div>
  );
}
