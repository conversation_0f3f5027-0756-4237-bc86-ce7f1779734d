"use client";

import { useState, useTransition } from "react";
import { Music, Play, Pause, Heart } from "lucide-react";
import Image from "next/image";
import { SpotifyEmbed } from "@/components/audio/spotify-embed";
import { useAudioPlayer, useTrackState } from "@/store/audio-player";
import { addToWishlistFromPublic, removeFromWishlistFromPublic } from "@/app/actions/wishlist";
import { LoginModal } from "./login-modal";
import { Session } from "next-auth";
import { useTranslations } from 'next-intl';
import { toast } from "sonner";

interface AffiliateLink {
  vendor: string;
  url: string;
  price: number | null;
  currency: string | null;
  merchantId: string;
  productName: string;
  inStock: boolean;
  asin?: string;
  searchKeyword?: string;
}

interface WishlistItem {
  id: number;
  artistName: string;
  albumTitle: string;
  albumCoverUrl?: string | null;
  listenScore: number;
  estimatedPlays: number | null;
  isOwned: boolean;
  isWishlisted: boolean;
  affiliateLinks?: unknown;
  topTrackName?: string | null;
  topTrackId?: string | null;
  topTrackPreviewUrl?: string | null;
  topTrackListenScore?: number | null;
  timeframe: string;
  createdAt: Date;
}

interface AlbumData {
  artistName: string;
  albumTitle: string;
  albumCoverUrl?: string | null;
  spotifyAlbumId?: string | null;
  discogsReleaseId?: number | null;
}

interface PublicWishlistCardProps {
  item: WishlistItem;
  publicListId: string;
  session: Session | null;
  isWishlisted: boolean;
  onSignupClick?: (albumData: { title: string; artist: string }) => void;
  showCollectionTag?: boolean;
  collectionTagText?: string;
  originalUserName?: string; // Nom de l'utilisateur d'origine
}

export function PublicWishlistCard({
  item,
  publicListId,
  session,
  isWishlisted,
  onSignupClick,
  showCollectionTag = false,
  collectionTagText,
  originalUserName
}: PublicWishlistCardProps) {
  const [isHovered, setIsHovered] = useState(false);
  const [showLoginModal, setShowLoginModal] = useState(false);
  const [isPending, startTransition] = useTransition();
  const t = useTranslations('public.wishlist');
  const tWishlist = useTranslations('wishlist');

  const { togglePlayPause } = useAudioPlayer();
  const trackState = useTrackState(item.topTrackPreviewUrl || '');

  // Vérifier si on a un embed Spotify
  const hasSpotifyEmbed = item.topTrackId && item.topTrackName;

  // Gestion du clic sur le cœur
  const handleHeartClick = () => {
    if (!session) {
      setShowLoginModal(true);
      return;
    }

    startTransition(async () => {
      try {
        if (isWishlisted) {
          // Retirer de la wishlist
          const result = await removeFromWishlistFromPublic(item.artistName, item.albumTitle);

          if (result.success) {
            toast.success(tWishlist('removed', {
              albumTitle: item.albumTitle,
              artistName: item.artistName
            }), {
              description: tWishlist('removedDescription'),
              action: {
                label: tWishlist('undo'),
                onClick: async () => {
                  // Remettre l'album dans la wishlist
                  const undoResult = await addToWishlistFromPublic({
                    artistName: item.artistName,
                    albumTitle: item.albumTitle,
                    albumCoverUrl: item.albumCoverUrl,
                    spotifyAlbumId: (item as any).spotifyAlbumId,
                    discogsReleaseId: (item as any).discogsReleaseId,
                    affiliateLinks: item.affiliateLinks,
                    topTrackName: item.topTrackName,
                    topTrackId: item.topTrackId,
                    topTrackPreviewUrl: item.topTrackPreviewUrl,
                    topTrackListenScore: item.topTrackListenScore,
                    originalUserName: originalUserName,
                  });

                  if (undoResult.success) {
                    toast.success(tWishlist('undoSuccess', { albumTitle: item.albumTitle }));
                  }
                }
              }
            });
          } else {
            toast.error(result.error || tWishlist('removeError'));
          }
        } else {
          // Ajouter à la wishlist
          const result = await addToWishlistFromPublic({
            artistName: item.artistName,
            albumTitle: item.albumTitle,
            albumCoverUrl: item.albumCoverUrl,
            spotifyAlbumId: (item as any).spotifyAlbumId,
            discogsReleaseId: (item as any).discogsReleaseId,
            affiliateLinks: item.affiliateLinks,
            topTrackName: item.topTrackName,
            topTrackId: item.topTrackId,
            topTrackPreviewUrl: item.topTrackPreviewUrl,
            topTrackListenScore: item.topTrackListenScore,
            originalUserName: originalUserName,
          });

          if (result.success) {
            toast.success(tWishlist('added', {
              albumTitle: item.albumTitle,
              artistName: item.artistName
            }), {
              description: tWishlist('addedDescription'),
              action: {
                label: tWishlist('viewWishlist'),
                onClick: () => window.location.href = "/wishlist"
              }
            });
          } else {
            toast.error(result.error || tWishlist('addError'));
          }
        }
      } catch (error) {
        console.error("Erreur wishlist:", error);
        toast.error(tWishlist('addErrorUnexpected'), {
          description: tWishlist('addErrorDescription')
        });
      }
    });
  };

  // Gestion du play/pause
  const handlePlayPause = () => {
    if (!item.topTrackPreviewUrl) return;

    togglePlayPause(
      item.topTrackPreviewUrl,
      item.topTrackName || '',
      item.albumTitle,
      item.artistName
    );
  };

  // Parsing des liens d'affiliation
  let affiliateLinks: AffiliateLink[] = [];
  try {
    if (item.affiliateLinks && typeof item.affiliateLinks === 'string') {
      affiliateLinks = JSON.parse(item.affiliateLinks);
    } else if (Array.isArray(item.affiliateLinks)) {
      affiliateLinks = item.affiliateLinks;
    }
  } catch (error) {
    console.error('Erreur lors du parsing des liens d\'affiliation:', error);
  }

  // Extraire le lien Amazon ou créer un lien de recherche Phase 1
  const amazonLink = affiliateLinks.find(link =>
    link.vendor === 'Amazon'
  );

  // Créer un lien de recherche Amazon Phase 1 si aucun lien n'existe (sans "vinyl" pour élargir les résultats)
  const searchQuery = encodeURIComponent(`${item.artistName} ${item.albumTitle}`);
  const amazonSearchUrl = `https://www.amazon.fr/s?k=${searchQuery}&tag=stream2spin-21`;

  const finalAmazonLink = amazonLink || {
    vendor: 'Amazon',
    url: amazonSearchUrl,
    price: null,
    currency: null,
    merchantId: 'amazon-fr',
    productName: `${item.albumTitle} - ${item.artistName}`,
    inStock: true,
    searchKeyword: `${item.artistName} ${item.albumTitle}`
  };

  return (
    <>
      <div
        className="bg-white/60 dark:bg-slate-800/60 backdrop-blur-sm rounded-2xl border border-slate-200 dark:border-slate-700 overflow-hidden hover:shadow-lg transition-all duration-300 group"
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
      >
        {/* Image de l'album */}
        <div className="aspect-square relative">
          {item.albumCoverUrl ? (
            <Image
              src={item.albumCoverUrl}
              alt={`${item.albumTitle} par ${item.artistName}`}
              fill
              className="object-cover"
              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
            />
          ) : (
            <div className="w-full h-full bg-gradient-to-br from-slate-100 to-slate-200 dark:from-slate-700 dark:to-slate-800 flex items-center justify-center">
              <Music className="w-16 h-16 text-slate-400 dark:text-slate-500" />
            </div>
          )}

          {/* Bouton cœur en haut à gauche */}
          <button
            onClick={handleHeartClick}
            disabled={isPending}
            className={`absolute top-3 left-3 w-8 h-8 rounded-full flex items-center justify-center transition-all duration-200 ${
              isWishlisted
                ? 'bg-red-500 text-white shadow-lg'
                : 'bg-white/80 dark:bg-slate-800/80 text-slate-600 dark:text-slate-300 hover:bg-white dark:hover:bg-slate-800'
            } backdrop-blur-sm border border-white/20 dark:border-slate-700/20`}
          >
            {isPending ? (
              <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
            ) : (
              <Heart className={`w-4 h-4 ${isWishlisted ? 'fill-current' : ''}`} />
            )}
          </button>

          {/* Tag collection si applicable */}
          {showCollectionTag && item.isOwned && (
            <div className="absolute top-3 right-3">
              <div className="bg-green-500/90 text-white text-xs px-2 py-1 rounded-full backdrop-blur-sm border border-white/20">
                {collectionTagText}
              </div>
            </div>
          )}

          {/* Overlay avec bouton play (seulement si on a un preview) */}
          {item.topTrackPreviewUrl && (
            <div className={`absolute inset-0 bg-black/40 flex items-center justify-center transition-opacity duration-300 ${
              isHovered ? 'opacity-100' : 'opacity-0'
            }`}>
              <button
                onClick={handlePlayPause}
                className="w-12 h-12 bg-white/90 dark:bg-slate-800/90 rounded-full flex items-center justify-center hover:bg-white dark:hover:bg-slate-800 transition-colors backdrop-blur-sm"
              >
                {trackState.isPlaying ? (
                  <Pause className="w-6 h-6 text-slate-900 dark:text-slate-100" />
                ) : (
                  <Play className="w-6 h-6 text-slate-900 dark:text-slate-100 ml-0.5" />
                )}
              </button>
            </div>
          )}
        </div>

        {/* Contenu de la carte */}
        <div className="p-4 space-y-3">
          {/* Titre et artiste */}
          <div>
            <h3 className="font-semibold text-slate-900 dark:text-white mb-1 line-clamp-2">
              {item.albumTitle}
            </h3>
            <p className="text-sm text-slate-600 dark:text-slate-300 line-clamp-1">
              {item.artistName}
            </p>
          </div>

          {/* Section du titre phare avec Spotify Embed */}
          {hasSpotifyEmbed && (
            <div className="mb-3">
              <div className="mb-2">
                <p className="text-xs text-slate-600 dark:text-slate-400">
                  {originalUserName
                    ? `${originalUserName}'s top track:`
                    : 'Top track:'
                  }
                </p>
              </div>
              <SpotifyEmbed
                trackId={item.topTrackId!}
                trackName={item.topTrackName!}
                artistName={item.artistName}
                albumName={item.albumTitle}
                compact={true}
              />
            </div>
          )}

          {/* Section du titre phare sans embed (fallback) */}
          {item.topTrackName && !hasSpotifyEmbed && (
            <div className="mb-3">
              <div className="mb-2">
                <p className="text-xs text-slate-600 dark:text-slate-400">
                  {originalUserName
                    ? `${originalUserName}'s top track:`
                    : 'Top track:'
                  }
                </p>
              </div>
              <div className="flex items-center space-x-2 p-2 bg-slate-50 dark:bg-slate-800/50 rounded-lg">
                <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center flex-shrink-0">
                  <Music className="w-4 h-4 text-white" />
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-slate-900 dark:text-slate-100 truncate">
                    {item.topTrackName}
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* Date d'ajout */}
          <p className="text-xs text-muted-foreground">
            {t('addedOn', { date: new Date(item.createdAt).toLocaleDateString() })}
          </p>

          {/* Bouton d'achat Amazon */}
          <div className="flex justify-end">
            <button
              className="text-xs bg-primary hover:bg-primary/90 text-primary-foreground px-3 py-1.5 rounded-full transition-colors flex items-center gap-1.5"
              onClick={(e) => {
                e.stopPropagation();
                window.open(finalAmazonLink.url, '_blank', 'noopener,noreferrer');
              }}
              title={finalAmazonLink.price ? `Acheter sur Amazon` : `Rechercher "${finalAmazonLink.searchKeyword || `${item.artistName} ${item.albumTitle}`}" sur Amazon`}
            >
              <Image
                src="/vendors/Amazon_icon_white.svg"
                alt="Amazon"
                width={14}
                height={14}
                className="w-3.5 h-3.5"
              />
              {t('buy')}
            </button>
          </div>
        </div>
      </div>

      {/* Modal de connexion */}
      <LoginModal
        open={showLoginModal}
        onOpenChange={setShowLoginModal}
        albumData={{
          artistName: item.artistName,
          albumTitle: item.albumTitle,
          albumCoverUrl: item.albumCoverUrl
        }}
        publicListId={publicListId}
        loginReason="wishlist"
      />
    </>
  );
}
