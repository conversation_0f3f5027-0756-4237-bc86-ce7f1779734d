"use client";

import { useState } from "react";
import { signIn } from "next-auth/react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Heart, Loader2, Music, UserPlus } from "lucide-react";
import Image from "next/image";
import { useTranslations } from 'next-intl';
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { useAuthModal } from "@/store/auth-modal";

export function AuthModal() {
  const [isLoading, setIsLoading] = useState(false);
  const t = useTranslations('login.modal');
  const { isOpen, loginReason, targetData, publicListId, mainUserName, mainUserImage, closeModal } = useAuthModal();

  const isWishlistReason = loginReason === 'wishlist';
  const isUserTarget = targetData?.type === 'user';
  const isAlbumTarget = targetData?.type === 'album';

  const handleSpotifySignIn = async () => {
    setIsLoading(true);
    try {
      // Construire l'URL de callback avec les paramètres pour l'ajout automatique
      let callbackUrl = `/login-redirect?from=public&publicListId=${publicListId}`;
      
      if (isWishlistReason && isAlbumTarget) {
        const albumData = targetData.payload as any;
        callbackUrl += `&action=wishlist&album=${encodeURIComponent(albumData.albumTitle)}&artist=${encodeURIComponent(albumData.artistName)}`;
        if (albumData.albumCoverUrl) {
          callbackUrl += `&cover=${encodeURIComponent(albumData.albumCoverUrl)}`;
        }
        if (albumData.spotifyAlbumId) {
          callbackUrl += `&spotifyId=${encodeURIComponent(albumData.spotifyAlbumId)}`;
        }
        if (albumData.discogsReleaseId) {
          callbackUrl += `&discogsId=${albumData.discogsReleaseId}`;
        }
      } else if (loginReason === 'follow') {
        callbackUrl += `&action=follow`;
      }

      await signIn("spotify", {
        callbackUrl,
        redirect: true
      });
    } catch (error) {
      console.error("Erreur lors de la connexion Spotify:", error);
    } finally {
      setIsLoading(false);
    }
  };

  // Déterminer le titre et la description selon le contexte
  let title: string;
  let description: string;
  let autoAddNote: string;

  if (isWishlistReason && isAlbumTarget) {
    const albumData = targetData.payload as any;
    title = t('title');
    description = t('description');
    autoAddNote = t('autoAddNote');
  } else if (loginReason === 'follow' && isUserTarget) {
    const userData = targetData.payload as any;
    title = t('followTitle', { name: userData.name || 'Utilisateur' });
    description = t('followDescription', { name: userData.name || 'Utilisateur' });
    autoAddNote = t('followAutoAddNote', { name: userData.name || 'Utilisateur' });
  } else {
    // Fallback pour les cas non gérés
    title = isWishlistReason ? t('title') : t('followTitle', { name: mainUserName || 'Utilisateur' });
    description = isWishlistReason ? t('description') : t('followDescription', { name: mainUserName || 'Utilisateur' });
    autoAddNote = isWishlistReason ? t('autoAddNote') : t('followAutoAddNote', { name: mainUserName || 'Utilisateur' });
  }

  return (
    <Dialog open={isOpen} onOpenChange={closeModal}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader className="text-center">
          <div className="mx-auto w-12 h-12 bg-gradient-to-br from-primary to-primary/80 rounded-full flex items-center justify-center mb-4">
            {isWishlistReason ? (
              <Heart className="w-6 h-6 text-white" />
            ) : (
              <UserPlus className="w-6 h-6 text-white" />
            )}
          </div>
          <DialogTitle className="text-xl font-bold text-center">
            {title}
          </DialogTitle>
          <DialogDescription className="text-center space-y-3">
            {/* Affiche les détails de l'album si la raison est 'wishlist' */}
            {isWishlistReason && isAlbumTarget && (
              <div className="bg-slate-50 dark:bg-slate-800 rounded-lg p-4 my-4">
                <div className="flex items-center gap-3">
                  {(targetData.payload as any).albumCoverUrl ? (
                    <Image
                      src={(targetData.payload as any).albumCoverUrl}
                      alt={`${(targetData.payload as any).albumTitle} - ${(targetData.payload as any).artistName}`}
                      width={48}
                      height={48}
                      className="rounded-md"
                    />
                  ) : (
                    <div className="w-12 h-12 bg-slate-200 dark:bg-slate-700 rounded-md flex items-center justify-center">
                      <Music className="w-6 h-6 text-slate-400" />
                    </div>
                  )}
                  <div className="text-left">
                    <p className="font-medium text-sm">{(targetData.payload as any).albumTitle}</p>
                    <p className="text-xs text-muted-foreground">{(targetData.payload as any).artistName}</p>
                  </div>
                </div>
              </div>
            )}

            {/* Affiche le profil utilisateur si la raison est 'follow' */}
            {loginReason === 'follow' && isUserTarget && (
              <div className="bg-slate-50 dark:bg-slate-800 rounded-lg p-4 my-4">
                <div className="flex items-center gap-3">
                  <Avatar className="h-12 w-12">
                    <AvatarImage src={(targetData.payload as any).image || undefined} alt={(targetData.payload as any).name || 'Utilisateur'} />
                    <AvatarFallback>
                      {(targetData.payload as any).name?.charAt(0).toUpperCase() || 'U'}
                    </AvatarFallback>
                  </Avatar>
                  <div className="text-left">
                    <p className="font-medium text-sm">{(targetData.payload as any).name || 'Utilisateur'}</p>
                  </div>
                </div>
              </div>
            )}

            <p className="text-sm text-muted-foreground">
              {description}
            </p>
            
            <p className="text-xs text-muted-foreground bg-muted/50 rounded-md p-2">
              {autoAddNote}
            </p>
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4 mt-6">
          {/* Bouton Spotify - identique à la page login */}
          <Button
            onClick={handleSpotifySignIn}
            disabled={isLoading}
            className="w-full h-12 flex items-center justify-center gap-3 bg-primary hover:bg-primary/90 text-primary-foreground font-semibold shadow-lg hover:shadow-xl transition-all duration-200 border-0"
          >
            {isLoading ? (
              <Loader2 className="h-5 w-5 animate-spin" />
            ) : (
              <svg
                className="h-5 w-5"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
                fill="currentColor"
              >
                <path d="M12 0C5.4 0 0 5.4 0 12s5.4 12 12 12 12-5.4 12-12S18.66 0 12 0zm5.521 17.34c-.24.359-.66.48-1.021.24-2.82-1.74-6.36-2.101-10.561-1.141-.418.122-.779-.179-.899-.539-.12-.421.18-.78.54-.9 4.56-1.021 8.52-.6 11.64 **********.479.659.301 1.02zm1.44-3.3c-.301.42-.841.6-1.262.3-3.239-1.98-8.159-2.58-11.939-1.38-.479.12-1.02-.12-1.14-.6-.12-.48.12-1.021.6-1.141C9.6 9.9 15 10.561 18.72 12.84c.361.181.54.78.241 1.2zm.12-3.36C15.24 8.4 8.82 8.16 5.16 9.301c-.6.179-1.2-.181-1.38-.721-.18-.601.18-1.2.72-1.381 4.26-1.26 11.28-1.02 15.721 1.621.539.3.719 1.02.42 1.56-.299.421-1.02.599-1.559.3z"/>
              </svg>
            )}
            {isLoading ? t('connecting') : t('continueWith')}
          </Button>

          {/* Bouton annuler */}
          <Button
            variant="outline"
            onClick={closeModal}
            className="w-full"
            disabled={isLoading}
          >
            {t('maybeLater')}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
} 