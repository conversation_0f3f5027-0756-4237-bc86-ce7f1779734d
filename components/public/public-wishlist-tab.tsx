"use client";

import { useState } from "react";
import { Session } from "next-auth";
import { Search } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { PublicWishlistCard } from "./public-wishlist-card";
import { useTranslations } from 'next-intl';

interface WishlistItem {
  id: number;
  artistName: string;
  albumTitle: string;
  albumCoverUrl?: string | null;
  listenScore: number;
  estimatedPlays: number | null;
  isOwned: boolean;
  isWishlisted: boolean;
  affiliateLinks?: unknown;
  topTrackName?: string | null;
  topTrackId?: string | null;
  topTrackPreviewUrl?: string | null;
  topTrackListenScore?: number | null;
  timeframe: string;
  createdAt: Date;
}

interface AlbumData {
  artistName: string;
  albumTitle: string;
  albumCoverUrl?: string | null;
  spotifyAlbumId?: string | null;
  discogsReleaseId?: number | null;
}

interface PublicWishlistTabProps {
  wishlist: WishlistItem[];
  publicListId: string;
  session: Session | null;
  visitorWishlistSet: Set<string>;
  onHeartClick: (albumData: AlbumData) => void;
  userName: string;
  originalUserFirstName: string; // Prénom de l'utilisateur d'origine
  showCollectionTag: boolean;
}

export function PublicWishlistTab({
  wishlist,
  publicListId,
  session,
  visitorWishlistSet,
  onHeartClick,
  userName,
  originalUserFirstName,
  showCollectionTag
}: PublicWishlistTabProps) {
  const [searchQuery, setSearchQuery] = useState("");
  const [sortBy, setSortBy] = useState("createdAt_desc");
  const t = useTranslations('public.wishlist');
  const tCommon = useTranslations('common');
  const tWishlist = useTranslations('wishlist.filters');

  // Options de tri disponibles
  const sortOptions = [
    { value: "createdAt_desc", label: tWishlist('sortOptions.createdAt_desc') },
    { value: "createdAt_asc", label: tWishlist('sortOptions.createdAt_asc') },
    { value: "artistName_asc", label: tWishlist('sortOptions.artistName_asc') },
    { value: "artistName_desc", label: tWishlist('sortOptions.artistName_desc') },
    { value: "albumTitle_asc", label: tWishlist('sortOptions.albumTitle_asc') },
    { value: "albumTitle_desc", label: tWishlist('sortOptions.albumTitle_desc') },
  ];

  // Filtrer et trier la wishlist
  let filteredWishlist = wishlist.filter(item =>
    item.artistName.toLowerCase().includes(searchQuery.toLowerCase()) ||
    item.albumTitle.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // Appliquer le tri
  filteredWishlist.sort((a, b) => {
    switch (sortBy) {
      case "createdAt_asc":
        return new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime();
      case "artistName_asc":
        return a.artistName.localeCompare(b.artistName);
      case "artistName_desc":
        return b.artistName.localeCompare(a.artistName);
      case "albumTitle_asc":
        return a.albumTitle.localeCompare(b.albumTitle);
      case "albumTitle_desc":
        return b.albumTitle.localeCompare(a.albumTitle);
      default: // "createdAt_desc"
        return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
    }
  });

  // Trouver le top track (premier album de la wishlist)
  const topTrack = wishlist[0];

  return (
    <div className="space-y-6">
      {/* En-tête */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h2 className="text-2xl font-bold">{t('title')}</h2>
        </div>
      </div>

      {/* Contrôles de recherche et tri */}
      {wishlist.length > 0 && (
        <div className="flex flex-col sm:flex-row gap-4 mb-6">
          {/* Champ de recherche */}
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
            <Input
              type="text"
              placeholder={t('search')}
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>

          {/* Sélecteur de tri */}
          <div className="sm:w-64">
            <Select value={sortBy} onValueChange={setSortBy}>
              <SelectTrigger>
                <SelectValue placeholder={tCommon('sortBy')} />
              </SelectTrigger>
              <SelectContent>
                {sortOptions.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Compteur de résultats */}
          <div className="flex items-center text-sm text-muted-foreground whitespace-nowrap">
            {tWishlist('albumCount', { count: filteredWishlist.length })}
          </div>
        </div>
      )}

      {/* Grille de la wishlist */}
      {filteredWishlist.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {filteredWishlist.map((item) => (
            <PublicWishlistCard
              key={`${item.artistName}-${item.albumTitle}`}
              item={item}
              publicListId={publicListId}
              session={session}
              isWishlisted={visitorWishlistSet.has(`${item.artistName}|${item.albumTitle}`)}
              onSignupClick={({ title, artist }) => onHeartClick({ artistName: artist, albumTitle: title })}
              showCollectionTag={showCollectionTag}
              collectionTagText={t('collectionTag')}
              originalUserName={originalUserFirstName}
            />
          ))}
        </div>
      ) : wishlist.length === 0 ? (
        <div className="text-center py-12">
          <div className="bg-white/60 dark:bg-slate-800/60 backdrop-blur-sm rounded-2xl border border-slate-200 dark:border-slate-700 p-8">
            <h3 className="text-lg font-medium mb-2">{t('empty.title')}</h3>
            <p className="text-muted-foreground">
              {t('empty.description', { userName })}
            </p>
          </div>
        </div>
      ) : (
        <div className="text-center py-12">
          <div className="bg-white/60 dark:bg-slate-800/60 backdrop-blur-sm rounded-2xl border border-slate-200 dark:border-slate-700 p-8">
            <h3 className="text-lg font-medium mb-2">{t('noResults.title')}</h3>
            <p className="text-muted-foreground mb-4">
              {t('noResults.description', { query: searchQuery })}
            </p>
            <Button
              variant="outline"
              onClick={() => setSearchQuery("")}
              className="bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm"
            >
              {t('noResults.clear')}
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}
