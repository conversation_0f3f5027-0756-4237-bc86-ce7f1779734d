"use client";

import { useState, useEffect } from "react";
import { Session } from "next-auth";
import { Loader2, Disc3, Shopping<PERSON>art, Filter, ChevronUp, ChevronDown } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { TimeframeFilter } from "@/components/recommendations/timeframe-filter";
import { PublicAlbumCard } from "./public-album-card";
import { useTranslations } from 'next-intl';
import { useRouter, useSearchParams } from "next/navigation";

interface AffiliateLink {
  vendor: string;
  url: string;
  price: number;
  currency: string;
  merchantId: string;
  productName: string;
  inStock: boolean;
  asin?: string;
}

interface Recommendation {
  id: number;
  artistName: string;
  albumTitle: string;
  albumCoverUrl?: string | null;
  listenScore: number;
  estimatedPlays: number | null;
  timeframe: string;
  isOwned: boolean;
  affiliateLinks?: unknown;
  topTrackName?: string | null;
  topTrackId?: string | null;
  topTrackPreviewUrl?: string | null;
  topTrackListenScore?: number | null;
  generatedAt?: Date;
}

interface AlbumData {
  artistName: string;
  albumTitle: string;
  albumCoverUrl?: string | null;
  spotifyAlbumId?: string | null;
  discogsReleaseId?: number | null;
}

interface PublicRecommendationsTabProps {
  recommendations: Recommendation[];
  publicListId: string;
  currentTimeframe: string;
  onTimeframeChange: (timeframe: string) => void;
  session: Session | null;
  visitorWishlistSet: Set<string>;
  onHeartClick: (albumData: AlbumData) => void;
  userName: string;
  originalUserFirstName: string; // Prénom de l'utilisateur d'origine
  showCollectionTag: boolean;
  hasDiscogsAccount?: boolean;
  publicCollectionEnabled?: boolean;
}

export function PublicRecommendationsTab({
  recommendations,
  publicListId,
  currentTimeframe,
  onTimeframeChange,
  session,
  visitorWishlistSet,
  onHeartClick,
  userName,
  originalUserFirstName,
  showCollectionTag,
  hasDiscogsAccount = false,
  publicCollectionEnabled = false
}: PublicRecommendationsTabProps) {
  const [filteredRecommendations, setFilteredRecommendations] = useState<Recommendation[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [showOwned, setShowOwned] = useState(true);
  const [withOffers, setWithOffers] = useState(false);
  const [filtersExpanded, setFiltersExpanded] = useState(false);
  const t = useTranslations('public.recommendations');
  const tFilters = useTranslations('recommendations.filters');
  const router = useRouter();
  const searchParams = useSearchParams();

  // Filtrer les recommandations par timeframe et autres filtres
  useEffect(() => {
    let filtered = recommendations.filter(rec => rec.timeframe === currentTimeframe);

    // Filtre des vinyles possédés (seulement si Discogs est connecté et collection publique)
    if (hasDiscogsAccount && publicCollectionEnabled && !showOwned) {
      filtered = filtered.filter(rec => !rec.isOwned);
    }

    // Filtre de disponibilité
    if (withOffers) {
      filtered = filtered.filter(rec => {
        const links = rec.affiliateLinks;
        return Array.isArray(links) && links.length > 0;
      });
    }

    setFilteredRecommendations(filtered);
  }, [recommendations, currentTimeframe, showOwned, withOffers, hasDiscogsAccount, publicCollectionEnabled]);

  // Gérer le changement de timeframe
  const handleTimeframeChange = async (newTimeframe: string) => {
    if (newTimeframe === currentTimeframe) return;

    setIsLoading(true);
    onTimeframeChange(newTimeframe);

    // Mettre à jour l'URL avec le nouveau timeframe
    const params = new URLSearchParams(searchParams);
    params.set('timeframe', newTimeframe);

    // Conserver l'onglet actuel
    const currentTab = searchParams.get('tab');
    if (currentTab) {
      params.set('tab', currentTab);
    }

    // Naviguer vers la nouvelle URL
    router.push(`?${params.toString()}`, { scroll: false });

    try {
      // Simuler un délai pour l'UX
      await new Promise(resolve => setTimeout(resolve, 300));
    } finally {
      setIsLoading(false);
    }
  };

  // Calculer les timeframes disponibles et leurs compteurs
  const timeframeCounts = {
    short_term: recommendations.filter(r => r.timeframe === 'short_term').length,
    medium_term: recommendations.filter(r => r.timeframe === 'medium_term').length,
    long_term: recommendations.filter(r => r.timeframe === 'long_term').length,
  };

  const availableTimeframes = {
    short_term: timeframeCounts.short_term > 0,
    medium_term: timeframeCounts.medium_term > 0,
    long_term: timeframeCounts.long_term > 0,
  };

  // Priorisation des timeframes : si le timeframe actuel est vide, passer au suivant
  useEffect(() => {
    const currentCount = timeframeCounts[currentTimeframe as keyof typeof timeframeCounts] || 0;

    if (currentCount === 0) {
      // Ordre de priorité : short_term -> medium_term -> long_term
      if (timeframeCounts.short_term > 0) {
        onTimeframeChange('short_term');
      } else if (timeframeCounts.medium_term > 0) {
        onTimeframeChange('medium_term');
      } else if (timeframeCounts.long_term > 0) {
        onTimeframeChange('long_term');
      }
    }
  }, [recommendations, currentTimeframe, onTimeframeChange, timeframeCounts]);

  const ActiveFilterTags = () => {
    let timeframeLabel;
    switch (currentTimeframe) {
      case 'medium_term':
        timeframeLabel = tFilters('activeTags.mediumTerm');
        break;
      case 'long_term':
        timeframeLabel = tFilters('activeTags.longTerm');
        break;
      case 'short_term':
      default:
        timeframeLabel = tFilters('activeTags.shortTerm');
    }

    return (
      <div className="flex flex-wrap gap-1 ml-3">
        <span className="inline-flex items-center px-2 py-0.5 bg-primary/10 text-primary rounded-full text-xs font-medium border border-primary/20">
          {timeframeLabel}
        </span>
        {!showOwned && hasDiscogsAccount && publicCollectionEnabled && (
          <span className="inline-flex items-center px-2 py-0.5 bg-primary/10 text-primary rounded-full text-xs font-medium border border-primary/20">
            {tFilters('activeTags.inCollection')}
          </span>
        )}
        {withOffers && (
          <span className="inline-flex items-center px-2 py-0.5 bg-primary/10 text-primary rounded-full text-xs font-medium border border-primary/20">
            {tFilters('activeTags.withOffers')}
          </span>
        )}
      </div>
    );
  };

  // Trouver le top track
  const topTrack = filteredRecommendations.find(rec => rec.topTrackName);

  return (
    <div className="space-y-6">
      {/* En-tête */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h2 className="text-2xl font-bold">{t('title')}</h2>
        </div>
      </div>

      {/* Filtres */}
      <div className="mb-8 bg-card border rounded-lg relative">
        {/* En-tête des filtres avec bouton collapse/expand */}
        <button
          onClick={() => setFiltersExpanded(!filtersExpanded)}
          className="w-full flex items-center justify-between p-4 border-b text-left hover:bg-accent/50 transition-colors"
        >
          <div className="flex items-center space-x-2">
            <Filter className="w-4 h-4 text-muted-foreground" />
            <h3 className="text-sm font-medium text-muted-foreground">
              {t('filters.title')}
            </h3>
            <ActiveFilterTags />
          </div>
          <div className="flex items-center space-x-1 text-xs text-muted-foreground">
            <span>{filtersExpanded ? t('filters.hide') : t('filters.show')}</span>
            {filtersExpanded ? (
              <ChevronUp className="w-3 h-3" />
            ) : (
              <ChevronDown className="w-3 h-3" />
            )}
          </div>
        </button>

        {/* Contenu des filtres (collapsible) */}
        {filtersExpanded && (
          <div className="p-6 space-y-6">
            {isLoading && (
              <div className="absolute inset-0 bg-background/50 backdrop-blur-sm rounded-lg flex items-center justify-center z-10">
                <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                  <Loader2 className="w-4 h-4 animate-spin" />
                  <span>Mise à jour des résultats...</span>
                </div>
              </div>
            )}
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Filtre temporel */}
            <TimeframeFilter
              currentTimeframe={currentTimeframe}
              availableTimeframes={availableTimeframes}
              onTimeframeChange={handleTimeframeChange}
              customDescription={t('filterDescription')}
              timeframeCounts={timeframeCounts}
            />

            {/* Filtre des vinyles possédés - seulement si Discogs connecté et collection publique */}
            {hasDiscogsAccount && publicCollectionEnabled && (
              <div className="flex flex-col space-y-3">
                <div className="flex items-center space-x-2">
                  <h3 className="text-sm font-medium text-muted-foreground">
                    Collection Discogs
                  </h3>
                </div>

                <div className="flex items-center space-x-3">
                  <Switch
                    id="show-owned-toggle"
                    checked={showOwned}
                    onCheckedChange={setShowOwned}
                    className="data-[state=checked]:bg-primary"
                  />
                  <Label
                    htmlFor="show-owned-toggle"
                    className="flex items-center space-x-2 text-sm font-medium cursor-pointer"
                  >
                    <Disc3 className="w-4 h-4" />
                    <span>{t('filters.owned')}</span>
                  </Label>
                </div>

                <p className="text-xs text-muted-foreground">
                  {showOwned ? t('filters.showOwned') : t('filters.hideOwned')}
                </p>
              </div>
            )}

            {/* Filtre de disponibilité */}
            <div className="flex flex-col space-y-3">
              <div className="flex items-center space-x-2">
                <h3 className="text-sm font-medium text-muted-foreground">
                  {t('filters.availability')}
                </h3>
              </div>

              <div className="flex items-center space-x-3">
                <Switch
                  id="with-offers"
                  checked={withOffers}
                  onCheckedChange={setWithOffers}
                />
                <Label
                  htmlFor="with-offers"
                  className="flex items-center space-x-2 text-sm font-medium cursor-pointer"
                >
                  <ShoppingCart className="w-4 h-4" />
                  <span>{t('filters.availabilityLabel')}</span>
                </Label>
              </div>

              <p className="text-xs text-muted-foreground">
                {withOffers
                  ? t('filters.availabilityEnabledDescription')
                  : t('filters.availabilityDisabledDescription')
                }
              </p>
            </div>
          </div>
        </div>
        )}
      </div>

      {/* Grille des recommandations */}
      {isLoading ? (
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      ) : filteredRecommendations.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {filteredRecommendations.map((recommendation) => (
            <PublicAlbumCard
              key={recommendation.id}
              recommendation={recommendation}
              publicListId={publicListId}
              session={session}
              isWishlisted={visitorWishlistSet.has(`${recommendation.artistName}|${recommendation.albumTitle}`)}
              onSignupClick={(albumData) => onHeartClick({
                artistName: albumData.artist,
                albumTitle: albumData.title,
                albumCoverUrl: recommendation.albumCoverUrl
              })}
              showCollectionTag={showCollectionTag}
              collectionTagText={t('collectionTag')}
              originalUserName={originalUserFirstName}
            />
          ))}
        </div>
      ) : (
        <div className="text-center py-12">
          <div className="bg-white/60 dark:bg-slate-800/60 backdrop-blur-sm rounded-2xl border border-slate-200 dark:border-slate-700 p-8">
            <h3 className="text-lg font-medium mb-2">{t('empty.title')}</h3>
            <p className="text-muted-foreground">
              {t('empty.description', { userName })}
            </p>
          </div>
        </div>
      )}
    </div>
  );
}
