"use client";

import { useState, useTransition } from "react";
import { Music, Play, Pause, Heart } from "lucide-react";
import Image from "next/image";
import { SpotifyEmbed } from "@/components/audio/spotify-embed";
import { useAudioPlayer, useTrackState } from "@/store/audio-player";
import { addToWishlistFromPublic, removeFromWishlistFromPublic } from "@/app/actions/wishlist";
import { LoginModal } from "./login-modal";
import { Session } from "next-auth";
import { useTranslations } from 'next-intl';
import { toast } from "sonner";

interface AffiliateLink {
  vendor: string;
  url: string;
  price: number | null;
  currency: string | null;
  merchantId: string;
  productName: string;
  inStock: boolean;
  asin?: string;
  searchKeyword?: string;
}

interface PublicRecommendation {
  id: number;
  artistName: string;
  albumTitle: string;
  albumCoverUrl?: string | null;
  listenScore: number;
  estimatedPlays?: number | null;
  timeframe: string;
  isOwned: boolean;
  affiliateLinks?: unknown;
  topTrackName?: string | null;
  topTrackId?: string | null;
  topTrackPreviewUrl?: string | null;
  topTrackListenScore?: number | null;
}

interface PublicAlbumCardProps {
  recommendation: PublicRecommendation;
  publicListId: string;
  session: Session | null;
  isWishlisted: boolean;
  onSignupClick?: (albumData: { title: string; artist: string }) => void;
  showCollectionTag?: boolean;
  collectionTagText?: string;
  originalUserName?: string; // Nom de l'utilisateur d'origine
}

export function PublicAlbumCard({
  recommendation,
  publicListId,
  session,
  isWishlisted,
  onSignupClick,
  showCollectionTag = false,
  collectionTagText = "Dans sa collection",
  originalUserName
}: PublicAlbumCardProps) {
  const [isWishlistedLocal, setIsWishlistedLocal] = useState(isWishlisted);
  const [showLoginModal, setShowLoginModal] = useState(false);
  const [isPending, startTransition] = useTransition();
  const { togglePlayPause } = useAudioPlayer();
  const t = useTranslations('recommendations');

  // État du lecteur audio pour ce track spécifique
  const trackState = useTrackState(recommendation.topTrackPreviewUrl || '');

  // Gérer le clic sur le cœur
  const handleWishlistClick = () => {
    if (!session) {
      // Ouvrir la modale de connexion pour les visiteurs non connectés
      setShowLoginModal(true);
      return;
    }

    // Pour les utilisateurs connectés, gérer l'ajout/suppression
    startTransition(async () => {
      try {
        if (isWishlistedLocal) {
          // UI optimiste : mettre à jour l'état local immédiatement
          setIsWishlistedLocal(false);

          // Retirer de la wishlist
          const result = await removeFromWishlistFromPublic(
            recommendation.artistName,
            recommendation.albumTitle
          );

          if (result.success) {
            toast.success(t('wishlist.removed', {
              albumTitle: recommendation.albumTitle,
              artistName: recommendation.artistName
            }), {
              description: t('wishlist.removedDescription'),
              action: {
                label: t('wishlist.undo'),
                onClick: async () => {
                  // Remettre l'album dans la wishlist
                  setIsWishlistedLocal(true);
                  const undoResult = await addToWishlistFromPublic({
                    artistName: recommendation.artistName,
                    albumTitle: recommendation.albumTitle,
                    albumCoverUrl: recommendation.albumCoverUrl,
                    spotifyAlbumId: (recommendation as any).spotifyAlbumId,
                    discogsReleaseId: (recommendation as any).discogsReleaseId,
                    affiliateLinks: recommendation.affiliateLinks,
                    topTrackName: recommendation.topTrackName,
                    topTrackId: recommendation.topTrackId,
                    topTrackPreviewUrl: recommendation.topTrackPreviewUrl,
                    topTrackListenScore: recommendation.topTrackListenScore,
                    originalUserName: originalUserName,
                  });

                  if (undoResult.success) {
                    toast.success(t('wishlist.undoSuccess', { albumTitle: recommendation.albumTitle }));
                  }
                }
              }
            });
          } else {
            // Revenir à l'état précédent en cas d'erreur
            setIsWishlistedLocal(true);
            toast.error(result.error || t('wishlist.removeError'));
          }
        } else {
          // UI optimiste : mettre à jour l'état local immédiatement
          setIsWishlistedLocal(true);

          // Ajouter à la wishlist avec toutes les données enrichies
          const result = await addToWishlistFromPublic({
            artistName: recommendation.artistName,
            albumTitle: recommendation.albumTitle,
            albumCoverUrl: recommendation.albumCoverUrl,
            spotifyAlbumId: (recommendation as any).spotifyAlbumId,
            discogsReleaseId: (recommendation as any).discogsReleaseId,
            affiliateLinks: recommendation.affiliateLinks,
            topTrackName: recommendation.topTrackName,
            topTrackId: recommendation.topTrackId,
            topTrackPreviewUrl: recommendation.topTrackPreviewUrl,
            topTrackListenScore: recommendation.topTrackListenScore,
            originalUserName: originalUserName,
          });

          if (result.success) {
            toast.success(t('wishlist.added', {
              albumTitle: recommendation.albumTitle,
              artistName: recommendation.artistName
            }), {
              description: t('wishlist.addedDescription'),
              action: {
                label: t('wishlist.viewWishlist'),
                onClick: () => window.location.href = "/wishlist"
              }
            });
          } else {
            // Revenir à l'état précédent en cas d'erreur
            setIsWishlistedLocal(false);
            toast.error(result.error || t('wishlist.addError'));
          }
        }
      } catch (error) {
        console.error("Erreur wishlist:", error);
        // Revenir à l'état précédent en cas d'erreur
        setIsWishlistedLocal(isWishlisted);
        toast.error(t('wishlist.addErrorUnexpected'), {
          description: t('wishlist.addErrorDescription')
        });
      }
    });
  };

  // Gérer la lecture audio
  const handlePlayClick = () => {
    if (recommendation.topTrackPreviewUrl && recommendation.topTrackName) {
      togglePlayPause(
        recommendation.topTrackPreviewUrl,
        recommendation.topTrackName,
        recommendation.albumTitle,
        recommendation.artistName
      );
    }
  };

  // Extraire le lien Amazon ou créer un lien de recherche Phase 1
  const affiliateLinks = Array.isArray(recommendation.affiliateLinks)
    ? recommendation.affiliateLinks as AffiliateLink[]
    : [];

  const amazonLink = affiliateLinks.find(link =>
    link.vendor === 'Amazon'
  );

  // Créer un lien de recherche Amazon Phase 1 si aucun lien n'existe (sans "vinyl" pour élargir les résultats)
  const searchQuery = encodeURIComponent(`${recommendation.artistName} ${recommendation.albumTitle}`);
  const amazonSearchUrl = `https://www.amazon.fr/s?k=${searchQuery}&tag=stream2spin-21`;

  const finalAmazonLink = amazonLink || {
    vendor: 'Amazon',
    url: amazonSearchUrl,
    price: null,
    currency: null,
    merchantId: 'amazon-fr',
    productName: `${recommendation.albumTitle} - ${recommendation.artistName}`,
    inStock: true,
    searchKeyword: `${recommendation.artistName} ${recommendation.albumTitle}`
  };

  const hasPreview = recommendation.topTrackPreviewUrl && recommendation.topTrackName;
  const hasSpotifyEmbed = recommendation.topTrackId && recommendation.topTrackName;

  return (
    <>
      <div className="bg-white dark:bg-slate-800 rounded-xl shadow-sm border border-slate-200 dark:border-slate-700 overflow-hidden hover:shadow-md transition-all duration-200">
        {/* Image de l'album */}
        <div className="aspect-square relative">
          {recommendation.albumCoverUrl ? (
            <Image
              src={recommendation.albumCoverUrl}
              alt={`${recommendation.albumTitle} by ${recommendation.artistName}`}
              fill
              className="object-cover"
              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 25vw"
            />
          ) : (
            <div className="w-full h-full bg-slate-200 dark:bg-slate-700 flex items-center justify-center">
              <Music className="w-12 h-12 text-slate-400" />
            </div>
          )}

          {/* Badge "Dans sa collection" si possédé */}
          {showCollectionTag && recommendation.isOwned && (
            <div className="absolute top-2 right-2 bg-green-500 text-white text-xs px-2 py-1 rounded-full">
              {collectionTagText}
            </div>
          )}

          {/* Bouton cœur pour la wishlist */}
          <button
            onClick={handleWishlistClick}
            disabled={isPending}
            className={`absolute top-2 left-2 w-8 h-8 rounded-full flex items-center justify-center transition-all duration-200 ${
              isWishlistedLocal
                ? 'bg-red-500 text-white shadow-lg'
                : 'bg-white/80 text-gray-600 hover:bg-white hover:text-red-500'
            } ${isPending ? 'opacity-50 cursor-not-allowed' : 'hover:scale-110'}`}
            title={isWishlistedLocal ? "Retirer de mes envies" : "Ajouter à mes envies"}
          >
            <Heart
              className={`w-4 h-4 transition-all duration-200 ${
                isWishlistedLocal ? 'fill-current' : ''
              }`}
            />
          </button>
        </div>

        {/* Informations de l'album */}
        <div className="p-4">
          <h3 className="font-semibold text-slate-900 dark:text-slate-100 line-clamp-2 mb-1">
            {recommendation.albumTitle}
          </h3>
          <p className="text-slate-600 dark:text-slate-400 text-sm line-clamp-1 mb-2">
            {recommendation.artistName}
          </p>

          {/* Section du titre phare avec Spotify Embed */}
          {hasSpotifyEmbed && (
            <div className="mb-3">
              <div className="mb-2">
                <p className="text-xs text-slate-600 dark:text-slate-400">
                  {originalUserName
                    ? `${originalUserName}'s top track:`
                    : 'Top track:'
                  }
                </p>
              </div>
              <SpotifyEmbed
                trackId={recommendation.topTrackId!}
                trackName={recommendation.topTrackName!}
                artistName={recommendation.artistName}
                albumName={recommendation.albumTitle}
                compact={true}
              />
            </div>
          )}

          {/* Fallback: Section du titre phare avec lecteur audio personnalisé */}
          {!hasSpotifyEmbed && hasPreview && (
            <div className="mb-3">
              <div className="mb-2">
                <p className="text-xs text-slate-600 dark:text-slate-400">
                  {originalUserName
                    ? `${originalUserName}'s top track:`
                    : 'Top track:'
                  }
                </p>
              </div>
              <div className="flex items-center space-x-2 p-2 bg-slate-50 dark:bg-slate-700/50 rounded-lg">
                <button
                  onClick={handlePlayClick}
                  className="flex-shrink-0 w-8 h-8 bg-primary hover:bg-primary/90 text-primary-foreground rounded-full flex items-center justify-center transition-colors"
                  title={trackState.isPlaying ? "Mettre en pause" : "Écouter l'extrait"}
                >
                  {trackState.isLoading ? (
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                  ) : trackState.isPlaying ? (
                    <Pause className="w-4 h-4" />
                  ) : (
                    <Play className="w-4 h-4 ml-0.5" />
                  )}
                </button>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-slate-900 dark:text-slate-100 truncate">
                    {recommendation.topTrackName}
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* Boutons d'action directs - Phase 1 */}
          <div className="flex justify-end">
            <div className="flex flex-col gap-2 items-end">
              {/* Bouton Amazon Phase 1 - toujours affiché */}
              <button
                className="text-xs bg-primary hover:bg-primary/90 text-primary-foreground px-3 py-1.5 rounded-full transition-colors flex items-center gap-1.5"
                onClick={(e) => {
                  e.stopPropagation();
                  window.open(finalAmazonLink.url, '_blank', 'noopener,noreferrer');
                }}
                title={finalAmazonLink.price ? `Acheter sur Amazon` : `Rechercher "${finalAmazonLink.searchKeyword || `${recommendation.artistName} ${recommendation.albumTitle}`}" sur Amazon`}
              >
                <Image
                  src="/vendors/Amazon_icon_white.svg"
                  alt="Amazon"
                  width={14}
                  height={14}
                  className="w-3.5 h-3.5"
                />
                Acheter
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Modale de connexion pour visiteurs non connectés */}
      <LoginModal
        open={showLoginModal}
        onOpenChange={setShowLoginModal}
        albumData={{
          artistName: recommendation.artistName,
          albumTitle: recommendation.albumTitle,
          albumCoverUrl: recommendation.albumCoverUrl,
        }}
        publicListId={publicListId}
        loginReason="wishlist"
      />
    </>
  );
}
