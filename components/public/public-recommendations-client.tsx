"use client";

import { useState } from "react";
import { <PERSON>, Sparkles, ExternalLink, Heart } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { PublicAlbumCard } from "./public-album-card";
import { TimeframeFilter } from "@/components/recommendations/timeframe-filter";
import { Session } from "next-auth";
import { useTranslations } from 'next-intl';

interface AffiliateLink {
  vendor: string;
  url: string;
  price: number;
  currency: string;
  merchantId: string;
  productName: string;
  inStock: boolean;
  asin?: string;
}

interface PublicRecommendation {
  id: number;
  artistName: string;
  albumTitle: string;
  albumCoverUrl?: string | null;
  listenScore: number;
  estimatedPlays?: number | null;
  timeframe: string;
  isOwned: boolean;
  affiliateLinks?: unknown;
  topTrackName?: string | null;
  topTrackId?: string | null;
  topTrackPreviewUrl?: string | null;
  topTrackListenScore?: number | null;
}

interface PublicData {
  user: {
    name: string | null;
    image: string | null;
  };
  recommendations: PublicRecommendation[];
  totalRecommendations: number;
}

interface PublicRecommendationsClientProps {
  publicData: PublicData;
  publicListId: string;
  initialTimeframe: string;
  session: Session | null;
  visitorWishlistSet: Set<string>;
}

export function PublicRecommendationsClient({
  publicData,
  publicListId,
  initialTimeframe,
  session,
  visitorWishlistSet
}: PublicRecommendationsClientProps) {
  const [currentTimeframe, setCurrentTimeframe] = useState(initialTimeframe);
  const [recommendations, setRecommendations] = useState(publicData.recommendations);
  const [isLoading, setIsLoading] = useState(false);
  const t = useTranslations('public.notFound');

  const userName = publicData.user.name || "Un mélomane";
  const userImage = publicData.user.image;

  // Fonction pour changer de timeframe
  const handleTimeframeChange = async (newTimeframe: string) => {
    if (newTimeframe === currentTimeframe) return;

    setIsLoading(true);
    try {
      const response = await fetch(`/api/u/${publicListId}?timeframe=${newTimeframe}`);
      if (response.ok) {
        const data = await response.json();
        setRecommendations(data.recommendations);
        setCurrentTimeframe(newTimeframe);

        // Mettre à jour l'URL sans recharger la page
        const url = new URL(window.location.href);
        url.searchParams.set('timeframe', newTimeframe);
        window.history.pushState({}, '', url.toString());
      }
    } catch (error) {
      console.error("Erreur lors du changement de timeframe:", error);
    } finally {
      setIsLoading(false);
    }
  };

  // Fonction pour tracker les clics d'inscription
  const trackSignupClick = async (source: string, albumData?: { title: string; artist: string }) => {
    try {
      await fetch('/api/analytics/track', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          publicListId,
          eventType: 'signup_click',
          eventData: {
            source,
            timeframe: currentTimeframe,
            albumTitle: albumData?.title,
            artistName: albumData?.artist,
          },
        }),
      });
    } catch (error) {
      console.error('Erreur tracking signup click:', error);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800">
      {/* Header avec informations utilisateur */}
      <div className="bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm border-b border-slate-200 dark:border-slate-700">
        <div className="container py-8">
          <div className="flex flex-col md:flex-row items-center gap-6">
            {/* Photo de profil */}
            <div className="relative">
              {userImage ? (
                <Image
                  src={userImage}
                  alt={userName}
                  width={80}
                  height={80}
                  className="rounded-full border-4 border-white dark:border-slate-700 shadow-lg"
                />
              ) : (
                <div className="w-20 h-20 rounded-full bg-gradient-to-br from-primary to-primary/80 flex items-center justify-center border-4 border-white dark:border-slate-700 shadow-lg">
                  <Music className="w-8 h-8 text-white" />
                </div>
              )}
              <div className="absolute -bottom-1 -right-1 bg-green-500 w-6 h-6 rounded-full border-2 border-white dark:border-slate-700 flex items-center justify-center">
                <Sparkles className="w-3 h-3 text-white" />
              </div>
            </div>

            {/* Informations utilisateur */}
            <div className="text-center md:text-left flex-1">
              <h1 className="text-3xl font-bold text-slate-900 dark:text-slate-100 mb-2">
                Découvertes musicales de {userName}
              </h1>
              <p className="text-slate-600 dark:text-slate-400 mb-4">
                {recommendations.length} recommandations d'albums personnalisées basées sur les goûts musicaux
              </p>
              <Badge variant="secondary" className="mb-4">
                <Heart className="w-4 h-4 mr-1" />
                Liste publique
              </Badge>
            </div>

            {/* Call-to-action */}
            <div className="flex flex-col gap-3">
              <Button
                className="bg-primary hover:bg-primary/90"
                onClick={() => {
                  trackSignupClick('header_cta');
                  window.location.href = `/login?from=public&publicListId=${publicListId}`;
                }}
              >
                <Sparkles className="w-4 h-4 mr-2" />
                {t('createList')}
              </Button>
              <Button variant="outline" asChild>
                <Link href="https://www.stream2spin.com/" className="text-sm">
                  <ExternalLink className="w-4 h-4 mr-2" />
                  {t('backHome')}
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Contenu principal */}
      <div className="container py-8">
        {/* Filtre de timeframe */}
        <div className="mb-8 bg-white/60 dark:bg-slate-800/60 backdrop-blur-sm rounded-lg border border-slate-200 dark:border-slate-700 p-6">
          <h3 className="text-lg font-semibold text-slate-900 dark:text-slate-100 mb-4">
            Période d'écoute
          </h3>
          <TimeframeFilter 
            currentTimeframe={currentTimeframe}
            onTimeframeChange={handleTimeframeChange}
            disabled={isLoading}
          />
        </div>

        {/* Grille des recommandations */}
        {isLoading ? (
          <div className="flex items-center justify-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        ) : recommendations.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {recommendations.map((recommendation) => (
              <PublicAlbumCard
                key={recommendation.id}
                recommendation={recommendation}
                publicListId={publicListId}
                session={session}
                isWishlisted={visitorWishlistSet.has(`${recommendation.artistName}|${recommendation.albumTitle}`)}
                onSignupClick={(albumData) => trackSignupClick('album_card', albumData)}
              />
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <Music className="w-16 h-16 text-slate-400 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-slate-600 dark:text-slate-400 mb-2">
              Aucune recommandation pour cette période
            </h3>
            <p className="text-slate-500 dark:text-slate-500">
              Essayez une autre période d'écoute
            </p>
          </div>
        )}

        {/* Footer avec call-to-action */}
        <div className="mt-16 text-center bg-white/60 dark:bg-slate-800/60 backdrop-blur-sm rounded-lg border border-slate-200 dark:border-slate-700 p-8">
          <h2 className="text-2xl font-bold text-slate-900 dark:text-slate-100 mb-4">
            Vous aimez ces découvertes ?
          </h2>
          <p className="text-slate-600 dark:text-slate-400 mb-6 max-w-2xl mx-auto">
            Créez votre propre liste de recommandations personnalisées en connectant votre compte Spotify. 
            Découvrez de nouveaux albums basés sur vos goûts musicaux uniques.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button
              size="lg"
              className="bg-primary hover:bg-primary/90"
              onClick={() => {
                trackSignupClick('footer_cta');
                window.location.href = `/login?from=public&publicListId=${publicListId}`;
              }}
            >
              <Sparkles className="w-5 h-5 mr-2" />
              Commencer gratuitement
            </Button>
            <Button variant="outline" size="lg" asChild>
              <Link href="https://www.stream2spin.com/">
                En savoir plus sur Stream2Spin
              </Link>
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
