"use client";

import { useState } from "react";
import { Session } from "next-auth";
import { Search } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { PublicCollectionCard } from "./public-collection-card";
import { useTranslations } from 'next-intl';

interface CollectionItem {
  id: number;
  discogsReleaseId: number;
  artistName: string;
  albumTitle: string;
  albumCoverUrl?: string | null;
  year?: number | null;
  format?: string | null;
  syncedAt: Date;
}

interface AlbumData {
  artistName: string;
  albumTitle: string;
  albumCoverUrl?: string | null;
  spotifyAlbumId?: string | null;
  discogsReleaseId?: number | null;
}

interface PublicCollectionTabProps {
  collection: CollectionItem[];
  session: Session | null;
  visitorWishlistSet: Set<string>;
  onHeartClick: (albumData: AlbumData) => void;
  userName: string;
}

export function PublicCollectionTab({
  collection,
  session,
  visitorWishlistSet,
  onHeartClick,
  userName
}: PublicCollectionTabProps) {
  const [searchQuery, setSearchQuery] = useState("");
  const [sortBy, setSortBy] = useState("artistName_asc");
  const t = useTranslations('public.collection');
  const tCommon = useTranslations('common');
  const tCollection = useTranslations('collection.filters');

  // Options de tri disponibles
  const sortOptions = [
    { value: "artistName_asc", label: tCollection('sortOptions.artistName_asc') },
    { value: "artistName_desc", label: tCollection('sortOptions.artistName_desc') },
    { value: "albumTitle_asc", label: tCollection('sortOptions.albumTitle_asc') },
    { value: "albumTitle_desc", label: tCollection('sortOptions.albumTitle_desc') },
    { value: "year_desc", label: tCollection('sortOptions.year_desc') },
    { value: "year_asc", label: tCollection('sortOptions.year_asc') },
    { value: "syncedAt_desc", label: tCollection('sortOptions.syncedAt_desc') },
    { value: "syncedAt_asc", label: tCollection('sortOptions.syncedAt_asc') },
  ];

  // Filtrer et trier la collection
  let filteredCollection = collection.filter(item =>
    item.artistName.toLowerCase().includes(searchQuery.toLowerCase()) ||
    item.albumTitle.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // Appliquer le tri
  const [sortColumn, sortOrder] = sortBy.split('_');
  filteredCollection = filteredCollection.sort((a, b) => {
    let aValue: any, bValue: any;

    switch (sortColumn) {
      case 'artistName':
        aValue = a.artistName.toLowerCase();
        bValue = b.artistName.toLowerCase();
        break;
      case 'albumTitle':
        aValue = a.albumTitle.toLowerCase();
        bValue = b.albumTitle.toLowerCase();
        break;
      case 'year':
        aValue = a.year || 0;
        bValue = b.year || 0;
        break;
      case 'syncedAt':
        aValue = new Date(a.syncedAt).getTime();
        bValue = new Date(b.syncedAt).getTime();
        break;
      default:
        return 0;
    }

    if (aValue < bValue) return sortOrder === 'asc' ? -1 : 1;
    if (aValue > bValue) return sortOrder === 'asc' ? 1 : -1;
    return 0;
  });

  // Trouver le top track (premier album de la collection)
  const topTrack = collection[0];

  return (
    <div className="space-y-6">
      {/* En-tête */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h2 className="text-2xl font-bold">{t('title')}</h2>
        </div>
      </div>

      {/* Contrôles de recherche et tri */}
      {collection.length > 0 && (
        <div className="flex flex-col sm:flex-row gap-4 mb-6">
          {/* Champ de recherche */}
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
            <Input
              type="text"
              placeholder={t('search')}
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>

          {/* Sélecteur de tri */}
          <div className="sm:w-64">
            <Select value={sortBy} onValueChange={setSortBy}>
              <SelectTrigger>
                <SelectValue placeholder={tCommon('sortBy')} />
              </SelectTrigger>
              <SelectContent>
                {sortOptions.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Compteur d'albums */}
          <div className="flex items-center text-sm text-muted-foreground whitespace-nowrap">
            {tCollection('albumCount', { count: filteredCollection.length })}
          </div>
        </div>
      )}

      {/* Grille de la collection */}
      {filteredCollection.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {filteredCollection.map((item) => (
            <PublicCollectionCard
              key={item.id}
              item={item}
              session={session}
              isWishlisted={visitorWishlistSet.has(`${item.artistName}|${item.albumTitle}`)}
              onHeartClick={onHeartClick}
            />
          ))}
        </div>
      ) : collection.length === 0 ? (
        <div className="text-center py-12">
          <div className="bg-white/60 dark:bg-slate-800/60 backdrop-blur-sm rounded-2xl border border-slate-200 dark:border-slate-700 p-8">
            <h3 className="text-lg font-medium mb-2">{t('empty.title')}</h3>
            <p className="text-muted-foreground">
              {t('empty.description', { userName })}
            </p>
          </div>
        </div>
      ) : (
        <div className="text-center py-12">
          <div className="bg-white/60 dark:bg-slate-800/60 backdrop-blur-sm rounded-2xl border border-slate-200 dark:border-slate-700 p-8">
            <h3 className="text-lg font-medium mb-2">{t('noResults.title')}</h3>
            <p className="text-muted-foreground mb-4">
              {t('noResults.description', { query: searchQuery })}
            </p>
            <Button
              variant="outline"
              onClick={() => setSearchQuery("")}
              className="bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm"
            >
              {t('noResults.clear')}
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}
