"use client";

import Link from "next/link";
import Image from "next/image";
import { Button } from "@/components/ui/button";
import { ExternalLink } from "lucide-react";
import { useTranslations } from 'next-intl';

export function PublicHeader() {
  const t = useTranslations('public.header');

  return (
    <header className="border-b border-slate-200 dark:border-slate-700 bg-white/80 dark:bg-slate-900/80 backdrop-blur-sm sticky top-0 z-50">
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between h-16">
          {/* Logo à gauche */}
          <Link href="/" className="flex items-center space-x-2 h-full">
            <Image
              src="/stream2spin-logo.svg"
              alt="Stream2Spin"
              width={140}
              height={32}
              className="h-10 w-auto"
            />
          </Link>

          {/* Navigation à droite */}
          <div className="flex items-center space-x-4">
            {/* Découvrir Stream2Spin */}
            <Button variant="ghost" asChild>
              <Link href="https://www.stream2spin.com/" className="flex items-center space-x-2">
                <ExternalLink className="w-4 h-4" />
                <span>{t('discover')}</span>
              </Link>
            </Button>

            {/* Se connecter */}
            <Button asChild>
              <Link href="/login" className="flex items-center space-x-2">
                <span>{t('login')}</span>
              </Link>
            </Button>
          </div>
        </div>
      </div>
    </header>
  );
}
