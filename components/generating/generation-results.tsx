"use client";

import { useState, useEffect } from "react";
import { AlbumCard } from "@/components/recommendations/album-card";
import { But<PERSON> } from "@/components/ui/button";
import { ArrowDown, Sparkles } from "lucide-react";

interface GenerationResultsProps {
  recommendations: any[];
  isComplete: boolean;
}

export function GenerationResults({ recommendations, isComplete }: GenerationResultsProps) {
  const [visibleRecommendations, setVisibleRecommendations] = useState<any[]>([]);

  // Animation d'apparition progressive des recommandations
  useEffect(() => {
    if (recommendations.length > visibleRecommendations.length) {
      const timer = setTimeout(() => {
        const nextRecommendation = recommendations[visibleRecommendations.length];
        setVisibleRecommendations(prev => [...prev, nextRecommendation]);
      }, 800); // Délai entre chaque apparition
      
      return () => clearTimeout(timer);
    }
  }, [recommendations, visibleRecommendations]);

  const scrollToResults = () => {
    const resultsElement = document.getElementById('generation-results');
    if (resultsElement) {
      resultsElement.scrollIntoView({ behavior: 'smooth' });
    }
  };

  if (visibleRecommendations.length === 0) {
    return null;
  }

  return (
    <div id="generation-results" className="space-y-8">
      {/* En-tête des résultats */}
      <div className="text-center">
        <div className="flex items-center justify-center space-x-2 mb-4">
          <Sparkles className="w-6 h-6 text-yellow-500" />
          <h2 className="text-2xl font-bold text-slate-900 dark:text-slate-100">
            Vos recommandations arrivent !
          </h2>
        </div>
        
        <p className="text-slate-600 dark:text-slate-400">
          {isComplete 
            ? `${visibleRecommendations.length} recommandations personnalisées prêtes`
            : `${visibleRecommendations.length} sur ${recommendations.length} recommandations chargées...`
          }
        </p>
      </div>

      {/* Grille des recommandations */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {visibleRecommendations.map((recommendation, index) => (
          <div
            key={recommendation.id}
            className="animate-in slide-in-from-bottom duration-700"
            style={{ animationDelay: `${index * 100}ms` }}
          >
            <AlbumCard
              recommendation={recommendation}
            />
          </div>
        ))}
      </div>

      {/* Indicateur de chargement pour les recommandations restantes */}
      {!isComplete && visibleRecommendations.length < recommendations.length && (
        <div className="text-center py-8">
          <div className="inline-flex items-center space-x-2 text-slate-600 dark:text-slate-400">
            <div className="w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
            <span>Chargement des recommandations suivantes...</span>
          </div>
        </div>
      )}

      {/* Bouton d'action final */}
      {isComplete && (
        <div className="text-center py-8">
          <div className="bg-gradient-to-r from-green-50 to-blue-50 dark:from-green-900/20 dark:to-blue-900/20 
                         border border-green-200 dark:border-green-800 rounded-lg p-6 max-w-md mx-auto">
            <div className="flex items-center justify-center space-x-2 mb-4">
              <Sparkles className="w-5 h-5 text-green-500" />
              <h3 className="text-lg font-semibold text-slate-900 dark:text-slate-100">
                Génération terminée !
              </h3>
            </div>
            
            <p className="text-slate-600 dark:text-slate-400 mb-4">
              Toutes vos recommandations personnalisées sont maintenant disponibles.
            </p>
            
            <Button 
              onClick={scrollToResults}
              className="bg-primary hover:bg-primary/90"
            >
              <ArrowDown className="w-4 h-4 mr-2" />
              Explorer mes recommandations
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}
