"use client";

import { useState, useEffect, useRef, useCallback } from "react";
import Image from "next/image";

interface AlbumBackgroundFlowProps {
  discoveredAlbums: Array<{
    artist: string;
    album?: string;
    imageUrl: string;
  }>;
}

interface GridCell {
  album: {
    artist: string;
    album?: string;
    imageUrl: string;
  } | null;
  isPopping: boolean;
  isFlipping: boolean;
  flipKey: number;
}

export function AlbumBackgroundFlow({ discoveredAlbums }: AlbumBackgroundFlowProps) {
  // Grille responsive selon les spécifications Epic 11
  const [gridSize, setGridSize] = useState({ rows: 6, cols: 8 });
  const [grid, setGrid] = useState<GridCell[]>([]);
  const [processedAlbums, setProcessedAlbums] = useState<Set<string>>(new Set());
  const [isHydrated, setIsHydrated] = useState(false);

  // Refs pour éviter les conflits lors du redimensionnement
  const timeoutsRef = useRef<Set<NodeJS.Timeout>>(new Set());
  const isResizingRef = useRef(false);
  const resizeTimeoutRef = useRef<NodeJS.Timeout | undefined>(undefined);

  // Ensure hydration is complete before using Math.random()
  useEffect(() => {
    setIsHydrated(true);
    // S'assurer que le flag de redimensionnement est bien à false au démarrage
    isResizingRef.current = false;
    if (process.env.NODE_ENV === 'development') {
    console.log("✅ Composant AlbumBackgroundFlow hydraté et prêt");
    }
  }, []);

  // Fonction pour nettoyer les timeouts en cours
  const clearAllTimeouts = useCallback(() => {
    timeoutsRef.current.forEach(timeout => clearTimeout(timeout));
    timeoutsRef.current.clear();
  }, []);

  // Initialiser la grille responsive selon la taille d'écran
  useEffect(() => {
    const updateGridSize = () => {
      // Éviter les mises à jour multiples pendant le redimensionnement
      if (resizeTimeoutRef.current) {
        clearTimeout(resizeTimeoutRef.current);
      }

      resizeTimeoutRef.current = setTimeout(() => {
        const width = window.innerWidth;
        const height = window.innerHeight;

        // Calcul responsive pour maintenir des pochettes carrées
        const cols = Math.floor(width / 120); // 120px par pochette minimum
        const rows = Math.floor(height / 120);

        const newGridSize = { rows: Math.max(rows, 4), cols: Math.max(cols, 6) };

        // Ne mettre à jour que si la taille a vraiment changé
        setGridSize(prevSize => {
          if (prevSize.rows !== newGridSize.rows || prevSize.cols !== newGridSize.cols) {
            if (process.env.NODE_ENV === 'development') {
            console.log(`🔄 Redimensionnement grille: ${prevSize.rows}x${prevSize.cols} → ${newGridSize.rows}x${newGridSize.cols}`);
            }

            // Marquer comme en cours de redimensionnement
            isResizingRef.current = true;
            // Nettoyer les timeouts en cours pour éviter les conflits
            clearAllTimeouts();

            // Réinitialiser le flag après un délai plus long pour être sûr
            setTimeout(() => {
              isResizingRef.current = false;
              if (process.env.NODE_ENV === 'development') {
              console.log("✅ Flag redimensionnement réinitialisé - albums peuvent être placés");
              }
            }, 300);

            return newGridSize;
          }
          return prevSize;
        });
      }, 150); // Debounce de 150ms
    };

    updateGridSize();
    window.addEventListener('resize', updateGridSize);

    return () => {
      window.removeEventListener('resize', updateGridSize);
      if (resizeTimeoutRef.current) {
        clearTimeout(resizeTimeoutRef.current);
      }
      clearAllTimeouts();
    };
  }, [clearAllTimeouts]);

  // Initialiser la grille vide
  useEffect(() => {
    const totalCells = gridSize.rows * gridSize.cols;
    const newGrid = Array(totalCells).fill(null).map(() => ({
      album: null,
      isPopping: false,
      isFlipping: false,
      flipKey: 0
    }));

    setGrid(newGrid);

    // Réinitialiser les albums traités lors du changement de grille pour éviter les conflits
    if (isResizingRef.current) {
      if (process.env.NODE_ENV === 'development') {
      console.log("🔄 Grille redimensionnée, conservation des albums traités");
      }
    }
  }, [gridSize]);

  // Traiter les nouveaux albums découverts avec animations pop/flip
  useEffect(() => {
    if (!isHydrated) return;

    const newAlbums = discoveredAlbums.filter(album =>
      !processedAlbums.has(album.imageUrl)
    );

    if (process.env.NODE_ENV === 'development') {
    console.log(`🎵 Albums découverts: ${discoveredAlbums.length}, Nouveaux à traiter: ${newAlbums.length}, Déjà traités: ${processedAlbums.size}`);
    }

    if (newAlbums.length === 0 || grid.length === 0) return;

    // Traiter chaque nouvel album avec un délai
    newAlbums.forEach((album, index) => {
      const delay = index * 200; // 0.2s entre chaque placement

      const timeout = setTimeout(() => {
        // Vérifier si on n'est pas en cours de redimensionnement (mais ne pas bloquer complètement)
        if (isResizingRef.current) {
          if (process.env.NODE_ENV === 'development') {
          console.log("⏸️ Placement d'album reporté - redimensionnement en cours");
          }
          // Reporter le placement au lieu de l'annuler
          const retryTimeout = setTimeout(() => {
            if (!isResizingRef.current) {
              if (process.env.NODE_ENV === 'development') {
              console.log(`🔄 Retry placement album: "${album.artist} - ${album.album}"`);
              }
              placeAlbumInGrid(album);
            }
          }, 500);
          timeoutsRef.current.add(retryTimeout);
          return;
        }

        if (process.env.NODE_ENV === 'development') {
        console.log(`🚀 Placement album: "${album.artist} - ${album.album}"`);
        }
        placeAlbumInGrid(album);
      }, delay);

      timeoutsRef.current.add(timeout);
    });
  }, [isHydrated, discoveredAlbums, grid.length]);

  // Fonction pour placer un album dans la grille
  const placeAlbumInGrid = (album: any) => {
    setGrid(currentGrid => {
      // Vérification de base de la validité de la grille
      if (currentGrid.length === 0) {
        if (process.env.NODE_ENV === 'development') {
        console.log("⚠️ Grille vide, placement reporté");
        }
        return currentGrid;
      }

      const emptyPositions = currentGrid
        .map((cell, index) => ({ cell, index }))
        .filter(({ cell }) => cell.album === null)
        .map(({ index }) => index);

      let position: number;
      let isEmptyPosition: boolean;

      if (emptyPositions.length > 0) {
        // Position vide disponible - effet "pop"
        position = emptyPositions[Math.floor(Math.random() * emptyPositions.length)];
        isEmptyPosition = true;
      } else {
        // Grille pleine - remplacer une position existante avec effet "flip"
        position = Math.floor(Math.random() * currentGrid.length);
        isEmptyPosition = false;
      }

      const newGrid = [...currentGrid];

      if (isEmptyPosition) {
        // Animation "pop" pour nouvelle pochette
        newGrid[position] = {
          album,
          isPopping: true,
          isFlipping: false,
          flipKey: newGrid[position].flipKey
        };

        // Terminer l'animation pop (moins restrictif)
        const popTimeout = setTimeout(() => {
          setGrid(current => {
            if (current.length === 0) return current;
            const updated = [...current];
            if (updated[position]) {
              updated[position] = { ...updated[position], isPopping: false };
            }
            return updated;
          });
        }, 600);
        timeoutsRef.current.add(popTimeout);
      } else {
        // Animation "flip" pour remplacement
        newGrid[position] = {
          album,
          isPopping: false,
          isFlipping: true,
          flipKey: newGrid[position].flipKey + 1
        };

        // Terminer l'animation flip (moins restrictif)
        const flipTimeout = setTimeout(() => {
          setGrid(current => {
            if (current.length === 0) return current;
            const updated = [...current];
            if (updated[position]) {
              updated[position] = { ...updated[position], isFlipping: false };
            }
            return updated;
          });
        }, 800);
        timeoutsRef.current.add(flipTimeout);
      }

      return newGrid;
    });

    // Marquer comme traité
    setProcessedAlbums(prev => new Set([...prev, album.imageUrl]));
  };

  // Nettoyage lors du démontage du composant
  useEffect(() => {
    return () => {
      clearAllTimeouts();
    };
  }, [clearAllTimeouts]);

  return (
    <div className="fixed inset-0 overflow-hidden pointer-events-none z-0">
      {/* Grille responsive d'arrière-plan - Nouvelles spécifications Epic 11 */}
      <div
        className="w-full h-full grid gap-2 p-4 album-flow-container"
        style={{
          gridTemplateColumns: `repeat(${gridSize.cols}, 1fr)`,
          gridTemplateRows: `repeat(${gridSize.rows}, 1fr)`,
        }}
      >
        {grid.map((cell, index) => (
          <div
            key={`cell-${index}-${cell.flipKey}`}
            className="relative w-full h-full aspect-square album-flow-item"
          >
            {cell.album && (
              <div
                className={`
                  relative w-full h-full rounded-lg overflow-hidden shadow-lg
                  transition-all duration-500 ease-in-out
                  ${cell.isPopping ? 'animate-album-pop' : ''}
                  ${cell.isFlipping ? 'animate-album-flip' : ''}
                `}
                style={{
                  opacity: 0.7, // Opacité pour arrière-plan selon specs
                }}
              >
                <Image
                  src={cell.album.imageUrl}
                  alt={`${cell.album.artist} - ${cell.album.album || 'Album'}`}
                  fill
                  className="object-cover"
                  sizes="(max-width: 768px) 120px, 150px"
                  priority={false}
                  loading="lazy"
                  onError={(e) => {
                    // Gestion d'erreur silencieuse pour éviter les crashes
                    if (process.env.NODE_ENV === 'development') {
                    console.warn(`❌ Erreur de chargement d'image: ${cell.album?.imageUrl}`);
                    }
                  }}
                />
              </div>
            )}
          </div>
        ))}
      </div>

      {/* Overlay subtil pour créer de la profondeur */}
      <div className="absolute inset-0 bg-gradient-to-br from-white/10 via-transparent to-white/10 pointer-events-none" />
    </div>
  );
}
