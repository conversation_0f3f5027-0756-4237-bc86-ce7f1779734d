"use client";

import { Music, BarChart3, Library, ShoppingCart, Sparkles, Check, Loader2 } from "lucide-react";
import { GenerationStep } from "./generating-client";

interface GenerationTimelineProps {
  steps: GenerationStep[];
  isComplete: boolean;
}

const iconMap = {
  Music,
  BarChart3,
  Library,
  ShoppingCart,
  Sparkles
};

export function GenerationTimeline({
  steps,
  isComplete
}: GenerationTimelineProps) {
  const getStepIcon = (step: GenerationStep) => {
    const IconComponent = iconMap[step.icon as keyof typeof iconMap] || Music;

    if (step.status === 'completed') {
      return <Check className="w-3 h-3 sm:w-4 sm:h-4 text-white" />;
    } else if (step.status === 'in_progress') {
      return <Loader2 className="w-3 h-3 sm:w-4 sm:h-4 animate-spin" style={{ color: '#6236FF' }} />;
    } else if (step.status === 'error') {
      return <IconComponent className="w-3 h-3 sm:w-4 sm:h-4 text-red-600" />;
    } else {
      return <IconComponent className="w-3 h-3 sm:w-4 sm:h-4 text-slate-400" />;
    }
  };

  const getStepStatus = (step: GenerationStep) => {
    switch (step.status) {
      case 'completed':
        return 'bg-green-100 border-green-300 dark:bg-green-900/20 dark:border-green-700 scale-110';
      case 'in_progress':
        return 'bg-white border-[#6236FF] dark:bg-slate-800 dark:border-[#6236FF] scale-110 shadow-lg';
      case 'error':
        return 'bg-red-100 border-red-300 dark:bg-red-900/20 dark:border-red-700';
      default:
        return 'bg-slate-100 border-slate-300 dark:bg-slate-800 dark:border-slate-600';
    }
  };



  return (
    <div className="mb-8">
      {/* Timeline sans barre de progression - Responsive design */}
      <div className="flex justify-center items-start gap-3 sm:gap-5 md:gap-7 lg:gap-9 px-4">
        {steps.map((step, index) => (
          <div key={step.id} className="flex flex-col items-center flex-1 max-w-[70px] sm:max-w-[85px] md:max-w-[100px]">
            {/* Conteneur fixe pour l'icône pour assurer l'alignement */}
            <div className="h-8 sm:h-10 md:h-12 flex items-center justify-center mb-2 sm:mb-3">
              <div className={`
                w-8 h-8 sm:w-10 sm:h-10 md:w-12 md:h-12 rounded-full border-2 flex items-center justify-center
                transition-all duration-500 ease-in-out
                ${step.status === 'completed' ? 'bg-green-500 border-green-500' :
                  step.status === 'in_progress' ? 'border-2 bg-white animate-pulse' :
                  step.status === 'error' ? 'bg-red-500 border-red-500' :
                  'border-slate-300 bg-slate-100'}
              `}
              style={step.status === 'in_progress' ? { borderColor: '#6236FF' } : {}}>
                {getStepIcon(step)}
              </div>
            </div>

            {/* Label de l'étape avec hauteur fixe pour éviter les décalages */}
            <div className="min-h-[32px] sm:min-h-[36px] flex items-center justify-center">
              <span className={`text-[10px] sm:text-xs font-medium text-center leading-tight ${
                step.status === 'completed' ? 'text-green-700' :
                step.status === 'in_progress' ? 'font-semibold' :
                step.status === 'error' ? 'text-red-700' :
                'text-slate-600'
              }`}
              style={step.status === 'in_progress' ? { color: '#6236FF' } : {}}>
                {step.name}
              </span>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}


