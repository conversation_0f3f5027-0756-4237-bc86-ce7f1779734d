"use client";

import { useState, useEffect, useRef, useTransition } from "react";
import { useRouter } from "next/navigation";
import { GenerationTimeline } from "./generation-timeline";
import { GenerationInsights } from "./generation-insights";
import { GenerationResults } from "./generation-results";
import { AlbumBackgroundFlow } from "./album-background-flow";
import { DynamicCentralText } from "./dynamic-central-text";
import { Logo } from "@/components/ui/logo";
import { useTranslations } from 'next-intl';
import { revalidateAndRedirectToRecommendations } from "@/actions/recommendations";

export interface GenerationEvent {
  type: 'step_update' | 'counter_update' | 'insight' | 'album_art_discovered' | 'recommendation_ready' | 'complete' | 'error' | 'status_update';
  step?: string;
  status?: 'waiting' | 'in_progress' | 'completed' | 'error';
  message?: string;
  entity?: string;
  value?: number;
  payload?: any;
}

export interface GenerationStep {
  id: string;
  name: string;
  icon: string;
  status: 'waiting' | 'in_progress' | 'completed' | 'error';
}

interface GeneratingClientProps {
  userId: string;
  userLanguage: string;
}

export function GeneratingClient({ userId, userLanguage }: GeneratingClientProps) {
  const router = useRouter();
  const eventSourceRef = useRef<EventSource | null>(null);
  const t = useTranslations('generating');
  const [isPending, startTransition] = useTransition();

  const [currentMessage, setCurrentMessage] = useState(t('initializing'));
  const [steps, setSteps] = useState<GenerationStep[]>([
    { id: 'analyzing', name: t('steps.analyzing'), icon: 'BarChart3', status: 'waiting' },
    { id: 'collection', name: t('steps.collection'), icon: 'Library', status: 'waiting' },
    { id: 'offers', name: t('steps.offers'), icon: 'ShoppingCart', status: 'waiting' },
    { id: 'finalizing', name: t('steps.finalizing'), icon: 'Sparkles', status: 'waiting' }
  ]);
  
  const [insights, setInsights] = useState<string[]>([]);
  const [discoveredAlbums, setDiscoveredAlbums] = useState<any[]>([]);
  const [lastDiscoveredAlbum, setLastDiscoveredAlbum] = useState<{
    artist: string;
    album?: string;
  } | undefined>();
  const [recommendations, setRecommendations] = useState<any[]>([]);
  const [counters, setCounters] = useState<Record<string, number>>({});
  const [isComplete, setIsComplete] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const isCompleteRef = useRef(false);
  const completionTimeRef = useRef<number | null>(null);

  useEffect(() => {
    // Établir la connexion SSE
    const eventSource = new EventSource(`/api/generation-stream?userId=${userId}&lang=${userLanguage}`);
    eventSourceRef.current = eventSource;

    eventSource.onopen = () => {
      if (process.env.NODE_ENV === 'development') {
      console.log("🔗 Connexion SSE établie");
      }
      setCurrentMessage(t('messages.connectionEstablished'));
    };

    eventSource.onmessage = (event) => {
      try {
        const data: GenerationEvent = JSON.parse(event.data);
        if (process.env.NODE_ENV === 'development') {
        console.log("📨 Événement SSE reçu:", data);
        }
        
        handleGenerationEvent(data);
      } catch (error) {
        console.error("❌ Erreur lors du parsing de l'événement SSE:", error);
      }
    };

    eventSource.onerror = (error) => {
      console.error("❌ Erreur SSE:", error);
      console.error("❌ EventSource readyState:", eventSource.readyState);
      console.error("❌ EventSource url:", eventSource.url);

      // Vérifier si la connexion est encore active
      const isConnectionActive = eventSourceRef.current && eventSourceRef.current.readyState !== EventSource.CLOSED;

      // Ne pas afficher d'erreur si :
      // - Le processus est déjà terminé
      // - La connexion est fermée
      // - L'erreur survient dans les 5 secondes suivant la completion
      // - L'erreur est liée à un changement de focus/redimensionnement
      const now = Date.now();
      const isRecentCompletion = completionTimeRef.current && (now - completionTimeRef.current) < 5000;

      // Vérifier si l'erreur est liée à un changement de visibilité de la page
      const isPageVisible = document.visibilityState === 'visible';

      if (!isCompleteRef.current && !isRecentCompletion && isConnectionActive && isPageVisible) {
        console.error("❌ SSE Error - Connection will be retried");
        
        // Limiter les tentatives de reconnexion
        const retryAttempts = (window as any).__sseRetryCount || 0;
        if (retryAttempts < 3) {
          (window as any).__sseRetryCount = retryAttempts + 1;
          console.log(`🔄 Tentative de reconnexion SSE (${retryAttempts + 1}/3)`);
          
          // Nettoyer l'ancienne connexion
          if (eventSourceRef.current) {
            eventSourceRef.current.close();
          }
          
          // Créer une nouvelle connexion après délai
          setTimeout(() => {
            try {
              const newEventSource = new EventSource(`/api/generation-stream?userId=${userId}&lang=${userLanguage}`);
              eventSourceRef.current = newEventSource;
              // Réattacher les handlers (code simplifié ici)
            } catch (retryError) {
              console.error("❌ Échec de reconnexion SSE:", retryError);
              setError(t('messages.connectionError'));
            }
          }, 2000);
        } else {
          console.error("❌ Trop de tentatives de reconnexion SSE");
          setError(t('messages.connectionError'));
        }
      } else {
        if (process.env.NODE_ENV === 'development') {
        console.log("🔒 Erreur SSE ignorée - processus terminé, connexion fermée ou page non visible");
        }
      }
    };

    // Nettoyage
    return () => {
      if (eventSourceRef.current) {
        eventSourceRef.current.close();
      }
    };
  }, [userId]);

  const handleGenerationEvent = (event: GenerationEvent) => {
    switch (event.type) {
      case 'step_update':
        if (event.step && event.status && event.message) {
          updateStep(event.step, event.status);
          setCurrentMessage(event.message);
        }
        break;
        
      case 'counter_update':
        if (event.entity && event.value !== undefined) {
          setCounters(prev => ({ ...prev, [event.entity as string]: event.value as number }));
        }
        break;
        
      case 'insight':
        if (event.message) {
          setInsights(prev => [...prev, event.message as string]);
        }
        break;

      case 'status_update':
        if (event.message) {
          setCurrentMessage(event.message);
        }
        break;

      case 'album_art_discovered':
        if (event.payload) {
          setDiscoveredAlbums(prev => [...prev, event.payload]);
          setLastDiscoveredAlbum({
            artist: event.payload.artist,
            album: event.payload.album
          });
        }
        break;
        
      case 'recommendation_ready':
        if (event.payload) {
          setRecommendations(prev => [...prev, event.payload]);
        }
        break;
        
      case 'complete':
        setIsComplete(true);
        isCompleteRef.current = true;
        completionTimeRef.current = Date.now();
        // Pas de message "Vos recommandations sont prêtes !" - passage direct à la redirection
        // Fermer proprement la connexion SSE immédiatement
        if (eventSourceRef.current) {
          if (process.env.NODE_ENV === 'development') {
          console.log("🔒 Fermeture de la connexion SSE après completion");
          }
          eventSourceRef.current.close();
          eventSourceRef.current = null; // Éviter les références obsolètes
        }
        break;
        
      case 'error':
        setError(event.message || "Une erreur est survenue");
        break;
    }
  };

  const updateStep = (stepId: string, status: GenerationStep['status']) => {
    setSteps(prev => prev.map(step =>
      step.id === stepId ? { ...step, status } : step
    ));
  };

  const handleTransitionComplete = () => {
    // Utiliser une transition pour appeler la Server Action
    // qui revalide les données et redirige vers les recommandations
    startTransition(async () => {
      await revalidateAndRedirectToRecommendations();
    });
  };

  if (error) {
    return (
      <div className="relative h-screen overflow-hidden">
        {/* Arrière-plan avec défilement de pochettes */}
        <AlbumBackgroundFlow discoveredAlbums={discoveredAlbums} />

        {/* Contenu d'erreur centré */}
        <div className="relative z-10 h-full flex items-center justify-center">
          <div className="w-full max-w-md mx-auto px-4 text-center">
            {/* Logo Stream2Spin */}
            <div className="mb-8">
              <Logo size="xl" showText={false} className="justify-center" />
            </div>

            <h1 className="text-2xl font-bold text-red-600 mb-4">Erreur</h1>
            <p className="text-slate-600 dark:text-slate-400 mb-6">{error}</p>
            <button
              onClick={() => startTransition(async () => {
                await revalidateAndRedirectToRecommendations();
              })}
              className="bg-primary text-primary-foreground px-6 py-2 rounded-lg hover:bg-primary/90"
              disabled={isPending}
            >
              {isPending ? 'Chargement...' : 'Retour aux recommandations'}
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="relative h-screen overflow-hidden">
      {/* Arrière-plan avec défilement de pochettes */}
      <AlbumBackgroundFlow discoveredAlbums={discoveredAlbums} />

      {/* Contenu principal - Nouvelles spécifications Epic 11 */}
      <div className="relative z-10 h-full flex items-center justify-center">
        <div className="w-full max-w-4xl mx-auto px-4">
          {/* Carte centrale unique avec effet verre dépoli */}
          <div className="bg-white/80 backdrop-blur-lg rounded-3xl shadow-xl border border-white/20 p-8">
            {/* Logo Stream2Spin dans la carte */}
            <div className="text-center mb-8">
              <Logo size="xl" showText={false} className="justify-center" />
            </div>

            {/* Timeline sans barre de progression */}
            <GenerationTimeline
              steps={steps}
              isComplete={isComplete}
            />

            {/* Texte dynamique intégré */}
            <DynamicCentralText
              discoveredAlbums={discoveredAlbums}
              isComplete={isComplete}
              onTransitionComplete={handleTransitionComplete}
              userLanguage={userLanguage}
            />

            {/* Compteurs en badges en bas de la carte */}
            <div className="mt-8 flex flex-wrap justify-center gap-3">
              {Object.entries(counters).map(([key, value]) => (
                <div
                  key={key}
                  className="bg-slate-100/80 backdrop-blur-sm px-4 py-2 rounded-full border border-slate-200/50"
                >
                  <span className="text-sm font-semibold text-slate-700">
                    {formatCounterLabel(key)}: <span className="font-bold">{value}</span>
                  </span>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  // Fonction utilitaire pour formater les labels des compteurs selon les specs
  function formatCounterLabel(key: string): string {
    try {
      return t(`counters.${key}`);
    } catch (error) {
      // Fallback si la clé n'existe pas
      return key;
    }
  }
}
