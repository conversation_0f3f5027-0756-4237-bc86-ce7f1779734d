"use client";

import { useState, useEffect } from "react";
import { Lightbulb, Music } from "lucide-react";
import Image from "next/image";

interface GenerationInsightsProps {
  insights: string[];
  discoveredAlbums: Array<{
    artist: string;
    album?: string;
    imageUrl: string;
  }>;
}

export function GenerationInsights({ insights, discoveredAlbums }: GenerationInsightsProps) {
  const [currentAlbumIndex, setCurrentAlbumIndex] = useState(0);
  const [visibleInsights, setVisibleInsights] = useState<string[]>([]);

  // Animation des insights - afficher un par un
  useEffect(() => {
    if (insights.length > visibleInsights.length) {
      const timer = setTimeout(() => {
        setVisibleInsights(prev => [...prev, insights[prev.length]]);
      }, 500);
      return () => clearTimeout(timer);
    }
  }, [insights, visibleInsights]);

  // Rotation des pochettes d'albums
  useEffect(() => {
    if (discoveredAlbums.length > 1) {
      const interval = setInterval(() => {
        setCurrentAlbumIndex(prev => (prev + 1) % discoveredAlbums.length);
      }, 2000);
      return () => clearInterval(interval);
    }
  }, [discoveredAlbums.length]);

  if (visibleInsights.length === 0 && discoveredAlbums.length === 0) {
    return null;
  }

  return (
    <div className="mb-12">
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Zone des insights */}
        {visibleInsights.length > 0 && (
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-slate-900 dark:text-slate-100 flex items-center space-x-2">
              <Lightbulb className="w-5 h-5 text-yellow-500" />
              <span>Découvertes</span>
            </h3>
            
            <div className="space-y-3">
              {visibleInsights.map((insight, index) => (
                <div
                  key={index}
                  className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 
                           border border-blue-200 dark:border-blue-800 rounded-lg p-4
                           animate-in slide-in-from-left duration-500"
                  style={{ animationDelay: `${index * 100}ms` }}
                >
                  <p className="text-slate-700 dark:text-slate-300 text-sm">
                    {insight}
                  </p>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Zone des pochettes d'albums */}
        {discoveredAlbums.length > 0 && (
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-slate-900 dark:text-slate-100 flex items-center space-x-2">
              <Music className="w-5 h-5 text-green-500" />
              <span>Vos albums favoris</span>
            </h3>
            
            <div className="relative">
              {/* Conteneur principal pour la pochette */}
              <div className="relative w-48 h-48 mx-auto">
                {discoveredAlbums.map((album, index) => (
                  <div
                    key={index}
                    className={`absolute inset-0 transition-all duration-1000 ${
                      index === currentAlbumIndex 
                        ? 'opacity-100 scale-100 rotate-0' 
                        : 'opacity-0 scale-95 rotate-3'
                    }`}
                  >
                    <div className="relative w-full h-full rounded-lg overflow-hidden shadow-2xl">
                      <Image
                        src={album.imageUrl}
                        alt={`${album.artist} - ${album.album || 'Album'}`}
                        fill
                        className="object-cover"
                        sizes="(max-width: 768px) 192px, 192px"
                      />
                      
                      {/* Overlay avec informations */}
                      <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent">
                        <div className="absolute bottom-0 left-0 right-0 p-4">
                          <p className="text-white font-semibold text-sm truncate">
                            {album.artist}
                          </p>
                          {album.album && (
                            <p className="text-white/80 text-xs truncate">
                              {album.album}
                            </p>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              {/* Indicateurs de pagination */}
              {discoveredAlbums.length > 1 && (
                <div className="flex justify-center space-x-2 mt-4">
                  {discoveredAlbums.map((_, index) => (
                    <button
                      key={index}
                      onClick={() => setCurrentAlbumIndex(index)}
                      className={`w-2 h-2 rounded-full transition-all duration-300 ${
                        index === currentAlbumIndex 
                          ? 'bg-blue-500 scale-125' 
                          : 'bg-slate-300 dark:bg-slate-600 hover:bg-slate-400 dark:hover:bg-slate-500'
                      }`}
                    />
                  ))}
                </div>
              )}

              {/* Compteur d'albums */}
              <div className="text-center mt-2">
                <p className="text-sm text-slate-600 dark:text-slate-400">
                  {discoveredAlbums.length} album{discoveredAlbums.length > 1 ? 's' : ''} découvert{discoveredAlbums.length > 1 ? 's' : ''}
                </p>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
