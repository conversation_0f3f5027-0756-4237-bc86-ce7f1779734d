"use client";

import { useState, useEffect } from "react";
import { useTranslations } from 'next-intl';

interface DynamicCentralTextProps {
  discoveredAlbums: Array<{
    artist: string;
    album?: string;
  }>;
  isComplete: boolean;
  onTransitionComplete?: () => void;
  userLanguage?: string;
}

export function DynamicCentralText({
  discoveredAlbums,
  isComplete,
  onTransitionComplete
}: DynamicCentralTextProps) {
  const t = useTranslations('generating');
  const [currentAlbum, setCurrentAlbum] = useState<{artist: string; album?: string} | null>(null);
  const [isAnimating, setIsAnimating] = useState(false);
  const [isTransitioning, setIsTransitioning] = useState(false);
  const [showLoader, setShowLoader] = useState(true);
  const [isHydrated, setIsHydrated] = useState(false);

  // Ensure hydration is complete before using Math.random()
  useEffect(() => {
    setIsHydrated(true);
  }, []);

  // setInterval de 1,3s découplé des événements SSE selon les spécifications Epic 11
  useEffect(() => {
    if (!isHydrated || discoveredAlbums.length === 0 || isComplete) return;

    // Fonction pour choisir un album aléatoire
    const selectRandomAlbum = () => {
      const randomIndex = Math.floor(Math.random() * discoveredAlbums.length);
      const album = discoveredAlbums[randomIndex];

      setIsAnimating(true);
      setTimeout(() => {
        setCurrentAlbum(album);
        setShowLoader(false);
        setIsAnimating(false);
      }, 150);
    };

    // Premier affichage immédiat
    selectRandomAlbum();

    // setInterval de 1,3s pour cadence régulière
    const interval = setInterval(selectRandomAlbum, 1300);

    return () => clearInterval(interval);
  }, [isHydrated, discoveredAlbums, isComplete]);

  // Gérer la transition finale selon les spécifications Epic 11
  useEffect(() => {
    if (isComplete && isHydrated) {
      setShowLoader(false);
      // NE PAS mettre currentAlbum à null pour éviter le trou d'affichage

      // Continuer d'afficher des albums pendant les 2 secondes d'attente
      const finalAlbumInterval = setInterval(() => {
        if (discoveredAlbums.length > 0) {
          const randomIndex = Math.floor(Math.random() * discoveredAlbums.length);
          const album = discoveredAlbums[randomIndex];
          setCurrentAlbum(album);
        }
      }, 400); // Changer d'album toutes les 400ms

      // Démarrer la transition après 1 seconde
      const timer = setTimeout(() => {
        clearInterval(finalAlbumInterval); // Arrêter les changements d'albums
        setCurrentAlbum(null); // Maintenant on peut vider l'affichage
        setIsTransitioning(true);

        // Redirection après l'animation
        const transitionTimer = setTimeout(() => {
          onTransitionComplete?.();
        }, 1000);

        return () => clearTimeout(transitionTimer);
      }, 1000);

      return () => {
        clearTimeout(timer);
        clearInterval(finalAlbumInterval);
      };
    }
  }, [isComplete, isHydrated, discoveredAlbums, onTransitionComplete]);

  // Rendu du contenu selon les spécifications Epic 11

  return (
    <div className="relative">
      {/* Overlay de transition finale */}
      {isTransitioning && (
        <div className="fixed inset-0 z-50 flex items-center justify-center">
          <div className="absolute inset-0 bg-white/90 backdrop-blur-sm animate-in fade-in duration-1000" />
          <div className="relative z-10 text-center animate-in zoom-in duration-500">
            <div className="w-16 h-16 mx-auto mb-4 rounded-full flex items-center justify-center" style={{ backgroundColor: '#6236FF' }}>
              <svg viewBox="0 0 24 24" fill="none" stroke="white" strokeWidth="3" className="w-8 h-8">
                <polyline points="20,6 9,17 4,12"></polyline>
              </svg>
            </div>
            <p className="text-xl font-semibold text-slate-700">
              {t('messages.yourRecommendationsAreReady')}
            </p>
          </div>
        </div>
      )}

      {/* Texte dynamique central selon Epic 11 */}
      <div className="text-center py-8">
        {isComplete ? (
          // Pas de message final - transition directe vers redirection
          <div className="text-2xl md:text-3xl font-bold text-slate-900 min-h-[100px] flex items-center justify-center">
            {/* Espace vide pour transition fluide */}
          </div>
        ) : showLoader ? (
          // Message d'initialisation
          <div className="text-2xl md:text-3xl font-bold text-slate-900 min-h-[100px] flex flex-col items-center justify-center">
            <div>{t('initializing')}</div>
            <div className="mt-4 flex space-x-1">
              <div className="w-2 h-2 rounded-full bg-slate-400 animate-bounce-dots" />
              <div className="w-2 h-2 rounded-full bg-slate-400 animate-bounce-dots" style={{ animationDelay: '0.2s' }} />
              <div className="w-2 h-2 rounded-full bg-slate-400 animate-bounce-dots" style={{ animationDelay: '0.4s' }} />
            </div>
          </div>
        ) : currentAlbum ? (
          // Affichage dynamique "Vous aimez..."
          <div
            className={`
              text-2xl md:text-3xl font-bold text-slate-900 min-h-[100px]
              flex flex-col items-center justify-center transition-all duration-300
              ${isAnimating ? 'opacity-0 scale-95' : 'opacity-100 scale-100'}
            `}
          >
            <div className="mb-2 text-slate-600">{t('messages.youLike')}</div>
            <div className="text-center">
              <div className="font-semibold" style={{ color: '#6236FF' }}>
                {currentAlbum.album || t('messages.unknownAlbum')}
              </div>
              <div className="text-lg text-slate-700">
                {t('messages.byArtist')} {currentAlbum.artist}
              </div>
            </div>
          </div>
        ) : null}
      </div>
    </div>
  );
}
