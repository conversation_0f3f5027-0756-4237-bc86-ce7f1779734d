"use client";

import { useSession } from "next-auth/react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { useTranslations } from 'next-intl';
import { Music, Album, Heart, Disc3, Users } from "lucide-react";
import { cn } from "@/lib/utils";
import { Logo } from "@/components/ui/logo";

interface SidebarProps {
  className?: string;
}

export function Sidebar({ className }: SidebarProps) {
  const { data: session } = useSession();
  const pathname = usePathname();
  const t = useTranslations('navigation');

  // Ne pas afficher la sidebar si l'utilisateur n'est pas connecté, sur la page de login, ou sur la page de génération
  if (!session?.user || pathname === "/login" || pathname === "/generating") {
    return null;
  }

  const navigationItems = [
    {
      href: "/recommendations",
      label: t('recommendations'),
      icon: Music,
      isActive: pathname === "/recommendations"
    },
    {
      href: "/social",
      label: t('social'),
      icon: Users,
      isActive: pathname === "/social"
    },
    {
      href: "/wishlist",
      label: t('wishlist'),
      icon: Heart,
      isActive: pathname === "/wishlist"
    },
    {
      href: "/collection",
      label: t('collection'),
      icon: Disc3,
      isActive: pathname === "/collection"
    }
  ];

  return (
    <aside className={cn(
      "hidden md:fixed md:left-0 md:top-0 md:z-40 md:h-screen md:w-64 md:border-r md:bg-background/95 md:backdrop-blur md:supports-[backdrop-filter]:bg-background/60 md:block",
      className
    )}>
      <div className="flex h-full flex-col">
        {/* Logo en haut de la sidebar - aligné à gauche - même hauteur que le header */}
        <div className="flex items-center h-16 px-6 border-b">
          <Link href="/recommendations" className="hover:opacity-80 transition-opacity">
            <Logo size="md" animated={true} />
          </Link>
        </div>

        {/* Navigation */}
        <nav className="flex-1 space-y-2 p-4">
          {navigationItems.map((item) => {
            const Icon = item.icon;
            return (
              <Link
                key={item.href}
                href={item.href}
                className={cn(
                  "flex items-center space-x-3 rounded-lg px-3 py-2 text-sm font-medium transition-colors",
                  item.isActive
                    ? "bg-primary text-primary-foreground shadow-sm"
                    : "text-muted-foreground hover:bg-accent hover:text-accent-foreground"
                )}
              >
                <Icon className="h-5 w-5" />
                <span>{item.label}</span>
              </Link>
            );
          })}
        </nav>
      </div>
    </aside>
  );
}
