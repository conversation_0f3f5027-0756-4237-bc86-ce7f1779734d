"use client";

import { useSession } from "next-auth/react";
import { usePathname } from "next/navigation";
import { useEffect } from "react";
import { Header } from "./header";
import { Sidebar } from "./sidebar";
import { preloadCriticalRoutes, optimizeDNS } from "@/lib/preload-critical-routes";

interface AuthenticatedLayoutProps {
  children: React.ReactNode;
}

export function AuthenticatedLayout({ children }: AuthenticatedLayoutProps) {
  const { data: session, status } = useSession();
  const pathname = usePathname();

  // 🚀 Pré-chargement des routes critiques et optimisations DNS
  useEffect(() => {
    preloadCriticalRoutes();
    optimizeDNS();
  }, []);

  // Vérifier si on est sur la page de login ou de génération
  const isLoginPage = pathname === "/login";
  const isGeneratingPage = pathname === "/generating";
  const isPublicPage = pathname.startsWith('/u/');

  // Affichage immédiat du layout, les composants gèrent eux-mêmes leur visibilité
  return (
    <div className="min-h-screen">
      {/* Sidebar - toujours rendue, gère sa propre visibilité */}
      <Sidebar />

      {/* Header - rendu conditionnellement */}
      {(!isPublicPage || (isPublicPage && session?.user)) && <Header />}

      {/* Contenu principal avec marge et padding conditionnels */}
      <main className={
        isLoginPage || isGeneratingPage || (isPublicPage && !session?.user)
          ? ""
          : (session && session.user)
            ? "md:ml-64 pt-16"
            : "pt-16"
      }>
        {children}
      </main>
    </div>
  );
}
