"use client";

import { useState, useRef, useEffect } from "react";
import { Search } from "lucide-react";
import { Input } from "@/components/ui/input";
import { useTranslations } from 'next-intl';
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";

export function SearchBar() {
  const [searchQuery, setSearchQuery] = useState("");
  const [showDropdown, setShowDropdown] = useState(false);
  const searchContainerRef = useRef<HTMLDivElement>(null);
  const t = useTranslations('search');

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    // TODO: Implémenter la logique de recherche plus tard
    // Cette fonction servira à rechercher manuellement des albums et artistes
    // pour obtenir les meilleurs prix via l'API Awin
    if (process.env.NODE_ENV === 'development') {
    console.log("Recherche:", searchQuery);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchQuery(value);
    setShowDropdown(value.length > 0);
  };

  const handleInputFocus = () => {
    if (searchQuery.length > 0) {
      setShowDropdown(true);
    }
  };

  // Handle click outside to close dropdown
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (searchContainerRef.current && !searchContainerRef.current.contains(event.target as Node)) {
        setShowDropdown(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Close dropdown when search query is cleared
  useEffect(() => {
    if (searchQuery.length === 0) {
      setShowDropdown(false);
    }
  }, [searchQuery]);

  return (
    <form onSubmit={handleSearch} className="w-full max-w-md">
      <div className="relative" ref={searchContainerRef}>
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground z-10" />
        <Input
          type="text"
          placeholder={t('placeholder')}
          value={searchQuery}
          onChange={handleInputChange}
          onFocus={handleInputFocus}
          className="pl-10 pr-4 w-full bg-background/50 border-border/50 focus:bg-background focus:border-primary/50 transition-all duration-200"
        />
        {searchQuery && (
          <Button
            type="submit"
            size="sm"
            className="absolute right-1 top-1/2 transform -translate-y-1/2 h-8 px-3 z-10"
          >
            <Search className="h-3 w-3" />
          </Button>
        )}

        {/* Search Dropdown */}
        {showDropdown && (
          <div className={cn(
            "absolute top-full left-0 right-0 mt-1 z-50",
            "bg-background/95 backdrop-blur-lg border border-border/50",
            "rounded-xl shadow-lg",
            "animate-in fade-in-0 zoom-in-95 slide-in-from-top-2",
            "duration-200"
          )}>
            <div className="p-3">
              <div className="flex items-center justify-center py-4 px-3">
                <span className="text-sm text-muted-foreground">
                  {t('comingSoon')}
                </span>
              </div>
            </div>
          </div>
        )}
      </div>
    </form>
  );
}
