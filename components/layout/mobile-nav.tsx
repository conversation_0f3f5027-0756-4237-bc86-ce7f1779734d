"use client";

import { useState } from "react";
import { useSession } from "next-auth/react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { useTranslations } from 'next-intl';
import { Menu, X, Music, Heart, Album, Disc3, Users } from "lucide-react";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { Logo } from "@/components/ui/logo";
import {
  Sheet,
  SheetContent,
  SheetTrigger,
} from "@/components/ui/sheet";

export function MobileNav() {
  const { data: session } = useSession();
  const pathname = usePathname();
  const t = useTranslations('navigation');
  const [open, setOpen] = useState(false);

  // Ne pas afficher le menu mobile si l'utilisateur n'est pas connecté
  if (!session?.user) {
    return null;
  }

  const navigationItems = [
    {
      href: "/recommendations",
      label: t('recommendations'),
      icon: Music,
      isActive: pathname === "/recommendations"
    },
    {
      href: "/social",
      label: t('social'),
      icon: Users,
      isActive: pathname === "/social"
    },
    {
      href: "/wishlist",
      label: t('wishlist'),
      icon: Heart,
      isActive: pathname === "/wishlist"
    },
    {
      href: "/collection",
      label: t('collection'),
      icon: Disc3,
      isActive: pathname === "/collection"
    }
  ];

  return (
    <Sheet open={open} onOpenChange={setOpen}>
      <SheetTrigger asChild>
        <Button
          variant="ghost"
          size="icon"
          className="h-9 w-9"
        >
          <Menu className="h-5 w-5" />
          <span className="sr-only">{t('toggleMenu')}</span>
        </Button>
      </SheetTrigger>
      <SheetContent side="left" className="w-64 p-0">
        <div className="flex h-full flex-col">
          <div className="flex h-16 items-center justify-center border-b px-4">
            <Link href="/recommendations" onClick={() => setOpen(false)}>
              <Logo size="sm" animated={true} />
            </Link>
          </div>
          <nav className="flex-1 space-y-2 p-4">
            {navigationItems.map((item) => {
              const Icon = item.icon;
              return (
                <Link
                  key={item.href}
                  href={item.href}
                  onClick={() => setOpen(false)}
                  className={cn(
                    "flex items-center space-x-3 rounded-lg px-3 py-2 text-sm font-medium transition-colors",
                    item.isActive
                      ? "bg-primary text-primary-foreground shadow-sm"
                      : "text-muted-foreground hover:bg-accent hover:text-accent-foreground"
                  )}
                >
                  <Icon className="h-5 w-5" />
                  <span>{item.label}</span>
                </Link>
              );
            })}
          </nav>
        </div>
      </SheetContent>
    </Sheet>
  );
}
