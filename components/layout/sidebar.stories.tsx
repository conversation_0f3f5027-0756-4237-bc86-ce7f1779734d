// components/layout/sidebar.stories.tsx
import type { <PERSON>a, StoryObj } from '@storybook/nextjs-vite';
import { Music, Heart, Disc3 } from 'lucide-react';

// Mock de la fonction cn pour le classname
const cn = (...classes: (string | undefined)[]) => classes.filter(Boolean).join(' ');

// Composant mock pour le logo - plus fidèle au vrai composant
const LogoMock = ({ size, animated }: { size: 'sm' | 'md' | 'lg' | 'xl'; animated: boolean }) => {
  const sizeConfig = {
    sm: "h-6",
    md: "h-8", 
    lg: "h-10",
    xl: "h-12"
  };
  
  return (
    <div className="flex items-center space-x-2">
      <div className={`${sizeConfig[size]} w-auto bg-primary rounded-sm flex items-center justify-center px-2 py-1 ${animated ? 'transition-transform hover:scale-105' : ''}`}>
        <span className="text-primary-foreground font-bold text-sm">S2S</span>
      </div>
      <span className="font-bold bg-gradient-to-r from-primary to-primary/80 bg-clip-text text-transparent sr-only">
        Stream2Spin
      </span>
    </div>
  );
};

// Mock du composant Link de Next.js
const LinkMock = ({ href, children, className }: any) => (
  <a href={href} className={className}>
    {children}
  </a>
);

// Composant Sidebar mock
const SidebarMock = ({ session, pathname, className }: any) => {
  // Reproduire la logique du Sidebar original
  if (!session?.user || pathname === "/login" || pathname === "/generating") {
    return null;
  }

  const navigationItems = [
    {
      href: "/recommendations",
      label: "Recommandations",
      icon: Music,
      isActive: pathname === "/recommendations"
    },
    {
      href: "/wishlist",
      label: "Mes envies",
      icon: Heart,
      isActive: pathname === "/wishlist"
    },
    {
      href: "/collection",
      label: "Ma collection",
      icon: Disc3,
      isActive: pathname === "/collection"
    }
  ];

  return (
    <aside className={cn(
      "hidden md:fixed md:left-0 md:top-0 md:z-40 md:h-screen md:w-64 md:border-r md:bg-background/95 md:backdrop-blur md:supports-[backdrop-filter]:bg-background/60 md:block",
      className
    )}>
      <div className="flex h-full flex-col">
        {/* Logo en haut de la sidebar - aligné à gauche - même hauteur que le header */}
        <div className="flex items-center h-16 px-6 border-b">
          <LinkMock href="/recommendations" className="hover:opacity-80 transition-opacity">
            <LogoMock size="md" animated={true} />
          </LinkMock>
        </div>

        {/* Navigation */}
        <nav className="flex-1 space-y-2 p-4">
          {navigationItems.map((item) => {
            const Icon = item.icon;
            return (
              <LinkMock
                key={item.href}
                href={item.href}
                className={cn(
                  "flex items-center space-x-3 rounded-lg px-3 py-2 text-sm font-medium transition-colors",
                  item.isActive
                    ? "bg-primary text-primary-foreground shadow-sm"
                    : "text-muted-foreground hover:bg-accent hover:text-accent-foreground"
                )}
              >
                <Icon className="h-5 w-5" />
                <span>{item.label}</span>
              </LinkMock>
            );
          })}
        </nav>
      </div>
    </aside>
  );
};

// Définition des métadonnées
const meta: Meta<typeof SidebarMock> = {
  title: 'Design System/Layout/Sidebar',
  component: SidebarMock,
  parameters: {
    layout: 'fullscreen',
    backgrounds: {
      default: 'light',
      values: [
        { name: 'light', value: '#ffffff' },
        { name: 'dark', value: '#0f172a' }
      ]
    }
  },
  argTypes: {
    session: {
      control: false,
      description: 'Objet de session next-auth simulé',
    },
    pathname: {
      control: 'text',
      description: 'Chemin de la page actuelle pour déterminer l\'élément actif',
    },
    className: {
      control: 'text',
      description: 'Classes CSS additionnelles',
    }
  },
  decorators: [
    (Story) => (
      <div className="min-h-screen bg-background flex">
        <Story />
        {/* Contenu principal factice pour voir la sidebar en contexte */}
        <div className="flex-1 md:ml-64">
          <div className="p-8">
            <div className="max-w-4xl mx-auto">
              <h1 className="text-2xl font-bold text-foreground mb-4">Contenu de la page</h1>
              <p className="text-muted-foreground mb-4">
                Ce contenu simule une page de l'application pour montrer la sidebar en contexte.
                La sidebar est fixe à gauche et contient la navigation principale.
              </p>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <div className="p-4 border rounded-lg">
                  <h3 className="font-semibold mb-2">Navigation active</h3>
                  <p className="text-sm text-muted-foreground">
                    L'élément de navigation correspondant à la page courante est mis en surbrillance.
                  </p>
                </div>
                <div className="p-4 border rounded-lg">
                  <h3 className="font-semibold mb-2">Responsive</h3>
                  <p className="text-sm text-muted-foreground">
                    La sidebar est masquée sur mobile et remplacée par un menu hamburger.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    ),
  ],
};

export default meta;
type Story = StoryObj<typeof meta>;

// Story pour la page des recommandations (élément actif)
export const RecommendationsActive: Story = {
  args: {
    session: {
      user: {
        name: 'Simon Gavelle',
        email: '<EMAIL>',
        image: null
      }
    },
    pathname: '/recommendations'
  },
};

// Story pour la page wishlist (élément actif)
export const WishlistActive: Story = {
  args: {
    session: {
      user: {
        name: 'Marie Dubois',
        email: '<EMAIL>',
        image: null
      }
    },
    pathname: '/wishlist'
  },
};

// Story pour la page collection (élément actif)
export const CollectionActive: Story = {
  args: {
    session: {
      user: {
        name: 'Jean Dupont',
        email: '<EMAIL>',
        image: null
      }
    },
    pathname: '/collection'
  },
};

// Story pour une page sans élément de navigation actif
export const AccountPage: Story = {
  args: {
    session: {
      user: {
        name: 'Utilisateur Test',
        email: '<EMAIL>',
        image: null
      }
    },
    pathname: '/account'
  },
  parameters: {
    docs: {
      description: {
        story: 'Aucun élément de navigation n\'est actif car nous sommes sur une page qui ne correspond à aucun élément du menu.'
      }
    }
  }
};

// Story pour utilisateur non connecté (sidebar masquée)
export const LoggedOut: Story = {
  args: {
    session: null,
    pathname: '/recommendations'
  },
  parameters: {
    docs: {
      description: {
        story: 'La sidebar est masquée quand aucun utilisateur n\'est connecté.'
      }
    }
  }
};

// Story pour page de login (sidebar masquée)
export const LoginPage: Story = {
  args: {
    session: {
      user: {
        name: 'Test User',
        email: '<EMAIL>',
        image: null
      }
    },
    pathname: '/login'
  },
  parameters: {
    docs: {
      description: {
        story: 'La sidebar est masquée sur la page de login, même si une session existe.'
      }
    }
  }
};

// Story pour page de génération (sidebar masquée)
export const GeneratingPage: Story = {
  args: {
    session: {
      user: {
        name: 'Test User',
        email: '<EMAIL>',
        image: null
      }
    },
    pathname: '/generating'
  },
  parameters: {
    docs: {
      description: {
        story: 'La sidebar est masquée sur la page de génération des recommandations.'
      }
    }
  }
};

// Story avec classe CSS personnalisée
export const WithCustomStyling: Story = {
  args: {
    session: {
      user: {
        name: 'Designer',
        email: '<EMAIL>',
        image: null
      }
    },
    pathname: '/recommendations',
    className: 'border-4 border-primary'
  },
  parameters: {
    docs: {
      description: {
        story: 'Exemple avec des classes CSS personnalisées appliquées à la sidebar.'
      }
    }
  }
}; 