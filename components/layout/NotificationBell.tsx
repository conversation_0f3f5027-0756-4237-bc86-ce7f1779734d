'use client';

import { useState, useEffect } from 'react';
import { Bell, User } from 'lucide-react';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import Link from 'next/link';
import { getUnreadNotifications, markNotificationsAsRead } from '@/app/actions/social';
import { useTranslations } from 'next-intl';

type Notification = Awaited<ReturnType<typeof getUnreadNotifications>>['notifications'][0];

export function NotificationBell() {
  const t = useTranslations('Notifications');
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [isOpen, setIsOpen] = useState(false);

  const fetchNotifications = async () => {
    const result = await getUnreadNotifications();
    setNotifications(result.notifications);
    setUnreadCount(result.count);
  };

  useEffect(() => {
    fetchNotifications();
  }, []);

  const handleOpenChange = async (open: boolean) => {
    setIsOpen(open);
    if (open && unreadCount > 0) {
      // Marquer comme lu après une petite attente pour l'ouverture de la popover
      setTimeout(async () => {
        await markNotificationsAsRead();
        setUnreadCount(0);
      }, 1000);
    }
  };

  return (
    <Popover open={isOpen} onOpenChange={handleOpenChange}>
      <PopoverTrigger asChild>
        <Button variant="ghost" size="icon" className="relative">
          <Bell className="h-5 w-5" />
          {unreadCount > 0 && (
            <span className="absolute top-1 right-1 flex h-3 w-3">
              <span className="animate-ping absolute inline-flex h-full w-full rounded-full bg-red-400 opacity-75"></span>
              <span className="relative inline-flex rounded-full h-3 w-3 bg-red-500"></span>
            </span>
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-80">
        <div className="grid gap-4">
          <div className="space-y-2">
            <h4 className="font-medium leading-none">{t('title')}</h4>
            <p className="text-sm text-muted-foreground">{t('description')}</p>
          </div>
          <div className="grid gap-2">
            {notifications.length > 0 ? (
              notifications.map((notif) => (
                <Link
                  key={notif.id}
                  href={`/u/${notif.actor.publicListId}`}
                  className="flex items-center gap-3 rounded-lg p-2 hover:bg-accent"
                  onClick={() => setIsOpen(false)}
                >
                  <Avatar className="h-8 w-8">
                    <AvatarImage src={notif.actor.image || ''} alt={notif.actor.name || ''} />
                    <AvatarFallback><User className="h-4 w-4" /></AvatarFallback>
                  </Avatar>
                  <p className="text-sm">
                    {t('newFollower', { name: notif.actor.name || 'Utilisateur' })}
                  </p>
                </Link>
              ))
            ) : (
              <p className="text-sm text-muted-foreground text-center py-4">{t('noNotifications')}</p>
            )}
          </div>
        </div>
      </PopoverContent>
    </Popover>
  );
}
