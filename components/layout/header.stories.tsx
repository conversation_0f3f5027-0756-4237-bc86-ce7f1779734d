// components/layout/header.stories.tsx
import type { Meta, StoryObj } from '@storybook/nextjs-vite';

// Composant mock pour le logo
const LogoMock = ({ size, animated }: any) => (
  <div className={`font-bold text-primary ${size === 'md' ? 'text-lg' : 'text-base'}`}>
    Stream2Spin {animated && '🎵'}
  </div>
);

// Composant mock pour la barre de recherche
const SearchBarMock = () => (
  <div className="flex-1 max-w-md">
    <input
      type="text"
      placeholder="Rechercher des albums..."
      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
      disabled
    />
  </div>
);

// Composant mock pour le menu mobile
const MobileNavMock = () => (
  <button className="p-2 hover:bg-accent rounded-lg md:hidden">
    <div className="w-6 h-6 flex flex-col justify-around">
      <div className="w-full h-0.5 bg-foreground"></div>
      <div className="w-full h-0.5 bg-foreground"></div>
      <div className="w-full h-0.5 bg-foreground"></div>
    </div>
  </button>
);

// Composant mock pour le UserNav
const UserNavMock = ({ user }: any) => {
  const getInitials = () => {
    if (user.name) {
      return user.name
        .split(" ")
        .map((n: string) => n[0])
        .join("")
        .toUpperCase()
        .slice(0, 2);
    }
    if (user.email) {
      return user.email.slice(0, 2).toUpperCase();
    }
    return "U";
  };

  return (
    <div className="flex items-center space-x-3 rounded-lg px-3 py-2 hover:bg-accent transition-all duration-200">
      <div className="h-10 w-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center text-white font-semibold">
        {user.image ? (
          <img 
            src={user.image} 
            alt={user.name || user.email || "User"}
            className="h-10 w-10 rounded-full object-cover"
          />
        ) : (
          getInitials()
        )}
      </div>
      <div className="hidden md:block text-left">
        <span className="text-sm font-medium text-foreground">
          {user.name || user.email}
        </span>
      </div>
    </div>
  );
};

// Composant Header mock
const HeaderMock = ({ session, pathname }: any) => {
  // Reproduire la logique du Header original
  if (!session?.user || pathname === "/login" || pathname === "/generating") {
    return null;
  }

  return (
    <header className="fixed top-0 left-0 right-0 z-50 md:left-64 border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="h-16 px-4">
        <div className="flex items-center justify-between h-full">

          {/* Left side - Mobile menu */}
          <div className="flex items-center">
            <div className="md:hidden flex items-center space-x-4">
              <MobileNavMock />
              <a href="/recommendations">
                <LogoMock size="md" animated={true} />
              </a>
            </div>
          </div>

          {/* Center - Search bar - Hidden on mobile */}
          <div className="hidden md:flex flex-1 justify-center max-w-md mx-8">
            <SearchBarMock />
          </div>

          {/* Right side - User menu - ALWAYS visible */}
          <div className="flex items-center">
            <UserNavMock user={session.user} />
          </div>

        </div>
      </div>
    </header>
  );
};

// Définition des métadonnées
const meta: Meta<typeof HeaderMock> = {
  title: 'Design System/Layout/Header',
  component: HeaderMock,
  parameters: {
    layout: 'fullscreen',
    backgrounds: {
      default: 'light',
      values: [
        { name: 'light', value: '#ffffff' },
        { name: 'dark', value: '#0f172a' }
      ]
    }
  },
  argTypes: {
    session: {
      control: false,
      description: 'Objet de session next-auth simulé',
    },
    pathname: {
      control: 'text',
      description: 'Chemin de la page actuelle',
    }
  },
  decorators: [
    (Story) => (
      <div className="min-h-screen bg-background">
        <Story />
        {/* Contenu de page factice pour voir le header en contexte */}
        <div className="pt-16 md:pl-64 p-8">
          <div className="max-w-4xl mx-auto">
            <h1 className="text-2xl font-bold text-foreground mb-4">Page de démonstration</h1>
            <p className="text-muted-foreground">
              Ce contenu simule une page de l'application pour montrer le header en contexte.
              Le header est fixe et s'adapte selon l'état de la session et la page courante.
            </p>
          </div>
        </div>
      </div>
    ),
  ],
};

export default meta;
type Story = StoryObj<typeof meta>;

// Story pour utilisateur connecté sur la page des recommandations
export const LoggedInRecommendations: Story = {
  args: {
    session: {
      user: {
        name: 'Simon Gavelle',
        email: '<EMAIL>',
        image: 'https://github.com/shadcn.png'
      }
    },
    pathname: '/recommendations'
  },
};

// Story pour utilisateur connecté sur la page collection
export const LoggedInCollection: Story = {
  args: {
    session: {
      user: {
        name: 'Marie Dubois',
        email: '<EMAIL>',
        image: null
      }
    },
    pathname: '/collection'
  },
};

// Story pour utilisateur connecté sur la wishlist
export const LoggedInWishlist: Story = {
  args: {
    session: {
      user: {
        name: null,
        email: '<EMAIL>',
        image: null
      }
    },
    pathname: '/wishlist'
  },
};

// Story pour page de login (header masqué)
export const LoginPage: Story = {
  args: {
    session: {
      user: {
        name: 'Test User',
        email: '<EMAIL>',
        image: null
      }
    },
    pathname: '/login'
  },
  parameters: {
    docs: {
      description: {
        story: 'Le header est masqué sur la page de login, même si une session existe.'
      }
    }
  }
};

// Story pour page de génération (header masqué)
export const GeneratingPage: Story = {
  args: {
    session: {
      user: {
        name: 'Test User',
        email: '<EMAIL>',
        image: null
      }
    },
    pathname: '/generating'
  },
  parameters: {
    docs: {
      description: {
        story: 'Le header est masqué sur la page de génération des recommandations.'
      }
    }
  }
};

// Story pour utilisateur non connecté (header masqué)
export const LoggedOut: Story = {
  args: {
    session: null,
    pathname: '/recommendations'
  },
  parameters: {
    docs: {
      description: {
        story: 'Le header est masqué quand aucun utilisateur n\'est connecté.'
      }
    }
  }
};

// Story pour utilisateur avec nom très long (test responsive)
export const LongUserName: Story = {
  args: {
    session: {
      user: {
        name: 'Jean-Baptiste Alexandre François-Xavier de Montmorency-Bouteville',
        email: '<EMAIL>',
        image: null
      }
    },
    pathname: '/account'
  },
  parameters: {
    docs: {
      description: {
        story: 'Test avec un nom d\'utilisateur très long pour vérifier la gestion responsive.'
      }
    }
  }
}; 