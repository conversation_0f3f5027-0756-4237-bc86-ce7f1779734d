"use client";

import { useSession } from "next-auth/react";
import { usePathname } from "next/navigation";
import Link from "next/link";
import { UserNav } from "./user-nav";
import { MobileNav } from "./mobile-nav";
import { Logo } from "@/components/ui/logo";
import { UserSearch } from "@/components/social/UserSearch";
import { NotificationBell } from "./NotificationBell";

export function Header() {
  const { data: session } = useSession();
  const pathname = usePathname();

  // Ne pas afficher le header si l'utilisateur n'est pas connecté, sur la page de login, ou sur la page de génération
  if (!session?.user || pathname === "/login" || pathname === "/generating") {
    return null;
  }

  return (
    <header className="fixed top-0 left-0 right-0 z-50 md:left-64 border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="h-16 px-4">
        {/* Layout responsive : mobile vs desktop */}
        
        {/* Mobile Layout - Sans logo, plus d'espace pour la recherche */}
        <div className="grid grid-cols-[auto_1fr_auto] items-center h-full gap-2 md:hidden">
          {/* Left - Mobile menu seulement */}
          <div className="flex items-center">
            <MobileNav />
          </div>

          {/* Center - Barre de recherche étendue */}
          <div className="flex justify-center min-w-0">
            <UserSearch className="w-full max-w-sm" />
          </div>

          {/* Right - Notifications & User menu */}
          <div className="flex items-center gap-1">
            <NotificationBell />
            <UserNav user={session.user} />
          </div>
        </div>

        {/* Desktop Layout - Sans logo, barre de recherche centrée dans l'espace disponible */}
        <div className="hidden md:flex md:items-center md:justify-end md:h-full md:relative">
          
          {/* Barre de recherche - centrée dans l'espace entre bordure gauche et notifications */}
          <div className="absolute left-0 right-24 flex justify-center">
            <div className="w-full max-w-md lg:max-w-lg">
              <UserSearch className="w-full" />
            </div>
          </div>

          {/* Right side - Notifications & User menu */}
          <div className="flex items-center gap-2">
            <NotificationBell />
            <UserNav user={session.user} />
          </div>
        </div>

      </div>
    </header>
  );
}
