// components/layout/user-nav.stories.tsx
import type { Meta, StoryObj } from '@storybook/nextjs-vite';

// Composant UserNav simplifié pour éviter les dépendances
const UserNavMock = ({ user }: any) => {
  const getInitials = () => {
    if (user.name) {
      return user.name
        .split(" ")
        .map((n: string) => n[0])
        .join("")
        .toUpperCase()
        .slice(0, 2);
    }
    if (user.email) {
      return user.email.slice(0, 2).toUpperCase();
    }
    return "U";
  };

  return (
    <div className="flex items-center space-x-3 rounded-lg px-3 py-2 hover:bg-accent transition-all duration-200">
      <div className="h-10 w-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center text-white font-semibold">
        {user.image ? (
          <img 
            src={user.image} 
            alt={user.name || user.email || "User"}
            className="h-10 w-10 rounded-full object-cover"
            onError={(e) => {
              const target = e.target as HTMLImageElement;
              target.style.display = 'none';
              target.parentElement!.textContent = getInitials();
            }}
          />
        ) : (
          getInitials()
        )}
      </div>
      <div className="hidden md:block text-left">
        <span className="text-sm font-medium text-foreground">
          {user.name || user.email}
        </span>
      </div>
    </div>
  );
};

// Définition des métadonnées
const meta: Meta<typeof UserNavMock> = {
  title: 'Design System/Layout/UserNav',
  component: UserNavMock,
  parameters: {
    layout: 'centered',
    backgrounds: {
      default: 'light',
      values: [
        { name: 'light', value: '#ffffff' },
        { name: 'dark', value: '#0f172a' }
      ]
    }
  },
  argTypes: {
    user: {
      control: false,
      description: 'Objet utilisateur avec name, email, et image optionnels',
    }
  },
  decorators: [
    (Story) => (
      <div className="w-80 p-4">
        <div className="flex justify-end">
          <Story />
        </div>
      </div>
    ),
  ],
};

export default meta;
type Story = StoryObj<typeof meta>;

// Story pour utilisateur complet avec nom et image
export const WithNameAndImage: Story = {
  args: {
    user: {
      name: 'Simon Gavelle',
      email: '<EMAIL>',
      image: 'https://github.com/shadcn.png'
    }
  },
};

// Story pour utilisateur avec nom uniquement (sans image)
export const WithNameOnly: Story = {
  args: {
    user: {
      name: 'Marie Dubois',
      email: '<EMAIL>',
      image: null
    }
  },
};

// Story pour utilisateur avec email uniquement (sans nom ni image)
export const EmailOnly: Story = {
  args: {
    user: {
      name: null,
      email: '<EMAIL>',
      image: null
    }
  },
};

// Story pour utilisateur avec nom très long
export const LongName: Story = {
  args: {
    user: {
      name: 'Jean-Baptiste Alexandre François-Xavier de Montmorency',
      email: '<EMAIL>',
      image: null
    }
  },
};

// Story pour utilisateur avec email très long
export const LongEmail: Story = {
  args: {
    user: {
      name: null,
      email: '<EMAIL>',
      image: null
    }
  },
};

// Story pour utilisateur avec initiales simples
export const SimpleInitials: Story = {
  args: {
    user: {
      name: 'A B',
      email: '<EMAIL>',
      image: null
    }
  },
};

// Story pour utilisateur avec image cassée/indisponible
export const BrokenImage: Story = {
  args: {
    user: {
      name: 'Utilisateur Test',
      email: '<EMAIL>',
      image: 'https://invalid-image-url.com/avatar.jpg'
    }
  },
};

// Story montrant le menu ouvert (via contrôles Storybook)
export const MenuOpen: Story = {
  args: {
    user: {
      name: 'Démo Stream2Spin',
      email: '<EMAIL>',
      image: '/erik-mclean-9y1cTVKe1IY-unsplash.jpg'
    }
  },
  parameters: {
    docs: {
      description: {
        story: 'Cliquez sur l\'avatar pour voir le menu déroulant avec les options de navigation et de déconnexion.'
      }
    }
  }
}; 