import NextAuth, { AuthOptions } from "next-auth";
import SpotifyProvider from "next-auth/providers/spotify";
import { DrizzleAdapter } from "@auth/drizzle-adapter";
import { db } from "@/lib/db";
import { users, accounts, sessions, verificationTokens } from "@/lib/db/schema";
import { triggerInitialRecommendationGeneration, syncDiscogsCollection, checkAndSyncDiscogsIfNeeded } from "@/app/actions/user";
import { getCachedUserQuery, cacheUserQuery } from "@/lib/db/query-cache";
import { eq, and } from "drizzle-orm";
import { headers } from "next/headers";
import { parse } from "accept-language-parser";

// Extension des types NextAuth pour inclure l'ID utilisateur
declare module "next-auth" {
  interface User {
    id: string;
  }

  interface Session {
    user: {
      id: string;
      name?: string | null;
      email?: string | null;
      image?: string | null;
    };
  }
}

// Note: La connexion Discogs est maintenant gérée par des routes API personnalisées
// dans /api/discogs/ car NextAuth.js v4 a des problèmes avec OAuth 1.0a personnalisé

export const authOptions: AuthOptions = {
  // ⚠️ CORRECTION Epic Social V1 : Désactiver DrizzleAdapter temporairement
  // Le DrizzleAdapter ne connaît pas les nouvelles colonnes Epic Social V1
  // et échoue lors de la création d'utilisateur avec contraintes NOT NULL
  // adapter: DrizzleAdapter(db, {
  //   usersTable: users,
  //   accountsTable: accounts,
  //   sessionsTable: sessions,
  //   verificationTokensTable: verificationTokens,
  // }),
  session: { strategy: "jwt" },
  pages: {
    signIn: "/login",
  },
  debug: process.env.NODE_ENV === "development" || process.env.VERCEL_ENV === "preview",
  // Configuration pour le développement local et staging
  useSecureCookies: process.env.NODE_ENV === "production" || process.env.VERCEL_ENV === "preview",
  cookies: {
    sessionToken: {
      name: (process.env.NODE_ENV === "production" || process.env.VERCEL_ENV === "preview") ? "__Secure-next-auth.session-token" : "next-auth.session-token",
      options: {
        httpOnly: true,
        sameSite: "lax",
        path: "/",
        secure: process.env.NODE_ENV === "production" || process.env.VERCEL_ENV === "preview",
      },
    },
  },
  providers: [
    SpotifyProvider({
      clientId: process.env.AUTH_SPOTIFY_ID!,
      clientSecret: process.env.AUTH_SPOTIFY_SECRET!,
      authorization: {
        params: { 
          scope: "user-read-email user-top-read",
          // Optimisation pour toutes les plateformes
          show_dialog: "false", // Éviter le dialogue si déjà autorisé
        },
      },
      allowDangerousEmailAccountLinking: true, // Permettre la liaison automatique des comptes
      profile(profile) {
        return {
          id: profile.id,
          name: profile.display_name,
          email: profile.email,
          image: profile.images?.[0]?.url || null,
        };
      },
    }),
  ],
  callbacks: {
    async jwt({ token, user, account, trigger }) {
      // Avec la stratégie JWT, nous stockons l'ID utilisateur dans le token
      if (user && account?.provider === "spotify") {
        // 🔧 CORRECTION Epic Social V1 : Gestion manuelle création utilisateur
        try {
          console.log(`🔍 Gestion utilisateur avec Epic Social V1: ${user.id}`);
          
          // ⚠️ CORRECTION ID UTILISATEUR : user.id = ID Spotify, mais nous voulons un UUID
          // Vérifier si l'utilisateur existe déjà par son providerAccountId
          const existingAccount = await db.query.accounts.findFirst({
            where: and(
              eq(accounts.provider, "spotify"),
              eq(accounts.providerAccountId, user.id) // user.id = ID Spotify
            ),
          });

          let dbUserId: string;

          if (!existingAccount) {
            // Créer un nouvel utilisateur avec UUID généré
            const newUserId = crypto.randomUUID();
            console.log(`👋 Création nouvel utilisateur: Spotify ID ${user.id} → DB UUID ${newUserId}`);
            
            await db.insert(users).values({
              id: newUserId, // ✅ UUID généré pour la DB
              name: user.name,
              email: user.email,
              image: user.image,
              
              // ✅ Epic Social V1 : Nouvelles colonnes avec valeurs par défaut
              profileVisibility: 'users_only',
              shareRecommendations: true,
              shareWishlist: true,
              shareCollection: true,
              emailOnNewFollower: true,
              
              // Colonnes existantes avec valeurs par défaut
              preferredLanguage: 'fr',
              emailFrequency: 'weekly',
              pushFrequency: 'weekly',
              firstRecommendationEmailSent: false,
              publicListEnabled: false,
              publicProfileEnabled: true,
              publicRecommendationsEnabled: true,
              publicWishlistEnabled: false,
              publicCollectionEnabled: false,
              emailNotificationsEnabled: true,
            });
            
            dbUserId = newUserId;
            console.log(`✅ Utilisateur créé: ${newUserId}`);
          } else {
            // L'utilisateur existe déjà, récupérer son UUID DB
            dbUserId = existingAccount.userId;
            console.log(`✅ Utilisateur existant trouvé: Spotify ID ${user.id} → DB UUID ${dbUserId}`);
          }

          // Créer/mettre à jour le compte OAuth avec le bon userId
          console.log(`🔍 Données account reçues:`, {
            provider: account.provider,
            type: account.type,
            spotifyId: user.id, // providerAccountId
            dbUserId: dbUserId, // userId réel en base
            has_access_token: !!account.access_token,
            has_refresh_token: !!account.refresh_token,
            access_token_length: account.access_token?.length || 0,
            refresh_token_length: account.refresh_token?.length || 0,
            expires_at: account.expires_at,
            token_type: account.token_type,
            scope: account.scope,
            expires_in: (account as any).expires_in,
          });

          await db.insert(accounts).values({
            userId: dbUserId, // ✅ UUID DB, pas l'ID Spotify
            type: account.type,
            provider: account.provider,
            providerAccountId: user.id, // ✅ ID Spotify dans providerAccountId
            access_token: account.access_token,
            refresh_token: account.refresh_token,
            expires_at: account.expires_at,
            token_type: account.token_type,
            scope: account.scope,
          }).onConflictDoUpdate({
            target: [accounts.provider, accounts.providerAccountId],
            set: {
              userId: dbUserId, // ✅ S'assurer que l'userId est correct
              access_token: account.access_token,
              refresh_token: account.refresh_token,
              expires_at: account.expires_at,
              token_type: account.token_type,
              scope: account.scope,
            }
          });

          console.log(`✅ Compte OAuth configuré: userId=${dbUserId}, providerAccountId=${user.id}`);

          // ✅ CORRECTION: Stocker l'UUID DB dans le token, pas l'ID Spotify
          token.sub = dbUserId; // UUID de notre table users
          token.name = user.name;
          token.email = user.email;
          token.picture = user.image;

          // 🌐 Détection de langue pour les nouveaux utilisateurs (simplifié)
          if (!existingAccount && user.email && user.name) {
            try {
              // Récupérer la langue du navigateur
              const headersList = await headers();
              const acceptLanguageHeader = headersList.get('Accept-Language');
              let preferredLanguage = 'fr'; // Par défaut français
              
              if (acceptLanguageHeader) {
                const languages = parse(acceptLanguageHeader);
                if (languages.length > 0 && languages[0].code === 'en') {
                  preferredLanguage = 'en';
                }
              }
              
              // Mettre à jour la langue préférée
              await db.update(users)
                .set({ preferredLanguage })
                .where(eq(users.id, dbUserId)); // ✅ Utiliser l'UUID DB
                
              console.log(`🌐 Langue détectée et mise à jour: ${preferredLanguage} pour ${dbUserId}`);
              
              // 📧 Envoyer l'email de bienvenue
              console.log(`📧 Envoi email de bienvenue à ${user.email}`);
              const { sendWelcomeEmail, sanitizeUserName } = await import("@/lib/email");
              const cleanName = sanitizeUserName(user.name);

              const result = await sendWelcomeEmail({
                name: cleanName,
                email: user.email,
                preferredLanguage
              });

              if (result.success) {
                console.log(`✅ Email de bienvenue envoyé: ${result.messageId}`);
              } else {
                console.error(`❌ Échec email de bienvenue: ${result.error}`);
              }
            } catch (emailError) {
              console.error("❌ Erreur email de bienvenue:", emailError);
            }
          }

        } catch (error) {
          console.error("❌ Erreur création utilisateur Epic Social V1:", error);
          // Ne pas faire échouer la connexion
        }
      } else if (user) {
        // Cas où user existe mais pas account (ne devrait pas arriver avec Spotify)
        token.sub = user.id;
        token.name = user.name;
        token.email = user.email;
        token.picture = user.image;
      }
      
      return token;
    },
    async session({ session, token }) {
      // Avec la stratégie JWT, nous utilisons le token mais récupérons les données fraîches depuis la DB
      if (token && session.user) {
        session.user.id = token.sub!;

        // 🚀 Récupérer les données utilisateur avec cache DB intelligent
        if (token.sub) {
          try {
            const startTime = Date.now();

            // 🚀 Essayer le cache DB en premier
            let userData: { id: string; name: string | null; email: string | null; image: string | null } | null = getCachedUserQuery(token.sub, 'profile');

            if (!userData) {
              console.log(`💾 Cache DB miss pour l'utilisateur: ${token.sub}`);

              // Import optimisé
              const { users } = await import("@/lib/db/schema");
              const { db } = await import("@/lib/db");

              // 🚀 Requête optimisée avec sélection spécifique des colonnes
              const dbResult = await db
                .select({
                  id: users.id,
                  name: users.name,
                  email: users.email,
                  image: users.image
                })
                .from(users)
                .where(eq(users.id, token.sub))
                .limit(1);

              const queryTime = Date.now() - startTime;
              console.log(`⚡ Requête DB utilisateur en ${queryTime}ms pour: ${token.sub}`);

              if (dbResult.length > 0) {
                userData = dbResult[0];
                // 🚀 Mettre en cache pour 30 minutes (TTL optimisé automatiquement)
                cacheUserQuery(token.sub, 'profile', userData);
                console.log(`✅ Données utilisateur mises en cache DB (30min): ${token.sub}`);
              }
            } else {
              const cacheTime = Date.now() - startTime;
              console.log(`🚀 Cache DB hit en ${cacheTime}ms pour: ${token.sub}`);
            }

            if (userData) {
              session.user.name = userData.name;
              session.user.email = userData.email;
              session.user.image = userData.image;
            } else {
              console.warn("Utilisateur non trouvé en base:", token.sub);
              // Fallback sur les données du token
              session.user.name = token.name;
              session.user.email = token.email;
              session.user.image = token.picture as string;
            }
          } catch (error) {
            console.error("Erreur lors de la récupération des données utilisateur:", error);
            console.error("Stack trace:", error instanceof Error ? error.stack : error);
            // Fallback sur les données du token
            session.user.name = token.name;
            session.user.email = token.email;
            session.user.image = token.picture as string;
          }
        }
      }
      return session;
    },
    async signIn({ user, account, profile }) {
      console.log("🔐 SignIn callback appelé:", {
        userId: user?.id,
        userEmail: user?.email,
        userName: user?.name,
        provider: account?.provider,
        accountType: account?.type,
        profileData: profile ? Object.keys(profile) : null
      });

      try {
        // Callback appelé à chaque connexion (pas seulement la première fois)
        if (user.id && account?.provider === "spotify") {
          console.log("✅ Connexion Spotify autorisée pour:", user.id);

          // Vérifier si l'utilisateur a aussi un compte Discogs connecté
          // et déclencher une synchronisation si la dernière sync date de plus de 24h
          setTimeout(async () => {
            try {
              await checkAndSyncDiscogsIfNeeded(user.id!);
            } catch (error) {
              console.error("Erreur lors de la vérification Discogs:", error);
            }
          }, 3000); // Attendre 3 secondes après la connexion
        }

        console.log("✅ SignIn callback terminé avec succès");
        return true; // Autoriser la connexion
      } catch (error) {
        console.error("❌ Erreur dans le callback signIn:", error);
        return true; // Autoriser quand même la connexion pour éviter les blocages
      }
    },
  },
  events: {
    async createUser({ user }) {
      // US 1.3 - Événement déclenché lors de la création d'un nouvel utilisateur
      console.log(`👋 Nouvel utilisateur créé: ${user.id} (${user.email})`);
      
      // Note: L'email de bienvenue est maintenant envoyé dans le callback JWT
      // après la détection de langue pour les nouveaux utilisateurs
      if (user.email && user.name) {
        console.log(`📧 Email de bienvenue sera envoyé après détection de langue pour ${user.email}`);
      } else {
        console.warn(`⚠️ Impossible d'envoyer l'email de bienvenue: email ou nom manquant pour l'utilisateur ${user.id}`);
      }
    },
    async linkAccount({ user, account }) {
      // Cet événement se déclenche à chaque liaison de compte OAuth.
      // C'est le moment parfait pour lancer une tâche en arrière-plan.
      if (user.id) {
        if (account?.provider === "spotify") {
          // Déclencher la génération de recommandations en arrière-plan
          console.log(`🎵 Déclenchement automatique de la génération de recommandations pour ${user.id}`);
          // Utiliser setTimeout pour éviter de bloquer la connexion
          setTimeout(async () => {
            try {
              await triggerInitialRecommendationGeneration(user.id!);
            } catch (error) {
              console.error("Erreur lors de la génération automatique:", error);
            }
          }, 2000); // Attendre 2 secondes après la connexion
        } else if (account?.provider === "discogs") {
          // Déclencher la synchronisation de la collection Discogs
          await syncDiscogsCollection(user.id);
        }
      }
    },
  },
};

export const { handlers, auth, signIn, signOut } = NextAuth(authOptions);
