# ✅ Checklist Migration Epic Social V1 → Production

## 🎯 Résumé Exécutif
**Statut** : ✅ **STAGING VALIDÉ** - Prêt pour production  
**Date validation staging** : 20/07/2025  
**Migrations requises** : 5 colonnes + 2 tables + 2 ENUMs + 6 index  

---

## 📋 Pré-Migration (À faire AVANT)

### 🔍 Vérifications Préalables
- [ ] **Tests staging 100% validés** ✅ (Fait le 20/07/2025)
- [ ] **Backup complet base production** ⚠️ (À faire)
- [ ] **API diagnostic déployée** ✅ (Disponible)
- [ ] **Fenêtre maintenance planifiée** ⚠️ (À planifier)

### 📊 État Actuel Production (À vérifier)
```bash
# Vérifier l'état actuel de la production
curl "https://stream2spin-production.vercel.app/api/admin/db-schema-check"
```

**Colonnes manquantes attendues** :
- `users.profile_visibility` ❓
- `users.share_recommendations` ❓  
- `users.share_wishlist` ❓
- `users.share_collection` ❓
- `users.emailOnNewFollower` ❓

**Tables manquantes attendues** :
- `followers` ❓
- `notifications` ❓

---

## 🚀 Migration (Exécution)

### Option A : Migration Automatique (Recommandée)
```bash
# 1. Vérification finale pré-migration
curl "https://stream2spin-production.vercel.app/api/admin/db-schema-check"

# 2. Application migration (si validé sur staging)
curl -X POST "https://stream2spin-production.vercel.app/api/admin/apply-epic-social-migration"

# 3. Vérification post-migration
curl "https://stream2spin-production.vercel.app/api/admin/db-schema-check"
```

### Option B : SQL Manuel (Backup)
```bash
# Appliquer le fichier migrations/add-epic-social-v1-schema.sql
# Via pgAdmin, psql, ou interface Supabase
```

**Temps estimé** : ~90 secondes  
**Impact utilisateurs** : Minimal (backward compatible)

---

## ✅ Post-Migration (Validation)

### 🔍 Tests Automatiques
- [ ] **API diagnostic** : `allTablesExist: true, allColumnsExist: true`
- [ ] **Login/Auth** : Vérifier flux OAuth Spotify fonctionne
- [ ] **Génération recommandations** : Test complet `/generating`

### 🧪 Tests Fonctionnels  
- [ ] **Profils publics** : Accès aux nouvelles pages utilisateur
- [ ] **Social** : Fonctionnalités follow/unfollow (si activées)
- [ ] **Notifications** : Tests système notifications

### 📊 Monitoring
- [ ] **Erreurs Vercel** : Surveiller logs 1h post-migration
- [ ] **Performance DB** : Vérifier temps réponse normal
- [ ] **Utilisateurs actifs** : Confirmer aucun impact négatif

---

## 🚨 Plan de Rollback

En cas de problème critique :
1. **Restaurer backup** base de données
2. **Revenir commit** précédent Epic Social V1  
3. **Redéployer** version stable
4. **Investigation** post-mortem

---

## 📞 Prêt pour Exécution

### ✅ Éléments Validés
- ✅ **Migrations testées** sur staging avec succès
- ✅ **APIs d'administration** déployées et fonctionnelles  
- ✅ **Backward compatibility** assurée
- ✅ **Plan de rollback** documenté

### ⚠️ À Faire
- ⚠️ **Backup production** avant migration
- ⚠️ **Fenêtre maintenance** (2-3 minutes recommandées)
- ⚠️ **Équipe technique** disponible pendant migration

---

**🎯 Prêt pour production - Migrations Epic Social V1 validées !** 