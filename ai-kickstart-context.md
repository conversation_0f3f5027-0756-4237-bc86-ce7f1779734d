Absolument. Cependant, en tant qu'IA, je ne peux pas générer un lien de téléchargement direct.

La meilleure et la plus sûre façon de procéder est que je vous fournisse l'intégralité du contenu du fichier `cursor.md` le plus récent que vous m'avez envoyé. Vous pourrez alors le copier-coller facilement dans un nouveau fichier sur votre ordinateur.

Voici le contenu complet :

---

**Instructions :**
1.  Cliquez sur l'icône "Copier" en haut à droite du bloc de code ci-dessous.
2.  Ouvrez un éditeur de texte (comme VS Code, Notepad, etc.).
3.  Collez le contenu.
4.  Enregistrez le fichier sous le nom `cursor.md`.

```markdown
# Stream2Spin - Documentation Technique Complète

## 🚀 Guide de Démarrage Rapide pour IA

### 🎯 Objectif du Projet
**Stream2Spin** transforme les habitudes d'écoute Spotify en recommandations de vinyles personnalisées. L'application analyse les top tracks des utilisateurs, évite les doublons via Discogs, et propose des liens d'achat affiliés.

### 📋 Checklist de Compréhension
- [ ] **Authentification** : Spotify OAuth uniquement, pas de mots de passe
- [ ] **Algorithme** : Scoring basé sur position dans top tracks (track #1 = 50 points)
- [ ] **Monétisation** : Liens de recherche Amazon avec tag d'affiliation (pas d'API Amazon PAAPI)
- [ ] **Architecture** : Next.js 15 + PostgreSQL + Drizzle ORM
- [ ] **Déploiement** : Vercel avec cron jobs automatiques
- [ ] **Sécurité** : Middleware NextAuth + protection des routes
- [ ] **Performance** : Cache DB + optimisations requêtes
- [ ] **UX** : Glassmorphism + animations + génération interactive
- [ ] **Storybook** : Bibliothèque de composants complète
- [ ] **Profils publics** : Partage des recommandations (Epic 16 implémentée)
- [ ] **Fonctionnalités sociales** : Suivi, feed, notifications (Epic 17 en développement)

### 🔧 Setup Rapide pour Développement
```bash
# 1. Cloner et installer
git clone <repo>
cd Stream2Spin
pnpm install

# 2. Variables d'environnement minimales
cp .env.example .env.local
# Remplir: DATABASE_URL, NEXTAUTH_SECRET, AUTH_SPOTIFY_ID, AUTH_SPOTIFY_SECRET

# 3. Base de données
pnpm db:migrate

# 4. Lancer
pnpm dev

# 5. Storybook (optionnel)
pnpm storybook
```

### 🎵 Workflow Principal
1. **Connexion Spotify** → Récupération top tracks (3 timeframes)
2. **Analyse algorithmique** → Scoring par position + estimation écoutes
3. **Croisement Discogs** → Évitement des doublons
4. **Génération liens** → Liens de recherche Amazon avec tag d'affiliation
5. **Sauvegarde DB** → Recommandations avec liens affiliés
6. **Notifications** → Emails hebdomadaires + push
7. **Partage public** → Profils publics des recommandations

## Vue d'ensemble

**Stream2Spin** est une application web Next.js qui analyse les habitudes d'écoute Spotify des utilisateurs pour leur recommander des vinyles correspondant à leurs goûts musicaux. L'application croise les données Spotify avec les collections Discogs pour éviter les doublons et propose des liens d'achat affiliés.

### Concept Principal
- **Analyse des données Spotify** : Récupération des top tracks sur différentes périodes (court, moyen, long terme)
- **Algorithme de scoring** : Calcul de scores basés sur la fréquence d'écoute et la position dans les classements
- **Évitement des doublons** : Croisement avec les collections Discogs pour ne pas recommander des albums déjà possédés
- **Monétisation** : Liens de recherche Amazon avec tag d'affiliation (API Amazon PAAPI non utilisée actuellement)
- **Expérience utilisateur** : Interface moderne avec glassmorphism et animations
- **Partage public** : Profils publics des recommandations (Epic 16)

## Architecture Technique

### 🏗️ Stack Technologique

```typescript
// 🎯 Technologies principales
- Next.js 15 avec App Router (SSR + SSG)
- React 19 avec Server Components (performance optimale)
- TypeScript strict pour la sécurité des types
- Tailwind CSS avec design system glassmorphism
- PostgreSQL + Drizzle ORM (type-safe queries)
- NextAuth.js v5 pour l'authentification OAuth
- Vercel pour le déploiement serverless
- Supabase pour l'hébergement PostgreSQL

// 🔌 Intégrations API externes
- Spotify Web API (OAuth 2.0) - Top tracks + user profile
- Discogs API (OAuth 1.0a) - Collection synchronization
- Amazon Affiliate Links - Liens de recherche avec tag d'affiliation (pas d'API PAAPI)
- Resend pour les emails transactionnels
- Firebase pour les notifications push (PWA)

// 🎨 UI/UX Framework
- Radix UI pour les composants accessibles
- Lucide React pour les icônes cohérentes
- Framer Motion pour les animations fluides
- Next-intl pour l'internationalisation (FR/EN)
- Zustand pour la gestion d'état client
- Sonner pour les notifications toast

// 📚 Storybook - Bibliothèque de Composants
- Storybook 9.0.16 pour la documentation des composants
- Stories pour tous les composants UI de base
- Stories pour les composants complexes (AlbumCard, Header, Sidebar)
- Documentation du design system avec MDX
- Tests visuels et contrôles interactifs
```

### 📊 Architecture des Données

```mermaid
graph TD
    A[Spotify API] --> B[Top Tracks Analysis]
    C[Discogs API] --> D[Collection Sync]
    B --> E[Scoring Algorithm]
    D --> F[Duplicate Detection]
    E --> G[Recommendations DB]
    F --> G
    G --> H[Amazon Search Links]
    H --> I[Affiliate Links]
    I --> J[User Interface]
    G --> K[Email Notifications]
    G --> L[Push Notifications]
    G --> M[Public Profiles]
```

### Structure du Projet

```
Stream2Spin/
├── app/                        # App Router Next.js 15
│   ├── (auth)/                # Groupe de routes d'authentification
│   │   └── login/
│   ├── account/               # Gestion du compte utilisateur
│   ├── api/                   # Routes API et endpoints
│   │   ├── auth/             # NextAuth.js callbacks
│   │   ├── cron/             # Tâches automatisées
│   │   ├── discogs/          # Intégration Discogs
│   │   ├── recommendations/  # Gestion des recommandations
│   │   ├── user/             # Actions utilisateur
│   ├── recommendations/       # Page principale des recommandations
│   ├── collection/           # Collection Discogs de l'utilisateur
│   ├── wishlist/             # Liste d'envies
│   ├── generating/           # Page de génération interactive
│   ├── public/               # Profils publics (/public/[id])
│   ├── u/                    # Profils publics (/u/[id])
│   └── error/                # Pages d'erreur
├── components/               # Composants React réutilisables
│   ├── account/             # Composants de gestion de compte
│   ├── auth/                # Composants d'authentification
│   ├── audio/               # Lecteur audio global
│   ├── error/               # Gestion d'erreurs
│   ├── generating/          # Expérience de génération
│   ├── layout/              # Layout et navigation
│   ├── public/              # Composants pour profils publics
│   ├── recommendations/     # Composants de recommandations
│   ├── ui/                  # Composants UI de base
│   └── wishlist/            # Composants de wishlist
├── lib/                     # Utilitaires et logique métier
│   ├── db/                  # Configuration et schémas DB
│   ├── album-matching.ts    # Algorithme de matching albums
│   ├── spotify.ts           # Intégration Spotify API
│   ├── auth.ts              # Utilitaires d'authentification
│   ├── amazon-phase1.ts     # Génération liens de recherche Amazon
│   ├── resend.ts            # Gestion des emails
│   └── analytics.ts         # Analytics pour profils publics
├── migrations/              # Scripts de migration DB
├── messages/               # Fichiers de traduction i18n
├── emails/                 # Templates d'email React
├── scripts/                # Scripts de maintenance et sécurité
└── .storybook/             # Configuration Storybook
```

## Base de Données (PostgreSQL + Drizzle ORM)

### 🗄️ Schéma Principal

```typescript
// 📋 Table users - Utilisateurs principaux
export const users = pgTable('users', {
  id: text('id').primaryKey().$defaultFn(() => crypto.randomUUID()),
  name: text('name'),                    // Nom depuis Spotify
  email: text('email').unique(),         // Email depuis Spotify
  emailVerified: timestamp('emailVerified', { mode: 'date' }),
  image: text('image'),                  // Photo de profil Spotify
  preferredLanguage: text('preferredLanguage').default('fr').notNull(),
  emailFrequency: text('emailFrequency').default('weekly').notNull(), // 'weekly' | 'bi-weekly' | 'monthly' | 'never'
  pushFrequency: text('pushFrequency').default('weekly').notNull(),
  firstRecommendationEmailSent: boolean('firstRecommendationEmailSent').default(false),
  
  // 🌐 Epic 16: Partage public des recommandations
  publicListEnabled: boolean('publicListEnabled').default(false).notNull(),
  publicListId: text('publicListId').unique().$defaultFn(() => crypto.randomUUID()),
  
  // 👤 Epic 17: Profil public granulaire
  publicProfileEnabled: boolean('publicProfileEnabled').default(true).notNull(),
  publicRecommendationsEnabled: boolean('publicRecommendationsEnabled').default(true).notNull(),
  publicWishlistEnabled: boolean('publicWishlistEnabled').default(false).notNull(),
  publicCollectionEnabled: boolean('publicCollectionEnabled').default(false).notNull(),
  
  // 📧 Tracking de délivrabilité des emails
  emailNotificationsEnabled: boolean('email_notifications_enabled').default(true),
  lastEmailSent: timestamp('last_email_sent', { mode: 'date' }),
  lastEmailBounce: timestamp('last_email_bounce', { mode: 'date' }),
  emailBounceReason: text('email_bounce_reason'),
  emailDeliverabilityScore: integer('email_deliverability_score').default(100),
  
  timezone: text('timezone').default('Europe/Paris'),
  createdAt: timestamp('createdAt', { mode: 'date' }).defaultNow(),
  updatedAt: timestamp('updatedAt', { mode: 'date' }).defaultNow(),
});

// 🎵 Table recommendations - Recommandations d'albums
export const recommendations = pgTable('recommendations', {
  id: bigserial('id', { mode: 'number' }).primaryKey(),
  userId: text('userId').notNull().references(() => users.id, { onDelete: 'cascade' }),
  artistName: text('artistName').notNull(),
  albumTitle: text('albumTitle').notNull(),
  albumCoverUrl: text('albumCoverUrl'),
  discogsReleaseId: bigint('discogsReleaseId', { mode: 'number' }),
  spotifyAlbumId: text('spotifyAlbumId'),
  listenScore: integer('listenScore').notNull(),        // Score calculé par l'algorithme
  estimatedPlays: integer('estimatedPlays'),            // Nombre d'écoutes estimé basé sur la position
  timeframe: text('timeframe').notNull(),               // 'short_term' | 'medium_term' | 'long_term'
  generatedAt: timestamp('generatedAt', { mode: 'date' }).notNull().defaultNow(),
  isOwned: boolean('isOwned').notNull().default(false), // Marqué via croisement Discogs
  affiliateLinks: jsonb('affiliateLinks'),              // [{vendor, url, price, currency, merchantId}]
  
  // 🎧 US 3.6: Informations du titre phare pour l'extrait audio
  topTrackName: text('topTrackName'),
  topTrackId: text('topTrackId'),                      // ID Spotify pour l'embed
  topTrackPreviewUrl: text('topTrackPreviewUrl'),      // URL de preview 30s
  topTrackListenScore: integer('topTrackListenScore'), // Score du titre phare
}, (table) => ({
  userAlbumTimeframeUnique: unique().on(table.userId, table.spotifyAlbumId, table.timeframe),
}));

// 💿 Table userDiscogsCollection - Collections Discogs synchronisées
export const userDiscogsCollection = pgTable('user_discogs_collection', {
  userId: text('userId').notNull().references(() => users.id, { onDelete: 'cascade' }),
  discogsReleaseId: bigint('discogsReleaseId', { mode: 'number' }).notNull(),
  artistName: text('artistName').notNull(),
  albumTitle: text('albumTitle').notNull(),
  year: integer('year'),
  format: text('format'),                              // 'Vinyl', 'CD', etc.
  notes: text('notes'),                                // Notes utilisateur Discogs
  folder: text('folder'),                              // ID du dossier Discogs
  syncedAt: timestamp('syncedAt', { mode: 'date' }).notNull().defaultNow(),
}, (table) => ({
  pk: primaryKey({ columns: [table.userId, table.discogsReleaseId] }),
}));

// ❤️ Table wishlistItems - Liste d'envies basée sur l'album
export const wishlistItems = pgTable('wishlist_items', {
  userId: text('userId').notNull().references(() => users.id, { onDelete: 'cascade' }),
  artistName: text('artistName').notNull(),
  albumTitle: text('albumTitle').notNull(),
  albumCoverUrl: text('album_cover_url'),
  spotifyAlbumId: text('spotify_album_id'),
  discogsReleaseId: bigint('discogs_release_id', { mode: 'number' }),
  affiliateLinks: jsonb('affiliate_links'),            // Liens d'achat mis à jour
  topTrackName: text('top_track_name'),
  topTrackId: text('top_track_id'),
  topTrackPreviewUrl: text('top_track_preview_url'),
  topTrackListenScore: integer('top_track_listen_score'),
  originalUserName: text('original_user_name'),        // Pour les ajouts depuis profils publics
  createdAt: timestamp('createdAt', { mode: 'date' }).notNull().defaultNow(),
}, (table) => ({
  pk: primaryKey({ columns: [table.userId, table.artistName, table.albumTitle] }),
}));

// 📊 Table publicListAnalytics - Analytics pour profils publics
export const publicListAnalytics = pgTable('public_list_analytics', {
  id: text('id').primaryKey().$defaultFn(() => crypto.randomUUID()),
  userId: text('userId').notNull().references(() => users.id, { onDelete: 'cascade' }),
  publicListId: text('publicListId').notNull().references(() => users.publicListId),
  eventType: text('eventType').notNull(),              // 'view' | 'share' | 'signup_click' | 'signup_conversion'
  eventData: jsonb('eventData'),                       // Données supplémentaires (tab, timeframe, etc.)
  ipAddress: text('ipAddress'),                        // Pour éviter les doublons de vues
  userAgent: text('userAgent'),
  referrer: text('referrer'),
  timestamp: timestamp('timestamp', { mode: 'date' }).defaultNow().notNull(),
});

// 👥 Epic 17: Fonctionnalités sociales (en développement)
// export const followers = pgTable('followers', {
//   followerId: text('followerId').notNull().references(() => users.id, { onDelete: 'cascade' }),
//   followingId: text('followingId').notNull().references(() => users.id, { onDelete: 'cascade' }),
//   createdAt: timestamp('createdAt', { mode: 'date' }).defaultNow().notNull(),
// }, (table) => ({
//   pk: primaryKey({ columns: [table.followerId, table.followingId] }),
// }));

// export const feedItems = pgTable('feed_items', {
//   id: text('id').primaryKey().$defaultFn(() => crypto.randomUUID()),
//   ownerId: text('ownerId').notNull().references(() => users.id, { onDelete: 'cascade' }),
//   eventType: text('eventType').notNull(),              // 'NEW_RECOMMENDATION' | 'NEW_WISHLIST_ITEM' | 'NEW_COLLECTION_ITEM'
//   eventData: jsonb('eventData'),                       // Données de l'événement (album, etc.)
//   publishedAt: timestamp('publishedAt', { mode: 'date' }).defaultNow().notNull(),
// });

// export const notifications = pgTable('notifications', {
//   id: text('id').primaryKey().$defaultFn(() => crypto.randomUUID()),
//   recipientId: text('recipientId').notNull().references(() => users.id, { onDelete: 'cascade' }),
//   type: text('type').notNull(),                        // 'NEW_FOLLOWER' | 'WISHLIST_DIGEST' | 'COMMUNITY_DIGEST'
//   payload: jsonb('payload'),                           // Données de la notification
//   isRead: boolean('isRead').default(false).notNull(),
//   createdAt: timestamp('createdAt', { mode: 'date' }).defaultNow().notNull(),
// });
```

## 🧠 Algorithme de Recommandation

### 🎯 Vue d'ensemble de l'Algorithme

L'algorithme de recommandation de Stream2Spin fonctionne en 4 étapes principales :

1. **Récupération des données Spotify** (3 timeframes)
2. **Calcul des scores par position** (track #1 = 50 points)
3. **Croisement avec collection Discogs** (évitement des doublons)
4. **Enrichissement avec liens d'achat** (Amazon uniquement)

### 📊 Formule de Scoring

```typescript
// Score = 51 - position (track #1 = 50 points, track #50 = 1 point)
const trackScore = 51 - position;

// Estimation des écoutes basée sur la position
const estimatedPlaysForTrack = Math.max(1, Math.round((51 - position) * 2.5));

// Score total d'un album = somme des scores de tous ses tracks
// Albums avec plusieurs tracks du même artiste ont un score plus élevé
```

### 1. Récupération des Données Spotify

```typescript
/**
 * Récupère les top tracks d'un utilisateur via l'API Spotify
 * 3 timeframes: short_term (4 semaines), medium_term (6 mois), long_term (toute la vie)
 */
export async function fetchUserTopTracks(
  accessToken: string,
  timeRange: "short_term" | "medium_term" | "long_term" = "short_term",
  limit: number = 50
): Promise<SpotifyTrack[] | null> {
  const url = `https://api.spotify.com/v1/me/top/tracks?limit=${limit}&time_range=${timeRange}`;
  
  const response = await fetch(url, {
    headers: {
      "Authorization": `Bearer ${accessToken}`,
      "Content-Type": "application/json",
    },
  });

  if (!response.ok) {
    throw new Error(`Erreur API Spotify: ${response.status}`);
  }

  const data: SpotifyTopTracksResponse = await response.json();
  return data.items || [];
}
```

### 2. Calcul des Scores d'Albums

```typescript
/**
 * 🧮 Algorithme de scoring des albums basé sur les top tracks Spotify
 * 
 * Principe: Plus un track est haut dans le classement, plus il contribue au score de l'album
 * - Track #1 = 50 points, Track #50 = 1 point
 * - Albums avec plusieurs tracks du même artiste ont un score cumulé
 * - Sélection du "titre phare" pour l'extrait audio
 */
export function analyzeTracksAndCalculateScores(tracks: SpotifyTrack[], timeframe: string) {
  const albumScores = new Map<string, any>();

  tracks.forEach((track, index) => {
    const album = track.album;
    if (!album) return;

    const albumKey = album.id;
    const position = index + 1; // Position dans le classement (1-50)

    // 🎯 Calcul du score : plus la position est haute, plus le score est élevé
    const trackScore = 51 - position; // Track #1 = 50 points, Track #50 = 1 point

    // 📊 Estimation du nombre d'écoutes basée sur la position
    const estimatedPlaysForTrack = Math.max(1, Math.round((51 - position) * 2.5));

    if (albumScores.has(albumKey)) {
      // Album déjà présent, additionner le score et les écoutes estimées
      const existingAlbum = albumScores.get(albumKey);
      existingAlbum.listenScore += trackScore;
      existingAlbum.estimatedPlays += estimatedPlaysForTrack;

      // 🎧 Stratégie pour le titre phare (meilleur score + preview URL)
      const shouldUpdateTopTrack =
        // Cas 1: Ce track a un meilleur score
        trackScore > (existingAlbum.topTrackListenScore || 0) ||
        // Cas 2: Score égal mais ce track a une preview_url et l'actuel n'en a pas
        (trackScore === (existingAlbum.topTrackListenScore || 0) &&
         track.preview_url && !existingAlbum.topTrackPreviewUrl) ||
        // Cas 3: L'actuel n'a pas de preview_url et ce track en a une
        (!existingAlbum.topTrackPreviewUrl && track.preview_url);

      if (shouldUpdateTopTrack) {
        existingAlbum.topTrackName = track.name;
        existingAlbum.topTrackId = track.id;
        existingAlbum.topTrackPreviewUrl = track.preview_url;
        existingAlbum.topTrackListenScore = trackScore;
      }
    } else {
      // Nouvel album
      albumScores.set(albumKey, {
        spotifyAlbumId: album.id,
        artistName: album.artists?.name || "Unknown Artist",
        albumTitle: album.name,
        albumCoverUrl: album.images?.url || null,
        listenScore: trackScore,
        estimatedPlays: estimatedPlaysForTrack,
        timeframe: timeframe,
        // 🎧 Titre phare (premier track de cet album)
        topTrackName: track.name,
        topTrackId: track.id,
        topTrackPreviewUrl: track.preview_url,
        topTrackListenScore: trackScore,
      });
    }
  });

  // 🏆 Retourner les 50 meilleurs albums triés par score décroissant
  return Array.from(albumScores.values())
    .sort((a, b) => b.listenScore - a.listenScore)
    .slice(0, 50);
}
```

### 3. Croisement avec les Collections Discogs

```typescript
/**
 * Marque les recommandations comme possédées en croisant avec la collection Discogs
 */
export function markOwnedAlbums(
  recommendations: SpotifyRecommendation[],
  discogsCollection: DiscogsCollectionItem[]
): (SpotifyRecommendation & { isOwned: boolean })[] {
  // Créer un Set de clés normalisées pour la collection Discogs (recherche O(1))
  const normalizedCollection = new Set<string>();
  
  discogsCollection.forEach(item => {
    const normalizedArtist = normalizeArtistName(item.artistName);
    const normalizedAlbum = normalizeAlbumTitle(item.albumTitle);
    const exactKey = `${normalizedArtist}|${normalizedAlbum}`;
    normalizedCollection.add(exactKey);
  });

  // Marquer chaque recommandation comme possédée ou non
  return recommendations.map(recommendation => {
    const normalizedSpotifyArtist = normalizeArtistName(recommendation.artistName);
    const normalizedSpotifyAlbum = normalizeAlbumTitle(recommendation.albumTitle);
    const exactKey = `${normalizedSpotifyArtist}|${normalizedSpotifyAlbum}`;
    
    return {
      ...recommendation,
      isOwned: normalizedCollection.has(exactKey)
    };
  });
}
```

## 🔐 Système d'Authentification

### 🎯 Stratégie d'Authentification

Stream2Spin utilise **Spotify OAuth uniquement** comme méthode d'authentification :
- **Pas de mots de passe** - sécurité maximale
- **Données utilisateur** récupérées automatiquement depuis Spotify
- **Tokens gérés** automatiquement par NextAuth.js
- **Sessions JWT** pour les performances

### Configuration NextAuth.js

```typescript
// auth.ts
export const authOptions: AuthOptions = {
  adapter: DrizzleAdapter(db, {
    usersTable: users,
    accountsTable: accounts,
    sessionsTable: sessions,
    verificationTokensTable: verificationTokens,
  }),
  session: { strategy: "jwt" },
  pages: {
    signIn: "/login",
  },
  providers: [
    SpotifyProvider({
      clientId: process.env.AUTH_SPOTIFY_ID!,
      clientSecret: process.env.AUTH_SPOTIFY_SECRET!,
      authorization: {
        params: {
          scope: "user-read-email user-top-read user-read-private",
        },
      },
      allowDangerousEmailAccountLinking: true, // Permet la liaison automatique des comptes
      profile(profile) {
        return {
          id: profile.id,
          name: profile.display_name,
          email: profile.email,
          image: profile.images?.?.url || null,
        };
      },
    }),
  ],
  callbacks: {
    async jwt({ token, user }) {
      if (user) {
        token.sub = user.id;
        token.name = user.name;
        token.email = user.email;
        token.picture = user.image;
      }
      return token;
    },
    async session({ session, token }) {
      if (token && session.user) {
        session.user.id = token.sub!;
        
        // 🚀 Récupérer les données fraîches depuis la DB avec cache optimisé
        const userData = getCachedUserQuery(token.sub!, 'profile');
        if (userData) {
          session.user.name = userData.name;
          session.user.email = userData.email;
          session.user.image = userData.image;
        }
      }
      return session;
    },
  },
};
```

## 🌐 Fonctionnalités de Partage Public (Epic 16)

### Profils Publics et Partage

```typescript
// Epic 16: Partage public des recommandations (implémenté)
interface PublicProfile {
  publicListEnabled: boolean;
  publicListId: string;
  publicProfileEnabled: boolean;
  publicRecommendationsEnabled: boolean;
  publicWishlistEnabled: boolean;
  publicCollectionEnabled: boolean;
}

// Epic 17: Système de suivi et communauté (en développement)
// interface SocialFeatures {
//   followers: User[];
//   following: User[];
//   feedItems: FeedItem[];
//   notifications: Notification[];
// }
```

### Système de Partage Public

```typescript
// actions/profile.ts
export async function updateProfileVisibility(
  publicProfileEnabled: boolean,
  publicRecommendationsEnabled: boolean,
  publicWishlistEnabled: boolean,
  publicCollectionEnabled: boolean
) {
  const session = await getServerSession(authOptions);
  if (!session?.user?.id) throw new Error("Non autorisé");

  await db.update(users)
    .set({
      publicProfileEnabled,
      publicRecommendationsEnabled,
      publicWishlistEnabled,
      publicCollectionEnabled,
    })
    .where(eq(users.id, session.user.id));

  revalidatePath('/account');
}
```

## 📚 Storybook - Bibliothèque de Composants

### Configuration Storybook

```typescript
// .storybook/main.ts
const config: StorybookConfig = {
  stories: ["../components/**/*.stories.@(js|jsx|mjs|ts|tsx)"],
  addons: [
    "@storybook/addon-links",
    "@storybook/addon-docs",
    "@chromatic-com/storybook"
  ],
  framework: {
    name: "@storybook/nextjs-vite",
    options: {},
  },
  staticDirs: ["../public"],
};
```

### Composants Documentés

```typescript
// Composants UI de base (atomes)
- Button (variants: default, destructive, outline, secondary, ghost, link)
- Input (types: text, email, password, search, number, tel, url)
- Card (avec CardHeader, CardContent, CardFooter)
- Avatar (avec image et fallback)
- Badge (variants: default, secondary, destructive, outline)

// Composants complexes (molécules)
- AlbumCard (états: default, owned, noLinks, longTitles)
- Header (états: loggedIn, loggedOut)
- Sidebar (avec navigation et session)
- UserNav (avec menu utilisateur)

// Layout et navigation
- Layout components avec décorateurs pour simuler les contextes
- Stories avec contrôles interactifs
- Documentation MDX du design system
```

### Exemple de Story

```typescript
// components/ui/button.stories.tsx
const meta = {
  title: 'Design System/UI/Button',
  component: Button,
  parameters: { layout: 'centered' },
  tags: ['autodocs'],
  argTypes: {
    variant: {
      control: 'select',
      options: ['default', 'destructive', 'outline', 'secondary', 'ghost', 'link'],
    },
    size: {
      control: 'select',
      options: ['default', 'sm', 'lg', 'icon'],
    },
  },
} satisfies Meta<typeof Button>;

export const Primary: Story = {
  args: {
    variant: 'default',
    children: 'Primary Action',
  },
};
```

## 🔒 Sécurité et Production

### Sécurisation Complète (Epic 13)

```typescript
// lib/debug-protection.ts
export function withDebugProtection<T extends (...args: any[]) => any>(
  fn: T,
  options: { requireAuth?: boolean; requireAdmin?: boolean } = {}
): T {
  return ((...args: Parameters<T>) => {
    if (process.env.NODE_ENV === 'production') {
      throw new Error('Fonctionnalité de debug non disponible en production');
    }
    return fn(...args);
  }) as T;
}
```

### Scripts de Sécurité

```json
{
  "scripts": {
    "security:cleanup": "node scripts/cleanup-debug-routes.js",
    "security:validate": "node scripts/validate-production-ready.js",
    "security:test": "node scripts/run-security-tests.js",
    "security:deploy": "./scripts/complete-security-deployment.sh",
    "storybook": "storybook dev -p 6006",
    "build-storybook": "storybook build"
  }
}
```

## 📧 Système de Notifications

### Emails Transactionnels

```typescript
// lib/resend.ts
export async function sendRecommendationsEmail(
  user: User,
  recommendations: Recommendation[]
) {
  const html = await render(
    <RecommendationsEmail
      user={user}
      recommendations={recommendations}
    />
  );

  await resend.emails.send({
    from: 'Stream2Spin <<EMAIL>>',
    to: user.email,
    subject: 'Vos nouvelles recommandations vinyles',
    html: html,
    tags: [{ name: 'category', value: 'recommendations' }],
  });
}
```

### Notifications Push (Firebase)

```typescript
// lib/firebase-messaging.ts
export async function sendPushNotification(
  userId: string,
  title: string,
  body: string,
  data?: Record<string, string>
) {
  const userTokens = await getUserFCMTokens(userId);
  
  if (userTokens.length === 0) return;

  const message = {
    notification: { title, body },
    data,
    tokens: userTokens,
  };

  const response = await admin.messaging().sendMulticast(message);
  return response;
}
```

## 🚀 Déploiement et Monitoring

### Configuration Vercel

```json
// vercel.json
{
  "functions": {
    "app/api/cron/**/*.ts": {
      "maxDuration": 300
    }
  },
  "crons": [
    {
      "path": "/api/cron/generate-recommendations",
      "schedule": "0 3 * * 0"
    },
    {
      "path": "/api/cron/send-notifications", 
      "schedule": "30 9 * * 4"
    }
  ]
}
```

### Variables d'Environnement

```bash
# Base de données
DATABASE_URL="postgresql://..."

# Authentification
NEXTAUTH_SECRET="your-nextauth-secret"
NEXTAUTH_URL="https://stream2spin.com"

# Spotify API
AUTH_SPOTIFY_ID="your-spotify-client-id"
AUTH_SPOTIFY_SECRET="your-spotify-client-secret"

# Discogs API
AUTH_DISCOGS_KEY="your-discogs-consumer-key"
AUTH_DISCOGS_SECRET="your-discogs-consumer-secret"

# Amazon Affiliate (Phase 1 - liens de recherche uniquement)
AMAZON_AFFILIATE_TAG="your-amazon-affiliate-tag"

# Email
RESEND_API_KEY="your-resend-api-key"

# Cron Jobs
CRON_SECRET="your-cron-secret"

# Firebase (Push Notifications)
FIREBASE_PROJECT_ID="your-firebase-project-id"
FIREBASE_PRIVATE_KEY="your-firebase-private-key"
FIREBASE_CLIENT_EMAIL="your-firebase-client-email"
```

## Structure des Composants UI

### 1. Design System

```typescript
// components/ui/button.tsx
import { cva } from 'class-variance-authority';
// ...
const buttonVariants = cva(
  "inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium...",
  {
    variants: {
      variant: {
        default: "bg-primary text-primary-foreground hover:bg-primary/90",
        // ... autres variantes
      },
      size: {
        default: "h-10 px-4 py-2",
        // ... autres tailles
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
);```

### 2. Layout Authentifié

```typescript
// components/layout/authenticated-layout.tsx
import { useSession } from "next-auth/react";
import { usePathname } from "next/navigation";
// ...
export function AuthenticatedLayout({ children }: AuthenticatedLayoutProps) {
  const { data: session } = useSession();
  const pathname = usePathname();

  const isLoginPage = pathname === "/login";
  const isGeneratingPage = pathname === "/generating";
  const isPublicPage = pathname.startsWith('/u/');

  return (
    <div className="min-h-screen bg-background">
      <Sidebar />
      <Header />
      <main className={
        isLoginPage || isGeneratingPage || isPublicPage
          ? ""
          : session?.user
            ? "md:ml-64 pt-16"
            : "pt-16"
      }>
        {children}
      </main>
    </div>
  );
}
```

## Workflow de Développement

### 1. Scripts Disponibles

```json
{
  "scripts": {
    "dev": "next dev -p 3000 --turbo",
    "build": "next build",
    "start": "next start",
    "lint": "next lint",
    "security:cleanup": "node scripts/cleanup-debug-routes.js",
    "security:validate": "node scripts/validate-production-ready.js",
    "security:test": "node scripts/run-security-tests.js",
    "security:deploy": "./scripts/complete-security-deployment.sh",
    "storybook": "storybook dev -p 6006",
    "build-storybook": "storybook build"
  }
}
```

### 2. Processus de Déploiement

```bash
# 1. Nettoyage et sécurisation
npm run security:cleanup

# 2. Validation avant déploiement
npm run security:validate

# 3. Build de production
npm run build

# 4. Déploiement automatisé
npm run security:deploy
```

## Monitoring et Analytics

### 1. Analytics des Profils Publics

```typescript
/**
 * Enregistrement des événements analytics
 */
export async function trackPublicListEvent(
  publicListId: string,
  eventType: 'view' | 'share' | 'signup_click' | 'signup_conversion',
  eventData?: any
) {
  // ... Logique pour traquer les événements
}
```

### 2. Métriques de Performance

```typescript
/**
 * Métriques de performance pour l'admin
 */
export async function getPerformanceMetrics() {
  // ... Logique pour récupérer les métriques
}
```

## 🚀 Guide de Développement pour IA

### 🎯 Patterns de Code Importants

#### 1. Gestion des Erreurs
```typescript
// Pattern standard pour les API routes
try {
  // Logique métier
  return NextResponse.json({ success: true, data });
} catch (error) {
  console.error("Erreur:", error);
  return NextResponse.json(
    { success: false, error: "Erreur interne" },
    { status: 500 }
  );
}
```

#### 2. Cache et Performance
```typescript
// Cache DB avec TTL automatique
const userData = getCachedUserQuery(userId, 'profile');
if (!userData) {
  // Requête DB + mise en cache
  cacheUserQuery(userId, 'profile', userData, 30 * 60 * 1000); // 30min
}
```

#### 3. Validation des Données
```typescript
// Validation avec Zod (exemple, non présent mais recommandé)
import { z } from "zod";

const schema = z.object({
  userId: z.string().uuid(),
  timeframe: z.enum(['short_term', 'medium_term', 'long_term']),
});
const validatedData = schema.parse(requestData);
```

### 🐛 Debugging Commun

- **Tokens Spotify expirés :** Vérifier que `getValidSpotifyToken` est bien appelé.
- **Recommandations vides :** Confirmer le scope Spotify (`user-top-read`).
- **Emails non envoyés :** Valider la clé API `RESEND_API_KEY` et les préférences email de l'utilisateur.
- **Storybook ne démarre pas :** Vérifier le port 6006 et utiliser le port alternatif si nécessaire.

### 📚 Ressources de Développement

#### Storybook
- **URL locale :** http://localhost:6006
- **Commandes :** `pnpm storybook` (dev) / `pnpm build-storybook` (build)
- **Structure :** Composants organisés par catégorie (UI, Layout, Components)
- **Documentation :** Stories avec contrôles interactifs et documentation MDX

#### Fonctionnalités de Partage Public
- **Profils publics :** `/u/[publicListId]` et `/public/[publicListId]` (Epic 16 implémentée)
- **Contrôles de visibilité :** Paramètres granulaires pour chaque section
- **Analytics :** Suivi des vues et interactions sur les profils publics

#### Sécurité
- **Protection debug :** Fonction `withDebugProtection` pour les environnements de production
- **Scripts de validation :** Tests automatisés de sécurité avant déploiement
- **Middleware sécurisé :** Logs conditionnels et protection des routes sensibles

Cette documentation complète permet à une IA de comprendre rapidement l'architecture, les patterns de code, et les bonnes pratiques du projet Stream2Spin, incluant les dernières fonctionnalités sociales et la bibliothèque Storybook.
```

---

C'est la méthode la plus fiable pour vous assurer d'avoir la version exacte du fichier que nous avons analysée ensemble.