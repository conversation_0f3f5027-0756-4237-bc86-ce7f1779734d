{"crons": [{"path": "/api/cron/generate-recommendations", "schedule": "30 0 * * 4"}, {"path": "/api/cron/send-notifications", "schedule": "30 9 * * 4"}], "functions": {"app/api/cron/generate-recommendations/route.ts": {"maxDuration": 300}, "app/api/cron/send-notifications/route.ts": {"maxDuration": 300}}, "headers": [{"source": "/api/cron/(.*)", "headers": [{"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Authorization, Content-Type"}, {"key": "X-Vercel-Auth-Bypass", "value": "true"}]}]}