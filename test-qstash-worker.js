require('dotenv').config({ path: '.env.local' });
const { Client } = require('@upstash/qstash');

async function testQueueWorker() {
  console.log('--- Début du test du worker QStash ---');

  if (!process.env.QSTASH_TOKEN) {
    console.error('Erreur: La variable d\'environnement QSTASH_TOKEN est manquante.');
    return;
  }

  const qstash = new Client({
    token: process.env.QSTASH_TOKEN,
  });

  const userIdToTest = '33d9-2260-869f-4d7b-aac2-94ff6b03c240'; // Un ID utilisateur valide

  try {
    console.log(`Envoi d'un job de test pour l'utilisateur: ${userIdToTest}`);
    
    const response = await qstash.publishJSON({
      // IMPORTANT: QStash doit pouvoir atteindre cette URL.
      // En développement local, cela nécessite un tunnel comme ngrok.
      // Pour ce test, nous allons juste vérifier si l'envoi à l'API QStash réussit.
      url: `${process.env.NEXTAUTH_URL}/api/queues/refresh-recommendations`,
      body: { userId: userIdToTest },
    });

    console.log('✅ Job envoyé avec succès à l\'API QStash !');
    console.log('Réponse de QStash:', response);
    console.log('\n--- Fin du test ---');
    console.log('NOTE: Pour que le worker s\'exécute, votre serveur local (port 3000) doit être accessible publiquement (via ngrok par exemple).');

  } catch (error) {
    console.error('❌ Erreur lors de l\'envoi du job à QStash:', error);
  }
}

testQueueWorker();
