# 🚀 Plan de Migration Epic Social V1 vers Production

## 📋 Résumé Exécutif

Ce document détaille les migrations nécessaires pour déployer Epic Social V1 en production. Les migrations ont été **testées et validées sur staging** le 20/07/2025.

## 🎯 Objectif

Migrer la base de données production pour supporter les fonctionnalités sociales :
- Profils publics avec contrôle de visibilité
- Système de suivi (followers/following)
- Notifications in-app et email
- Partage sélectif des recommandations/wishlist/collection

## 🔍 État Actuel

### ✅ **Staging** (validé le 20/07/2025)
- ✅ Toutes les migrations Epic Social V1 appliquées
- ✅ 5 nouvelles colonnes dans `users`
- ✅ 2 nouvelles tables : `followers`, `notifications`  
- ✅ 2 nouveaux ENUMs : `profile_visibility`, `notification_type`
- ✅ 6 index de performance créés

### ❓ **Production** (à vérifier)
- ❓ Migrations Epic Social V1 probablement manquantes
- ❓ Colonnes `profile_visibility` etc. probablement absentes

## 📦 Migrations à Appliquer

### 1. **ENUMs** (Types personnalisés)

```sql
-- ENUM pour la visibilité des profils
DO $$ BEGIN
    CREATE TYPE profile_visibility AS ENUM ('private', 'users_only', 'public');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- ENUM pour les types de notifications
DO $$ BEGIN
    CREATE TYPE notification_type AS ENUM ('new_follower');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;
```

### 2. **Table Users** (5 nouvelles colonnes)

```sql
-- Contrôle de visibilité du profil
ALTER TABLE users ADD COLUMN IF NOT EXISTS "profile_visibility" profile_visibility NOT NULL DEFAULT 'users_only';

-- Contrôles de partage granulaire
ALTER TABLE users ADD COLUMN IF NOT EXISTS "share_recommendations" BOOLEAN NOT NULL DEFAULT TRUE;
ALTER TABLE users ADD COLUMN IF NOT EXISTS "share_wishlist" BOOLEAN NOT NULL DEFAULT TRUE;
ALTER TABLE users ADD COLUMN IF NOT EXISTS "share_collection" BOOLEAN NOT NULL DEFAULT TRUE;

-- Notifications email pour nouveaux followers
ALTER TABLE users ADD COLUMN IF NOT EXISTS "emailOnNewFollower" BOOLEAN NOT NULL DEFAULT TRUE;
```

### 3. **Table Followers** (Relations de suivi)

```sql
CREATE TABLE IF NOT EXISTS followers (
    follower_id TEXT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    following_id TEXT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    created_at TIMESTAMP DEFAULT NOW() NOT NULL,
    PRIMARY KEY (follower_id, following_id)
);
```

### 4. **Table Notifications** (Notifications in-app)

```sql
CREATE TABLE IF NOT EXISTS notifications (
    id TEXT PRIMARY KEY DEFAULT gen_random_uuid(),
    recipient_id TEXT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    actor_id TEXT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    type notification_type NOT NULL,
    is_read BOOLEAN DEFAULT FALSE NOT NULL,
    created_at TIMESTAMP DEFAULT NOW() NOT NULL
);
```

### 5. **Index de Performance**

```sql
-- Index pour optimiser les requêtes users
CREATE INDEX IF NOT EXISTS idx_users_profile_visibility ON users(profile_visibility);
CREATE INDEX IF NOT EXISTS idx_users_share_recommendations ON users(share_recommendations);

-- Index pour optimiser les requêtes followers
CREATE INDEX IF NOT EXISTS idx_followers_follower_id ON followers(follower_id);
CREATE INDEX IF NOT EXISTS idx_followers_following_id ON followers(following_id);
CREATE INDEX IF NOT EXISTS idx_followers_created_at ON followers(created_at);

-- Index pour optimiser les requêtes notifications
CREATE INDEX IF NOT EXISTS idx_notifications_recipient_id ON notifications(recipient_id);
CREATE INDEX IF NOT EXISTS idx_notifications_is_read ON notifications(is_read);
CREATE INDEX IF NOT EXISTS idx_notifications_created_at ON notifications(created_at);
```

## 🛠️ Méthodes d'Application

### Option A : API Automatique (Recommandée)
```bash
# Vérification préalable
curl "https://stream2spin-production.vercel.app/api/admin/full-schema-check"

# Application automatique (SI staging validé)
curl -X POST "https://stream2spin-production.vercel.app/api/admin/apply-epic-social-migration"
```

### Option B : SQL Manuel
Exécuter le fichier `migrations/add-epic-social-v1-schema.sql` via :
- pgAdmin / Interface Supabase
- CLI psql
- Drizzle push

### Option C : Drizzle Migration
```bash
npm run db:push  # Push du schéma Drizzle vers production
```

## ⚠️ Précautions de Sécurité

### Pré-Migration
1. **Backup complet** de la base production
2. **Vérification** que staging fonctionne parfaitement
3. **Test** de la migration sur une copie de production
4. **Validation** des nouvelles fonctionnalités sociales

### Pendant la Migration
1. **Fenêtre de maintenance** courte (estimée : 2-3 minutes)
2. **Monitoring** des erreurs et performances
3. **Rollback plan** préparé si problème critique

### Post-Migration
1. **Tests fonctionnels** des nouvelles features
2. **Vérification** de la compatibilité ascendante
3. **Monitoring** des performances 24h

## 📊 Impact Estimé

### Temps d'Application
- **ENUMs** : ~10 secondes
- **Colonnes users** : ~30 secondes  
- **Nouvelles tables** : ~20 secondes
- **Index** : ~30 secondes
- **Total estimé** : ~90 secondes

### Compatibilité
- ✅ **Backward compatible** : Code existant continue de fonctionner
- ✅ **Valeurs par défaut** : Colonnes avec defaults sûrs
- ✅ **Références** : Foreign keys avec CASCADE appropriés

## 🎯 Validation Post-Migration

### Tests Automatiques
```bash
# Vérification complète du schéma
curl "https://stream2spin-production.vercel.app/api/admin/full-schema-check"

# Doit retourner : "allTablesExist": true, "allColumnsExist": true
```

### Tests Fonctionnels
1. **Login/OAuth** : Vérifier que l'auth fonctionne toujours
2. **Profils publics** : Tester les nouveaux profils sociaux
3. **Recommendations** : Valider que la génération marche
4. **Social** : Tester follow/unfollow, notifications

## 📋 Checklist Déploiement

### Pré-Déploiement
- [ ] Backup base production effectué
- [ ] Tests staging validés à 100%
- [ ] Fenêtre de maintenance planifiée
- [ ] Équipe technique disponible

### Déploiement
- [ ] Exécution des migrations
- [ ] Vérification schéma via API
- [ ] Tests fonctionnels rapides
- [ ] Monitoring erreurs activé

### Post-Déploiement
- [ ] Tests complets des nouvelles features
- [ ] Validation performances
- [ ] Communication aux utilisateurs
- [ ] Surveillance 24h

## 🚨 Plan de Rollback

En cas de problème critique :

1. **Restaurer backup** de la base
2. **Revenir** au commit précédent Epic Social V1
3. **Redéployer** la version stable
4. **Analyser** les causes et planifier correction

## 📞 Contacts

- **Lead Développeur** : Simon Gavelle
- **DBA** : [À définir]
- **DevOps** : [À définir]

---

**Date de création** : 20/07/2025  
**Dernière mise à jour** : 20/07/2025  
**Version** : 1.0  
**Statut** : Prêt pour production 