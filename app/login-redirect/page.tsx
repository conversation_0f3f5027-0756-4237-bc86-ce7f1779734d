import { redirect } from "next/navigation";
import { getSession, hasRecentRecommendations } from "@/lib/auth";
import { addToWishlistFromPublic } from "@/app/actions/wishlist";

/**
 * Page de redirection intelligente après connexion
 * Vérifie la fraîcheur des recommandations et redirige en conséquence
 * Gère aussi l'ajout automatique à la wishlist depuis les pages publiques (US 16.4)
 */
export default async function LoginRedirectPage({
  searchParams
}: {
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>
}) {
  const session = await getSession();

  if (!session?.user) {
    // Si pas de session, rediriger vers login
    redirect("/login");
  }

  const userId = (session.user as any)?.id;

  if (!userId) {
    // Si pas d'ID utilisateur, rediriger vers login
    redirect("/login");
  }

  const params = await searchParams;

  // Gérer l'ajout automatique à la wishlist depuis une page publique (US 16.4)
  if (params.from === 'public' && params.album && params.artist) {
    try {
      const albumData = {
        artistName: decodeURIComponent(params.artist as string),
        albumTitle: decodeURIComponent(params.album as string),
        albumCoverUrl: params.cover ? decodeURIComponent(params.cover as string) : null,
        spotifyAlbumId: params.spotifyId ? decodeURIComponent(params.spotifyId as string) : null,
        discogsReleaseId: params.discogsId ? parseInt(params.discogsId as string) : null,
      };

      await addToWishlistFromPublic(albumData);

      // Rediriger vers la page publique avec un message de succès
      if (params.publicListId) {
        redirect(`/u/${params.publicListId}?wishlist=added`);
      }
    } catch (error) {
      console.error("Erreur lors de l'ajout automatique à la wishlist:", error);
      // Continuer avec la redirection normale en cas d'erreur
    }
  }

  let hasRecent = false;

  try {
    // Vérifier si l'utilisateur a des recommandations récentes (moins de 24h)
    hasRecent = await hasRecentRecommendations(userId);
  } catch (error) {
    console.error("Erreur lors de la vérification des recommandations récentes:", error);
    // En cas d'erreur, considérer comme non récent pour forcer la régénération
    hasRecent = false;
  }

  // Redirection en dehors du try/catch pour éviter de capturer l'exception NEXT_REDIRECT
  if (hasRecent) {
    if (process.env.NODE_ENV === 'development') {
    console.log(`✅ Utilisateur ${userId} a des recommandations récentes → Redirection vers /recommendations`);
    }
    redirect("/recommendations");
  } else {
    if (process.env.NODE_ENV === 'development') {
    console.log(`🔄 Utilisateur ${userId} n'a pas de recommandations récentes → Redirection vers /generating`);
    }
    redirect("/generating");
  }
}
