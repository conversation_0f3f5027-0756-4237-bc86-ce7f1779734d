"use client";

import { ErrorStatePage } from "@/components/error/error-state-page";
import { ServerErrorIllustration } from "@/components/error/error-illustrations";
import { useErrorRedirect } from "@/hooks/use-error-redirect";
import { useEffect } from "react";

interface ErrorPageProps {
  error: Error & { digest?: string };
  reset: () => void;
}

export default function Error({ error, reset }: ErrorPageProps) {
  const { ctaText, ctaLink } = useErrorRedirect();

  useEffect(() => {
    // Log l'erreur côté client (sans données sensibles)
    console.error("Application error:", {
      message: error.message,
      digest: error.digest,
      timestamp: new Date().toISOString(),
    });
  }, [error]);

  return (
    <div className="container py-12">
      <ErrorStatePage
        illustration={<ServerErrorIllustration />}
        title="Ça patine un peu..."
        message="Il y a un petit souci technique de notre côté. Notre équipe est déjà sur le coup. Réessayez dans un instant."
        ctaText={ctaText}
        ctaLink={ctaLink}
      />
    </div>
  );
}
