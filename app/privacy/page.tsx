import { getTranslations } from 'next-intl/server';

export default async function PrivacyPage() {
  const t = await getTranslations('privacy');

  return (
    <div className="container py-12">
      <h1 className="text-3xl font-bold mb-6">{t('title')}</h1>
      
      <div className="prose prose-sm sm:prose lg:prose-lg">
        <h2>{t('introduction.title')}</h2>
        <p>{t('introduction.content')}</p>
        
        <h2>{t('dataCollection.title')}</h2>
        <p>{t('dataCollection.intro')}</p>
        <ul>
          {t.raw('dataCollection.items').map((item: string, index: number) => (
            <li key={index}>{item}</li>
          ))}
        </ul>
        
        <h2>{t('dataUsage.title')}</h2>
        <p>{t('dataUsage.intro')}</p>
        <ul>
          {t.raw('dataUsage.items').map((item: string, index: number) => (
            <li key={index}>{item}</li>
          ))}
        </ul>
        
        <h2>{t('dataSharing.title')}</h2>
        <p>{t('dataSharing.intro')}</p>
        <ul>
          {t.raw('dataSharing.items').map((item: string, index: number) => (
            <li key={index}>{item}</li>
          ))}
        </ul>
        
        <h2>{t('dataSecurity.title')}</h2>
        <p>{t('dataSecurity.content')}</p>
        
        <h2>{t('userRights.title')}</h2>
        <p>{t('userRights.content')}</p>
        
        <h2>{t('dataRetention.title')}</h2>
        <p>{t('dataRetention.content')}</p>
        
        <h2>{t('policyChanges.title')}</h2>
        <p>{t('policyChanges.content')}</p>
        
        <h2>{t('contact.title')}</h2>
        <p>{t('contact.content')}</p>
      </div>
    </div>
  );
}