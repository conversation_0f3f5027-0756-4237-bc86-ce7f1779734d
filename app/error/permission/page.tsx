"use client";

import { PermissionErrorPage } from "@/components/error/permission-error-page";
import { useSearchParams } from "next/navigation";
import { Suspense } from "react";

function PermissionErrorContent() {
  const searchParams = useSearchParams();
  const fromUrl = searchParams.get("from");

  return <PermissionErrorPage fromUrl={fromUrl || undefined} />;
}

export default function PermissionError() {
  return (
    <Suspense fallback={<div>Chargement...</div>}>
      <PermissionErrorContent />
    </Suspense>
  );
}
