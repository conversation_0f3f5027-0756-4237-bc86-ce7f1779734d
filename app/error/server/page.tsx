"use client";

import { ErrorStatePage } from "@/components/error/error-state-page";
import { ServerErrorIllustration } from "@/components/error/error-illustrations";
import { useSearchParams } from "next/navigation";
import { useErrorRedirect } from "@/hooks/use-error-redirect";
import { Suspense } from "react";

function ServerErrorContent() {
  const searchParams = useSearchParams();
  const fromUrl = searchParams.get("from");
  const { ctaText, ctaLink } = useErrorRedirect(fromUrl || undefined);

  return (
    <div className="container py-12">
      <ErrorStatePage
        illustration={<ServerErrorIllustration />}
        title="Ça patine un peu..."
        message="Il y a un petit souci technique de notre côté. Notre équipe est déjà sur le coup. Réessayez dans un instant."
        ctaText={ctaText}
        ctaLink={ctaLink}
      />
    </div>
  );
}

export default function ServerError() {
  return (
    <Suspense fallback={<div>Chargement...</div>}>
      <ServerErrorContent />
    </Suspense>
  );
}
