import { getSession } from "@/lib/auth";
import { redirect } from "next/navigation";
import { NotificationTestPanel } from "@/components/admin/notification-test-panel";

export default async function AdminNotificationsPage() {
  const session = await getSession();
  
  if (!session?.user) {
    redirect("/login");
  }

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="max-w-4xl mx-auto">
        <div className="mb-8">
          <h1 className="text-3xl font-bold mb-2">Administration - Notifications</h1>
          <p className="text-gray-600">
            Panel de test et de configuration du système de notifications Epic 6
          </p>
        </div>

        <NotificationTestPanel />

        <div className="mt-8 p-4 bg-blue-50 rounded-lg">
          <h3 className="font-semibold text-blue-900 mb-2">ℹ️ Informations</h3>
          <ul className="text-sm text-blue-800 space-y-1">
            <li>• Cette page permet de tester le système de notifications email et push</li>
            <li>• Assurez-vous d'avoir configuré toutes les variables d'environnement Firebase et Resend</li>
            <li>• Les notifications push nécessitent HTTPS en production</li>
            <li>• Les cron jobs sont programmés pour jeudi 00h30 (génération) et 9h30 (envoi)</li>
          </ul>
        </div>
      </div>
    </div>
  );
}
