/**
 * Page d'administration pour les métriques de délivrabilité des emails
 * Accessible uniquement aux administrateurs
 */

'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { RefreshCw, TrendingUp, TrendingDown, AlertTriangle, CheckCircle } from 'lucide-react';

interface EmailMetrics {
  global: {
    totalActiveUsers: number;
    emailsSent: number;
    emailsDelivered: number;
    emailsBounced: number;
    emailsComplained: number;
    emailsOpened: number;
    emailsClicked: number;
    deliverabilityRate: string;
    bounceRate: string;
    complaintRate: string;
    openRate: string;
    clickRate: string;
  };
  events: Array<{
    eventType: string;
    count: number;
    lastOccurrence: string;
  }>;
  problematicDomains: Array<{
    domain: string;
    bounces: number;
    complaints: number;
    total: number;
  }>;
  problematicUsers: Array<{
    id: string;
    email: string;
    emailDeliverabilityScore: number;
    lastEmailBounce: string | null;
    emailBounceReason: string | null;
    lastEmailComplaint: string | null;
    emailComplaintReason: string | null;
    emailNotificationsEnabled: boolean;
  }>;
}

export default function EmailMetricsPage() {
  const [metrics, setMetrics] = useState<EmailMetrics | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [days, setDays] = useState(30);

  const fetchMetrics = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/admin/email-metrics?days=${days}`);
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
      const data = await response.json();
      
      if (data.success) {
        setMetrics(data.metrics);
        setError(null);
      } else {
        throw new Error(data.message || 'Erreur lors du chargement des métriques');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Erreur inconnue');
      console.error('Erreur lors du chargement des métriques:', err);
    } finally {
      setLoading(false);
    }
  };

  const recalculateScores = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/admin/email-metrics', {
        method: 'POST'
      });
      
      if (response.ok) {
        await fetchMetrics(); // Recharger les métriques
      } else {
        throw new Error('Erreur lors du recalcul des scores');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Erreur lors du recalcul');
    }
  };

  useEffect(() => {
    fetchMetrics();
  }, [days]);

  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getScoreBadge = (score: number) => {
    if (score >= 80) return <Badge variant="default" className="bg-green-100 text-green-800">Excellent</Badge>;
    if (score >= 60) return <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">Moyen</Badge>;
    return <Badge variant="destructive">Problématique</Badge>;
  };

  if (loading && !metrics) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex items-center justify-center h-64">
          <RefreshCw className="h-8 w-8 animate-spin" />
          <span className="ml-2">Chargement des métriques...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto p-6">
        <Card className="border-red-200">
          <CardHeader>
            <CardTitle className="text-red-600">Erreur</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-red-600">{error}</p>
            <Button onClick={fetchMetrics} className="mt-4">
              <RefreshCw className="h-4 w-4 mr-2" />
              Réessayer
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!metrics) {
    return null;
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Métriques de Délivrabilité Email</h1>
          <p className="text-muted-foreground">
            Surveillance des performances d'envoi d'emails
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <select
            value={days}
            onChange={(e) => setDays(parseInt(e.target.value))}
            className="px-3 py-2 border rounded-md"
          >
            <option value={7}>7 derniers jours</option>
            <option value={30}>30 derniers jours</option>
            <option value={90}>90 derniers jours</option>
          </select>
          <Button onClick={fetchMetrics} variant="outline" size="sm">
            <RefreshCw className="h-4 w-4 mr-2" />
            Actualiser
          </Button>
          <Button onClick={recalculateScores} variant="outline" size="sm">
            Recalculer les scores
          </Button>
        </div>
      </div>

      {/* Métriques globales */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Taux de Délivrabilité</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {metrics.global.deliverabilityRate}%
            </div>
            <p className="text-xs text-muted-foreground">
              {metrics.global.emailsDelivered} / {metrics.global.emailsSent} emails
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Taux d'Ouverture</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">
              {metrics.global.openRate}%
            </div>
            <p className="text-xs text-muted-foreground">
              {metrics.global.emailsOpened} ouvertures
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Taux de Bounce</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">
              {metrics.global.bounceRate}%
            </div>
            <p className="text-xs text-muted-foreground">
              {metrics.global.emailsBounced} bounces
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Utilisateurs Actifs</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {metrics.global.totalActiveUsers}
            </div>
            <p className="text-xs text-muted-foreground">
              Notifications activées
            </p>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Vue d'ensemble</TabsTrigger>
          <TabsTrigger value="domains">Domaines</TabsTrigger>
          <TabsTrigger value="users">Utilisateurs</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Événements par Type</CardTitle>
              <CardDescription>
                Répartition des événements email sur les {days} derniers jours
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {metrics.events.map((event) => (
                  <div key={event.eventType} className="flex items-center justify-between">
                    <span className="capitalize">{event.eventType}</span>
                    <div className="flex items-center space-x-2">
                      <span className="font-mono">{event.count}</span>
                      {event.eventType === 'delivered' && <CheckCircle className="h-4 w-4 text-green-500" />}
                      {event.eventType === 'bounced' && <AlertTriangle className="h-4 w-4 text-red-500" />}
                      {event.eventType === 'opened' && <TrendingUp className="h-4 w-4 text-blue-500" />}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="domains" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Domaines Problématiques</CardTitle>
              <CardDescription>
                Domaines avec le plus de bounces et plaintes
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {metrics.problematicDomains.length === 0 ? (
                  <p className="text-muted-foreground">Aucun domaine problématique détecté</p>
                ) : (
                  metrics.problematicDomains.map((domain) => (
                    <div key={domain.domain} className="flex items-center justify-between">
                      <span>{domain.domain}</span>
                      <div className="flex items-center space-x-2">
                        <Badge variant="outline">{domain.bounces} bounces</Badge>
                        <Badge variant="outline">{domain.complaints} plaintes</Badge>
                      </div>
                    </div>
                  ))
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="users" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Utilisateurs avec Problèmes de Délivrabilité</CardTitle>
              <CardDescription>
                Utilisateurs nécessitant une attention particulière
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {metrics.problematicUsers.length === 0 ? (
                  <p className="text-muted-foreground">Aucun utilisateur problématique</p>
                ) : (
                  metrics.problematicUsers.map((user) => (
                    <div key={user.id} className="flex items-center justify-between p-3 border rounded">
                      <div>
                        <p className="font-medium">{user.email}</p>
                        {user.emailBounceReason && (
                          <p className="text-sm text-red-600">Bounce: {user.emailBounceReason}</p>
                        )}
                        {user.emailComplaintReason && (
                          <p className="text-sm text-red-600">Plainte: {user.emailComplaintReason}</p>
                        )}
                      </div>
                      <div className="flex items-center space-x-2">
                        <span className={`font-mono ${getScoreColor(user.emailDeliverabilityScore)}`}>
                          {user.emailDeliverabilityScore}
                        </span>
                        {getScoreBadge(user.emailDeliverabilityScore)}
                      </div>
                    </div>
                  ))
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
