import { getSession } from "@/lib/auth";
import { redirect } from "next/navigation";
import { US13TestPanel } from "@/components/admin/us-1-3-test-panel";

export default async function US13TestPage() {
  const session = await getSession();
  
  if (!session?.user) {
    redirect("/login");
  }

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="max-w-4xl mx-auto">
        <div className="mb-8">
          <h1 className="text-3xl font-bold mb-2">Test US 1.3 - Email de Bienvenue et de Conseils</h1>
          <p className="text-gray-600">
            Test et validation de l'envoi automatique d'emails de bienvenue avec conseils d'utilisation lors de la création d'un compte
          </p>
        </div>

        <US13TestPanel />

        <div className="mt-8 p-4 bg-blue-50 rounded-lg">
          <h3 className="font-semibold text-blue-900 mb-2">ℹ️ À propos de l'US 1.3 (Mise à jour)</h3>
          <ul className="text-sm text-blue-800 space-y-1">
            <li>• <strong>Objectif</strong> : Envoyer automatiquement un email de bienvenue avec conseils d'utilisation</li>
            <li>• <strong>Objet</strong> : "Bienvenue sur Stream2Spin ! Voici comment bien démarrer."</li>
            <li>• <strong>Contenu enrichi</strong> : 2 conseils pratiques (Discogs + Wishlist)</li>
            <li>• <strong>Boutons</strong> : CTA principal vers recommandations + CTA secondaire vers compte Discogs</li>
            <li>• <strong>Déclencheur</strong> : Événement <code>createUser</code> dans NextAuth (auth.ts)</li>
            <li>• <strong>Service</strong> : Resend pour l'envoi d'emails transactionnels</li>
          </ul>
        </div>

        <div className="mt-4 p-4 bg-green-50 rounded-lg">
          <h3 className="font-semibold text-green-900 mb-2">✅ Dernières améliorations</h3>
          <ul className="text-sm text-green-800 space-y-1">
            <li>• <strong>Logo officiel</strong> : Utilisation du vrai logo Stream2Spin blanc</li>
            <li>• <strong>Icône disc3</strong> : Icône vinyle pour la synchronisation Discogs</li>
            <li>• <strong>Bouton "Ajouter des envies"</strong> : CTA secondaire pour la wishlist</li>
            <li>• <strong>Conseil n°1</strong> : Synchronisation Discogs avec icône disc3</li>
            <li>• <strong>Conseil n°2</strong> : Wishlist avec bouton d'action</li>
            <li>• <strong>Design cohérent</strong> : Hiérarchie visuelle des boutons respectée</li>
          </ul>
        </div>
      </div>
    </div>
  );
}
