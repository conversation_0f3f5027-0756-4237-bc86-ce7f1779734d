import { getSession } from "@/lib/auth";
import { redirect } from "next/navigation";
import { db } from "@/lib/db";
import { recommendations, wishlistItems } from "@/lib/db/schema";
import { eq, desc, asc, ilike, or, and } from "drizzle-orm";
import { getTranslations } from 'next-intl/server';
import { AlbumCard } from "@/components/recommendations/album-card";
import { WishlistEmptyState } from "@/components/wishlist/wishlist-empty-state";
import { WishlistControls } from "@/components/wishlist/wishlist-controls";
import { SharePopover } from "@/components/recommendations/SharePopover";
import { getUserDiscogsCollection, markOwnedAlbums } from "@/lib/album-matching";

interface SearchParams {
  q?: string;
  sortBy?: string;
  page?: string;
}

interface WishlistPageProps {
  searchParams: Promise<SearchParams>;
}

export default async function WishlistPage({ searchParams }: WishlistPageProps) {
  const session = await getSession();
  const t = await getTranslations('wishlist');

  // Vérifier si l'utilisateur est connecté
  if (!session) {
    redirect("/login?callbackUrl=/wishlist");
  }

  const resolvedSearchParams = await searchParams;
  const searchQuery = resolvedSearchParams.q || "";
  const sortBy = resolvedSearchParams.sortBy || "createdAt_desc";
  const page = parseInt(resolvedSearchParams.page || "1", 10);
  const itemsPerPage = 20;

  const userId = session.user?.id;
  let wishlistedRecommendations: any[] = [];
  let totalCount = 0;

  if (userId) {
    try {
      // Récupérer les albums en wishlist (nouvelle structure basée sur l'album)
      let whereConditions = [eq(wishlistItems.userId, userId)];

      // Ajouter la recherche si présente
      if (searchQuery) {
        whereConditions.push(
          or(
            ilike(wishlistItems.artistName, `%${searchQuery}%`),
            ilike(wishlistItems.albumTitle, `%${searchQuery}%`)
          )!
        );
      }

      // Construire la requête pour récupérer les albums en wishlist
      let query = db
        .select()
        .from(wishlistItems)
        .where(whereConditions.length > 1 ? and(...whereConditions) : whereConditions[0]);

      // Ajouter le tri
      switch (sortBy) {
        case "createdAt_asc":
          query = (query as any).orderBy(asc(wishlistItems.createdAt));
          break;
        case "artistName_asc":
          query = (query as any).orderBy(asc(wishlistItems.artistName));
          break;
        case "artistName_desc":
          query = (query as any).orderBy(desc(wishlistItems.artistName));
          break;
        case "albumTitle_asc":
          query = (query as any).orderBy(asc(wishlistItems.albumTitle));
          break;
        case "albumTitle_desc":
          query = (query as any).orderBy(desc(wishlistItems.albumTitle));
          break;
        default: // "createdAt_desc"
          query = (query as any).orderBy(desc(wishlistItems.createdAt));
          break;
      }

      // Exécuter la requête
      const wishlistData = await query;
      totalCount = wishlistData.length;

      // Pour chaque album en wishlist, récupérer les données de recommandation les plus récentes
      const wishlistWithRecommendations = await Promise.all(
        wishlistData.map(async (wishlistItem) => {
          // Récupérer la recommandation la plus récente pour cet album
          const recommendation = await db.query.recommendations.findFirst({
            where: and(
              eq(recommendations.userId, userId),
              eq(recommendations.artistName, wishlistItem.artistName),
              eq(recommendations.albumTitle, wishlistItem.albumTitle)
            ),
            orderBy: [desc(recommendations.generatedAt)],
          });

          if (recommendation) {
            return {
              id: recommendation.id,
              artistName: recommendation.artistName,
              albumTitle: recommendation.albumTitle,
              albumCoverUrl: recommendation.albumCoverUrl,
              listenScore: recommendation.listenScore,
              estimatedPlays: recommendation.estimatedPlays,
              isOwned: recommendation.isOwned,
              isWishlisted: true,
              affiliateLinks: recommendation.affiliateLinks,
              topTrackName: recommendation.topTrackName,
              topTrackId: recommendation.topTrackId,
              topTrackPreviewUrl: recommendation.topTrackPreviewUrl,
              topTrackListenScore: recommendation.topTrackListenScore,
              createdAt: wishlistItem.createdAt,
            };
          } else {
            // Si aucune recommandation n'existe pour cet album, créer un objet minimal
            // en utilisant les données sauvegardées dans wishlistItems
            return {
              id: 0, // ID temporaire
              artistName: wishlistItem.artistName,
              albumTitle: wishlistItem.albumTitle,
              albumCoverUrl: wishlistItem.albumCoverUrl || null,
              spotifyAlbumId: wishlistItem.spotifyAlbumId || undefined,
              discogsReleaseId: wishlistItem.discogsReleaseId || undefined,
              listenScore: 0,
              estimatedPlays: 0,
              isOwned: false, // Sera recalculé plus bas
              isWishlisted: true,
              affiliateLinks: (wishlistItem as any).affiliateLinks || null,
              topTrackName: (wishlistItem as any).topTrackName || null,
              topTrackId: (wishlistItem as any).topTrackId || null,
              topTrackPreviewUrl: (wishlistItem as any).topTrackPreviewUrl || null,
              topTrackListenScore: (wishlistItem as any).topTrackListenScore || null,
              originalUserName: (wishlistItem as any).originalUserName || null,
              createdAt: wishlistItem.createdAt,
            };
          }
        })
      );

      let wishlistRecommendations = wishlistWithRecommendations.filter(Boolean);

      // Recalculer le statut isOwned pour tous les albums en wishlist en croisant avec la collection Discogs
      try {
        const discogsCollection = await getUserDiscogsCollection(userId, db);

        if (discogsCollection.length > 0) {
          // Recalculer le statut isOwned en temps réel pour tous les albums en wishlist
          const recalculatedWishlist = markOwnedAlbums(
            wishlistRecommendations.map(rec => ({
              artistName: rec.artistName,
              albumTitle: rec.albumTitle,
              spotifyAlbumId: undefined,
              albumCoverUrl: rec.albumCoverUrl || undefined,
              listenScore: rec.listenScore,
              timeframe: 'short_term' // Valeur par défaut pour les albums sans recommandation
            })),
            discogsCollection
          );

          // Fusionner les données recalculées avec les données originales
          wishlistRecommendations = wishlistRecommendations.map((original, index) => ({
            ...original,
            isOwned: recalculatedWishlist[index].isOwned
          }));
        }
      } catch (discogsError) {
        if (process.env.NODE_ENV === 'development') {
        console.warn("⚠️ Erreur lors du recalcul du statut isOwned pour la wishlist, utilisation des données en base:", discogsError);
        }
        // En cas d'erreur, utiliser les données existantes
      }

      // Enrichir les albums sans liens d'affiliation en cherchant dans les recommandations existantes
      try {
        const albumsWithoutLinks = wishlistRecommendations.filter(album =>
          !album.affiliateLinks && album.id === 0
        );

        if (albumsWithoutLinks.length > 0) {
          // Récupérer les recommandations existantes pour ces albums
          const enrichmentPromises = albumsWithoutLinks.map(async (album) => {
            const existingRecommendation = await db.query.recommendations.findFirst({
              where: and(
                eq(recommendations.userId, userId),
                eq(recommendations.artistName, album.artistName),
                eq(recommendations.albumTitle, album.albumTitle)
              ),
              columns: {
                affiliateLinks: true,
                albumCoverUrl: true,
                spotifyAlbumId: true,
                discogsReleaseId: true,
                topTrackName: true,
                topTrackId: true,
                topTrackPreviewUrl: true,
                topTrackListenScore: true,
              },
            });

            if (existingRecommendation) {
              return {
                ...album,
                affiliateLinks: existingRecommendation.affiliateLinks,
                // Enrichir aussi les autres données si elles manquent
                albumCoverUrl: album.albumCoverUrl || existingRecommendation.albumCoverUrl,
                spotifyAlbumId: album.spotifyAlbumId || existingRecommendation.spotifyAlbumId || undefined,
                discogsReleaseId: album.discogsReleaseId || existingRecommendation.discogsReleaseId || undefined,
                // Utiliser la top track de l'utilisateur d'origine si disponible, sinon celle de la recommandation
                topTrackName: album.topTrackName || existingRecommendation.topTrackName,
                topTrackId: album.topTrackId || existingRecommendation.topTrackId,
                topTrackPreviewUrl: album.topTrackPreviewUrl || existingRecommendation.topTrackPreviewUrl,
                topTrackListenScore: album.topTrackListenScore || existingRecommendation.topTrackListenScore,
              };
            }
            return album;
          });

          const enrichedAlbums = await Promise.all(enrichmentPromises);

          // Remplacer les albums enrichis dans la liste
          wishlistRecommendations = wishlistRecommendations.map((album: any) => {
            const enriched = enrichedAlbums.find((enrichedAlbum: any) =>
              enrichedAlbum.artistName === album.artistName &&
              enrichedAlbum.albumTitle === album.albumTitle
            );
            return enriched || album;
          });
        }
      } catch (enrichmentError) {
        if (process.env.NODE_ENV === 'development') {
        console.warn("⚠️ Erreur lors de l'enrichissement des albums de wishlist:", enrichmentError);
        }
        // En cas d'erreur, continuer avec les données existantes
      }

      wishlistedRecommendations = wishlistRecommendations;

    } catch (error) {
      console.error("Erreur lors de la récupération de la wishlist:", error);
    }
  }

  return (
    <div className="container py-12">
      {totalCount === 0 && !searchQuery ? (
        <WishlistEmptyState />
      ) : (
        <>
          <div className="mb-8">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
              <div>
                <h1 className="text-3xl font-bold mb-2">{t('title')}</h1>
                <p className="text-muted-foreground">
                  {t('subtitle', { count: totalCount })}
                </p>
              </div>
              <SharePopover />
            </div>
          </div>
          <WishlistControls
            currentQuery={searchQuery}
            currentSortBy={sortBy}
            totalCount={totalCount}
          />

          {wishlistedRecommendations.length === 0 && searchQuery ? (
            <div className="text-center py-12 space-y-4">
              <h3 className="text-xl font-semibold text-muted-foreground">
                {t('filters.noResults')}
              </h3>
              <p className="text-muted-foreground">
                {t('filters.noResultsDescription', { query: searchQuery })}
              </p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              {wishlistedRecommendations.map((recommendation) => (
                <AlbumCard
                  key={recommendation.id}
                  recommendation={recommendation}
                  isWishlisted={true}
                />
              ))}
            </div>
          )}
        </>
      )}
    </div>
  );
}
