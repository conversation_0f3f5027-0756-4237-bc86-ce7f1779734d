"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { useSearchParams, useRouter } from "next/navigation";
import { Loader2, Music, ChevronDown, ChevronUp, Filter, RefreshCw } from "lucide-react";
import { useTranslations } from 'next-intl';

import Link from "next/link";
import { AlbumCard } from "@/components/recommendations/album-card";
import { TimeframeFilter } from "@/components/recommendations/timeframe-filter";
import { ToggleOwnedFilter } from "@/components/recommendations/toggle-owned-filter";
import { AvailabilityFilter } from "@/components/recommendations/availability-filter";

import { RefreshRecommendationsButton } from "@/components/recommendations/refresh-recommendations-button";

interface AffiliateLink {
  vendor: string;
  url: string;
  price: number;
  currency: string;
  merchantId: string;
  productName: string;
  inStock: boolean;
  asin?: string; // Amazon-specific identifier
}

interface Recommendation {
  id: number;
  artistName: string;
  albumTitle: string;
  albumCoverUrl: string | null;
  listenScore: number;
  estimatedPlays?: number | null; // Nombre d'écoutes estimé
  isOwned: boolean;
  isWishlisted?: boolean; // Epic 9: Wishlist
  affiliateLinks?: AffiliateLink[] | null;
  // US 3.6: Données du titre phare pour Spotify Embed
  topTrackName?: string | null;
  topTrackId?: string | null;
  topTrackPreviewUrl?: string | null;
  topTrackListenScore?: number | null;
}



interface RecommendationsClientProps {
  initialRecommendations: Recommendation[];
  hasSpotifyAccount: boolean;
  userId: string | null;
  hasDiscogsAccount?: boolean;
  pageTitle: string;
  
  initialTimeframe: string;
  initialHideOwned: boolean;
  initialWithOffers: boolean;
  fromGenerating?: boolean;
}

export default function RecommendationsClient({
  initialRecommendations,
  hasSpotifyAccount,
  userId,
  hasDiscogsAccount = false,
  pageTitle,
  initialTimeframe,
  initialHideOwned,
  initialWithOffers,
  fromGenerating = false
}: RecommendationsClientProps) {
  const { data: session } = useSession();
  const t = useTranslations('recommendations.filters');
  const searchParams = useSearchParams();
  const router = useRouter();

  const [recommendations, setRecommendations] = useState<Recommendation[]>(initialRecommendations);
  const [isLoadingFilters, setIsLoadingFilters] = useState(false);
  const [timeframeCounts, setTimeframeCounts] = useState<Record<string, number>>({});
  const [currentTimeframe, setCurrentTimeframe] = useState(initialTimeframe);
  const [isInitialLoad, setIsInitialLoad] = useState(true);

  // ��tat pour l'expansion/collapse des filtres (replié par défaut pour mettre le focus sur les recommandations)
  const [filtersExpanded, setFiltersExpanded] = useState(false);

  // Récupérer les comptes de recommandations par timeframe au chargement
  useEffect(() => {
    const fetchTimeframeCounts = async () => {
      if (!userId) return;

      try {
        const response = await fetch(`/api/recommendations/counts?userId=${userId}`);
        const data = await response.json();

        if (data.success) {
          setTimeframeCounts(data.counts);
        }
      } catch (error) {
        console.error('Erreur lors de la récupération des comptes de timeframes:', error);
      }
    };

    fetchTimeframeCounts();
  }, [userId]);

  // Charger les recommandations avec priorisation au premier chargement
  useEffect(() => {
    const loadInitialRecommendations = async () => {
      if (!userId || !isInitialLoad) return;

      try {
        setIsLoadingFilters(true);

        // Charger les recommandations avec priorisation
        const params = new URLSearchParams();
        params.set('userId', userId);

        const response = await fetch(`/api/recommendations/filtered?${params.toString()}`);

        if (response.ok) {
          const data = await response.json();
          if (data.success && data.recommendations) {
            setRecommendations(data.recommendations);

            // Mettre à jour le timeframe actuel avec celui effectivement utilisé par l'API
            if (data.filters && data.filters.timeframe) {
              const actualTimeframe = data.filters.timeframe;
              setCurrentTimeframe(actualTimeframe);

              // Mettre à jour l'URL si nécessaire
              const urlTimeframe = searchParams.get('timeframe') || 'short_term';
              if (actualTimeframe !== urlTimeframe) {
                const newParams = new URLSearchParams(searchParams.toString());
                if (actualTimeframe === 'short_term') {
                  newParams.delete('timeframe');
                } else {
                  newParams.set('timeframe', actualTimeframe);
                }
                const queryString = newParams.toString();
                const newUrl = `/recommendations${queryString ? `?${queryString}` : ''}`;
                router.replace(newUrl);
              }
            }
          }
        }
      } catch (error) {
        console.error('Erreur lors du chargement initial des recommandations:', error);
      } finally {
        setIsLoadingFilters(false);
        setIsInitialLoad(false);
      }
    };

    loadInitialRecommendations();
  }, [userId, isInitialLoad, searchParams, router]);

  // Obtenir les valeurs actuelles des filtres
  const getCurrentTimeframe = () => {
    return currentTimeframe;
  };

  const getCurrentHideOwned = () => {
    return searchParams.get('hideOwned') === 'true';
  };

  const getCurrentWithOffers = () => {
    return searchParams.get('withOffers') === 'true';
  };

  // Nettoyer l'URL si on vient de /generating
  useEffect(() => {
    if (fromGenerating) {
      const timer = setTimeout(() => {
        const url = new URL(window.location.href);
        url.searchParams.delete('from');
        window.history.replaceState({}, '', url.toString());
      }, 1000);
      return () => clearTimeout(timer);
    }
  }, [fromGenerating]);

  // Fonction pour vérifier les recommandations
  const checkRecommendations = async () => {
    if (!userId) return;

    try {
      const response = await fetch(`/api/recommendations/check?userId=${userId}`);
      const data = await response.json();

      if (data.recommendations && data.recommendations.length > 0) {
        setRecommendations(data.recommendations);
      }
    } catch (error) {
      console.error("Erreur lors de la vérification des recommandations:", error);
    }
  };



  // Effet pour recharger les recommandations quand les paramètres URL changent
  useEffect(() => {
    const loadFilteredRecommendations = async () => {
      if (!userId) return;

      setIsLoadingFilters(true);

      try {
        // Construire l'URL avec les paramètres actuels
        const currentParams = new URLSearchParams(searchParams.toString());
        const response = await fetch(`/api/recommendations/filtered?${currentParams.toString()}&userId=${userId}`);

        if (response.ok) {
          const data = await response.json();
          if (data.success && data.recommendations) {
            setRecommendations(data.recommendations);
            // Mettre à jour le timeframe actuel avec celui effectivement utilisé par l'API
            if (data.filters && data.filters.timeframe) {
              setCurrentTimeframe(data.filters.timeframe);
            }
          }
        }
      } catch (error) {
        console.error("Erreur lors du rechargement des recommandations filtrées:", error);
      } finally {
        setIsLoadingFilters(false);
      }
    };

    // Recharger les recommandations à chaque changement de paramètres URL (sauf au premier chargement)
    if (!isInitialLoad) {
      loadFilteredRecommendations();

      // Mettre à jour le timeframe actuel basé sur l'URL
      const urlTimeframe = searchParams.get('timeframe') || 'short_term';
      setCurrentTimeframe(urlTimeframe);
    }
  }, [searchParams, userId, isInitialLoad]);

  // Effet pour écouter les événements de synchronisation Discogs
  useEffect(() => {
    const handleDiscogsSync = () => {
      if (process.env.NODE_ENV === 'development') {
      console.log("🔄 Synchronisation Discogs détectée, rechargement des recommandations...");
      }
      // Recharger les recommandations pour afficher les nouveaux badges
      if (userId) {
        checkRecommendations();
      }
    };

    // Écouter l'événement personnalisé de synchronisation Discogs
    window.addEventListener('discogs-sync-completed', handleDiscogsSync);

    return () => {
      window.removeEventListener('discogs-sync-completed', handleDiscogsSync);
    };
  }, [userId]);

  const ActiveFilterTags = () => (
    <div className="flex flex-wrap gap-1 ml-3">
      {/* Filtre timeframe */}
      {(() => {
        const timeframeParam = searchParams.get('timeframe');
        const shouldShowTimeframeTag = 
          getCurrentTimeframe() === 'short_term' || 
          getCurrentTimeframe() === 'medium_term' || 
          (getCurrentTimeframe() === 'long_term' && timeframeParam === 'long_term');

        if (shouldShowTimeframeTag) {
          let timeframeLabel;
          switch (getCurrentTimeframe()) {
            case 'medium_term':
              timeframeLabel = t('activeTags.mediumTerm');
              break;
            case 'long_term':
              timeframeLabel = t('activeTags.longTerm');
              break;
            case 'short_term':
            default:
              timeframeLabel = t('activeTags.shortTerm');
          }
          
          return (
            <span className="inline-flex items-center px-2 py-0.5 bg-primary/10 text-primary rounded-full text-xs font-medium border border-primary/20">
              {timeframeLabel}
            </span>
          );
        }
        return null;
      })()}
      
      {/* Filtre hideOwned */}
      {getCurrentHideOwned() && hasDiscogsAccount && (
        <span className="inline-flex items-center px-2 py-0.5 bg-primary/10 text-primary rounded-full text-xs font-medium border border-primary/20">
          {t('activeTags.inCollection')}
        </span>
      )}
      
      {/* Filtre withOffers */}
      {getCurrentWithOffers() && (
        <span className="inline-flex items-center px-2 py-0.5 bg-primary/10 text-primary rounded-full text-xs font-medium border border-primary/20">
          {t('activeTags.withOffers')}
        </span>
      )}
    </div>
  );

  // Si pas de compte Spotify
  if (!hasSpotifyAccount) {
    return (
      <div>
        <p className="text-muted-foreground mb-8">
          {session?.user?.name
            ? t('welcome', { name: session.user.name })
            : t('welcomeWithEmail', { email: session?.user?.email || '' })
          } {t('noRecommendationsYet')}
        </p>
        <div className="mt-8 p-6 border rounded-lg">
          <p className="text-sm text-muted-foreground">
            {t('noSpotify.message')}
          </p>
        </div>
      </div>
    );
  }

  // Si on a des recommandations après filtrage
  if (recommendations.length > 0) {
    return (
      <div>
        <div className="mb-8">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <h1 className="text-3xl font-bold">{t('title')}</h1>
          </div>
        </div>

        {/* Barre de filtres */}
        <div className="mb-8 bg-card border rounded-lg relative">
          {/* En-tête des filtres avec bouton collapse/expand */}
          <button
            onClick={() => setFiltersExpanded(!filtersExpanded)}
            className="w-full flex items-center justify-between p-4 border-b text-left hover:bg-accent/50 transition-colors"
          >
            <div className="flex items-center space-x-2 flex-1">
              <Filter className="w-4 h-4 text-muted-foreground" />
              <h3 className="text-sm font-medium text-muted-foreground">
                {t('filterList')}
              </h3>
              <ActiveFilterTags />
            </div>
            <div className="flex items-center space-x-1 text-xs text-muted-foreground">
              <span>{filtersExpanded ? t('hide') : t('show')}</span>
              {filtersExpanded ? (
                <ChevronUp className="w-3 h-3" />
              ) : (
                <ChevronDown className="w-3 h-3" />
              )}
            </div>
          </button>

          {/* Contenu des filtres (collapsible) */}
          {filtersExpanded && (
            <div className="p-6 space-y-6">
              {isLoadingFilters && (
                <div className="absolute inset-0 bg-background/50 backdrop-blur-sm rounded-lg flex items-center justify-center z-10">
                  <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                    <Loader2 className="w-4 h-4 animate-spin" />
                    <span>Mise à jour des résultats...</span>
                  </div>
                </div>
              )}
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                {/* Filtre temporel */}
                <TimeframeFilter
                  currentTimeframe={getCurrentTimeframe()}
                  timeframeCounts={timeframeCounts}
                />

                {/* Filtre des vinyles possédés */}
                <ToggleOwnedFilter
                  hasDiscogsAccount={hasDiscogsAccount}
                  hideOwned={getCurrentHideOwned()}
                />

                {/* Filtre de disponibilité */}
                <AvailabilityFilter withOffers={getCurrentWithOffers()} />
              </div>
            </div>
          )}
        </div>

        {/* Grille des recommandations */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {recommendations.map((recommendation) => (
            <AlbumCard
              key={recommendation.id}
              recommendation={recommendation}
              isWishlisted={recommendation.isWishlisted}
            />
          ))}
        </div>
      </div>
    );
  }

  // Si on a des recommandations initiales mais aucune après filtrage (état vide)
  if (initialRecommendations.length > 0 && recommendations.length === 0 && !isLoadingFilters) {
    return (
      <div>
        <div className="mb-8">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <RefreshRecommendationsButton />
          </div>


        </div>

        {/* Barre de filtres */}
        <div className="mb-8 bg-card border rounded-lg relative">
          {/* En-tête des filtres avec bouton collapse/expand */}
          <button
            onClick={() => setFiltersExpanded(!filtersExpanded)}
            className="w-full flex items-center justify-between p-4 border-b text-left hover:bg-accent/50 transition-colors"
          >
            <div className="flex items-center space-x-2 flex-1">
              <Filter className="w-4 h-4 text-muted-foreground" />
              <h3 className="text-sm font-medium text-muted-foreground">
                {t('filterList')}
              </h3>
              <ActiveFilterTags />
            </div>
            <div className="flex items-center space-x-1 text-xs text-muted-foreground">
              <span>{filtersExpanded ? t('hide') : t('show')}</span>
              {filtersExpanded ? (
                <ChevronUp className="w-3 h-3" />
              ) : (
                <ChevronDown className="w-3 h-3" />
              )}
            </div>
          </button>

          {/* Contenu des filtres (collapsible) */}
          {filtersExpanded && (
            <div className="p-6 space-y-6">
              {isLoadingFilters && (
                <div className="absolute inset-0 bg-background/50 backdrop-blur-sm rounded-lg flex items-center justify-center z-10">
                  <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                    <Loader2 className="w-4 h-4 animate-spin" />
                    <span>Mise à jour des résultats...</span>
                  </div>
                </div>
              )}
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                {/* Filtre temporel */}
                <TimeframeFilter
                  currentTimeframe={getCurrentTimeframe()}
                  timeframeCounts={timeframeCounts}
                />

                {/* Filtre des vinyles possédés */}
                <ToggleOwnedFilter
                  hasDiscogsAccount={hasDiscogsAccount}
                  hideOwned={getCurrentHideOwned()}
                />

                {/* Filtre de disponibilité */}
                <AvailabilityFilter withOffers={getCurrentWithOffers()} />
              </div>
            </div>
          )}
        </div>

        {/* État vide */}
        <div className="text-center py-12 space-y-6">
          <div className="relative mx-auto w-20 h-20 flex items-center justify-center">
            <Music className="w-20 h-20 text-muted-foreground/50" />
          </div>
          <div className="space-y-2">
            <h3 className="text-xl font-semibold text-muted-foreground">
              Aucune recommandation trouvée
            </h3>
            <p className="text-muted-foreground max-w-md mx-auto">
              Aucun résultat ne correspond aux filtres sélectionnés. Essayez de modifier vos critères de recherche.
            </p>
          </div>
          <div className="flex flex-col sm:flex-row gap-3 justify-center">
            <button
              onClick={() => window.location.href = '/recommendations'}
              className="px-4 py-2 bg-secondary hover:bg-secondary/80 text-secondary-foreground rounded-lg transition-colors"
            >
              Réinitialiser les filtres
            </button>
          </div>
        </div>
      </div>
    );
  }



  // Empty state : aucune recommandation disponible
  if (initialRecommendations.length === 0 && !isLoadingFilters) {
    return (
      <div>
        <div className="text-center py-12 space-y-6">
          <div className="relative mx-auto w-20 h-20 flex items-center justify-center">
            <Music className="w-20 h-20 text-muted-foreground/50" />
          </div>
          <div className="space-y-2">
            <h2 className="text-4xl font-bold text-muted-foreground mb-4">
              ¯\_(ツ)_/¯
            </h2>
            <h3 className="text-xl font-semibold text-muted-foreground">
              Aucune recommandation pour le moment. Revenez plus tard !
            </h3>
          </div>
          <div className="flex flex-col sm:flex-row gap-3 justify-center">
            <RefreshRecommendationsButton />
          </div>
        </div>
      </div>
    );
  }

  // Cas spécial : on vient de /generating mais il n'y a pas de recommandations
  if (fromGenerating && initialRecommendations.length === 0 && recommendations.length === 0) {
    return (
      <div>
        <div className="text-center py-12 space-y-6">
          <div className="relative mx-auto w-20 h-20 flex items-center justify-center">
            <Music className="w-20 h-20 text-muted-foreground/50" />
          </div>
          <div className="space-y-2">
            <h2 className="text-4xl font-bold text-muted-foreground mb-4">
              ¯\_(ツ)_/¯
            </h2>
            <h3 className="text-xl font-semibold text-muted-foreground">
              Aucune recommandation pour le moment. Revenez plus tard !
            </h3>
          </div>
          <div className="flex flex-col sm:flex-row gap-3 justify-center">
            <RefreshRecommendationsButton />
          </div>
        </div>
      </div>
    );
  }

  // Fallback - ne devrait jamais arriver
  return null;
}
