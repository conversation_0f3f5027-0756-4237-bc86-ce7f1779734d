import { MetadataRoute } from 'next';
import { db } from "@/lib/db";
import { users } from "@/lib/db/schema";
import { eq } from "drizzle-orm";

export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
  const baseUrl = process.env.NEXTAUTH_URL || 'https://stream2spin.com';

  // Pages statiques
  const staticPages: MetadataRoute.Sitemap = [
    {
      url: baseUrl,
      lastModified: new Date(),
      changeFrequency: 'weekly',
      priority: 1,
    },
    {
      url: `${baseUrl}/login`,
      lastModified: new Date(),
      changeFrequency: 'monthly',
      priority: 0.8,
    },
  ];

  try {
    // Récupérer toutes les listes publiques
    const publicUsers = await db.query.users.findMany({
      where: eq(users.publicListEnabled, true),
      columns: {
        publicListId: true,
      },
    });

    // Générer les URLs des pages publiques (format /u/ selon specs)
    const publicPages: MetadataRoute.Sitemap = publicUsers
      .filter(user => user.publicListId)
      .map(user => ({
        url: `${baseUrl}/u/${user.publicListId}`,
        lastModified: new Date(),
        changeFrequency: 'weekly' as const,
        priority: 0.7,
      }));

    return [...staticPages, ...publicPages];
  } catch (error) {
    console.error('Erreur lors de la génération du sitemap:', error);
    // Retourner seulement les pages statiques en cas d'erreur
    return staticPages;
  }
}
