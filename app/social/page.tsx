import { getTranslations } from 'next-intl/server';
import { getSession } from '@/lib/auth';
import { redirect } from 'next/navigation';
import { getFollowers, getFollowing, getFollowStats, getSocialFeed } from '@/app/actions/social';
import { SocialConnections } from '@/components/social/SocialConnections';
import { ProfileSuggestions } from '@/components/social/ProfileSuggestions';
import { SocialFeed } from '@/components/social/SocialFeed';
import { SocialStats } from '@/components/social/SocialStats';
import Link from 'next/link';
import { Button } from '@/components/ui/button';

export default async function SocialPage() {
  const session = await getSession();
  const t = await getTranslations('Social');

  if (!session?.user?.id) {
    redirect('/login?callbackUrl=/social');
  }
  const currentUserId = session.user.id;

  // Récupérer les données pour la page
  const [stats, followersData, followingData, initialFeedItems] = await Promise.all([
    getFollowStats(currentUserId),
    getFollowers(currentUserId),
    getFollowing(currentUserId),
    getSocialFeed({ page: 1, limit: 9 }), // 9 pour un affichage 3x3
  ]);

  const followers = followersData.slice(0, 5);
  const following = followingData.slice(0, 5);
  const followersCount = stats.followers;
  const followingCount = stats.following;

  return (
    <div className="container py-6 md:py-12">
      <div className="mb-6 md:mb-8">
        <h1 className="text-2xl md:text-3xl font-bold tracking-tight">
          {t('title')}
        </h1>
        <p className="text-muted-foreground mt-2">
          {t('description')}
        </p>
      </div>

      {/* Compteurs sociaux - Affichés en haut sur mobile ET tablet, cachés sur desktop (car intégrés dans SocialConnections) */}
      <div className="lg:hidden mb-6">
        <div className="rounded-lg border bg-card p-4">
          <SocialStats
            targetUserId={currentUserId}
            followersCount={followersCount}
            followingCount={followingCount}
            userName={session.user.name || "Utilisateur"}
            currentUserId={currentUserId}
          />
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 lg:gap-8 lg:items-start">
        <div className="lg:col-span-2 space-y-6">
          {/* Feed Social */}
          {initialFeedItems.length > 0 ? (
            <SocialFeed initialItems={initialFeedItems} />
          ) : (
            <div className="rounded-lg border bg-card p-6 text-center py-12 text-muted-foreground">
              <p className="text-lg font-semibold">{t('feed.emptyState.title')}</p>
              <p className="mt-2">{t('feed.emptyState.description')}</p>
            </div>
          )}
        </div>

        {/* Sidebar - Masqué sur mobile pour économiser l'espace, visible sur desktop */}
        <div className="hidden lg:block space-y-6 lg:sticky lg:top-20">
          <SocialConnections
            currentUserId={currentUserId}
            followers={followers}
            following={following}
            followersCount={followersCount}
            followingCount={followingCount}
          />
          <ProfileSuggestions />
        </div>

        {/* Suggestions de profils - Visible sur mobile mais après le feed */}
        <div className="lg:hidden">
          <ProfileSuggestions />
        </div>
      </div>
    </div>
  );
} 