import { db } from "@/lib/db";
import { users, recommendations, wishlistItems, userDiscogsCollection, followers } from "@/lib/db/schema";
import { eq, desc, and } from "drizzle-orm";
import { notFound } from "next/navigation";
import { Metadata } from "next";
import { PublicProfileClient } from "@/components/public/public-profile-client";
import { trackPublicListEvent } from "@/lib/analytics";
import { getSession } from "@/lib/auth";
import { getTranslations } from 'next-intl/server';

interface PublicPageProps {
  params: Promise<{ publicListId: string }>;
  searchParams: Promise<{ tab?: string; timeframe?: string }>;
}

/**
 * Extrait le prénom d'un nom complet
 */
function getFirstName(fullName: string | null): string {
  if (!fullName) return "Utilisateur";
  return fullName.split(' ')[0];
}

/**
 * <PERSON><PERSON>cup<PERSON> les données publiques complètes d'un utilisateur,
 * en faisant la distinction entre le propriétaire du profil et un visiteur.
 */
async function getPublicUserData(publicListId: string, currentUserId?: string | null) {
  try {
    const user = await db.query.users.findFirst({
      where: eq(users.publicListId, publicListId),
      columns: {
        id: true,
        name: true,
        image: true,
        profileVisibility: true,
        shareRecommendations: true,
        shareWishlist: true,
        shareCollection: true,
        publicListEnabled: true,
        publicProfileEnabled: true,
        publicRecommendationsEnabled: true,
        publicWishlistEnabled: true,
        publicCollectionEnabled: true,
      },
    });

    if (!user) return null;

    const isOwner = user.id === currentUserId;
    const session = await getSession();
    const visitorIsLoggedIn = !!session?.user?.id;

    if (!isOwner) {
      if (user.profileVisibility === 'private') return null;
      if (user.profileVisibility === 'users_only' && !visitorIsLoggedIn) return null;
    }

    const allRecommendations = user.shareRecommendations ? await db.query.recommendations.findMany({
      where: eq(recommendations.userId, user.id),
      orderBy: [desc(recommendations.listenScore)],
    }) : [];

    const wishlistData = user.shareWishlist ? await db.query.wishlistItems.findMany({
      where: eq(wishlistItems.userId, user.id),
      orderBy: [desc(wishlistItems.createdAt)],
    }) : [];

    const collectionData = user.shareCollection ? await db.query.userDiscogsCollection.findMany({
      where: eq(userDiscogsCollection.userId, user.id),
      orderBy: [desc(userDiscogsCollection.syncedAt)],
    }) : [];

    return {
      user: { 
        id: user.id, 
        name: user.name, 
        image: user.image,
        publicListEnabled: user.publicListEnabled,
        publicProfileEnabled: user.publicProfileEnabled,
        publicRecommendationsEnabled: user.publicRecommendationsEnabled,
        publicWishlistEnabled: user.publicWishlistEnabled,
        publicCollectionEnabled: user.publicCollectionEnabled,
      },
      recommendations: allRecommendations,
      wishlist: wishlistData,
      collection: collectionData,
    } as any;
  } catch (error) {
    console.error("Erreur lors de la récupération des données publiques:", error);
    return null;
  }
}

/**
 * Génère les métadonnées pour le partage social selon les specs US 16.2
 */
export async function generateMetadata({ params }: PublicPageProps): Promise<Metadata> {
  const { publicListId } = await params;
  const publicData = await getPublicUserData(publicListId);

  if (!publicData) {
    const t = await getTranslations('public.notFound');
    return {
      title: `${t('title')} - Stream2Spin`,
      description: t('description'),
      robots: {
        index: false,
        follow: false,
      },
    };
  }

  const t = await getTranslations('public.meta');
  const userName = publicData.user.name || t('fallbackUser');
  const userFirstName = userName.split(' ')[0]; // Extraire le prénom

  const title = t('title', { firstName: userFirstName });
  const description = t('description');
  
  const baseUrl = process.env.NEXTAUTH_URL || 'https://stream2spin.com';
  const pageUrl = `${baseUrl}/u/${publicListId}`;
  
  // Image: La pochette de l'album le plus recommandé (top #1) selon les specs
  const topRecommendation = publicData.recommendations[0];
  const imageUrl = topRecommendation?.albumCoverUrl || `${baseUrl}/api/og/public-list?publicListId=${publicListId}`;

  return {
    title,
    description,
    keywords: [
      t('keywords'),
      userName,
    ].join(', '),
    authors: [{ name: userName }],
    creator: userName,
    publisher: 'Stream2Spin',
    openGraph: {
      title,
      description,
      type: "website",
      url: pageUrl,
      images: [
        {
          url: imageUrl,
          width: 1200,
          height: 630,
          alt: t('ogImageAlt', { firstName: userFirstName }),
          type: 'image/jpeg',
        },
      ],
      siteName: "Stream2Spin",
      locale: 'fr_FR',
    },
    twitter: {
      card: "summary_large_image",
      title,
      description,
      images: [imageUrl],
      creator: "@stream2spin",
      site: "@stream2spin",
    },
    robots: {
      index: true,
      follow: true,
      googleBot: {
        index: true,
        follow: true,
        'max-video-preview': -1,
        'max-image-preview': 'large',
        'max-snippet': -1,
      },
    },
    alternates: {
      canonical: pageUrl,
    },
  };
}

/**
 * Page publique d'affichage des recommandations (format /u/ selon specs)
 */
export default async function PublicProfilePage({ params, searchParams }: PublicPageProps) {
  const { publicListId } = await params;
  const { tab = "recommendations", timeframe = "short_term" } = await searchParams;

  // Récupérer la session du visiteur pour la wishlist (US 16.4) et pour vérifier s'il est propriétaire de la page
  const session = await getSession();
  const publicData = await getPublicUserData(publicListId, session?.user?.id);

  if (!publicData) {
    notFound();
  }

  // Récupérer la wishlist du visiteur connecté (si applicable)
  const visitorWishlist = session?.user?.id
    ? await db.query.wishlistItems.findMany({
        where: eq(wishlistItems.userId, session.user.id),
        columns: {
          artistName: true,
          albumTitle: true,
        }
      })
    : [];

  // Créer un Set pour une recherche rapide côté client
  const visitorWishlistSet = new Set(
    visitorWishlist.map(item => `${item.artistName}|${item.albumTitle}`)
  );

  // Vérifier l'état de suivi pour le FollowButton (US-02)
  let isFollowing = false;
  let isFollowingBackAvailable = false; // L'utilisateur consulté nous suit-il ?
  
  if (session?.user?.id && session.user.id !== publicData.user.id) {
    // Vérifier si on suit l'utilisateur consulté
    const followRelation = await db.query.followers.findFirst({
      where: and(
        eq(followers.followerId, session.user.id),
        eq(followers.followingId, publicData.user.id)
      ),
    });
    isFollowing = !!followRelation;

    // Vérifier si l'utilisateur consulté nous suit en retour
    const reverseFollowRelation = await db.query.followers.findFirst({
      where: and(
        eq(followers.followerId, publicData.user.id),
        eq(followers.followingId, session.user.id)
      ),
    });
    isFollowingBackAvailable = !!reverseFollowRelation;
  }

  // Tracker la vue de la page publique
  await trackPublicListEvent(publicListId, 'view', {
    tab,
    timeframe,
  });

  // Générer les données structurées JSON-LD
  const userName = publicData.user.name || "Un mélomane";
  const baseUrl = process.env.NEXTAUTH_URL || 'https://stream2spin.com';
  const pageUrl = `${baseUrl}/u/${publicListId}`;
  
  const jsonLd = {
    "@context": "https://schema.org",
    "@type": "ItemList",
    "name": `Recommandations musicales de ${userName}`,
    "description": `Liste de recommandations d'albums personnalisées basées sur les goûts musicaux de ${userName}`,
    "url": pageUrl,
    "author": {
      "@type": "Person",
      "name": userName,
      "image": publicData.user.image,
    },
    "publisher": {
      "@type": "Organization",
      "name": "Stream2Spin",
      "url": baseUrl,
    },
    "numberOfItems": publicData.recommendations.length,
    "itemListElement": publicData.recommendations.slice(0, 10).map((rec: any, index: number) => ({
      "@type": "ListItem",
      "position": index + 1,
      "item": {
        "@type": "MusicAlbum",
        "name": rec.albumTitle,
        "byArtist": {
          "@type": "MusicGroup",
          "name": rec.artistName,
        },
        "image": rec.albumCoverUrl,
      },
    })),
  };

  return (
    <>
      {/* Données structurées JSON-LD */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }}
      />
      
      <PublicProfileClient
        publicData={publicData}
        publicListId={publicListId}
        initialTab={tab}
        initialTimeframe={timeframe}
        session={session}
        visitorWishlistSet={visitorWishlistSet}
        originalUserFirstName={getFirstName(publicData.user.name)}
        isFollowingInitial={isFollowing}
        isFollowingBackAvailable={isFollowingBackAvailable}
      />
    </>
  );
}
