import Link from "next/link";
import { <PERSON>, <PERSON>Lef<PERSON>, Sparkles } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useTranslations } from 'next-intl';

export default function PublicListNotFound() {
  const t = useTranslations('public.notFound');

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800 flex items-center justify-center p-4">
      <div className="text-center max-w-md mx-auto">
        {/* Illustration */}
        <div className="relative mx-auto w-24 h-24 mb-8">
          <div className="absolute inset-0 bg-gradient-to-br from-primary/20 to-primary/10 rounded-full animate-pulse"></div>
          <div className="relative w-full h-full bg-gradient-to-br from-primary to-primary/80 rounded-full flex items-center justify-center">
            <Music className="w-12 h-12 text-white" />
          </div>
        </div>

        {/* Titre et message */}
        <h1 className="text-3xl font-bold text-slate-900 dark:text-slate-100 mb-4">
          {t('title')}
        </h1>
        <p className="text-slate-600 dark:text-slate-400 mb-8 leading-relaxed">
          {t('description')}
        </p>

        {/* Actions */}
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Button asChild className="bg-primary hover:bg-primary/90">
            <Link href="/login">
              <Sparkles className="w-4 h-4 mr-2" />
              {t('createList')}
            </Link>
          </Button>
          <Button variant="outline" asChild>
            <Link href="https://www.stream2spin.com/">
              <ArrowLeft className="w-4 h-4 mr-2" />
              {t('backHome')}
            </Link>
          </Button>
        </div>

        {/* Information supplémentaire */}
        <div className="mt-8 p-4 bg-white/60 dark:bg-slate-800/60 backdrop-blur-sm rounded-lg border border-slate-200 dark:border-slate-700">
          <p className="text-sm text-slate-600 dark:text-slate-400">
            <strong>Qu'est-ce que Stream2Spin ?</strong><br />
            Découvrez de nouveaux albums vinyles basés sur vos goûts musicaux Spotify. 
            Créez et partagez vos propres listes de recommandations personnalisées.
          </p>
        </div>
      </div>
    </div>
  );
}
