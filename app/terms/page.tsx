import { getTranslations } from 'next-intl/server';

export default async function TermsPage() {
  const t = await getTranslations('terms');

  return (
    <div className="container py-12">
      <h1 className="text-3xl font-bold mb-6">{t('title')}</h1>
      
      <div className="prose prose-sm sm:prose lg:prose-lg">
        <h2>{t('introduction.title')}</h2>
        <p>{t('introduction.content')}</p>
        
        <h2>{t('service.title')}</h2>
        <p>{t('service.content')}</p>
        
        <h2>{t('account.title')}</h2>
        <p>{t('account.content')}</p>
        
        <h2>{t('dataUsage.title')}</h2>
        <p>{t('dataUsage.content')}</p>
        
        <h2>{t('intellectualProperty.title')}</h2>
        <p>{t('intellectualProperty.content')}</p>
        
        <h2>{t('liability.title')}</h2>
        <p>{t('liability.content')}</p>
        
        <h2>{t('modifications.title')}</h2>
        <p>{t('modifications.content')}</p>
        
        <h2>{t('termination.title')}</h2>
        <p>{t('termination.content')}</p>
        
        <h2>{t('law.title')}</h2>
        <p>{t('law.content')}</p>
      </div>
    </div>
  );
}