import { redirect } from "next/navigation";

interface PublicRedirectPageProps {
  params: Promise<{ publicListId: string }>;
}

/**
 * Page de redirection de l'ancienne route /public/{id} vers /u/{id}
 */
export default async function PublicRedirectPage({ params }: PublicRedirectPageProps) {
  const { publicListId } = await params;
  
  // Rediriger vers la nouvelle route
  redirect(`/u/${publicListId}`);
}
