import { getSession } from "@/lib/auth";
import { redirect } from "next/navigation";
import { db } from "@/lib/db";
import { accounts, userDiscogsCollection } from "@/lib/db/schema";
import { eq, and, asc, desc, like, ilike, or, count } from "drizzle-orm";
import { ConnectDiscogsPrompt } from "@/components/collection/connect-discogs-prompt";
import { CollectionGrid } from "@/components/collection/collection-grid";
import { CollectionControls } from "@/components/collection/collection-controls";
import { CollectionPagination } from "@/components/collection/collection-pagination";
import { SharePopover } from "@/components/recommendations/SharePopover";
import { getTranslations } from 'next-intl/server';

interface CollectionPageProps {
  searchParams: Promise<{
    q?: string;
    sortBy?: string;
    page?: string;
  }>;
}

export default async function CollectionPage({ searchParams }: CollectionPageProps) {
  // Vérifier que l'utilisateur est connecté
  const session = await getSession();
  const t = await getTranslations('collection');
  if (!session?.user?.id) {
    redirect("/login");
  }

  // Vérifier si l'utilisateur a connecté son compte Discogs
  const discogsAccount = await db.query.accounts.findFirst({
    where: and(
      eq(accounts.userId, session.user.id),
      eq(accounts.provider, "discogs")
    ),
  });

  // Si pas de compte Discogs connecté, afficher le prompt de connexion
  if (!discogsAccount) {
    return <ConnectDiscogsPrompt />;
  }

  // Extraire les paramètres de recherche, tri et pagination
  const params = await searchParams;
  const query = params.q || '';
  const sortBy = params.sortBy || 'artistName_asc';
  const currentPage = parseInt(params.page || '1', 10);
  const itemsPerPage = 24; // 24 albums par page (6x4 grille)
  const [sortColumn, sortOrder] = sortBy.split('_');

  // Construire la clause WHERE pour la recherche
  const whereConditions = [eq(userDiscogsCollection.userId, session.user.id)];

  if (query) {
    whereConditions.push(
      or(
        ilike(userDiscogsCollection.artistName, `%${query}%`),
        ilike(userDiscogsCollection.albumTitle, `%${query}%`)
      )!
    );
  }

  // Construire la clause ORDER BY
  const getOrderByClause = () => {
    const column = userDiscogsCollection[sortColumn as keyof typeof userDiscogsCollection];
    if (!column) {
      // Fallback to syncedAt if column is not found
      return sortOrder === 'asc' ? asc(userDiscogsCollection.syncedAt) : desc(userDiscogsCollection.syncedAt);
    }
    return sortOrder === 'asc' ? asc(column as any) : desc(column as any);
  };

  // Compter le nombre total d'éléments pour la pagination
  const [totalCountResult] = await db
    .select({ count: count() })
    .from(userDiscogsCollection)
    .where(and(...whereConditions));

  const totalItems = totalCountResult.count;
  const totalPages = Math.ceil(totalItems / itemsPerPage);
  const offset = (currentPage - 1) * itemsPerPage;

  // Récupérer la collection de l'utilisateur avec recherche, tri et pagination
  const collection = await db.query.userDiscogsCollection.findMany({
    where: and(...whereConditions),
    orderBy: [getOrderByClause()],
    limit: itemsPerPage,
    offset: offset,
  });

  return (
    <div className="container py-12">
      <div className="mb-8">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-3xl font-bold mb-2">{t('title')}</h1>
            <p className="text-muted-foreground">
              {t('subtitle')}
            </p>
          </div>
          <SharePopover />
        </div>
      </div>

      <div className="mb-6">
        <CollectionControls
          currentQuery={query}
          currentSortBy={sortBy}
          totalCount={totalItems}
        />
      </div>

      <CollectionGrid albums={collection} />

      <CollectionPagination
        currentPage={currentPage}
        totalPages={totalPages}
        totalItems={totalItems}
        itemsPerPage={itemsPerPage}
      />
    </div>
  );
}
