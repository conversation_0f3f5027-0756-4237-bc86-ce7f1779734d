import { ErrorStatePage } from "@/components/error/error-state-page";
import { NotFoundIllustration } from "@/components/error/error-illustrations";

export default function NotFound() {
  // Version statique pour la 404 - pas de logique conditionnelle côté client
  // pour éviter les problèmes de pré-rendu
  return (
    <div className="container py-12">
      <ErrorStatePage
        illustration={<NotFoundIllustration />}
        title="Page introuvable"
        message="Oups ! Il semble que le disque que vous cherchez ait sauté. Cette page n'existe pas."
        ctaText="Retourner aux recommandations"
        ctaLink="/recommendations"
      />
    </div>
  );
}
