"use server";

import { getSession } from "@/lib/auth";
import { db } from "@/lib/db";
import { wishlistItems, recommendations } from "@/lib/db/schema";
import { and, eq } from "drizzle-orm";
import { revalidatePath } from "next/cache";

/**
 * Ajoute un album à la wishlist de l'utilisateur (évite les doublons basés sur artistName + albumTitle)
 * @param recommendationId - L'ID de la recommandation à ajouter
 * @returns Promise<{ success: boolean; error?: string }>
 */
export async function addToWishlist(recommendationId: number) {
  try {
    // Récupérer la session de l'utilisateur
    const session = await getSession();

    if (!session?.user?.id) {
      return { success: false, error: "Utilisateur non authentifié" };
    }

    const userId = session.user.id;

    // Récupérer toutes les informations enrichies de l'album depuis la recommandation
    const recommendation = await db.query.recommendations.findFirst({
      where: eq(recommendations.id, recommendationId),
      columns: {
        artistName: true,
        albumTitle: true,
        albumCoverUrl: true,
        spotifyAlbumId: true,
        discogsReleaseId: true,
        affiliateLinks: true,
        topTrackName: true,
        topTrackId: true,
        topTrackPreviewUrl: true,
        topTrackListenScore: true,
      },
    });

    if (!recommendation) {
      return { success: false, error: "Recommandation introuvable" };
    }

    // Vérifier si cet album (artistName + albumTitle) est déjà en wishlist pour cet utilisateur
    const existingWishlistItem = await db.query.wishlistItems.findFirst({
      where: and(
        eq(wishlistItems.userId, userId),
        eq(wishlistItems.artistName, recommendation.artistName),
        eq(wishlistItems.albumTitle, recommendation.albumTitle)
      ),
    });

    if (existingWishlistItem) {
      return { success: false, error: "Cet album est déjà dans votre liste d'envies" };
    }

    // Ajouter l'album à la wishlist avec toutes les données enrichies
    await db.insert(wishlistItems).values({
      userId,
      artistName: recommendation.artistName,
      albumTitle: recommendation.albumTitle,
      albumCoverUrl: recommendation.albumCoverUrl,
      spotifyAlbumId: recommendation.spotifyAlbumId,
      discogsReleaseId: recommendation.discogsReleaseId,
      affiliateLinks: recommendation.affiliateLinks,
      topTrackName: recommendation.topTrackName,
      topTrackId: recommendation.topTrackId,
      topTrackPreviewUrl: recommendation.topTrackPreviewUrl,
      topTrackListenScore: recommendation.topTrackListenScore,
    });

    // Revalider les pages concernées
    revalidatePath('/recommendations');
    revalidatePath('/wishlist');

    // Invalider le cache de la wishlist
    const { invalidateWishlistCache } = await import('@/lib/album-matching');
    invalidateWishlistCache(userId);

    return { success: true };
  } catch (error) {
    console.error('Erreur lors de l\'ajout à la wishlist:', error);
    return { success: false, error: "Erreur lors de l'ajout à la liste d'envies" };
  }
}

/**
 * Retire un album de la wishlist de l'utilisateur (supprime toutes les occurrences de cet album)
 * @param recommendationId - L'ID de la recommandation pour récupérer les infos de l'album
 * @returns Promise<{ success: boolean; error?: string }>
 */
export async function removeFromWishlist(recommendationId: number) {
  try {
    // Récupérer la session de l'utilisateur
    const session = await getSession();

    if (!session?.user?.id) {
      return { success: false, error: "Utilisateur non authentifié" };
    }

    const userId = session.user.id;

    // Récupérer les informations de l'album depuis la recommandation
    const recommendation = await db.query.recommendations.findFirst({
      where: eq(recommendations.id, recommendationId),
      columns: {
        artistName: true,
        albumTitle: true,
      },
    });

    if (!recommendation) {
      return { success: false, error: "Recommandation introuvable" };
    }

    // Supprimer l'album de la wishlist (nouvelle structure basée sur l'album)
    await db.delete(wishlistItems).where(
      and(
        eq(wishlistItems.userId, userId),
        eq(wishlistItems.artistName, recommendation.artistName),
        eq(wishlistItems.albumTitle, recommendation.albumTitle)
      )
    );

    // Revalider les pages concernées
    revalidatePath('/recommendations');
    revalidatePath('/wishlist');

    // Invalider le cache de la wishlist
    const { invalidateWishlistCache } = await import('@/lib/album-matching');
    invalidateWishlistCache(userId);

    return { success: true };
  } catch (error) {
    console.error('Erreur lors de la suppression de la wishlist:', error);
    return { success: false, error: "Erreur lors de la suppression de la liste d'envies" };
  }
}

/**
 * Ajoute un album à la wishlist de l'utilisateur directement par artistName et albumTitle
 * Utilisé pour les liens depuis les emails de recommandations
 * @param artistName - Le nom de l'artiste
 * @param albumTitle - Le titre de l'album
 * @returns Promise<{ success: boolean; error?: string }>
 */
export async function addToWishlistByAlbum(artistName: string, albumTitle: string) {
  try {
    // Récupérer la session de l'utilisateur
    const session = await getSession();

    if (!session?.user?.id) {
      return { success: false, error: "Utilisateur non authentifié" };
    }

    const userId = session.user.id;

    // Vérifier si cet album (artistName + albumTitle) est déjà en wishlist pour cet utilisateur
    const existingWishlistItem = await db.query.wishlistItems.findFirst({
      where: and(
        eq(wishlistItems.userId, userId),
        eq(wishlistItems.artistName, artistName),
        eq(wishlistItems.albumTitle, albumTitle)
      ),
    });

    if (existingWishlistItem) {
      return { success: false, error: "Cet album est déjà dans votre liste d'envies" };
    }

    // Ajouter l'album à la wishlist
    await db.insert(wishlistItems).values({
      userId,
      artistName,
      albumTitle,
    });

    // Revalider les pages concernées
    revalidatePath('/recommendations');
    revalidatePath('/wishlist');

    // Invalider le cache de la wishlist
    const { invalidateWishlistCache } = await import('@/lib/album-matching');
    invalidateWishlistCache(userId);

    return { success: true };
  } catch (error) {
    console.error('Erreur lors de l\'ajout à la wishlist:', error);
    return { success: false, error: "Erreur lors de l'ajout à la liste d'envies" };
  }
}

/**
 * Vérifie si un album est dans la wishlist de l'utilisateur (basé sur artistName + albumTitle)
 * @param artistName - Le nom de l'artiste
 * @param albumTitle - Le titre de l'album
 * @returns Promise<boolean>
 */
export async function isInWishlist(artistName: string, albumTitle: string): Promise<boolean> {
  try {
    // Récupérer la session de l'utilisateur
    const session = await getSession();

    if (!session?.user?.id) {
      return false;
    }

    const userId = session.user.id;

    // Vérifier directement si l'album est en wishlist (nouvelle structure basée sur l'album)
    const existingItem = await db.query.wishlistItems.findFirst({
      where: and(
        eq(wishlistItems.userId, userId),
        eq(wishlistItems.artistName, artistName),
        eq(wishlistItems.albumTitle, albumTitle)
      ),
    });

    return !!existingItem;
  } catch (error) {
    console.error('Erreur lors de la vérification de la wishlist:', error);
    return false;
  }
}

/**
 * Synchronise les données de la wishlist avec les nouvelles données de recommandations
 * Appelée automatiquement lors de la génération des recommandations
 * @param userId - L'ID de l'utilisateur
 * @param recommendations - Les nouvelles recommandations générées
 * @returns Promise<void>
 */
export async function syncWishlistWithRecommendations(userId: string, recommendations: any[]): Promise<void> {
  try {
    // Récupérer tous les albums en wishlist de l'utilisateur
    const wishlistedItems = await db.query.wishlistItems.findMany({
      where: eq(wishlistItems.userId, userId),
    });

    if (wishlistedItems.length === 0) {
      if (process.env.NODE_ENV === 'development') {
      console.log(`📋 Aucun album en wishlist à synchroniser pour l'utilisateur ${userId}`);
      }
      return;
    }

    let updatedCount = 0;

    // Pour chaque album en wishlist, vérifier s'il a une nouvelle recommandation
    for (const wishlistItem of wishlistedItems) {
      const matchingRecommendation = recommendations.find(rec =>
        rec.artistName === wishlistItem.artistName &&
        rec.albumTitle === wishlistItem.albumTitle
      );

      if (matchingRecommendation) {
        // Mettre à jour la wishlist avec les nouvelles données de la recommandation
        // Garder originalUserName s'il existe (pour les albums ajoutés depuis profils publics)
        await db.update(wishlistItems)
          .set({
            albumCoverUrl: matchingRecommendation.albumCoverUrl || wishlistItem.albumCoverUrl,
            spotifyAlbumId: matchingRecommendation.spotifyAlbumId || wishlistItem.spotifyAlbumId,
            discogsReleaseId: matchingRecommendation.discogsReleaseId || wishlistItem.discogsReleaseId,
            affiliateLinks: matchingRecommendation.affiliateLinks || wishlistItem.affiliateLinks,
            topTrackName: matchingRecommendation.topTrackName || wishlistItem.topTrackName,
            topTrackId: matchingRecommendation.topTrackId || wishlistItem.topTrackId,
            topTrackPreviewUrl: matchingRecommendation.topTrackPreviewUrl || wishlistItem.topTrackPreviewUrl,
            topTrackListenScore: matchingRecommendation.topTrackListenScore || wishlistItem.topTrackListenScore,
          })
          .where(and(
            eq(wishlistItems.userId, userId),
            eq(wishlistItems.artistName, wishlistItem.artistName),
            eq(wishlistItems.albumTitle, wishlistItem.albumTitle)
          ));

        updatedCount++;
      }
    }

    if (process.env.NODE_ENV === 'development') {
    console.log(`✅ Synchronisation wishlist terminée: ${updatedCount} albums mis à jour sur ${wishlistedItems.length} albums en wishlist`);
    }

    // Revalider la page wishlist pour refléter les changements
    revalidatePath('/wishlist');

    // Invalider le cache de la wishlist
    const { invalidateWishlistCache } = await import('@/lib/album-matching');
    invalidateWishlistCache(userId);

  } catch (error) {
    console.error('Erreur lors de la synchronisation de la wishlist:', error);
  }
}

/**
 * Ajoute un album à la wishlist depuis une page publique
 * @param albumData - Les données de l'album à ajouter
 * @returns Promise<{success: boolean, error?: string}>
 */
export async function addToWishlistFromPublic(albumData: {
  artistName: string;
  albumTitle: string;
  albumCoverUrl?: string | null;
  spotifyAlbumId?: string | null;
  discogsReleaseId?: number | null;
  affiliateLinks?: unknown;
  topTrackName?: string | null;
  topTrackId?: string | null;
  topTrackPreviewUrl?: string | null;
  topTrackListenScore?: number | null;
  originalUserName?: string | null; // Nom de l'utilisateur d'origine
}): Promise<{success: boolean, error?: string}> {
  try {
    // Récupérer la session de l'utilisateur
    const session = await getSession();

    if (!session?.user?.id) {
      return { success: false, error: "Utilisateur non connecté" };
    }

    const userId = session.user.id;

    // Vérifier si cet album est déjà en wishlist
    const existingWishlistItem = await db.query.wishlistItems.findFirst({
      where: and(
        eq(wishlistItems.userId, userId),
        eq(wishlistItems.artistName, albumData.artistName),
        eq(wishlistItems.albumTitle, albumData.albumTitle)
      ),
    });

    if (existingWishlistItem) {
      return { success: false, error: "Cet album est déjà dans votre liste d'envies" };
    }

    // Ajouter l'album à la wishlist
    await db.insert(wishlistItems).values({
      userId,
      artistName: albumData.artistName,
      albumTitle: albumData.albumTitle,
      albumCoverUrl: albumData.albumCoverUrl,
      spotifyAlbumId: albumData.spotifyAlbumId,
      discogsReleaseId: albumData.discogsReleaseId,
      affiliateLinks: albumData.affiliateLinks,
      topTrackName: albumData.topTrackName,
      topTrackId: albumData.topTrackId,
      topTrackPreviewUrl: albumData.topTrackPreviewUrl,
      topTrackListenScore: albumData.topTrackListenScore,
      originalUserName: albumData.originalUserName,
    });

    revalidatePath("/wishlist");
    return { success: true };
  } catch (error) {
    console.error("Erreur lors de l'ajout à la wishlist depuis page publique:", error);
    return { success: false, error: "Erreur lors de l'ajout à la wishlist" };
  }
}

/**
 * Retire un album de la wishlist depuis une page publique
 * @param artistName - Le nom de l'artiste
 * @param albumTitle - Le titre de l'album
 * @returns Promise<{success: boolean, error?: string}>
 */
export async function removeFromWishlistFromPublic(
  artistName: string,
  albumTitle: string
): Promise<{success: boolean, error?: string}> {
  try {
    // Récupérer la session de l'utilisateur
    const session = await getSession();

    if (!session?.user?.id) {
      return { success: false, error: "Utilisateur non connecté" };
    }

    const userId = session.user.id;

    // Supprimer l'album de la wishlist
    await db.delete(wishlistItems)
      .where(and(
        eq(wishlistItems.userId, userId),
        eq(wishlistItems.artistName, artistName),
        eq(wishlistItems.albumTitle, albumTitle)
      ));

    revalidatePath("/wishlist");
    return { success: true };
  } catch (error) {
    console.error("Erreur lors de la suppression de la wishlist depuis page publique:", error);
    return { success: false, error: "Erreur lors de la suppression de la wishlist" };
  }
}
