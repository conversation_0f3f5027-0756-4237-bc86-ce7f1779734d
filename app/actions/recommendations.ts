"use server";

import { getSession } from "@/lib/auth";
import { db } from "@/lib/db";
import { users } from "@/lib/db/schema";
import { eq } from "drizzle-orm";
import { revalidatePath } from "next/cache";

/**
 * Met à jour le statut de visibilité publique de la liste de recommandations de l'utilisateur
 * @param isPublic - true pour rendre la liste publique, false pour la rendre privée
 * @returns Promise<{ success: boolean; error?: string }>
 */
export async function setListVisibility(isPublic: boolean) {
  try {
    // Récupérer la session de l'utilisateur
    const session = await getSession();

    if (!session?.user?.id) {
      return { success: false, error: "Utilisateur non authentifié" };
    }

    const userId = session.user.id;

    if (process.env.NODE_ENV === 'development') {
    console.log(`🔄 Mise à jour de la visibilité de la liste pour l'utilisateur ${userId}: ${isPublic ? 'publique' : 'privée'}`);
    }

    // Mettre à jour le statut de visibilité dans la base de données
    await db.update(users)
      .set({ publicListEnabled: isPublic })
      .where(eq(users.id, userId));

    if (process.env.NODE_ENV === 'development') {
    console.log(`✅ Visibilité de la liste mise à jour avec succès`);
    }

    // Revalider la page des recommandations pour refléter les changements
    revalidatePath('/recommendations');

    return { success: true };
  } catch (error) {
    console.error('Erreur lors de la mise à jour de la visibilité:', error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : "Erreur lors de la mise à jour de la visibilité" 
    };
  }
}

/**
 * Récupère les informations de visibilité publique de l'utilisateur
 * @returns Promise<{ publicListEnabled: boolean; publicListId: string | null } | null>
 */
export async function getUserVisibilitySettings() {
  try {
    // Récupérer la session de l'utilisateur
    const session = await getSession();

    if (!session?.user?.id) {
      return null;
    }

    const userId = session.user.id;

    // Récupérer les paramètres de visibilité depuis la base de données
    const user = await db.query.users.findFirst({
      where: eq(users.id, userId),
      columns: {
        publicListEnabled: true,
        publicListId: true,
      },
    });

    if (!user) {
      return null;
    }

    return {
      publicListEnabled: user.publicListEnabled || false,
      publicListId: user.publicListId || null,
    };
  } catch (error) {
    console.error('Erreur lors de la récupération des paramètres de visibilité:', error);
    return null;
  }
}
