'use server';

import { db } from '@/lib/db';
import { followers, users, recommendations, notifications } from '@/lib/db/schema';
import { getSession } from '@/lib/auth';
import { and, eq, desc, count, ilike, ne, inArray, sql, or } from 'drizzle-orm';
import { revalidatePath } from 'next/cache';
import { sendNewFollowerEmail } from '@/lib/email';
import { enqueueRefresh } from '@/lib/queue';

// Types
export interface SocialFeedItem {
  id: number;
  userId: string;
  artistName: string;
  albumTitle: string;
  albumCoverUrl: string | null;
  spotifyAlbumId: string | null;
  listenScore: number;
  generatedAt: Date;
  author: {
    id: string;
    name: string | null;
    image: string | null;
    publicListId: string | null;
  };
  isWishlisted: boolean;
  isOwned: boolean;
}

export interface UserSearchResult {
  id: string;
  name: string | null;
  image: string | null;
  publicListId: string | null;
  isFollowing: boolean;
}

export interface ProfileSuggestion {
  id: string;
  name: string | null;
  image: string | null;
  publicListId: string | null;
  mutualFollowers: number;
}

export interface Notification {
  id: string;
  recipientId: string;
  actorId: string;
  type: 'new_follower';
  isRead: boolean;
  createdAt: Date;
  actor: {
    id: string;
    name: string | null;
    image: string | null;
    publicListId: string | null;
  };
}

// Fonction pour suivre un utilisateur
export async function followUser(targetUserId: string): Promise<{ success: boolean; error?: string }> {
  try {
    const session = await getSession();
    if (!session?.user?.id) {
      return { success: false, error: 'Not authenticated' };
    }

    if (session.user.id === targetUserId) {
      return { success: false, error: 'Cannot follow yourself' };
    }

    // Vérifier si déjà suivi
    const existingFollow = await db.query.followers.findFirst({
      where: and(
        eq(followers.followerId, session.user.id),
        eq(followers.followingId, targetUserId)
      ),
    });

    if (existingFollow) {
      return { success: false, error: 'Already following this user' };
    }

    // Créer la relation de suivi
    await db.insert(followers).values({
      followerId: session.user.id,
      followingId: targetUserId,
    });

    // Créer une notification
    await db.insert(notifications).values({
      recipientId: targetUserId,
      actorId: session.user.id,
      type: 'new_follower',
    });

    // Envoyer l'email de notification si activé
    const targetUser = await db.query.users.findFirst({
      where: eq(users.id, targetUserId),
      columns: {
        email: true,
        emailOnNewFollower: true,
        name: true,
        preferredLanguage: true,
      },
    });

    if (targetUser?.emailOnNewFollower && targetUser.email) {
      const followerUser = await db.query.users.findFirst({
        where: eq(users.id, session.user.id),
        columns: {
          name: true,
        },
      });

      if (followerUser?.name) {
        try {
          await sendNewFollowerEmail({
            recipientEmail: targetUser.email,
            recipientName: targetUser.name || 'Utilisateur',
            actorName: followerUser.name,
            locale: targetUser.preferredLanguage || 'fr',
          });
        } catch (emailError) {
          console.error('❌ Erreur envoi email newFollower:', emailError);
        }
      }
    }

    revalidatePath('/social');
    return { success: true };
  } catch (error) {
    console.error('Error following user:', error);
    return { success: false, error: 'Failed to follow user' };
  }
}

// Fonction pour ne plus suivre un utilisateur
export async function unfollowUser(targetUserId: string): Promise<{ success: boolean; error?: string }> {
  try {
    const session = await getSession();
    if (!session?.user?.id) {
      return { success: false, error: 'Not authenticated' };
    }

    await db.delete(followers).where(
      and(
        eq(followers.followerId, session.user.id),
        eq(followers.followingId, targetUserId)
      )
    );

    revalidatePath('/social');
    return { success: true };
  } catch (error) {
    console.error('Error unfollowing user:', error);
    return { success: false, error: 'Failed to unfollow user' };
  }
}

// Fonction pour rechercher des utilisateurs
export async function searchUsers(query: string): Promise<UserSearchResult[]> {
  try {
    const session = await getSession();
    if (!session?.user?.id || !query.trim()) {
      return [];
    }

    // 🔒 SÉCURITÉ : Bloquer la recherche sur "@" pour éviter la découverte d'adresses email
    if (query.includes('@')) {
      console.warn(`🚨 Tentative de recherche avec "@" bloquée pour l'utilisateur ${session.user.id}: ${query}`);
      return [];
    }

    // 🔒 SÉCURITÉ : Validation supplémentaire - bloquer les tentatives de domaines communs
    const suspiciousDomains = ['gmail', 'hotmail', 'yahoo', 'outlook', 'icloud', '.com', '.fr', '.org'];
    if (suspiciousDomains.some(domain => query.toLowerCase().includes(domain))) {
      console.warn(`🚨 Tentative de recherche de domaine bloquée pour l'utilisateur ${session.user.id}: ${query}`);
      return [];
    }

    // Récupérer les utilisateurs correspondants à la recherche
    const searchResults = await db
      .select({
        id: users.id,
        name: users.name,
        image: users.image,
        publicListId: users.publicListId,
      })
      .from(users)
      .where(
        and(
          or(
            // Recherche sur le nom (inchangée)
            ilike(users.name, `%${query}%`),
            // 🔒 SÉCURITÉ : Recherche uniquement sur la partie locale de l'email (avant @)
            ilike(sql`split_part(${users.email}, '@', 1)`, `%${query}%`)
          ),
          ne(users.id, session.user.id),
          ne(users.profileVisibility, 'private')
        )
      )
      .limit(20);

    // Vérifier quels utilisateurs sont déjà suivis
    const followedUsers = await db.query.followers.findMany({
      where: eq(followers.followerId, session.user.id),
      columns: { followingId: true },
    });
    const followedUserIds = new Set(followedUsers.map(f => f.followingId));

    return searchResults.map(user => ({
      ...user,
      isFollowing: followedUserIds.has(user.id),
    }));
  } catch (error) {
    console.error('Error searching users:', error);
    return [];
  }
}

// Fonction pour obtenir les suggestions de profils
export async function getProfileSuggestions(): Promise<ProfileSuggestion[]> {
  try {
    const session = await getSession();
    if (!session?.user?.id) {
      return [];
    }

    // Algorithme simple : utilisateurs avec des followers en commun
    const suggestions = await db
      .select({
        id: users.id,
        name: users.name,
        image: users.image,
        publicListId: users.publicListId,
        mutualFollowers: count().as('mutualFollowers'),
      })
      .from(users)
      .innerJoin(followers, eq(followers.followingId, users.id))
      .where(
        and(
          ne(users.id, session.user.id),
          ne(users.profileVisibility, 'private'),
          inArray(
            followers.followerId,
            db.select({ id: followers.followingId })
              .from(followers)
              .where(eq(followers.followerId, session.user.id))
          )
        )
      )
      .groupBy(users.id, users.name, users.image, users.publicListId)
      .orderBy(desc(count()))
      .limit(10);

    return suggestions.map(s => ({
      ...s,
      mutualFollowers: Number(s.mutualFollowers),
    }));
  } catch (error) {
    console.error('Error getting profile suggestions:', error);
    return [];
  }
}

// Fonction pour obtenir les followers d'un utilisateur
export async function getFollowers(userId?: string): Promise<UserSearchResult[]> {
  try {
    const session = await getSession();
    if (!session?.user?.id) {
      return [];
    }

    const targetUserId = userId || session.user.id;

    const followersList = await db
      .select({
        id: users.id,
        name: users.name,
        image: users.image,
        publicListId: users.publicListId,
      })
      .from(users)
      .innerJoin(followers, eq(followers.followerId, users.id))
      .where(eq(followers.followingId, targetUserId));

    // Vérifier quels utilisateurs sont suivis par l'utilisateur actuel
    const followedUsers = await db.query.followers.findMany({
      where: eq(followers.followerId, session.user.id),
      columns: { followingId: true },
    });
    const followedUserIds = new Set(followedUsers.map(f => f.followingId));

    return followersList.map(user => ({
      ...user,
      isFollowing: followedUserIds.has(user.id),
    }));
  } catch (error) {
    console.error('Error getting followers:', error);
    return [];
  }
}

// Fonction pour obtenir les utilisateurs suivis
export async function getFollowing(userId?: string): Promise<UserSearchResult[]> {
  try {
    const session = await getSession();
    if (!session?.user?.id) {
      return [];
    }

    const targetUserId = userId || session.user.id;

    const followingList = await db
      .select({
        id: users.id,
        name: users.name,
        image: users.image,
        publicListId: users.publicListId,
      })
      .from(users)
      .innerJoin(followers, eq(followers.followingId, users.id))
      .where(eq(followers.followerId, targetUserId));

    // Vérifier quels utilisateurs sont suivis par l'utilisateur actuel
    const followedUsers = await db.query.followers.findMany({
      where: eq(followers.followerId, session.user.id),
      columns: { followingId: true },
    });
    const followedUserIds = new Set(followedUsers.map(f => f.followingId));

    return followingList.map(user => ({
      ...user,
      isFollowing: followedUserIds.has(user.id),
    }));
  } catch (error) {
    console.error('Error getting following:', error);
    return [];
  }
}

// Fonction pour obtenir les statistiques de suivi
export async function getFollowStats(userId?: string): Promise<{ followers: number; following: number }> {
  try {
    const session = await getSession();
    const targetUserId = userId || session?.user?.id;
    
    if (!targetUserId) {
      return { followers: 0, following: 0 };
    }

    const [followersCount, followingCount] = await Promise.all([
      db.select({ count: count() }).from(followers).where(eq(followers.followingId, targetUserId)),
      db.select({ count: count() }).from(followers).where(eq(followers.followerId, targetUserId)),
    ]);

    return {
      followers: Number(followersCount[0]?.count || 0),
      following: Number(followingCount[0]?.count || 0),
    };
  } catch (error) {
    console.error('Error getting follow stats:', error);
    return { followers: 0, following: 0 };
  }
}

// Fonction pour obtenir les notifications non lues
export async function getUnreadNotifications(): Promise<{ notifications: Notification[]; count: number }> {
  try {
    const session = await getSession();
    if (!session?.user?.id) {
      return { notifications: [], count: 0 };
    }

    const unreadNotifications = await db
      .select({
        notification: notifications,
        actor: {
          id: users.id,
          name: users.name,
          image: users.image,
          publicListId: users.publicListId,
        },
      })
      .from(notifications)
      .innerJoin(users, eq(notifications.actorId, users.id))
      .where(
        and(
          eq(notifications.recipientId, session.user.id),
          eq(notifications.isRead, false)
        )
      )
      .orderBy(desc(notifications.createdAt))
      .limit(20);

    const formattedNotifications = unreadNotifications.map(({ notification, actor }) => ({
      ...notification,
      actor,
    }));

    return {
      notifications: formattedNotifications,
      count: formattedNotifications.length,
    };
  } catch (error) {
    console.error('Error getting unread notifications:', error);
    return { notifications: [], count: 0 };
  }
}

// Fonction pour marquer les notifications comme lues
export async function markNotificationsAsRead(notificationIds?: string[]): Promise<{ success: boolean }> {
  try {
    const session = await getSession();
    if (!session?.user?.id) {
      return { success: false };
    }

    if (notificationIds && notificationIds.length > 0) {
      // Marquer des notifications spécifiques
      await db.update(notifications)
        .set({ isRead: true })
        .where(
          and(
            eq(notifications.recipientId, session.user.id),
            inArray(notifications.id, notificationIds)
          )
        );
    } else {
      // Marquer toutes les notifications comme lues
      await db.update(notifications)
        .set({ isRead: true })
        .where(
          and(
            eq(notifications.recipientId, session.user.id),
            eq(notifications.isRead, false)
          )
        );
    }

    revalidatePath('/social');
    return { success: true };
  } catch (error) {
    console.error('Error marking notifications as read:', error);
    return { success: false };
  }
}

// Fonction pour obtenir le feed social avec pagination et rafraîchissement automatique
async function queueStaleProfileRefreshes(userIds: string[]) {
  // Logique pour rafraîchir les profils obsolètes
  try {
    for (const userId of userIds) {
      await enqueueRefresh(userId);
    }
  } catch (error) {
    console.error('Error queueing profile refreshes:', error);
  }
}

export async function getSocialFeed(options: { page?: number; limit?: number } = {}): Promise<SocialFeedItem[]> {
  try {
    const session = await getSession();
    if (!session?.user?.id) return [];

    const { page = 1, limit = 20 } = options;
    const offset = (page - 1) * limit;

    const followedUsers = await db.query.followers.findMany({
      where: eq(followers.followerId, session.user.id),
      columns: { followingId: true },
    });
    
    if (followedUsers.length === 0) return [];
    
    const followedUserIds = followedUsers.map((f) => f.followingId);

    // Déclenche le rafraîchissement en arrière-plan
    queueStaleProfileRefreshes(followedUserIds);

    const rawFeedItems = await db
      .select({
        recommendation: recommendations,
        author: {
          id: users.id,
          name: users.name,
          image: users.image,
          publicListId: users.publicListId,
        },
      })
      .from(recommendations)
      .innerJoin(users, eq(recommendations.userId, users.id))
      .where(
        and(
          inArray(recommendations.userId, followedUserIds),
          eq(users.shareRecommendations, true),
          ne(users.profileVisibility, 'private')
        )
      )
      .orderBy(desc(recommendations.listenScore))
      .offset(offset)
      .limit(limit);
    
    // Mapper les données et ajouter des informations supplémentaires
    const finalItems: SocialFeedItem[] = rawFeedItems.map(item => ({
      ...item.recommendation,
      author: item.author,
      isWishlisted: false, // TODO: Vérifier si l'album est dans la wishlist de l'utilisateur actuel
      isOwned: item.recommendation.isOwned || false
    }));

    return finalItems;
  } catch (error) {
    console.error('Error getting social feed:', error);
    return [];
  }
}