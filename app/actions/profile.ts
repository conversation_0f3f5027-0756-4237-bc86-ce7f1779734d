"use server";

import { z } from "zod";
import { getSession } from "@/lib/auth";
import { db } from "@/lib/db";
import { users } from "@/lib/db/schema";
import { eq } from "drizzle-orm";
import { revalidatePath } from "next/cache";

const visibilitySettingsSchema = z.object({
  profileVisibility: z.enum(["public", "users_only", "private"]),
  shareRecommendations: z.boolean(),
  shareWishlist: z.boolean(),
  shareCollection: z.boolean(),
});

export type VisibilitySettings = z.infer<typeof visibilitySettingsSchema>;

const profileVisibilitySchema = z.object({
  publicProfileEnabled: z.boolean(),
  publicRecommendationsEnabled: z.boolean(),
  publicWishlistEnabled: z.boolean(),
  publicCollectionEnabled: z.boolean(),
});

export type ProfileVisibilitySettings = z.infer<typeof profileVisibilitySchema>;

export async function updateVisibilitySettings(settings: VisibilitySettings) {
  const session = await getSession();
  if (!session?.user?.id) {
    return { success: false, error: "Non autorisé" };
  }

  const parsedSettings = visibilitySettingsSchema.safeParse(settings);
  if (!parsedSettings.success) {
    return { success: false, error: "Données invalides" };
  }

  try {
    // Récupérer le publicListId de l'utilisateur
    const userResult = await db
      .select({ publicListId: users.publicListId })
      .from(users)
      .where(eq(users.id, session.user.id))
      .limit(1);

    const user = userResult[0];

    await db
      .update(users)
      .set(parsedSettings.data)
      .where(eq(users.id, session.user.id));

    revalidatePath("/account");
    if (user?.publicListId) {
      revalidatePath(`/u/${user.publicListId}`);
    }

    return { success: true };
  } catch (error) {
    console.error("Erreur lors de la mise à jour des paramètres de visibilité:", error);
    return { success: false, error: "Une erreur est survenue" };
  }
}

export async function updateProfileVisibility(settings: ProfileVisibilitySettings) {
  const session = await getSession();
  if (!session?.user?.id) {
    return { success: false, error: "Non autorisé" };
  }

  const parsedSettings = profileVisibilitySchema.safeParse(settings);
  if (!parsedSettings.success) {
    return { success: false, error: "Données invalides" };
  }

  try {
    // Récupérer le publicListId de l'utilisateur
    const userResult = await db
      .select({ publicListId: users.publicListId })
      .from(users)
      .where(eq(users.id, session.user.id))
      .limit(1);

    const user = userResult[0];

    await db
      .update(users)
      .set(parsedSettings.data)
      .where(eq(users.id, session.user.id));

    revalidatePath("/account");
    if (user?.publicListId) {
      revalidatePath(`/u/${user.publicListId}`);
    }

    return { success: true };
  } catch (error) {
    console.error("Erreur lors de la mise à jour des paramètres de visibilité du profil:", error);
    return { success: false, error: "Une erreur est survenue" };
  }
}

export async function getProfileVisibilitySettings() {
  const session = await getSession();
  if (!session?.user?.id) {
    return null;
  }

  try {
    const userResult = await db
      .select({
        publicProfileEnabled: users.publicProfileEnabled,
        publicRecommendationsEnabled: users.publicRecommendationsEnabled,
        publicWishlistEnabled: users.publicWishlistEnabled,
        publicCollectionEnabled: users.publicCollectionEnabled,
      })
      .from(users)
      .where(eq(users.id, session.user.id))
      .limit(1);

    return userResult[0] || null;
  } catch (error) {
    console.error("Erreur lors de la récupération des paramètres de visibilité:", error);
    return null;
  }
}

export async function getUserPublicListId() {
  const session = await getSession();
  if (!session?.user?.id) {
    return null;
  }

  try {
    const userResult = await db
      .select({ publicListId: users.publicListId })
      .from(users)
      .where(eq(users.id, session.user.id))
      .limit(1);

    return userResult[0]?.publicListId || null;
  } catch (error) {
    console.error("Erreur lors de la récupération de l'ID de liste publique:", error);
    return null;
  }
}

export async function getProfileSharingSettings() {
  const session = await getSession();
  if (!session?.user?.id) {
    return null;
  }

  try {
    const userResult = await db
      .select({
        publicListId: users.publicListId,
        profileVisibility: users.profileVisibility,
      })
      .from(users)
      .where(eq(users.id, session.user.id))
      .limit(1);

    const settings = userResult[0];

    if (settings && settings.profileVisibility) {
       return {
        publicListId: settings.publicListId,
        profileVisibility: settings.profileVisibility as 'public' | 'users_only' | 'private',
      };
    }
    return null;

  } catch (error) {
    console.error("Erreur lors de la récupération des paramètres de partage:", error);
    return null;
  }
}

const visibilityLevelSchema = z.enum(["public", "users_only", "private"]);

export async function updateProfileVisibilityLevel(visibility: 'public' | 'users_only' | 'private') {
  const session = await getSession();
  if (!session?.user?.id) {
    return { success: false, error: "Non autorisé" };
  }

  const parsedVisibility = visibilityLevelSchema.safeParse(visibility);
  if (!parsedVisibility.success) {
    return { success: false, error: "Données invalides" };
  }

  try {
    await db
      .update(users)
      .set({ profileVisibility: parsedVisibility.data })
      .where(eq(users.id, session.user.id));

    revalidatePath("/account");
    // No need to revalidate the /u/ path here as the content doesn't change, only access.
    
    return { success: true };
  } catch (error) {
    console.error("Erreur lors de la mise à jour du niveau de visibilité:", error);
    return { success: false, error: "Une erreur est survenue" };
  }
}