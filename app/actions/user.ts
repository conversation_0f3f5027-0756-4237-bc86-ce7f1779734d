"use server";

import { getSession } from "@/lib/auth";
import { db } from "@/lib/db";
import { users, accounts, recommendations, userDiscogsCollection, wishlistItems } from "@/lib/db/schema";
import { eq, and, desc } from "drizzle-orm";
import { revalidatePath } from "next/cache";
import {
  getValidSpotifyToken,
  fetchUserTopTracks,
  analyzeTracksAndCalculateScores
} from "@/lib/spotify";

/**
 * Vérifie si l'utilisateur doit recevoir son premier email de recommandations
 * et l'envoie si les conditions sont remplies
 * Cette fonction est appelée après chaque génération réussie
 */
export async function triggerFirstRecommendationEmailIfNeeded(userId: string) {
  try {
    if (process.env.NODE_ENV === 'development') {
    console.log(`📧 Vérification de l'envoi du premier email pour l'utilisateur ${userId}`);
    }

    // Vérifier si l'utilisateur a déjà reçu son premier email
    const user = await db.query.users.findFirst({
      where: eq(users.id, userId),
      columns: {
        id: true,
        name: true,
        email: true,
        preferredLanguage: true,
        firstRecommendationEmailSent: true,
        emailFrequency: true
      }
    });

    if (!user) {
      if (process.env.NODE_ENV === 'development') {
      console.log(`⚠️ Utilisateur ${userId} non trouvé`);
      }
      return;
    }

    if (user.firstRecommendationEmailSent) {
      if (process.env.NODE_ENV === 'development') {
      console.log(`✅ Premier email déjà envoyé pour l'utilisateur ${user.email} - aucune action nécessaire`);
      }
      return;
    }

    if (user.emailFrequency === 'never') {
      if (process.env.NODE_ENV === 'development') {
      console.log(`⚠️ Notifications email désactivées pour l'utilisateur ${user.email} - aucun email envoyé`);
      }
      return;
    }

    if (process.env.NODE_ENV === 'development') {
    console.log(`🔍 Utilisateur ${user.email} éligible pour le premier email - recherche des recommandations...`);
    }

    // Récupérer les recommandations short_term non possédées
    const shortTermRecommendations = await db.query.recommendations.findMany({
      where: and(
        eq(recommendations.userId, userId),
        eq(recommendations.timeframe, 'short_term'),
        eq(recommendations.isOwned, false)
      ),
      orderBy: [desc(recommendations.listenScore)],
      limit: 50,
      columns: {
        artistName: true,
        albumTitle: true,
        albumCoverUrl: true,
        listenScore: true,
        affiliateLinks: true,
        isOwned: true
      }
    });

    if (process.env.NODE_ENV === 'development') {
    console.log(`📊 Trouvé ${shortTermRecommendations.length} recommandations short_term non possédées pour ${user.email}`);
    }

    if (shortTermRecommendations.length === 0) {
      if (process.env.NODE_ENV === 'development') {
      console.log(`⚠️ Aucune recommandation short_term non possédée pour l'utilisateur ${user.email} - email non envoyé`);
      }
      return;
    }

    // Filtrer les recommandations avec liens d'achat
    const recommendationsWithLinks = shortTermRecommendations.filter(rec =>
      rec.affiliateLinks &&
      Array.isArray(rec.affiliateLinks) &&
      rec.affiliateLinks.length > 0
    );

    if (process.env.NODE_ENV === 'development') {
    console.log(`💰 ${recommendationsWithLinks.length} recommandations avec liens d'achat sur ${shortTermRecommendations.length} total`);
    }

    if (recommendationsWithLinks.length === 0) {
      if (process.env.NODE_ENV === 'development') {
      console.log(`⚠️ Aucune recommandation avec liens d'achat pour l'utilisateur ${user.email} - email non envoyé`);
      }
      return;
    }

    if (process.env.NODE_ENV === 'development') {
    console.log(`📧 Envoi du premier email de recommandations à ${user.email} (${recommendationsWithLinks.length} recommandations)`);
    }

    // Préparer les données pour l'email
    const emailUserData = {
      id: user.id,
      name: user.name || undefined,
      email: user.email!,
      preferredLanguage: user.preferredLanguage
    };

    const emailRecommendations = recommendationsWithLinks.slice(0, 20).map(rec => ({
      artistName: rec.artistName,
      albumTitle: rec.albumTitle,
      albumCoverUrl: rec.albumCoverUrl || undefined,
      listenScore: rec.listenScore,
      affiliateLinks: Array.isArray(rec.affiliateLinks) ? rec.affiliateLinks : undefined,
      isOwned: rec.isOwned
    }));

    // Envoyer l'email
    const { sendRecommendationsEmail } = await import('@/lib/resend');
    const emailResult = await sendRecommendationsEmail(emailUserData, emailRecommendations);

    if (emailResult.success) {
      // Marquer que l'email de première génération a été envoyé
      await db.update(users)
        .set({ firstRecommendationEmailSent: true })
        .where(eq(users.id, userId));

      if (process.env.NODE_ENV === 'development') {
      console.log(`✅ Premier email de recommandations envoyé avec succès à ${user.email} (ID: ${emailResult.messageId})`);
      }
    } else {
      console.error(`❌ Échec de l'envoi du premier email de recommandations: ${emailResult.error}`);
    }

  } catch (error) {
    console.error(`❌ Erreur lors de la vérification/envoi du premier email pour l'utilisateur ${userId}:`, error);
  }
}

/**
 * Déclenche la génération initiale de recommandations pour un nouvel utilisateur
 * Cette fonction est appelée automatiquement lors de la liaison d'un compte Spotify
 * Envoie automatiquement l'email de recommandations après la première génération réussie
 */
export async function triggerInitialRecommendationGeneration(userId: string) {
  try {
    if (process.env.NODE_ENV === 'development') {
    console.log(`🎵 Déclenchement de la génération initiale de recommandations pour l'utilisateur ${userId}`);
    }

    // Vérifier si l'utilisateur a déjà reçu son premier email de recommandations
    const user = await db.query.users.findFirst({
      where: eq(users.id, userId),
      columns: {
        id: true,
        name: true,
        email: true,
        preferredLanguage: true,
        firstRecommendationEmailSent: true
      }
    });

    if (!user) {
      throw new Error(`Utilisateur ${userId} non trouvé`);
    }

    // Générer les recommandations directement
    const result = await generateRecommendationsForUser(userId);

    // Note: L'email de première recommandation est maintenant géré par
    // triggerFirstRecommendationEmailIfNeeded() appelé après la génération interactive

    if (process.env.NODE_ENV === 'development') {
    console.log(`✅ Génération initiale terminée pour l'utilisateur ${userId}`);
    }
  } catch (error) {
    console.error(`❌ Erreur lors du déclenchement de la génération initiale pour l'utilisateur ${userId}:`, error);
  }
}

/**
 * Génère les recommandations pour un utilisateur spécifique pour tous les timeframes
 * Fonction utilitaire utilisée par le cron job et la génération initiale
 */
export async function generateRecommendationsForUser(userId: string) {
  try {
    if (process.env.NODE_ENV === 'development') {
    console.log(`🔄 Génération de recommandations pour l'utilisateur ${userId}`);
    }

    // Étape 1: Obtenir un access token valide
    const validAccessToken = await getValidSpotifyToken(userId);

    if (!validAccessToken) {
      throw new Error("Impossible d'obtenir un token Spotify valide");
    }

    const timeframes = ['short_term', 'medium_term', 'long_term'] as const;
    let totalRecommendations = 0;

    // Générer les recommandations pour chaque timeframe
    for (const timeframe of timeframes) {
      try {
        if (process.env.NODE_ENV === 'development') {
        console.log(`🔄 Génération pour la période ${timeframe}`);
        }

        // Étape 2: Récupérer les top tracks de l'utilisateur pour ce timeframe
        const topTracks = await fetchUserTopTracks(validAccessToken, timeframe);

        if (!topTracks || topTracks.length === 0) {
          if (process.env.NODE_ENV === 'development') {
          console.log(`⚠️ Aucun top track trouvé pour l'utilisateur ${userId} (${timeframe})`);
          }
          continue;
        }

        // Étape 3: Analyser et calculer les scores d'albums
        const albumRecommendations = analyzeTracksAndCalculateScores(topTracks, timeframe);

        if (albumRecommendations.length === 0) {
          if (process.env.NODE_ENV === 'development') {
          console.log(`⚠️ Aucune recommandation générée pour l'utilisateur ${userId} (${timeframe})`);
          }
          continue;
        }

        // Étape 4: Sauvegarder les recommandations en base
        await saveRecommendations(userId, albumRecommendations, timeframe);

        totalRecommendations += albumRecommendations.length;
        if (process.env.NODE_ENV === 'development') {
        console.log(`✅ ${albumRecommendations.length} recommandations générées pour ${timeframe}`);
        }

      } catch (timeframeError) {
        console.error(`❌ Erreur lors de la génération pour ${timeframe}:`, timeframeError);
        // Continuer avec les autres timeframes même si un échoue
      }
    }

    if (totalRecommendations === 0) {
      if (process.env.NODE_ENV === 'development') {
      console.log(`⚠️ Aucune recommandation générée pour l'utilisateur ${userId} sur tous les timeframes`);
      }
      return { status: "no_recommendations" };
    }

    if (process.env.NODE_ENV === 'development') {
    console.log(`✅ ${totalRecommendations} recommandations générées au total pour l'utilisateur ${userId}`);
    }

    return {
      status: "success",
      recommendationsCount: totalRecommendations
    };

  } catch (error) {
    console.error(`❌ Erreur lors de la génération de recommandations pour l'utilisateur ${userId}:`, error);
    throw error;
  }
}

/**
 * Sauvegarde les recommandations en base de données
 * Intègre le croisement avec la collection Discogs (US 3.2)
 */
async function saveRecommendations(userId: string, albumRecommendations: any[], timeframe: string = "short_term"): Promise<void> {
  try {
    // Supprimer les anciennes recommandations de cet utilisateur pour ce timeframe
    await db.delete(recommendations)
      .where(
        and(
          eq(recommendations.userId, userId),
          eq(recommendations.timeframe, timeframe)
        )
      );

    if (albumRecommendations.length === 0) {
      if (process.env.NODE_ENV === 'development') {
      console.log(`💾 Aucune recommandation à sauvegarder pour l'utilisateur ${userId}`);
      }
      return;
    }

    // US 3.2: Croiser avec la collection Discogs pour marquer les albums possédés
    const { getUserDiscogsCollection, markOwnedAlbums } = await import('@/lib/album-matching');

    let processedRecommendations = albumRecommendations;

    try {
      // Récupérer la collection Discogs de l'utilisateur
      const discogsCollection = await getUserDiscogsCollection(userId, db);

      if (discogsCollection.length > 0) {
        if (process.env.NODE_ENV === 'development') {
        console.log(`🎵 Collection Discogs trouvée: ${discogsCollection.length} albums`);
        }

        // Marquer les albums possédés
        processedRecommendations = markOwnedAlbums(albumRecommendations, discogsCollection);

        const ownedCount = processedRecommendations.filter(r => r.isOwned).length;
        if (process.env.NODE_ENV === 'development') {
        console.log(`🔍 ${ownedCount} albums marqués comme possédés sur ${processedRecommendations.length} recommandations`);
        }
      } else {
        if (process.env.NODE_ENV === 'development') {
        console.log(`📭 Aucune collection Discogs trouvée pour l'utilisateur ${userId}`);
        }
        // Marquer tous comme non possédés
        processedRecommendations = albumRecommendations.map(album => ({ ...album, isOwned: false }));
      }
    } catch (discogsError) {
      if (process.env.NODE_ENV === 'development') {
      console.warn("⚠️ Erreur lors du croisement Discogs, marquage de tous les albums comme non possédés:", discogsError);
      }
      // En cas d'erreur, marquer tous comme non possédés pour ne pas bloquer le processus
      processedRecommendations = albumRecommendations.map(album => ({ ...album, isOwned: false }));
    }

    // US 3.3 & US 8.2: Enrichir avec les offres Rakuten et Amazon
    const enrichedRecommendations = await enrichWithAffiliateOffers(processedRecommendations, userId);

    // Insérer les nouvelles recommandations avec gestion des doublons
    // Utilise ON CONFLICT pour mettre à jour les recommandations existantes
    const { sql } = await import('drizzle-orm');

    for (const album of enrichedRecommendations) {
      try {
        await db.execute(sql`
          INSERT INTO recommendations (
            "userId", "artistName", "albumTitle", "albumCoverUrl",
            "spotifyAlbumId", "listenScore", "estimatedPlays", "timeframe", "isOwned", "affiliateLinks",
            "topTrackName", "topTrackId", "topTrackPreviewUrl", "topTrackListenScore"
          ) VALUES (
            ${userId}, ${album.artistName}, ${album.albumTitle}, ${album.albumCoverUrl},
            ${album.spotifyAlbumId}, ${album.listenScore}, ${album.estimatedPlays}, ${album.timeframe},
            ${album.isOwned}, ${JSON.stringify(album.affiliateLinks)},
            ${album.topTrackName}, ${album.topTrackId}, ${album.topTrackPreviewUrl}, ${album.topTrackListenScore}
          )
          ON CONFLICT ("userId", "spotifyAlbumId", "timeframe")
          DO UPDATE SET
            "listenScore" = EXCLUDED."listenScore",
            "estimatedPlays" = EXCLUDED."estimatedPlays",
            "isOwned" = EXCLUDED."isOwned",
            "affiliateLinks" = EXCLUDED."affiliateLinks",
            "topTrackName" = EXCLUDED."topTrackName",
            "topTrackId" = EXCLUDED."topTrackId",
            "topTrackPreviewUrl" = EXCLUDED."topTrackPreviewUrl",
            "topTrackListenScore" = EXCLUDED."topTrackListenScore",
            "generatedAt" = NOW()
        `);
      } catch (error) {
        // Fallback pour les albums sans spotifyAlbumId
        if (album.spotifyAlbumId === null) {
          await db.execute(sql`
            INSERT INTO recommendations (
              "userId", "artistName", "albumTitle", "albumCoverUrl",
              "spotifyAlbumId", "listenScore", "estimatedPlays", "timeframe", "isOwned", "affiliateLinks",
              "topTrackName", "topTrackId", "topTrackPreviewUrl", "topTrackListenScore"
            ) VALUES (
              ${userId}, ${album.artistName}, ${album.albumTitle}, ${album.albumCoverUrl},
              ${album.spotifyAlbumId}, ${album.listenScore}, ${album.estimatedPlays}, ${album.timeframe},
              ${album.isOwned}, ${JSON.stringify(album.affiliateLinks)},
              ${album.topTrackName}, ${album.topTrackId}, ${album.topTrackPreviewUrl}, ${album.topTrackListenScore}
            )
            ON CONFLICT ("userId", "artistName", "albumTitle", "timeframe")
            WHERE "spotifyAlbumId" IS NULL
            DO UPDATE SET
              "listenScore" = EXCLUDED."listenScore",
              "isOwned" = EXCLUDED."isOwned",
              "affiliateLinks" = EXCLUDED."affiliateLinks",
              "topTrackName" = EXCLUDED."topTrackName",
              "topTrackPreviewUrl" = EXCLUDED."topTrackPreviewUrl",
              "topTrackListenScore" = EXCLUDED."topTrackListenScore",
              "generatedAt" = NOW()
          `);
        } else {
          console.error("Erreur lors de l'insertion de la recommandation:", error);
        }
      }
    }

    const ownedCount = processedRecommendations.filter(r => r.isOwned).length;
    const notOwnedCount = processedRecommendations.length - ownedCount;

    if (process.env.NODE_ENV === 'development') {
    console.log(`💾 ${processedRecommendations.length} recommandations sauvegardées pour l'utilisateur ${userId} (${notOwnedCount} nouvelles, ${ownedCount} possédées)`);
    }

    // Synchroniser la wishlist avec les nouvelles données des recommandations
    try {
      const { syncWishlistWithRecommendations } = await import('@/app/actions/wishlist');
      await syncWishlistWithRecommendations(userId, enrichedRecommendations);
    } catch (syncError) {
      if (process.env.NODE_ENV === 'development') {
      console.warn('⚠️ Erreur lors de la synchronisation de la wishlist:', syncError);
      }
      // Ne pas faire échouer la sauvegarde si la synchronisation échoue
    }

  } catch (error) {
    console.error("❌ Erreur lors de la sauvegarde des recommandations:", error);
    throw error;
  }
}

/**
 * Met à jour les préférences de notification d'un utilisateur
 */
export async function updateNotificationPreferences(formData: FormData) {
  const session = await getSession();
  if (!session?.user?.id) {
    throw new Error("Non autorisé");
  }

  const frequency = formData.get("frequency") as string;
  const channel = formData.get("channel") as string;

  // Validation des données
  const validFrequencies = ["weekly", "bi-weekly", "monthly", "never"];
  const validChannels = ["email", "push"];

  if (!validFrequencies.includes(frequency) || !validChannels.includes(channel)) {
    throw new Error("Données invalides");
  }

  try {
    const updateField = channel === "email" ? "emailFrequency" : "pushFrequency";
    
    await db.update(users)
      .set({ [updateField]: frequency })
      .where(eq(users.id, session.user.id));

    revalidatePath("/account");
    
    return { success: true };
  } catch (error) {
    console.error("Erreur lors de la mise à jour des préférences:", error);
    throw new Error("Erreur lors de la sauvegarde");
  }
}

/**
 * Met à jour la préférence de notification pour les nouveaux followers
 */
export async function updateNewFollowerNotification(enabled: boolean) {
  const session = await getSession();
  if (!session?.user?.id) {
    throw new Error("Non autorisé");
  }

  try {
    await db.update(users)
      .set({ emailOnNewFollower: enabled })
      .where(eq(users.id, session.user.id));

    revalidatePath("/account");
    return { success: true };
  } catch (error) {
    console.error("Erreur lors de la mise à jour de la notification 'nouveau follower':", error);
    throw new Error("Erreur lors de la sauvegarde");
  }
}

/**
 * Met à jour la langue préférée de l'utilisateur
 */
export async function updateLanguage(newLocale: string) {
  const session = await getSession();
  if (!session?.user?.id) {
    throw new Error("Non autorisé");
  }

  const validLocales = ["fr", "en"];
  if (!validLocales.includes(newLocale)) {
    throw new Error("Langue invalide");
  }

  try {
    await db.update(users)
      .set({ preferredLanguage: newLocale })
      .where(eq(users.id, session.user.id));

    revalidatePath("/account");
    
    return { success: true };
  } catch (error) {
    console.error("Erreur lors de la mise à jour de la langue:", error);
    throw new Error("Erreur lors de la sauvegarde");
  }
}

/**
 * Déconnecte le compte Discogs de l'utilisateur
 */
export async function disconnectDiscogs() {
  const session = await getSession();
  if (!session?.user?.id) {
    throw new Error("Non autorisé");
  }

  try {
    // Supprimer l'enregistrement du compte Discogs
    await db.delete(accounts)
      .where(and(
        eq(accounts.userId, session.user.id),
        eq(accounts.provider, "discogs")
      ));

    // Supprimer aussi les données de collection mises en cache
    await db.delete(userDiscogsCollection).where(eq(userDiscogsCollection.userId, session.user.id));

    // Invalider le cache de collection Discogs
    const { invalidateDiscogsCache } = await import('@/lib/album-matching');
    invalidateDiscogsCache(session.user.id);

    revalidatePath("/account");
    
    return { success: true };
  } catch (error) {
    console.error("Erreur lors de la déconnexion Discogs:", error);
    throw new Error("Erreur lors de la déconnexion");
  }
}

/**
 * Resynchronise manuellement les recommandations Spotify pour l'utilisateur connecté
 * Appelé depuis l'interface Mon Compte
 */
export async function manualSyncSpotify() {
  const session = await getSession();
  if (!session?.user?.id) {
    throw new Error("Non autorisé");
  }

  try {
    if (process.env.NODE_ENV === 'development') {
    console.log(`🎵 Resynchronisation manuelle Spotify pour l'utilisateur ${session.user.id}`);
    }

    // Générer de nouvelles recommandations
    const result = await generateRecommendationsForUser(session.user.id);

    if (result.status === "success") {
      revalidatePath("/account");
      return {
        success: true,
        message: `${result.recommendationsCount} nouvelles recommandations générées`,
        recommendationsCount: result.recommendationsCount
      };
    } else {
      return {
        success: false,
        error: `Échec de la synchronisation: ${result.status}`
      };
    }

  } catch (error) {
    console.error("Erreur lors de la resynchronisation Spotify:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Erreur inconnue"
    };
  }
}

/**
 * Resynchronise manuellement la collection Discogs pour l'utilisateur connecté
 * Appelé depuis l'interface Mon Compte
 */
export async function manualSyncDiscogs() {
  const session = await getSession();

  // Debug: afficher les informations de session
  if (process.env.NODE_ENV === 'development') {
    console.log(`🔍 Session debug dans manualSyncDiscogs:`, {
      hasSession: !!session,
      hasUser: !!session?.user,
      userId: session?.user?.id?.substring(0, 8) + "..." || "none"
    });
  }

  if (!session?.user?.id) {
    console.error("❌ Pas de session ou d'utilisateur dans manualSyncDiscogs");
    throw new Error("Non autorisé");
  }

  try {
    if (process.env.NODE_ENV === 'development') {
    console.log(`🎵 Resynchronisation manuelle Discogs pour l'utilisateur ${session.user.id}`);
    }

    // Vérifier si l'utilisateur a un compte Discogs connecté
    const discogsAccount = await db.query.accounts.findFirst({
      where: and(
        eq(accounts.userId, session.user.id),
        eq(accounts.provider, "discogs")
      ),
    });

    if (!discogsAccount) {
      return {
        success: false,
        error: "Aucun compte Discogs connecté"
      };
    }

    // Synchroniser la collection Discogs
    const syncResult = await syncDiscogsCollection(session.user.id);

    if (syncResult.success) {
      revalidatePath("/account");
      revalidatePath("/recommendations"); // Recharger aussi la page des recommandations
      return {
        success: true,
        message: `${syncResult.syncedCount} albums synchronisés depuis Discogs`,
        syncedCount: syncResult.syncedCount,
        username: syncResult.username
      };
    } else {
      return {
        success: false,
        error: syncResult.error || "Erreur lors de la synchronisation"
      };
    }

  } catch (error) {
    console.error("Erreur lors de la resynchronisation Discogs:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Erreur inconnue"
    };
  }
}

/**
 * Vérifie si l'utilisateur a un compte Discogs et synchronise si nécessaire
 * Appelé à chaque connexion utilisateur pour maintenir la collection à jour
 */
export async function checkAndSyncDiscogsIfNeeded(userId: string) {
  try {
    if (process.env.NODE_ENV === 'development') {
    console.log(`🔍 Vérification de la synchronisation Discogs pour l'utilisateur ${userId}`);
    }

    // Vérifier si l'utilisateur a un compte Discogs connecté
    const discogsAccount = await db.query.accounts.findFirst({
      where: and(
        eq(accounts.userId, userId),
        eq(accounts.provider, "discogs")
      ),
    });

    if (!discogsAccount) {
      if (process.env.NODE_ENV === 'development') {
      console.log(`📭 Aucun compte Discogs connecté pour l'utilisateur ${userId}`);
      }
      return { success: true, message: "Pas de compte Discogs" };
    }

    // Vérifier la date de dernière synchronisation
    const lastSyncedItem = await db.query.userDiscogsCollection.findFirst({
      where: eq(userDiscogsCollection.userId, userId),
      orderBy: [desc(userDiscogsCollection.syncedAt)],
      columns: {
        syncedAt: true
      }
    });

    const now = new Date();
    const shouldSync = !lastSyncedItem ||
      (now.getTime() - lastSyncedItem.syncedAt.getTime()) > (24 * 60 * 60 * 1000); // 24 heures

    if (!shouldSync) {
      if (process.env.NODE_ENV === 'development') {
      console.log(`⏰ Synchronisation Discogs récente pour l'utilisateur ${userId}, pas de nouvelle sync nécessaire`);
      }
      return { success: true, message: "Synchronisation récente" };
    }

    if (process.env.NODE_ENV === 'development') {
    console.log(`🔄 Déclenchement de la synchronisation Discogs pour l'utilisateur ${userId} (dernière sync: ${lastSyncedItem?.syncedAt || 'jamais'})`);
    }

    // Déclencher la synchronisation
    const syncResult = await syncDiscogsCollection(userId);

    if (syncResult.success) {
      if (process.env.NODE_ENV === 'development') {
      console.log(`✅ Synchronisation Discogs réussie pour l'utilisateur ${userId}: ${syncResult.syncedCount} albums`);
      }
    } else {
      if (process.env.NODE_ENV === 'development') {
      console.log(`❌ Échec de la synchronisation Discogs pour l'utilisateur ${userId}: ${syncResult.error}`);
      }
    }

    return syncResult;

  } catch (error) {
    console.error("Erreur lors de la vérification/synchronisation Discogs:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error"
    };
  }
}

/**
 * Synchronise les collections Discogs de tous les utilisateurs connectés
 * Fonction optimisée pour le traitement en lot (cron job)
 */
export async function syncAllDiscogsCollections() {
  try {
    if (process.env.NODE_ENV === 'development') {
    console.log("🎵 Démarrage de la synchronisation Discogs pour tous les utilisateurs");
    }

    // Récupérer tous les utilisateurs avec un compte Discogs connecté
    const usersWithDiscogs = await db
      .select({
        userId: users.id,
        userName: users.name,
        userEmail: users.email,
        accessToken: accounts.access_token,
        accessTokenSecret: accounts.access_token_secret,
      })
      .from(users)
      .innerJoin(accounts, eq(accounts.userId, users.id))
      .where(
        and(
          eq(accounts.provider, "discogs"),
          // Vérifier que les tokens existent
        )
      );

    if (process.env.NODE_ENV === 'development') {
    console.log(`📊 ${usersWithDiscogs.length} utilisateurs avec compte Discogs trouvés`);
    }

    if (usersWithDiscogs.length === 0) {
      return {
        success: true,
        message: "Aucun utilisateur avec compte Discogs trouvé",
        processedUsers: 0,
        successCount: 0,
        errorCount: 0
      };
    }

    let successCount = 0;
    let errorCount = 0;
    const results: any[] = [];

    // Traitement séquentiel optimisé pour respecter les limites de l'API Discogs
    // (60 requêtes/minute, donc on traite un utilisateur à la fois)
    for (let i = 0; i < usersWithDiscogs.length; i++) {
      const user = usersWithDiscogs[i];
      const startTime = Date.now();

      try {
        if (process.env.NODE_ENV === 'development') {
        console.log(`🔄 [${i + 1}/${usersWithDiscogs.length}] Synchronisation Discogs pour ${user.userName || user.userEmail} (${user.userId})`);
        }

        // Vérifier d'abord si une synchronisation récente existe (< 6 jours)
        // pour éviter les synchronisations inutiles dans le cron hebdomadaire
        const lastSyncedItem = await db.query.userDiscogsCollection.findFirst({
          where: eq(userDiscogsCollection.userId, user.userId),
          orderBy: [desc(userDiscogsCollection.syncedAt)],
          columns: { syncedAt: true }
        });

        const now = new Date();
        const shouldSync = !lastSyncedItem ||
          (now.getTime() - lastSyncedItem.syncedAt.getTime()) > (6 * 24 * 60 * 60 * 1000); // 6 jours

        if (!shouldSync) {
          if (process.env.NODE_ENV === 'development') {
          console.log(`⏰ Synchronisation récente pour ${user.userId}, ignoré`);
          }
          results.push({
            userId: user.userId,
            status: "skipped",
            reason: "Synchronisation récente"
          });
          continue;
        }

        const syncResult = await syncDiscogsCollection(user.userId);

        if (syncResult.success) {
          successCount++;
          results.push({
            userId: user.userId,
            status: "success",
            syncedCount: syncResult.syncedCount,
            username: syncResult.username,
            duration: Date.now() - startTime
          });
        } else {
          errorCount++;
          results.push({
            userId: user.userId,
            status: "error",
            error: syncResult.error,
            duration: Date.now() - startTime
          });
        }

      } catch (error) {
        console.error(`❌ Erreur lors de la synchronisation Discogs pour l'utilisateur ${user.userId}:`, error);
        errorCount++;
        results.push({
          userId: user.userId,
          status: "error",
          error: error instanceof Error ? error.message : "Unknown error",
          duration: Date.now() - startTime
        });
      }

      // Délai intelligent entre les utilisateurs pour respecter les limites API
      // Plus de délai si la synchronisation a été longue (beaucoup d'albums)
      if (i < usersWithDiscogs.length - 1) {
        const syncDuration = Date.now() - startTime;
        const baseDelay = 2000; // 2 secondes de base
        const additionalDelay = Math.min(syncDuration * 0.1, 3000); // Max 3 secondes supplémentaires
        const totalDelay = baseDelay + additionalDelay;

        if (process.env.NODE_ENV === 'development') {
        console.log(`⏳ Attente de ${Math.round(totalDelay / 1000)}s avant le prochain utilisateur...`);
        }
        await new Promise(resolve => setTimeout(resolve, totalDelay));
      }
    }

    if (process.env.NODE_ENV === 'development') {
    console.log(`🎯 Synchronisation Discogs terminée: ${successCount} succès, ${errorCount} erreurs`);
    }

    return {
      success: true,
      message: "Synchronisation Discogs pour tous les utilisateurs terminée",
      processedUsers: usersWithDiscogs.length,
      successCount,
      errorCount,
      results
    };

  } catch (error) {
    console.error("💥 Erreur critique lors de la synchronisation Discogs globale:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error",
      processedUsers: 0,
      successCount: 0,
      errorCount: 0
    };
  }
}

/**
 * Synchronise la collection Discogs de l'utilisateur
 */
export async function syncDiscogsCollection(userId: string) {
  const startTime = Date.now();

  // Importer le logger
  const { DiscogsSyncLogger, categorizeDiscogsError, getRetryStrategy } = await import('@/lib/sync-logger');

  DiscogsSyncLogger.log({
    userId,
    action: 'sync_start'
  });

  try {
    if (process.env.NODE_ENV === 'development') {
    console.log(`🎵 Déclenchement de la synchronisation Discogs pour l'utilisateur ${userId}`);
    }

    // Récupérer le token d'accès Discogs de l'utilisateur avec une approche robuste
    let discogsAccount: any = null;

    try {
      // Essayer d'abord avec Drizzle ORM (approche préférée)
      discogsAccount = await db.query.accounts.findFirst({
        where: and(
          eq(accounts.userId, userId),
          eq(accounts.provider, "discogs")
        ),
        columns: {
          userId: true,
          provider: true,
          providerAccountId: true,
          access_token: true,
          access_token_secret: true,
        }
      });
    } catch (drizzleError) {
      if (process.env.NODE_ENV === 'development') {
      console.warn('⚠️ Erreur Drizzle, utilisation de SQL direct:', drizzleError);
      }

      // Fallback avec SQL direct si Drizzle échoue
      const { sql } = await import('drizzle-orm');
      const discogsAccountResult = await db.execute(sql`
        SELECT "userId", "provider", "providerAccountId", "access_token", "access_token_secret"
        FROM accounts
        WHERE "userId" = ${userId} AND provider = 'discogs'
        LIMIT 1
      `);

      discogsAccount = discogsAccountResult[0] as any;
    }

    // Debug: afficher les détails du compte Discogs trouvé
    if (process.env.NODE_ENV === 'development') {
      console.log(`🔍 Compte Discogs trouvé pour ${userId}:`, {
        hasAccount: !!discogsAccount,
        provider: discogsAccount?.provider,
        providerAccountId: discogsAccount?.providerAccountId,
        hasAccessToken: !!discogsAccount?.access_token,
        hasAccessTokenSecret: !!discogsAccount?.access_token_secret,
        tokenLength: discogsAccount?.access_token?.length || 0,
        secretLength: discogsAccount?.access_token_secret?.length || 0
      });
    }

    if (!discogsAccount?.access_token || !discogsAccount?.access_token_secret) {
      const error = "Token d'accès Discogs manquant";
      console.error(error, userId);
      console.error("Détails du compte:", discogsAccount);

      DiscogsSyncLogger.log({
        userId,
        action: 'sync_error',
        error,
        duration: Date.now() - startTime
      });

      return { success: false, error };
    }

    // Importer la bibliothèque Discogs
    const Discogs = require('disconnect').Client;

    // Créer un client authentifié avec les tokens OAuth
    // La bibliothèque disconnect nécessite une configuration complète pour OAuth 1.0a
    const authenticatedClient = new Discogs({
      method: 'oauth',
      level: 2, // Niveau 2 = access token obtenu
      consumerKey: process.env.AUTH_DISCOGS_KEY!,
      consumerSecret: process.env.AUTH_DISCOGS_SECRET!,
      token: discogsAccount.access_token,
      tokenSecret: discogsAccount.access_token_secret
    });

    // Récupérer l'identité de l'utilisateur pour obtenir son username
    const userIdentity = await new Promise((resolve, reject) => {
      authenticatedClient.getIdentity((err: any, data: any) => {
        if (err) reject(err);
        else resolve(data);
      });
    });

    const username = (userIdentity as any).username;
    if (process.env.NODE_ENV === 'development') {
    console.log(`👤 Synchronisation pour l'utilisateur Discogs: ${username}`);
    }

    // Supprimer l'ancienne collection pour éviter les doublons
    if (process.env.NODE_ENV === 'development') {
    console.log(`🗑️ Suppression de l'ancienne collection pour l'utilisateur ${userId}...`);
    }
    try {
      const deleteResult = await db.delete(userDiscogsCollection).where(eq(userDiscogsCollection.userId, userId));
      if (process.env.NODE_ENV === 'development') {
      console.log(`✅ Ancienne collection supprimée avec succès`);
      }
    } catch (drizzleError) {
      if (process.env.NODE_ENV === 'development') {
      console.warn('⚠️ Erreur Drizzle pour suppression, utilisation de SQL direct:', drizzleError);
      }
      const { sql } = await import('drizzle-orm');
      const deleteResult = await db.execute(sql`DELETE FROM user_discogs_collection WHERE "userId" = ${userId}`);
      if (process.env.NODE_ENV === 'development') {
      console.log(`✅ Ancienne collection supprimée avec SQL direct`);
      }
    }

    let totalSynced = 0;
    let page = 1;
    const perPage = 100; // Maximum autorisé par l'API Discogs

    // Récupérer la collection avec pagination
    while (true) {
      if (process.env.NODE_ENV === 'development') {
      console.log(`📄 Récupération de la page ${page}...`);
      }

      const collectionData = await new Promise((resolve, reject) => {
        authenticatedClient.user().collection().getReleases(username, 0, {
          page,
          per_page: perPage,
          sort: 'added',
          sort_order: 'desc'
        }, (err: any, data: any) => {
          if (err) reject(err);
          else resolve(data);
        });
      });

      const collection = collectionData as any;

      if (!collection.releases || collection.releases.length === 0) {
        if (process.env.NODE_ENV === 'development') {
        console.log(`📄 Aucune release trouvée sur la page ${page}, fin de la synchronisation`);
        }
        break;
      }

      // Préparer les données pour l'insertion en base
      const releasesToInsert = collection.releases
        .filter((release: any) => release.basic_information) // S'assurer que les infos de base existent
        .map((release: any) => {
          // Extraire l'URL de la couverture (prendre la première image disponible)
          let albumCoverUrl = null;
          if (release.basic_information.cover_image && release.basic_information.cover_image !== '') {
            albumCoverUrl = release.basic_information.cover_image;
          } else if (release.basic_information.thumb && release.basic_information.thumb !== '') {
            albumCoverUrl = release.basic_information.thumb;
          }

          return {
            userId,
            discogsReleaseId: release.basic_information.id,
            artistName: release.basic_information.artists?.[0]?.name || 'Unknown Artist',
            albumTitle: release.basic_information.title || 'Unknown Title',
            albumCoverUrl,
            year: release.basic_information.year || null,
            format: release.basic_information.formats?.[0]?.name || null,
          };
        });

      // Insérer en base de données (insertion en lot pour la performance)
      if (releasesToInsert.length > 0) {
        try {
          // Essayer d'abord avec Drizzle (plus sécurisé et performant)
          await db.insert(userDiscogsCollection).values(releasesToInsert);
        } catch (drizzleError) {
          if (process.env.NODE_ENV === 'development') {
          console.warn('⚠️ Erreur Drizzle pour insertion, utilisation de SQL direct:', drizzleError);
          }

          // Fallback avec insertions individuelles sécurisées avec UPSERT
          const { sql } = await import('drizzle-orm');
          for (const release of releasesToInsert) {
            await db.execute(sql`
              INSERT INTO user_discogs_collection ("userId", "discogsReleaseId", "artistName", "albumTitle", "albumCoverUrl", "year", "format", "syncedAt")
              VALUES (${release.userId}, ${release.discogsReleaseId}, ${release.artistName}, ${release.albumTitle}, ${release.albumCoverUrl}, ${release.year}, ${release.format}, NOW())
              ON CONFLICT ("userId", "discogsReleaseId")
              DO UPDATE SET
                "artistName" = EXCLUDED."artistName",
                "albumTitle" = EXCLUDED."albumTitle",
                "albumCoverUrl" = EXCLUDED."albumCoverUrl",
                "year" = EXCLUDED."year",
                "format" = EXCLUDED."format",
                "syncedAt" = NOW()
            `);
          }
        }

        totalSynced += releasesToInsert.length;
        if (process.env.NODE_ENV === 'development') {
        console.log(`💾 ${releasesToInsert.length} releases sauvegardées (page ${page})`);
        }
      }

      // Vérifier s'il y a d'autres pages
      if (collection.pagination.page >= collection.pagination.pages) {
        if (process.env.NODE_ENV === 'development') {
        console.log(`📄 Dernière page atteinte (${collection.pagination.pages} pages au total)`);
        }
        break;
      }

      page++;

      // Respecter les limites de taux de l'API Discogs (60 requêtes par minute)
      await new Promise(resolve => setTimeout(resolve, 1100)); // 1.1 seconde entre les requêtes
    }

    const duration = Date.now() - startTime;
    if (process.env.NODE_ENV === 'development') {
    console.log(`✅ Synchronisation Discogs terminée pour l'utilisateur ${userId}: ${totalSynced} releases synchronisées en ${Math.round(duration / 1000)}s`);
    }

    // Invalider le cache pour forcer le rechargement lors de la prochaine génération de recommandations
    const { invalidateDiscogsCache } = await import('@/lib/album-matching');
    invalidateDiscogsCache(userId);

    // Déclencher automatiquement la mise à jour des recommandations existantes
    try {
      if (process.env.NODE_ENV === 'development') {
      console.log(`🔄 Mise à jour automatique des recommandations après synchronisation Discogs...`);
      }
      await updateExistingRecommendationsWithDiscogs(userId);
    } catch (updateError) {
      if (process.env.NODE_ENV === 'development') {
      console.warn("⚠️ Erreur lors de la mise à jour des recommandations après sync Discogs:", updateError);
      }
      // Ne pas faire échouer la synchronisation si la mise à jour échoue
    }

    DiscogsSyncLogger.log({
      userId,
      action: 'sync_success',
      details: { syncedCount: totalSynced, username },
      duration
    });

    return {
      success: true,
      syncedCount: totalSynced,
      username
    };

  } catch (error) {
    const duration = Date.now() - startTime;
    const errorType = categorizeDiscogsError(error);
    const errorMessage = error instanceof Error ? error.message : "Erreur inconnue";

    console.error("Erreur lors de la synchronisation Discogs:", error);

    DiscogsSyncLogger.log({
      userId,
      action: 'sync_error',
      error: errorMessage,
      details: { errorType },
      duration
    });

    return {
      success: false,
      error: errorMessage,
      errorType
    };
  }
}

/**
 * Met à jour les recommandations existantes avec les nouvelles données Discogs
 * Appelé automatiquement après une synchronisation Discogs réussie
 */
async function updateExistingRecommendationsWithDiscogs(userId: string) {
  try {
    if (process.env.NODE_ENV === 'development') {
    console.log(`🔄 Mise à jour des recommandations existantes avec les nouvelles données Discogs pour l'utilisateur ${userId}`);
    }

    // Récupérer toutes les recommandations existantes de l'utilisateur
    const existingRecommendations = await db.query.recommendations.findMany({
      where: eq(recommendations.userId, userId),
    });

    if (existingRecommendations.length === 0) {
      if (process.env.NODE_ENV === 'development') {
      console.log(`📭 Aucune recommandation existante à mettre à jour pour l'utilisateur ${userId}`);
      }
      return;
    }

    if (process.env.NODE_ENV === 'development') {
    console.log(`📊 ${existingRecommendations.length} recommandations existantes à vérifier`);
    }

    // Importer les fonctions de matching
    const { getUserDiscogsCollection, markOwnedAlbums, getUserWishlist, markWishlistedAlbums } = await import('@/lib/album-matching');

    // Récupérer la collection Discogs mise à jour
    const discogsCollection = await getUserDiscogsCollection(userId, db);

    if (discogsCollection.length === 0) {
      if (process.env.NODE_ENV === 'development') {
      console.log(`📭 Aucune collection Discogs trouvée, marquage de tous les albums comme non possédés`);
      }

      // Marquer tous les albums comme non possédés
      for (const rec of existingRecommendations) {
        if (rec.isOwned) {
          await db.update(recommendations)
            .set({ isOwned: false })
            .where(eq(recommendations.id, rec.id));
        }
      }
      return;
    }

    if (process.env.NODE_ENV === 'development') {
    console.log(`🎵 Collection Discogs trouvée: ${discogsCollection.length} albums`);
    }

    // Marquer les albums possédés avec la nouvelle collection
    // Mapper les données pour convertir null en undefined pour la compatibilité de type
    const mappedRecommendations = existingRecommendations.map(rec => ({
      artistName: rec.artistName,
      albumTitle: rec.albumTitle,
      spotifyAlbumId: rec.spotifyAlbumId || undefined,
      albumCoverUrl: rec.albumCoverUrl || undefined,
      listenScore: rec.listenScore,
      timeframe: rec.timeframe
    }));

    const updatedRecommendations = markOwnedAlbums(mappedRecommendations, discogsCollection);

    // Mettre à jour chaque recommandation si nécessaire
    let updatedCount = 0;
    for (let i = 0; i < existingRecommendations.length; i++) {
      const original = existingRecommendations[i];
      const updated = updatedRecommendations[i];

      if (original.isOwned !== updated.isOwned) {
        await db.update(recommendations)
          .set({ isOwned: updated.isOwned })
          .where(eq(recommendations.id, original.id));
        updatedCount++;
      }
    }

    const ownedCount = updatedRecommendations.filter(r => r.isOwned).length;
    if (process.env.NODE_ENV === 'development') {
    console.log(`✅ Mise à jour terminée: ${updatedCount} recommandations modifiées, ${ownedCount} albums marqués comme possédés`);
    }

    // Note: Les albums de la wishlist ne sont plus automatiquement supprimés lors de la synchronisation Discogs
    // Les utilisateurs peuvent maintenir leurs envies même s'ils possèdent déjà l'album dans leur collection
    // Cela permet de garder une trace des albums souhaités pour d'autres formats/éditions
    if (process.env.NODE_ENV === 'development') {
    console.log(`📋 Synchronisation terminée - Les albums en wishlist sont préservés même s'ils sont dans la collection`);
    }

    // Invalider le cache de la wishlist pour s'assurer que les statuts "In your collection" sont à jour
    try {
      const { invalidateWishlistCache } = await import('@/lib/album-matching');
      invalidateWishlistCache(userId);
      if (process.env.NODE_ENV === 'development') {
      console.log(`🔄 Cache wishlist invalidé pour mise à jour des statuts de possession`);
      }
    } catch (cacheError) {
      if (process.env.NODE_ENV === 'development') {
      console.warn("⚠️ Erreur lors de l'invalidation du cache wishlist:", cacheError);
      }
    }

  } catch (error) {
    console.error("❌ Erreur lors de la mise à jour des recommandations avec Discogs:", error);
    throw error;
  }
}

/**
 * Supprime définitivement le compte utilisateur et toutes ses données
 */
export async function deleteAccount() {
  const session = await getSession();
  if (!session?.user?.id) {
    throw new Error("Non autorisé");
  }

  const userId = session.user.id;

  try {
    if (process.env.NODE_ENV === 'development') {
    console.log(`🗑️ Début de la suppression du compte utilisateur ${userId}`);
    }

    // Supprimer explicitement toutes les données liées pour s'assurer de la cohérence
    // même si les contraintes CASCADE ne fonctionnent pas correctement

    // 1. Supprimer les recommandations
    const deletedRecommendations = await db.delete(recommendations).where(eq(recommendations.userId, userId));
    if (process.env.NODE_ENV === 'development') {
    console.log(`✅ Recommandations supprimées pour l'utilisateur ${userId}`);
    }

    // 2. Supprimer la collection Discogs
    const deletedDiscogs = await db.delete(userDiscogsCollection).where(eq(userDiscogsCollection.userId, userId));
    if (process.env.NODE_ENV === 'development') {
    console.log(`✅ Collection Discogs supprimée pour l'utilisateur ${userId}`);
    }

    // 3. Supprimer les éléments de la wishlist
    const deletedWishlistItems = await db.delete(wishlistItems).where(eq(wishlistItems.userId, userId));
    if (process.env.NODE_ENV === 'development') {
    console.log(`✅ Éléments de la wishlist supprimés pour l'utilisateur ${userId}`);
    }

    // 4. Supprimer les comptes connectés (Spotify, Discogs)
    const deletedAccounts = await db.delete(accounts).where(eq(accounts.userId, userId));
    if (process.env.NODE_ENV === 'development') {
    console.log(`✅ Comptes connectés supprimés pour l'utilisateur ${userId}`);
    }

    // 5. Supprimer l'utilisateur principal
    const deletedUser = await db.delete(users).where(eq(users.id, userId));
    if (process.env.NODE_ENV === 'development') {
    console.log(`✅ Utilisateur principal supprimé: ${userId}`);
    }

    if (process.env.NODE_ENV === 'development') {
    console.log(`🗑️ Compte utilisateur ${userId} et toutes ses données supprimés avec succès`);
    }

    return { success: true };
  } catch (error) {
    console.error("Erreur lors de la suppression du compte:", error);
    throw new Error("Erreur lors de la suppression du compte");
  }
}

/**
 * US 3.3: Enrichit les recommandations non possédées avec les offres Rakuten
 * Fonction utilitaire pour le processus de génération de recommandations
 */
async function enrichWithRakutenOffers(recommendations: any[], userId: string): Promise<any[]> {
  try {
    if (process.env.NODE_ENV === 'development') {
    console.log(`💰 Enrichissement Rakuten pour ${recommendations.length} recommandations (utilisateur ${userId})`);
    }

    // Importer le client Rakuten
    const { searchVinylOffers } = await import('@/lib/rakuten');

    // Filtrer les albums non possédés (seuls ceux-ci ont besoin d'offres d'achat)
    const notOwnedAlbums = recommendations.filter(album => !album.isOwned);
    const ownedAlbums = recommendations.filter(album => album.isOwned);

    if (process.env.NODE_ENV === 'development') {
    console.log(`🎯 ${notOwnedAlbums.length} albums non possédés à enrichir, ${ownedAlbums.length} albums possédés ignorés`);
    }

    if (notOwnedAlbums.length === 0) {
      if (process.env.NODE_ENV === 'development') {
      console.log(`📭 Aucun album à enrichir pour l'utilisateur ${userId}`);
      }
      return recommendations.map(album => ({ ...album, affiliateLinks: null }));
    }

    // Limiter à 10 albums maximum pour éviter les timeouts et respecter les limites API
    const albumsToEnrich = notOwnedAlbums.slice(0, 10);
    if (notOwnedAlbums.length > 10) {
      if (process.env.NODE_ENV === 'development') {
      console.log(`⚠️ Limitation à 10 albums sur ${notOwnedAlbums.length} pour optimiser les performances`);
      }
    }

    // Enrichir chaque album avec les offres Awin (traitement séquentiel pour respecter les limites API)
    const enrichedNotOwnedAlbums: any[] = [];
    let successCount = 0;
    let errorCount = 0;

    for (let i = 0; i < albumsToEnrich.length; i++) {
      const album = albumsToEnrich[i];
      const startTime = Date.now();

      try {
        if (process.env.NODE_ENV === 'development') {
        console.log(`🔍 [${i + 1}/${albumsToEnrich.length}] Recherche offres pour: ${album.artistName} - ${album.albumTitle}`);
        }

        // Rechercher les offres Rakuten pour cet album
        const offers = await searchVinylOffers(album.artistName, album.albumTitle);

        if (offers.length > 0) {
          // Convertir les offres au format attendu par la base de données
          const affiliateLinks = offers.map(offer => ({
            vendor: offer.vendor,
            url: offer.url,
            price: offer.price,
            currency: offer.currency,
            merchantId: offer.merchantId,
            productName: offer.productName,
            inStock: offer.inStock,
          }));

          enrichedNotOwnedAlbums.push({
            ...album,
            affiliateLinks: affiliateLinks
          });

          successCount++;
          if (process.env.NODE_ENV === 'development') {
          console.log(`✅ ${offers.length} offres trouvées pour "${album.albumTitle}" (${Date.now() - startTime}ms)`);
          }
        } else {
          // Aucune offre trouvée
          enrichedNotOwnedAlbums.push({
            ...album,
            affiliateLinks: []
          });

          if (process.env.NODE_ENV === 'development') {
          console.log(`❌ Aucune offre trouvée pour "${album.albumTitle}" (${Date.now() - startTime}ms)`);
          }
        }

      } catch (error) {
        console.error(`❌ Erreur lors de l'enrichissement de "${album.albumTitle}":`, error);

        // En cas d'erreur, ajouter l'album sans offres pour ne pas bloquer le processus
        enrichedNotOwnedAlbums.push({
          ...album,
          affiliateLinks: []
        });

        errorCount++;
      }

      // Délai entre les recherches pour respecter les limites API Rakuten
      if (i < albumsToEnrich.length - 1) {
        const searchDuration = Date.now() - startTime;
        const baseDelay = 1000; // 1 seconde de base
        const additionalDelay = Math.max(0, 2000 - searchDuration); // Assurer au moins 2 secondes entre les requêtes
        const totalDelay = baseDelay + additionalDelay;

        if (process.env.NODE_ENV === 'development') {
        console.log(`⏳ Attente de ${Math.round(totalDelay / 1000)}s avant la prochaine recherche...`);
        }
        await new Promise(resolve => setTimeout(resolve, totalDelay));
      }
    }

    // Ajouter les albums non enrichis (au-delà de la limite de 10) sans offres
    const remainingNotOwnedAlbums = notOwnedAlbums.slice(10).map(album => ({
      ...album,
      affiliateLinks: []
    }));

    // Combiner tous les résultats
    const allEnrichedAlbums = [
      ...enrichedNotOwnedAlbums,
      ...remainingNotOwnedAlbums,
      ...ownedAlbums.map(album => ({ ...album, affiliateLinks: null })) // Albums possédés n'ont pas besoin d'offres
    ];

    if (process.env.NODE_ENV === 'development') {
    console.log(`🎯 Enrichissement Rakuten terminé: ${successCount} succès, ${errorCount} erreurs sur ${albumsToEnrich.length} albums traités`);
    }

    return allEnrichedAlbums;

  } catch (error) {
    console.error("❌ Erreur critique lors de l'enrichissement Rakuten:", error);

    // En cas d'erreur critique, retourner les recommandations sans enrichissement
    return recommendations.map(album => ({
      ...album,
      affiliateLinks: album.isOwned ? null : []
    }));
  }
}

/**
 * US 8.2 Phase 1: Enrichit TOUTES les recommandations avec les liens Amazon et offres Rakuten
 * Fonction combinée qui utilise Amazon Phase 1 (liens de recherche) + Rakuten
 *
 * Phase 1 - Amorçage:
 * - Amazon: liens de recherche pour TOUS les albums (possédés et non possédés) - SANS API
 * - Rakuten: recherche API uniquement pour les albums non possédés (comme avant)
 * - Pas de rate limiting Amazon (pas d'appels API)
 */
async function enrichWithAffiliateOffers(recommendations: any[], userId: string): Promise<any[]> {
  try {
    // Vérifier si Rakuten est activé
    const isRakutenEnabled = process.env.RAKUTEN_ENABLED !== 'false';

    if (isRakutenEnabled) {
      if (process.env.NODE_ENV === 'development') {
      console.log(`💰 Enrichissement Amazon Phase 1 + Rakuten pour ${recommendations.length} recommandations (utilisateur ${userId})`);
      }
    } else {
      if (process.env.NODE_ENV === 'development') {
      console.log(`💰 Enrichissement Amazon Phase 1 uniquement pour ${recommendations.length} recommandations (utilisateur ${userId}) - Rakuten désactivé`);
      }
    }

    // Importer les clients d'affiliation
    const { enrichWithAmazonSearchLinks } = await import('@/lib/amazon-phase1');

    // Phase 1: Enrichir TOUS les albums avec des liens de recherche Amazon (instantané)
    if (process.env.NODE_ENV === 'development') {
    console.log(`🔗 Phase 1: Ajout des liens de recherche Amazon pour tous les albums...`);
    }
    const amazonEnrichedRecommendations = enrichWithAmazonSearchLinks(recommendations);

    // Si Rakuten est désactivé, retourner directement les résultats Amazon
    if (!isRakutenEnabled) {
      if (process.env.NODE_ENV === 'development') {
      console.log(`🚫 Rakuten désactivé - utilisation d'Amazon Phase 1 uniquement`);
      }
      console.log(`🎯 Enrichissement terminé: ${recommendations.length} liens Amazon générés`);
      return amazonEnrichedRecommendations;
    }

    // Séparer les albums non possédés pour Rakuten (seulement si activé)
    const { searchVinylOffers } = await import('@/lib/rakuten');
    const notOwnedAlbums = amazonEnrichedRecommendations.filter(album => !album.isOwned);
    const maxRakutenAlbums = 10; // Limiter Rakuten pour éviter les timeouts

    const albumsForRakuten = notOwnedAlbums.slice(0, maxRakutenAlbums);

    if (process.env.NODE_ENV === 'development') {
    console.log(`📊 Traitement: ${recommendations.length} albums avec liens Amazon, ${albumsForRakuten.length} albums pour Rakuten API`);
    }

    // Phase 1: Amazon enrichment déjà fait (liens de recherche instantanés)
    // Pas besoin de boucle ou de rate limiting

    // Enrichir avec Rakuten (albums non possédés uniquement) - inchangé
    const rakutenEnrichedAlbums = new Map<string, any[]>();
    let rakutenSuccessCount = 0;
    let rakutenErrorCount = 0;

    for (let i = 0; i < albumsForRakuten.length; i++) {
      const album = albumsForRakuten[i];
      const albumKey = `${album.artistName}-${album.albumTitle}`;
      const startTime = Date.now();

      try {
        if (process.env.NODE_ENV === 'development') {
        console.log(`🔍 Rakuten [${i + 1}/${albumsForRakuten.length}] Recherche pour: ${album.artistName} - ${album.albumTitle}`);
        }

        const rakutenOffers = await searchVinylOffers(album.artistName, album.albumTitle);

        if (rakutenOffers.length > 0) {
          // Convertir au format attendu
          const formattedOffers = rakutenOffers.map(offer => ({
            vendor: offer.vendor,
            url: offer.url,
            price: offer.price,
            currency: offer.currency,
            merchantId: offer.merchantId,
            productName: offer.productName,
            inStock: offer.inStock,
          }));

          rakutenEnrichedAlbums.set(albumKey, formattedOffers);
          rakutenSuccessCount++;
          if (process.env.NODE_ENV === 'development') {
          console.log(`✅ Rakuten: ${rakutenOffers.length} offres trouvées (${Date.now() - startTime}ms)`);
          }
        } else {
          if (process.env.NODE_ENV === 'development') {
          console.log(`❌ Rakuten: aucune offre trouvée (${Date.now() - startTime}ms)`);
          }
        }

      } catch (error) {
        console.error(`❌ Erreur Rakuten pour "${album.albumTitle}":`, error);
        rakutenErrorCount++;
      }

      // Rate limiting Rakuten: 2 secondes entre chaque requête
      if (i < albumsForRakuten.length - 1) {
        const searchDuration = Date.now() - startTime;
        const baseDelay = 1000;
        const additionalDelay = Math.max(0, 2000 - searchDuration);
        const totalDelay = baseDelay + additionalDelay;

        if (process.env.NODE_ENV === 'development') {
        console.log(`⏳ Attente ${Math.round(totalDelay / 1000)}s (rate limiting Rakuten)...`);
        }
        await new Promise(resolve => setTimeout(resolve, totalDelay));
      }
    }

    // Combiner les résultats: Amazon (déjà dans amazonEnrichedRecommendations) + Rakuten
    const finalEnrichedAlbums = amazonEnrichedRecommendations.map(album => {
      const albumKey = `${album.artistName}-${album.albumTitle}`;

      // Commencer avec les liens existants (Amazon Phase 1 déjà ajouté)
      let affiliateLinks = [...(album.affiliateLinks || [])];

      // Ajouter les offres Rakuten si disponibles (uniquement pour les albums non possédés)
      if (!album.isOwned) {
        const rakutenOffers = rakutenEnrichedAlbums.get(albumKey);
        if (rakutenOffers) {
          affiliateLinks.push(...rakutenOffers);
        }
      }

      return {
        ...album,
        affiliateLinks: affiliateLinks.length > 0 ? affiliateLinks : (album.isOwned ? null : [])
      };
    });

    if (process.env.NODE_ENV === 'development') {
    console.log(`🎯 Enrichissement Phase 1 terminé:`);
    }
    console.log(`   Amazon Phase 1: ${recommendations.length} liens de recherche générés (instantané)`);
    if (process.env.RAKUTEN_ENABLED !== 'false') {
      if (process.env.NODE_ENV === 'development') {
      console.log(`   Rakuten: ${rakutenSuccessCount} succès, ${rakutenErrorCount} erreurs sur ${albumsForRakuten.length} albums`);
      }
    } else {
      if (process.env.NODE_ENV === 'development') {
      console.log(`   Rakuten: désactivé`);
      }
    }

    return finalEnrichedAlbums;

  } catch (error) {
    console.error("❌ Erreur critique lors de l'enrichissement des offres:", error);

    // En cas d'erreur critique, retourner les recommandations sans enrichissement
    return recommendations.map(album => ({
      ...album,
      affiliateLinks: album.isOwned ? null : []
    }));
  }
}
