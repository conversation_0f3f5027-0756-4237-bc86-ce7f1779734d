import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { users, recommendations } from "@/lib/db/schema";
import { eq, desc, and } from "drizzle-orm";

/**
 * API pour récupérer les recommandations publiques d'un utilisateur
 * Utilisée pour le changement de timeframe côté client
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ publicListId: string }> }
) {
  try {
    const { publicListId } = await params;
    const { searchParams } = new URL(request.url);
    const timeframe = searchParams.get("timeframe") || "short_term";

    // Valider le timeframe
    const validTimeframes = ["short_term", "medium_term", "long_term"];
    if (!validTimeframes.includes(timeframe)) {
      return NextResponse.json(
        { error: "Timeframe invalide" },
        { status: 400 }
      );
    }

    // Récupérer l'utilisateur par son publicListId
    const user = await db.query.users.findFirst({
      where: and(
        eq(users.publicListId, publicListId),
        eq(users.publicListEnabled, true)
      ),
      columns: {
        id: true,
        publicListEnabled: true,
      },
    });

    if (!user || !user.publicListEnabled) {
      return NextResponse.json(
        { error: "Liste introuvable ou privée" },
        { status: 404 }
      );
    }

    // Récupérer les recommandations pour le timeframe demandé
    const userRecommendations = await db.query.recommendations.findMany({
      where: and(
        eq(recommendations.userId, user.id),
        eq(recommendations.timeframe, timeframe)
      ),
      orderBy: [desc(recommendations.listenScore), desc(recommendations.generatedAt)],
      limit: 50,
    });

    return NextResponse.json({
      recommendations: userRecommendations,
      timeframe,
      totalRecommendations: userRecommendations.length,
    });

  } catch (error) {
    console.error("Erreur lors de la récupération des données publiques:", error);
    return NextResponse.json(
      { error: "Erreur serveur" },
      { status: 500 }
    );
  }
}
