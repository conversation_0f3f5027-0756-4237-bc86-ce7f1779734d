import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { sql } from "drizzle-orm";

/**
 * API pour diagnostiquer et corriger l'incohérence de nomenclature
 * de la colonne email_on_new_follower vs emailOnNewFollower
 */
export async function GET(request: NextRequest) {
  try {
    console.log("🔍 Diagnostic nomenclature colonne email notification...");

    const environment = process.env.VERCEL_ENV || process.env.NODE_ENV || 'unknown';
    
    const result = {
      environment,
      timestamp: new Date().toISOString(),
      columns: {
        camelCase: { exists: false, type: null },
        snakeCase: { exists: false, type: null }
      },
      diagnosis: "",
      action: ""
    };

    // Vérifier l'existence des deux variantes de noms
    const columnsResult = await db.execute(sql`
      SELECT column_name, data_type
      FROM information_schema.columns 
      WHERE table_name = 'users' 
      AND column_name IN ('emailOnNewFollower', 'email_on_new_follower')
      ORDER BY column_name;
    `);

    // Analyser les résultats
    for (const col of columnsResult) {
      const colName = (col as any).column_name;
      const colType = (col as any).data_type;
      
      if (colName === 'emailOnNewFollower') {
        result.columns.camelCase.exists = true;
        result.columns.camelCase.type = colType;
      } else if (colName === 'email_on_new_follower') {
        result.columns.snakeCase.exists = true;
        result.columns.snakeCase.type = colType;
      }
    }

    // Diagnostic
    if (result.columns.camelCase.exists && result.columns.snakeCase.exists) {
      result.diagnosis = "❌ PROBLÈME: Les deux colonnes existent (doublon)";
      result.action = "Supprimer l'ancienne colonne camelCase et garder snake_case";
    } else if (result.columns.camelCase.exists && !result.columns.snakeCase.exists) {
      result.diagnosis = "⚠️ STAGING: Utilise camelCase (emailOnNewFollower)";
      result.action = "Renommer vers snake_case (email_on_new_follower)";
    } else if (!result.columns.camelCase.exists && result.columns.snakeCase.exists) {
      result.diagnosis = "✅ PRODUCTION: Utilise snake_case correct (email_on_new_follower)";
      result.action = "RAS - Nomenclature correcte";
    } else {
      result.diagnosis = "❌ ERREUR: Aucune colonne trouvée";
      result.action = "Ajouter email_on_new_follower";
    }

    console.log(`📊 Diagnostic: ${result.diagnosis}`);

    return NextResponse.json(result);

  } catch (error) {
    console.error("❌ Erreur diagnostic colonne email:", error);
    return NextResponse.json(
      { 
        error: "Erreur diagnostic colonne email", 
        details: error instanceof Error ? error.message : String(error),
        environment: process.env.VERCEL_ENV || process.env.NODE_ENV || 'unknown'
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    console.log("🔧 Correction nomenclature colonne email notification...");

    const environment = process.env.VERCEL_ENV || process.env.NODE_ENV || 'unknown';
    
    const result = {
      environment,
      timestamp: new Date().toISOString(),
      operations: [] as string[],
      success: false,
      error: null as string | null
    };

    try {
      // D'abord faire le diagnostic
      const columnsResult = await db.execute(sql`
        SELECT column_name
        FROM information_schema.columns 
        WHERE table_name = 'users' 
        AND column_name IN ('emailOnNewFollower', 'email_on_new_follower')
      `);

      const existingColumns = columnsResult.map(col => (col as any).column_name);
      
      if (existingColumns.includes('emailOnNewFollower') && !existingColumns.includes('email_on_new_follower')) {
        // Cas staging: renommer camelCase vers snake_case
        console.log("🔄 Renommage emailOnNewFollower → email_on_new_follower...");
        
        await db.execute(sql`
          ALTER TABLE users RENAME COLUMN "emailOnNewFollower" TO "email_on_new_follower";
        `);
        
        result.operations.push("✅ Colonne renommée: emailOnNewFollower → email_on_new_follower");
        
      } else if (existingColumns.includes('emailOnNewFollower') && existingColumns.includes('email_on_new_follower')) {
        // Doublon: copier data et supprimer camelCase
        console.log("🔄 Migration données et suppression doublon...");
        
        await db.execute(sql`
          UPDATE users SET email_on_new_follower = "emailOnNewFollower";
        `);
        result.operations.push("✅ Données copiées vers email_on_new_follower");
        
        await db.execute(sql`
          ALTER TABLE users DROP COLUMN "emailOnNewFollower";
        `);
        result.operations.push("✅ Ancienne colonne emailOnNewFollower supprimée");
        
      } else if (!existingColumns.includes('email_on_new_follower')) {
        // Manquante: ajouter la colonne
        console.log("➕ Ajout colonne email_on_new_follower...");
        
        await db.execute(sql`
          ALTER TABLE users ADD COLUMN IF NOT EXISTS "email_on_new_follower" BOOLEAN NOT NULL DEFAULT TRUE;
        `);
        result.operations.push("✅ Colonne email_on_new_follower ajoutée");
        
      } else {
        result.operations.push("ℹ️ Colonne email_on_new_follower déjà correcte");
      }

      result.success = true;
      console.log("🎉 Correction nomenclature terminée avec succès");

    } catch (operationError) {
      result.error = operationError instanceof Error ? operationError.message : String(operationError);
      console.error("❌ Erreur correction:", operationError);
    }

    return NextResponse.json(result);

  } catch (error) {
    console.error("❌ Erreur correction colonne email:", error);
    return NextResponse.json(
      { 
        error: "Erreur correction colonne email", 
        details: error instanceof Error ? error.message : String(error),
        environment: process.env.VERCEL_ENV || process.env.NODE_ENV || 'unknown'
      },
      { status: 500 }
    );
  }
} 