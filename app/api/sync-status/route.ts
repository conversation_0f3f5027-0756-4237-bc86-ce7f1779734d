import { NextRequest, NextResponse } from "next/server";
import { getSession } from "@/lib/auth";
import { db } from "@/lib/db";
import { recommendations, userDiscogsCollection } from "@/lib/db/schema";
import { eq, desc } from "drizzle-orm";

/**
 * API pour récupérer les informations de synchronisation réelles
 */
export async function GET(request: NextRequest) {
  try {
    const session = await getSession();
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: "Non autorisé" },
        { status: 401 }
      );
    }

    const userId = session.user.id;
    const { searchParams } = new URL(request.url);
    const type = searchParams.get("type"); // "spotify" ou "discogs"

    if (type === "spotify") {
      // Récupérer les informations de synchronisation Spotify
      const latestRecommendations = await db.query.recommendations.findMany({
        where: eq(recommendations.userId, userId),
        orderBy: [desc(recommendations.generatedAt)],
        limit: 1
      });

      const totalRecommendations = await db.query.recommendations.findMany({
        where: eq(recommendations.userId, userId),
      });

      const lastSync = latestRecommendations[0]?.generatedAt;
      const count = totalRecommendations.length;

      return NextResponse.json({
        success: true,
        data: {
          type: "spotify",
          lastSync: lastSync?.toISOString(),
          count,
          status: getStatus(lastSync)
        }
      });

    } else if (type === "discogs") {
      // Récupérer les informations de synchronisation Discogs
      const latestCollection = await db.query.userDiscogsCollection.findMany({
        where: eq(userDiscogsCollection.userId, userId),
        orderBy: [desc(userDiscogsCollection.syncedAt)],
        limit: 1
      });

      const totalCollection = await db.query.userDiscogsCollection.findMany({
        where: eq(userDiscogsCollection.userId, userId),
      });

      const lastSync = latestCollection[0]?.syncedAt;
      const count = totalCollection.length;

      return NextResponse.json({
        success: true,
        data: {
          type: "discogs",
          lastSync: lastSync?.toISOString(),
          count,
          status: getStatus(lastSync)
        }
      });

    } else {
      // Récupérer les deux types
      const [latestRecommendations, totalRecommendations, latestCollection, totalCollection] = await Promise.all([
        db.query.recommendations.findMany({
          where: eq(recommendations.userId, userId),
          orderBy: [desc(recommendations.generatedAt)],
          limit: 1
        }),
        db.query.recommendations.findMany({
          where: eq(recommendations.userId, userId),
        }),
        db.query.userDiscogsCollection.findMany({
          where: eq(userDiscogsCollection.userId, userId),
          orderBy: [desc(userDiscogsCollection.syncedAt)],
          limit: 1
        }),
        db.query.userDiscogsCollection.findMany({
          where: eq(userDiscogsCollection.userId, userId),
        })
      ]);

      const spotifyLastSync = latestRecommendations[0]?.generatedAt;
      const discogsLastSync = latestCollection[0]?.syncedAt;

      return NextResponse.json({
        success: true,
        data: {
          spotify: {
            type: "spotify",
            lastSync: spotifyLastSync?.toISOString(),
            count: totalRecommendations.length,
            status: getStatus(spotifyLastSync)
          },
          discogs: {
            type: "discogs",
            lastSync: discogsLastSync?.toISOString(),
            count: totalCollection.length,
            status: getStatus(discogsLastSync)
          }
        }
      });
    }

  } catch (error) {
    console.error("Erreur lors de la récupération du statut de sync:", error);
    
    return NextResponse.json(
      {
        success: false,
        error: "Erreur lors de la récupération du statut",
        message: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}

/**
 * Détermine le statut basé sur la date de dernière synchronisation
 */
function getStatus(lastSync: Date | null | undefined): "never" | "recent" | "old" | "syncing" {
  if (!lastSync) return "never";
  
  const now = new Date();
  const hoursSinceSync = Math.floor((now.getTime() - lastSync.getTime()) / (1000 * 60 * 60));
  
  if (hoursSinceSync < 6) return "recent";
  return "old";
}
