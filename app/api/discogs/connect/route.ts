import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/auth";
import { db } from "@/lib/db";
import { accounts } from "@/lib/db/schema";
import { eq, and } from "drizzle-orm";
const Discogs = require('disconnect').Client;

// Configuration pour Discogs
const DISCOGS_CONFIG = {
  consumerKey: process.env.AUTH_DISCOGS_KEY!,
  consumerSecret: process.env.AUTH_DISCOGS_SECRET!,
  callbackUrl: `${process.env.NEXTAUTH_URL}/api/discogs/callback`,
};

export async function GET(request: NextRequest) {
  try {
    if (process.env.NODE_ENV === 'development') {
      console.log("🔗 Tentative de connexion Discogs");
    }

    // Récupérer le paramètre returnTo depuis l'URL
    const { searchParams } = new URL(request.url);
    const returnTo = searchParams.get("returnTo") || "/account";
    if (process.env.NODE_ENV === 'development') {
      console.log("📍 Page de retour demandée:", returnTo);
    }

    // Vérifier que l'utilisateur est connecté
    const session = await getServerSession(authOptions);
    if (process.env.NODE_ENV === 'development') {
      if (process.env.NODE_ENV === 'development') {
        console.log("🔍 Session récupérée:", {
      hasSession: !!session,
      hasUser: !!session?.user,
      hasUserId: !!session?.user?.id,
      userId: session?.user?.id
    });
      }
    }

    if (!session?.user?.id) {
      if (process.env.NODE_ENV === 'development') {
        console.log("❌ Utilisateur non connecté");
      }
      return NextResponse.json({ error: "Non autorisé" }, { status: 401 });
    }

    if (process.env.NODE_ENV === 'development') {

      console.log("✅ Utilisateur connecté:", session.user.id);

    }

    // Vérifier si l'utilisateur n'est pas déjà connecté à Discogs
    const existingAccount = await db.query.accounts.findFirst({
      where: and(
        eq(accounts.userId, session.user.id),
        eq(accounts.provider, "discogs")
      ),
    });

    if (existingAccount) {
      if (process.env.NODE_ENV === 'development') {
        console.log("⚠️ Compte Discogs déjà connecté");
      }
      return NextResponse.json({ error: "Compte Discogs déjà connecté" }, { status: 400 });
    }

    if (process.env.NODE_ENV === 'development') {

      console.log("🔄 Demande de request token à Discogs...");

    }

    // Utiliser la bibliothèque disconnect pour gérer OAuth
    const oAuth = new Discogs().oauth();

    // Obtenir un request token
    const requestTokenData = await new Promise((resolve, reject) => {
      oAuth.getRequestToken(
        DISCOGS_CONFIG.consumerKey,
        DISCOGS_CONFIG.consumerSecret,
        DISCOGS_CONFIG.callbackUrl,
        (err: any, data: any) => {
          if (err) reject(err);
          else resolve(data);
        }
      );
    });

    if (process.env.NODE_ENV === 'development') {

      console.log("📝 Request token reçu:", {
      token: (requestTokenData as any).token?.substring(0, 10) + "...",
      hasSecret: !!(requestTokenData as any).tokenSecret
    });

    }

    // Stocker temporairement le token secret et la page de retour dans la base de données
    await db.insert(accounts).values({
      userId: session.user.id,
      type: "oauth_temp",
      provider: "discogs_temp",
      providerAccountId: (requestTokenData as any).token,
      access_token: (requestTokenData as any).tokenSecret, // Stockage temporaire du secret
      access_token_secret: returnTo, // Stockage temporaire de la page de retour
      token_type: "request_token",
    });

    // L'URL d'autorisation est fournie par la bibliothèque
    const authorizeUrl = (requestTokenData as any).authorizeUrl;

    return NextResponse.json({ authorizeUrl });

  } catch (error) {
    console.error("Erreur lors de la connexion Discogs:", error);
    return NextResponse.json(
      { error: "Erreur lors de la connexion à Discogs" },
      { status: 500 }
    );
  }
}
