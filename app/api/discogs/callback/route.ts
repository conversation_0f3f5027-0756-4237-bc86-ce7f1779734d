import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/auth";
import { db } from "@/lib/db";
import { accounts } from "@/lib/db/schema";
import { eq, and } from "drizzle-orm";
import { syncDiscogsCollection } from "@/app/actions/user";
const Discogs = require('disconnect').Client;

// Configuration pour Discogs
const DISCOGS_CONFIG = {
  consumerKey: process.env.AUTH_DISCOGS_KEY!,
  consumerSecret: process.env.AUTH_DISCOGS_SECRET!,
};

export async function GET(request: NextRequest) {
  try {
    if (process.env.NODE_ENV === 'development') {
      console.log("🔍 Discogs callback appelé avec URL:", request.url);
    }

    const { searchParams } = new URL(request.url);
    const oauthToken = searchParams.get("oauth_token");
    const oauthVerifier = searchParams.get("oauth_verifier");

    if (process.env.NODE_ENV === 'development') {
      console.log("📋 Paramètres reçus:", { oauthToken, oauthVerifier });
    }

    if (!oauthToken || !oauthVerifier) {
      if (process.env.NODE_ENV === 'development') {
        console.log("❌ Paramètres manquants");
      }
      return NextResponse.redirect(`${process.env.NEXTAUTH_URL}/account?error=missing_params`);
    }

    // Vérifier que l'utilisateur est connecté
    const session = await getServerSession(authOptions);
    if (process.env.NODE_ENV === 'development') {
      if (process.env.NODE_ENV === 'development') {
        console.log("🔍 Session dans callback:", {
      hasSession: !!session,
      hasUser: !!session?.user,
      hasUserId: !!session?.user?.id,
      userId: session?.user?.id
    });
      }
    }

    if (!session?.user?.id) {
      if (process.env.NODE_ENV === 'development') {
        console.log("❌ Session manquante dans callback");
      }
      return NextResponse.redirect(`${process.env.NEXTAUTH_URL}/account?error=session_required`);
    }

    // Récupérer le token secret temporaire depuis la base de données
    const tempAccount = await db.query.accounts.findFirst({
      where: and(
        eq(accounts.userId, session.user.id),
        eq(accounts.provider, "discogs_temp"),
        eq(accounts.providerAccountId, oauthToken)
      ),
    });

    if (!tempAccount) {
      return NextResponse.redirect(`${process.env.NEXTAUTH_URL}/account?error=token_not_found`);
    }

    const tokenSecret = tempAccount.access_token!;
    const returnTo = tempAccount.access_token_secret || "/account"; // Page de retour stockée
    console.log("🔐 Token secret récupéré:", tokenSecret.substring(0, 10) + "...");
    if (process.env.NODE_ENV === 'development') {
      console.log("📍 Page de retour:", returnTo);
    }

    // Étape 2: Échanger le request token contre un access token
    if (process.env.NODE_ENV === 'development') {
      console.log("🔄 Échange du request token contre un access token...");
    }

    // Reconstruire l'objet auth comme attendu par la bibliothèque disconnect
    const authData = {
      method: 'oauth',
      level: 1, // Niveau 1 = request token obtenu
      consumerKey: DISCOGS_CONFIG.consumerKey,
      consumerSecret: DISCOGS_CONFIG.consumerSecret,
      token: oauthToken,
      tokenSecret: tokenSecret
    };

    // Créer un client Discogs avec les données d'auth
    const oAuth = new Discogs(authData).oauth();

    // Obtenir l'access token
    const accessTokenData = await new Promise((resolve, reject) => {
      oAuth.getAccessToken(
        oauthVerifier,
        (err: any, data: any) => {
          if (err) reject(err);
          else resolve(data);
        }
      );
    });

    if (process.env.NODE_ENV === 'development') {

      console.log("🎫 Access token reçu:", {
      token: (accessTokenData as any).token?.substring(0, 10) + "...",
      hasSecret: !!(accessTokenData as any).tokenSecret
    });

    }

    // Récupérer les informations du profil utilisateur Discogs
    if (process.env.NODE_ENV === 'development') {
      console.log("👤 Récupération du profil Discogs...");
    }

    // Créer un client authentifié avec l'access token
    // L'objet accessTokenData retourné par disconnect a déjà le bon format
    const authenticatedClient = new Discogs(accessTokenData);

    // Récupérer l'identité de l'utilisateur
    const profileData = await new Promise((resolve, reject) => {
      authenticatedClient.getIdentity((err: any, data: any) => {
        if (err) reject(err);
        else resolve(data);
      });
    });

    if (process.env.NODE_ENV === 'development') {

      console.log("✅ Profil Discogs récupéré:", {
      id: (profileData as any).id,
      username: (profileData as any).username
    });

    }

    // Supprimer l'enregistrement temporaire
    await db.delete(accounts).where(
      and(
        eq(accounts.userId, session.user.id),
        eq(accounts.provider, "discogs_temp"),
        eq(accounts.providerAccountId, oauthToken)
      )
    );

    // Vérifier si le compte Discogs existe déjà (par provider et providerAccountId)
    const existingDiscogsAccount = await db.query.accounts.findFirst({
      where: and(
        eq(accounts.provider, "discogs"),
        eq(accounts.providerAccountId, (profileData as any).id.toString())
      ),
    });

    if (existingDiscogsAccount) {
      if (process.env.NODE_ENV === 'development') {
        console.log("🔄 Mise à jour du compte Discogs existant");
      }
      // Mettre à jour le compte existant avec le nouvel utilisateur
      await db.update(accounts)
        .set({
          userId: session.user.id,
          access_token: (accessTokenData as any).token,
          access_token_secret: (accessTokenData as any).tokenSecret,
          token_type: "oauth_token",
        })
        .where(
          and(
            eq(accounts.provider, "discogs"),
            eq(accounts.providerAccountId, (profileData as any).id.toString())
          )
        );
    } else {
      if (process.env.NODE_ENV === 'development') {
        console.log("➕ Création d'un nouveau compte Discogs");
      }
      // Créer un nouveau compte
      await db.insert(accounts).values({
        userId: session.user.id,
        type: "oauth",
        provider: "discogs",
        providerAccountId: (profileData as any).id.toString(),
        access_token: (accessTokenData as any).token,
        token_type: "oauth_token",
        access_token_secret: (accessTokenData as any).tokenSecret,
      });
    }

    // Déclencher la synchronisation de la collection en arrière-plan
    try {
      await syncDiscogsCollection(session.user.id);
    } catch (error) {
      console.error("Erreur lors de la synchronisation Discogs:", error);
      // Ne pas faire échouer la connexion si la sync échoue
    }

    // Rediriger vers la page d'origine avec un message de succès
    const redirectUrl = `${process.env.NEXTAUTH_URL}${returnTo}${returnTo.includes('?') ? '&' : '?'}success=discogs_connected`;
    if (process.env.NODE_ENV === 'development') {
      console.log("🔄 Redirection vers:", redirectUrl);
    }
    return NextResponse.redirect(redirectUrl);

  } catch (error) {
    console.error("Erreur lors du callback Discogs:", error);
    // En cas d'erreur, rediriger vers /account par défaut
    return NextResponse.redirect(`${process.env.NEXTAUTH_URL}/account?error=callback_failed`);
  }
}
