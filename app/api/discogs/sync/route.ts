import { NextRequest, NextResponse } from "next/server";
import { getSession } from "@/lib/auth";
import { syncDiscogsCollection } from "@/app/actions/user";
import { revalidatePath } from "next/cache";

export async function POST(request: NextRequest) {
  try {
    // Vérifier que l'utilisateur est connecté
    const session = await getSession();
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: "Non autorisé" },
        { status: 401 }
      );
    }

    if (process.env.NODE_ENV === 'development') {
    console.log(`🔄 Synchronisation manuelle demandée par l'utilisateur ${session.user.id}`);
    }

    // Déclencher la synchronisation
    const result = await syncDiscogsCollection(session.user.id);

    if (result.success) {
      if (process.env.NODE_ENV === 'development') {
      console.log(`✅ Synchronisation manuelle réussie: ${result.syncedCount} albums`);
      }

      // Invalider le cache des pages pour forcer le rechargement
      revalidatePath("/collection");
      revalidatePath("/recommendations");

      return NextResponse.json({
        success: true,
        syncedCount: result.syncedCount,
        message: "Synchronisation réussie"
      });
    } else {
      console.error(`❌ Erreur lors de la synchronisation manuelle:`, result.error);
      return NextResponse.json(
        { error: result.error || "Erreur lors de la synchronisation" },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error("❌ Erreur dans l'API de synchronisation:", error);
    return NextResponse.json(
      { error: "Erreur interne du serveur" },
      { status: 500 }
    );
  }
}
