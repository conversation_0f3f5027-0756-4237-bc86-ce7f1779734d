import { type NextRequest, NextResponse } from 'next/server';
import { generateRecommendationsForUser } from '@/app/actions/user';

export async function POST(request: NextRequest) {
  // 1. Vérification de base de sécurité (simplifié pour le moment)
  const authHeader = request.headers.get('authorization');
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return new Response('Non autorisé', { status: 401 });
  }

  // 2. Extraire le payload du job
  const body = await request.json();
  const { userId } = body;

  if (!userId) {
    return new Response('User ID manquant', { status: 400 });
  }

  try {
    console.log(`[Worker] Démarrage du rafraîchissement pour l'utilisateur: ${userId}`);
    
    // 3. Exécuter la logique de génération de recommandations
    const result = await generateRecommendationsForUser(userId);

    if (result.status === 'success') {
      console.log(`[Worker] Rafraîchissement réussi pour l'utilisateur: ${userId}, ${result.recommendationsCount} recommandations générées.`);
      return NextResponse.json({ success: true, message: 'Rafraîchissement réussi' });
    } else {
      console.warn(`[Worker] Rafraîchissement terminé sans nouvelles recommandations pour l'utilisateur: ${userId}`);
      return NextResponse.json({ success: true, message: 'Aucune nouvelle recommandation' });
    }
  } catch (error) {
    console.error(`[Worker] Erreur lors du rafraîchissement pour l'utilisateur: ${userId}`, error);
    return new Response(`Erreur lors du traitement du job pour ${userId}`, { status: 500 });
  }
}
