import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { sql } from "drizzle-orm";

/**
 * API PUBLIQUE pour vérifier l'état des migrations Epic Social V1
 * Accessible publiquement pour diagnostic production
 */
export async function GET(request: NextRequest) {
  try {
    console.log("🔍 Vérification état Epic Social V1...");

    const environment = process.env.VERCEL_ENV || process.env.NODE_ENV || 'unknown';
    
    const result = {
      environment,
      timestamp: new Date().toISOString(),
      databaseUrl: process.env.DATABASE_URL ? "configured" : "missing",
      tableExists: false,
      totalColumns: 0,
      allColumns: [] as any[],
      epicSocialStatus: {
        requiredColumns: [
          "profile_visibility",
          "share_recommendations", 
          "share_wishlist",
          "share_collection",
          "email_on_new_follower"
        ],
        presentColumns: [] as string[],
        missingColumns: [] as string[],
        allPresent: false,
        migrationNeeded: true
      },
      tables: {
        followers: { exists: false, columns: 0 },
        notifications: { exists: false, columns: 0 }
      },
      enums: {
        profile_visibility: { exists: false, values: [] as string[] },
        notification_type: { exists: false, values: [] as string[] }
      },
      recommendations: [] as string[]
    };

    try {
      // Vérifier la table users et ses colonnes
      const columnsResult = await db.execute(sql`
        SELECT column_name, data_type, is_nullable, column_default
        FROM information_schema.columns 
        WHERE table_name = 'users' 
        ORDER BY ordinal_position;
      `);

      result.tableExists = Array.isArray(columnsResult) && columnsResult.length > 0;
      result.totalColumns = columnsResult.length;
      result.allColumns = columnsResult.map((col: any) => ({
        name: col.column_name,
        type: col.data_type,
        nullable: col.is_nullable === 'YES',
        default: col.column_default
      }));

      // Vérifier les colonnes Epic Social V1
      const presentEpicColumns = result.allColumns
        .map(col => col.name)
        .filter(name => result.epicSocialStatus.requiredColumns.includes(name));
      
      result.epicSocialStatus.presentColumns = presentEpicColumns;
      result.epicSocialStatus.missingColumns = result.epicSocialStatus.requiredColumns
        .filter(col => !presentEpicColumns.includes(col));
      result.epicSocialStatus.allPresent = result.epicSocialStatus.missingColumns.length === 0;
      result.epicSocialStatus.migrationNeeded = !result.epicSocialStatus.allPresent;

      // Vérifier les tables followers et notifications
      const tablesResult = await db.execute(sql`
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_name IN ('followers', 'notifications') 
        AND table_schema = 'public';
      `);

      const existingTables = tablesResult.map((t: any) => t.table_name);
      result.tables.followers.exists = existingTables.includes('followers');
      result.tables.notifications.exists = existingTables.includes('notifications');

      // Vérifier les ENUMs
      const enumsResult = await db.execute(sql`
        SELECT t.typname, e.enumlabel
        FROM pg_type t 
        JOIN pg_enum e ON t.oid = e.enumtypid  
        WHERE t.typname IN ('profile_visibility', 'notification_type')
        ORDER BY t.typname, e.enumsortorder;
      `);

      for (const enumRow of enumsResult) {
        const enumName = (enumRow as any).typname;
        const enumValue = (enumRow as any).enumlabel;
        
        if (enumName === 'profile_visibility') {
          result.enums.profile_visibility.exists = true;
          result.enums.profile_visibility.values.push(enumValue);
        } else if (enumName === 'notification_type') {
          result.enums.notification_type.exists = true;
          result.enums.notification_type.values.push(enumValue);
        }
      }

      // Générer des recommandations
      if (result.epicSocialStatus.allPresent) {
        result.recommendations.push("✅ MIGRATIONS OK: Toutes les colonnes Epic Social V1 sont présentes");
      } else {
        result.recommendations.push(`❌ MIGRATION REQUISE: ${result.epicSocialStatus.missingColumns.length} colonnes manquantes`);
        result.epicSocialStatus.missingColumns.forEach(col => {
          result.recommendations.push(`   • ${col}`);
        });
      }

      if (!result.tables.followers.exists) {
        result.recommendations.push("❌ Table 'followers' manquante");
      }
      if (!result.tables.notifications.exists) {
        result.recommendations.push("❌ Table 'notifications' manquante");
      }
      if (!result.enums.profile_visibility.exists) {
        result.recommendations.push("❌ ENUM 'profile_visibility' manquant");
      }
      if (!result.enums.notification_type.exists) {
        result.recommendations.push("❌ ENUM 'notification_type' manquant");
      }

    } catch (queryError) {
      result.recommendations.push(`❌ Erreur base de données: ${queryError}`);
    }

    console.log(`📊 Vérification terminée - ${result.totalColumns} colonnes, Epic Social V1: ${result.epicSocialStatus.allPresent ? 'OK' : 'MANQUANT'}`);

    return NextResponse.json(result);

  } catch (error) {
    console.error("❌ Erreur lors de la vérification:", error);
    return NextResponse.json(
      { 
        error: "Erreur lors de la vérification", 
        details: error instanceof Error ? error.message : String(error),
        environment: process.env.VERCEL_ENV || process.env.NODE_ENV || 'unknown'
      },
      { status: 500 }
    );
  }
} 