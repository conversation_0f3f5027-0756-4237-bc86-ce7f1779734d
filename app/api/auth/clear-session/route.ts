import { NextRequest, NextResponse } from "next/server";
import { cookies } from "next/headers";

/**
 * API pour nettoyer les cookies de session corrompus
 * Utile quand il y a des erreurs JWT "Invalid Compact JWE"
 */
export async function POST(request: NextRequest) {
  try {
    if (process.env.NODE_ENV === 'development') {
    console.log("🧹 Nettoyage des cookies de session");
    }

    const cookieStore = await cookies();
    
    // Liste des cookies NextAuth à supprimer
    const authCookies = [
      'next-auth.session-token',
      'next-auth.csrf-token',
      'next-auth.callback-url',
      'next-auth.state',
      '__Secure-next-auth.session-token',
      '__Host-next-auth.csrf-token',
      '__Secure-next-auth.callback-url'
    ];

    // Créer la réponse
    const response = NextResponse.json({
      success: true,
      message: "Cookies de session nettoyés avec succès",
      clearedCookies: authCookies
    });

    // Supprimer tous les cookies d'authentification
    authCookies.forEach(cookieName => {
      response.cookies.delete(cookieName);
      response.cookies.delete({
        name: cookieName,
        path: '/',
        domain: undefined
      });
    });

    if (process.env.NODE_ENV === 'development') {
    console.log("✅ Cookies de session nettoyés");
    }

    return response;

  } catch (error) {
    console.error("❌ Erreur lors du nettoyage des cookies:", error);
    
    return NextResponse.json(
      {
        success: false,
        error: "Erreur lors du nettoyage des cookies",
        message: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}

// Méthodes HTTP non autorisées
export async function GET() {
  return NextResponse.json({ error: "Method not allowed" }, { status: 405 });
}
