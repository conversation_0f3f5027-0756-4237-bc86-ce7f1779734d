import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { sql } from "drizzle-orm";

/**
 * API pour diagnostiquer et corriger les problèmes avec les tables
 * followers et notifications (structure, contraintes, etc.)
 */
export async function GET(request: NextRequest) {
  try {
    console.log("🔍 Diagnostic tables followers et notifications...");

    const environment = process.env.VERCEL_ENV || process.env.NODE_ENV || 'unknown';
    
    const result = {
      environment,
      timestamp: new Date().toISOString(),
      tables: {
        followers: { exists: false, columns: [] as any[], constraints: [] as any[] },
        notifications: { exists: false, columns: [] as any[], constraints: [] as any[] }
      },
      issues: [] as string[],
      recommendations: [] as string[]
    };

    // Vérifier la table followers
    const followersColumns = await db.execute(sql`
      SELECT column_name, data_type, is_nullable, column_default
      FROM information_schema.columns 
      WHERE table_name = 'followers'
      ORDER BY ordinal_position;
    `);

    if (followersColumns.length > 0) {
      result.tables.followers.exists = true;
      result.tables.followers.columns = followersColumns.map((col: any) => ({
        name: col.column_name,
        type: col.data_type,
        nullable: col.is_nullable === 'YES',
        default: col.column_default
      }));
    }

    // Vérifier la table notifications
    const notificationsColumns = await db.execute(sql`
      SELECT column_name, data_type, is_nullable, column_default
      FROM information_schema.columns 
      WHERE table_name = 'notifications'
      ORDER BY ordinal_position;
    `);

    if (notificationsColumns.length > 0) {
      result.tables.notifications.exists = true;
      result.tables.notifications.columns = notificationsColumns.map((col: any) => ({
        name: col.column_name,
        type: col.data_type,
        nullable: col.is_nullable === 'YES',
        default: col.column_default
      }));
    }

    // Vérifier les contraintes
    const constraints = await db.execute(sql`
      SELECT 
        tc.table_name,
        tc.constraint_name,
        tc.constraint_type,
        kcu.column_name,
        ccu.table_name AS foreign_table_name,
        ccu.column_name AS foreign_column_name
      FROM information_schema.table_constraints AS tc 
      JOIN information_schema.key_column_usage AS kcu
        ON tc.constraint_name = kcu.constraint_name
        AND tc.table_schema = kcu.table_schema
      LEFT JOIN information_schema.constraint_column_usage AS ccu
        ON ccu.constraint_name = tc.constraint_name
        AND ccu.table_schema = tc.table_schema
      WHERE tc.table_name IN ('followers', 'notifications')
      ORDER BY tc.table_name, tc.constraint_type;
    `);

    // Organiser les contraintes par table
    for (const constraint of constraints) {
      const tableName = (constraint as any).table_name;
      const constraintInfo = {
        name: (constraint as any).constraint_name,
        type: (constraint as any).constraint_type,
        column: (constraint as any).column_name,
        foreign_table: (constraint as any).foreign_table_name,
        foreign_column: (constraint as any).foreign_column_name
      };

      if (tableName === 'followers') {
        result.tables.followers.constraints.push(constraintInfo);
      } else if (tableName === 'notifications') {
        result.tables.notifications.constraints.push(constraintInfo);
      }
    }

    // Analyser les problèmes potentiels
    if (!result.tables.followers.exists) {
      result.issues.push("❌ Table 'followers' manquante");
      result.recommendations.push("Créer la table followers avec la migration Epic Social V1");
    } else {
      // Vérifier les colonnes attendues pour followers
      const expectedFollowersColumns = ['follower_id', 'following_id', 'created_at'];
      const actualFollowersColumns = result.tables.followers.columns.map(c => c.name);
      
      for (const expectedCol of expectedFollowersColumns) {
        if (!actualFollowersColumns.includes(expectedCol)) {
          result.issues.push(`❌ Colonne '${expectedCol}' manquante dans followers`);
        }
      }
      
      // Vérifier les doublons possibles (colonnes avec d'autres noms)
      const alternativeNames = {
        'follower_id': ['followerId', 'follower_id'],
        'following_id': ['followingId', 'following_id'], 
        'created_at': ['createdAt', 'created_at']
      };
      
      for (const [expected, alternatives] of Object.entries(alternativeNames)) {
        const found = alternatives.filter(alt => actualFollowersColumns.includes(alt));
        if (found.length > 1) {
          result.issues.push(`⚠️ Nomenclature incohérente pour '${expected}': ${found.join(', ')}`);
          result.recommendations.push(`Standardiser '${expected}' en snake_case`);
        }
      }
    }

    if (!result.tables.notifications.exists) {
      result.issues.push("❌ Table 'notifications' manquante");
      result.recommendations.push("Créer la table notifications avec la migration Epic Social V1");
    } else {
      // Vérifier les colonnes attendues pour notifications
      const expectedNotificationsColumns = ['id', 'recipient_id', 'actor_id', 'type', 'is_read', 'created_at'];
      const actualNotificationsColumns = result.tables.notifications.columns.map(c => c.name);
      
      for (const expectedCol of expectedNotificationsColumns) {
        if (!actualNotificationsColumns.includes(expectedCol)) {
          result.issues.push(`❌ Colonne '${expectedCol}' manquante dans notifications`);
        }
      }
    }

    // Vérifier les ENUMs
    const enumsResult = await db.execute(sql`
      SELECT t.typname
      FROM pg_type t 
      WHERE t.typname IN ('profile_visibility', 'notification_type');
    `);
    
    const existingEnums = enumsResult.map(e => (e as any).typname);
    
    if (!existingEnums.includes('notification_type')) {
      result.issues.push("❌ ENUM 'notification_type' manquant");
      result.recommendations.push("Créer l'ENUM notification_type avec les valeurs ['new_follower']");
    }

    // Résumé
    if (result.issues.length === 0) {
      result.recommendations.push("✅ Structure des tables correcte");
    }

    console.log(`📊 Diagnostic terminé - ${result.issues.length} problèmes détectés`);

    return NextResponse.json(result);

  } catch (error) {
    console.error("❌ Erreur diagnostic tables follow:", error);
    return NextResponse.json(
      { 
        error: "Erreur diagnostic tables follow", 
        details: error instanceof Error ? error.message : String(error),
        environment: process.env.VERCEL_ENV || process.env.NODE_ENV || 'unknown'
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    console.log("🔧 Correction tables followers et notifications...");

    const environment = process.env.VERCEL_ENV || process.env.NODE_ENV || 'unknown';
    
    const result = {
      environment,
      timestamp: new Date().toISOString(),
      operations: [] as string[],
      success: false,
      error: null as string | null
    };

    try {
      // Vérifier et corriger la table followers
      const followersExists = await db.execute(sql`
        SELECT EXISTS (
          SELECT FROM information_schema.tables 
          WHERE table_name = 'followers'
        );
      `);

      if (!(followersExists[0] as any).exists) {
        console.log("📋 Création table followers...");
        
        await db.execute(sql`
          CREATE TABLE IF NOT EXISTS followers (
            id TEXT PRIMARY KEY DEFAULT gen_random_uuid(),
            follower_id TEXT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
            following_id TEXT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
            created_at TIMESTAMP DEFAULT NOW() NOT NULL,
            UNIQUE(follower_id, following_id),
            CHECK (follower_id != following_id)
          );
        `);
        
        result.operations.push("✅ Table followers créée");
        
        // Créer les index
        await db.execute(sql`CREATE INDEX IF NOT EXISTS idx_followers_follower_id ON followers(follower_id);`);
        await db.execute(sql`CREATE INDEX IF NOT EXISTS idx_followers_following_id ON followers(following_id);`);
        await db.execute(sql`CREATE INDEX IF NOT EXISTS idx_followers_created_at ON followers(created_at);`);
        
        result.operations.push("✅ Index followers créés");
      } else {
        result.operations.push("ℹ️ Table followers existe déjà");
      }

      // Vérifier et corriger l'ENUM notification_type
      const enumExists = await db.execute(sql`
        SELECT EXISTS (
          SELECT FROM pg_type 
          WHERE typname = 'notification_type'
        );
      `);

      if (!(enumExists[0] as any).exists) {
        console.log("📋 Création ENUM notification_type...");
        
        await db.execute(sql`
          DO $$ BEGIN
            CREATE TYPE notification_type AS ENUM ('new_follower');
          EXCEPTION
            WHEN duplicate_object THEN null;
          END $$;
        `);
        
        result.operations.push("✅ ENUM notification_type créé");
      } else {
        result.operations.push("ℹ️ ENUM notification_type existe déjà");
      }

      // Vérifier et corriger la table notifications
      const notificationsExists = await db.execute(sql`
        SELECT EXISTS (
          SELECT FROM information_schema.tables 
          WHERE table_name = 'notifications'
        );
      `);

      if (!(notificationsExists[0] as any).exists) {
        console.log("📋 Création table notifications...");
        
        await db.execute(sql`
          CREATE TABLE IF NOT EXISTS notifications (
            id TEXT PRIMARY KEY DEFAULT gen_random_uuid(),
            recipient_id TEXT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
            actor_id TEXT REFERENCES users(id) ON DELETE SET NULL,
            type notification_type NOT NULL,
            is_read BOOLEAN NOT NULL DEFAULT FALSE,
            created_at TIMESTAMP DEFAULT NOW() NOT NULL
          );
        `);
        
        result.operations.push("✅ Table notifications créée");
        
        // Créer les index
        await db.execute(sql`CREATE INDEX IF NOT EXISTS idx_notifications_recipient_id ON notifications(recipient_id);`);
        await db.execute(sql`CREATE INDEX IF NOT EXISTS idx_notifications_is_read ON notifications(is_read);`);
        await db.execute(sql`CREATE INDEX IF NOT EXISTS idx_notifications_created_at ON notifications(created_at);`);
        
        result.operations.push("✅ Index notifications créés");
      } else {
        result.operations.push("ℹ️ Table notifications existe déjà");
      }

      result.success = true;
      console.log("🎉 Correction tables follow terminée avec succès");

    } catch (operationError) {
      result.error = operationError instanceof Error ? operationError.message : String(operationError);
      console.error("❌ Erreur correction:", operationError);
    }

    return NextResponse.json(result);

  } catch (error) {
    console.error("❌ Erreur correction tables follow:", error);
    return NextResponse.json(
      { 
        error: "Erreur correction tables follow", 
        details: error instanceof Error ? error.message : String(error),
        environment: process.env.VERCEL_ENV || process.env.NODE_ENV || 'unknown'
      },
      { status: 500 }
    );
  }
} 