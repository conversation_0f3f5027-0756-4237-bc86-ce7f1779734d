import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/auth";
import { generateRecommendationsForUser } from "@/app/actions/user";

/**
 * API pour régénérer les recommandations de l'utilisateur connecté
 * avec enrichissement Rakuten automatique
 */
export async function POST(request: NextRequest) {
  try {
    if (process.env.NODE_ENV === 'development') {
    console.log("🔄 Régénération des recommandations avec enrichissement Rakuten");
    }

    // Vérifier que l'utilisateur est connecté
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: "Utilisateur non connecté" },
        { status: 401 }
      );
    }

    const userId = session.user.id;
    if (process.env.NODE_ENV === 'development') {
    console.log(`👤 Régénération pour l'utilisateur: ${userId}`);
    }

    // Déclencher la génération complète avec enrichissement Rakuten
    const startTime = Date.now();
    const result = await generateRecommendationsForUser(userId);
    const duration = Date.now() - startTime;

    if (result.status === "success") {
      if (process.env.NODE_ENV === 'development') {
      console.log(`✅ Recommandations régénérées avec succès en ${duration}ms`);
      }
      
      return NextResponse.json({
        success: true,
        message: "Recommandations régénérées avec enrichissement Rakuten ✅",
        data: {
          userId,
          recommendationsCount: result.recommendationsCount,
          generationTime: `${duration}ms`,
          status: result.status,
          enrichmentEnabled: true
        }
      });
    } else {
      console.error(`❌ Échec de la régénération:`, result);
      
      return NextResponse.json(
        {
          success: false,
          error: "Échec de la régénération",
          details: result
        },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error("💥 Erreur lors de la régénération:", error);
    
    return NextResponse.json(
      {
        success: false,
        error: "Erreur lors de la régénération",
        message: error instanceof Error ? error.message : "Unknown error"
      },
      { status: 500 }
    );
  }
}
