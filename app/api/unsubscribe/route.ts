/**
 * API Route pour le désabonnement des emails
 * Conforme aux standards RFC 8058 pour le désabonnement en un clic
 */

import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/db';
import { users } from '@/lib/db/schema';
import { eq } from 'drizzle-orm';

/**
 * GET - Affiche la page de désabonnement
 */
export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const email = searchParams.get('email');
  const type = searchParams.get('type');

  if (!email) {
    return NextResponse.json(
      { error: 'Email parameter is required' },
      { status: 400 }
    );
  }

  // Pour le désabonnement en un clic, traiter immédiatement
  if (type === 'one-click') {
    return handleUnsubscribe(email);
  }

  // Sinon, rediriger vers la page de gestion des notifications
  const accountUrl = `${process.env.NEXTAUTH_URL}/account?tab=notifications&email=${encodeURIComponent(email)}`;
  return NextResponse.redirect(accountUrl);
}

/**
 * POST - Traite le désabonnement en un clic (RFC 8058)
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { email } = body;

    if (!email) {
      return NextResponse.json(
        { error: 'Email is required' },
        { status: 400 }
      );
    }

    return handleUnsubscribe(email);
  } catch (error) {
    console.error('❌ Erreur lors du désabonnement:', error);
    return NextResponse.json(
      { error: 'Invalid request body' },
      { status: 400 }
    );
  }
}

/**
 * Traite le désabonnement d'un utilisateur
 */
async function handleUnsubscribe(email: string): Promise<NextResponse> {
  try {
    if (process.env.NODE_ENV === 'development') {
    console.log(`📧 Désabonnement demandé pour: ${email}`);
    }

    // Vérifier que l'utilisateur existe
    const user = await db
      .select()
      .from(users)
      .where(eq(users.email, email))
      .limit(1);

    if (user.length === 0) {
      if (process.env.NODE_ENV === 'development') {
      console.warn(`⚠️ Tentative de désabonnement pour un email inexistant: ${email}`);
      }
      // On retourne succès pour éviter de révéler l'existence ou non de l'email
      return NextResponse.json({
        success: true,
        message: 'If this email exists in our system, it has been unsubscribed.'
      });
    }

    // Désactiver les notifications email
    await db
      .update(users)
      .set({
        emailNotificationsEnabled: false,
        updatedAt: new Date()
      })
      .where(eq(users.email, email));

    if (process.env.NODE_ENV === 'development') {
    console.log(`✅ Désabonnement réussi pour: ${email}`);
    }

    // Retourner une réponse HTML simple pour les clients de messagerie
    const htmlResponse = `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Désabonnement réussi - Stream2Spin</title>
          <style>
            body {
              font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
              max-width: 600px;
              margin: 0 auto;
              padding: 40px 20px;
              background-color: #f6f9fc;
            }
            .container {
              background: white;
              padding: 40px;
              border-radius: 8px;
              box-shadow: 0 2px 4px rgba(0,0,0,0.1);
              text-align: center;
            }
            .logo {
              width: 180px;
              height: 40px;
              margin-bottom: 30px;
            }
            h1 {
              color: #333;
              margin-bottom: 20px;
            }
            p {
              color: #666;
              line-height: 1.6;
              margin-bottom: 20px;
            }
            .success {
              color: #28a745;
              font-weight: bold;
            }
            .button {
              display: inline-block;
              background: #6236FF;
              color: white;
              padding: 12px 24px;
              text-decoration: none;
              border-radius: 6px;
              margin-top: 20px;
            }
          </style>
        </head>
        <body>
          <div class="container">
            <img src="${process.env.NEXTAUTH_URL}/Stream2Spin_white_logo.svg" alt="Stream2Spin" class="logo">
            <h1>Désabonnement réussi</h1>
            <p class="success">✅ Vous avez été désabonné(e) avec succès des notifications email de Stream2Spin.</p>
            <p>Vous ne recevrez plus d'emails de recommandations de notre part.</p>
            <p>Vous pouvez réactiver les notifications à tout moment depuis votre compte.</p>
            <a href="${process.env.NEXTAUTH_URL}/account?tab=notifications" class="button">
              Gérer mes préférences
            </a>
          </div>
        </body>
      </html>
    `;

    return new NextResponse(htmlResponse, {
      status: 200,
      headers: {
        'Content-Type': 'text/html; charset=utf-8',
      },
    });

  } catch (error) {
    console.error('💥 Erreur lors du désabonnement:', error);

    const errorHtml = `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Erreur - Stream2Spin</title>
          <style>
            body {
              font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
              max-width: 600px;
              margin: 0 auto;
              padding: 40px 20px;
              background-color: #f6f9fc;
            }
            .container {
              background: white;
              padding: 40px;
              border-radius: 8px;
              box-shadow: 0 2px 4px rgba(0,0,0,0.1);
              text-align: center;
            }
            .error {
              color: #dc3545;
            }
          </style>
        </head>
        <body>
          <div class="container">
            <h1 class="error">Erreur lors du désabonnement</h1>
            <p>Une erreur s'est produite. Veuillez réessayer plus tard ou nous contacter.</p>
            <a href="${process.env.NEXTAUTH_URL}/contact">Nous contacter</a>
          </div>
        </body>
      </html>
    `;

    return new NextResponse(errorHtml, {
      status: 500,
      headers: {
        'Content-Type': 'text/html; charset=utf-8',
      },
    });
  }
}

// Autres méthodes HTTP non autorisées
export async function PUT() {
  return NextResponse.json({ error: 'Method not allowed' }, { status: 405 });
}

export async function DELETE() {
  return NextResponse.json({ error: 'Method not allowed' }, { status: 405 });
}

export async function PATCH() {
  return NextResponse.json({ error: 'Method not allowed' }, { status: 405 });
}
