import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { users, accounts } from "@/lib/db/schema";
import { eq, and } from "drizzle-orm";
import { generateRecommendationsForUser, syncAllDiscogsCollections } from "@/app/actions/user";

/**
 * API Route sécurisée pour la génération de recommandations (Cron Job)
 * Epic 3 - US 3.1 & 3.2: Récupération et Analyse des Données Spotify + Synchronisation Discogs
 *
 * Cette route est appelée périodiquement par Vercel Cron pour :
 * 1. Synchroniser les collections Discogs de tous les utilisateurs connectés
 * 2. Récupérer tous les utilisateurs actifs avec compte Spotify
 * 3. Analyser leurs top tracks via l'API Spotify
 * 4. Calculer des scores d'écoute pour chaque album
 * 5. Croiser avec les collections Discogs pour marquer les albums possédés
 * 6. Sauvegarder les recommandations en base de données
 */
export async function POST(request: NextRequest) {
  if (process.env.NODE_ENV === 'development') {
  console.log("🎵 Démarrage du job hebdomadaire: Synchronisation Discogs + Génération de recommandations");
  }

  try {
    // Vérification de sécurité : authentification par secret
    const authHeader = request.headers.get("authorization");
    const expectedSecret = `Bearer ${process.env.CRON_SECRET}`;

    if (!authHeader || authHeader !== expectedSecret) {
      console.error("❌ Tentative d'accès non autorisée au cron job");
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // ÉTAPE 1: Synchronisation des collections Discogs
    if (process.env.NODE_ENV === 'development') {
    console.log("📀 Phase 1: Synchronisation des collections Discogs");
    }
    const discogsResult = await syncAllDiscogsCollections();

    if (process.env.NODE_ENV === 'development') {
    console.log(`📊 Synchronisation Discogs: ${discogsResult.successCount} succès, ${discogsResult.errorCount} erreurs`);
    }

    // ÉTAPE 2: Génération des recommandations Spotify

    if (process.env.NODE_ENV === 'development') {
    console.log("🎵 Phase 2: Génération des recommandations Spotify");
    }

    // Récupérer tous les utilisateurs avec un compte Spotify actif
    const usersWithSpotify = await db
      .select({
        userId: users.id,
        userName: users.name,
        userEmail: users.email,
        refreshToken: accounts.refresh_token,
        accessToken: accounts.access_token,
        expiresAt: accounts.expires_at,
      })
      .from(users)
      .innerJoin(accounts, eq(accounts.userId, users.id))
      .where(
        and(
          eq(accounts.provider, "spotify"),
          // Vérifier que le refresh_token existe
          // (nécessaire pour renouveler l'accès à l'API Spotify)
        )
      );

    if (process.env.NODE_ENV === 'development') {
    console.log(`📊 ${usersWithSpotify.length} utilisateurs avec compte Spotify trouvés`);
    }

    if (usersWithSpotify.length === 0) {
      return NextResponse.json({
        success: true,
        message: "Aucun utilisateur avec compte Spotify trouvé",
        processedUsers: 0,
      });
    }

    let successCount = 0;
    let errorCount = 0;

    // Traitement de chaque utilisateur avec gestion d'erreurs individuelles
    const results = await Promise.allSettled(
      usersWithSpotify.map(async (user) => {
        try {
          if (process.env.NODE_ENV === 'development') {
          console.log(`🔄 Traitement de l'utilisateur ${user.userName || user.userEmail} (${user.userId})`);
          }

          // Utiliser la fonction partagée de génération de recommandations
          const result = await generateRecommendationsForUser(user.userId);

          return {
            userId: user.userId,
            ...result
          };

        } catch (error) {
          console.error(`❌ Erreur lors du traitement de l'utilisateur ${user.userId}:`, error);
          return {
            userId: user.userId,
            status: "error",
            error: error instanceof Error ? error.message : "Unknown error"
          };
        }
      })
    );

    // Compter les succès et échecs
    results.forEach((result) => {
      if (result.status === "fulfilled" && result.value.status === "success") {
        successCount++;
      } else {
        errorCount++;
      }
    });

    if (process.env.NODE_ENV === 'development') {
    console.log(`🎯 Job terminé - Recommandations: ${successCount} succès, ${errorCount} erreurs`);
    }

    return NextResponse.json({
      success: true,
      message: "Job hebdomadaire terminé: Synchronisation Discogs + Génération de recommandations",
      discogs: {
        processedUsers: discogsResult.processedUsers,
        successCount: discogsResult.successCount,
        errorCount: discogsResult.errorCount,
      },
      recommendations: {
        processedUsers: usersWithSpotify.length,
        successCount,
        errorCount,
        results: results.map(r => r.status === "fulfilled" ? r.value : { status: "error" }),
      },
    });

  } catch (error) {
    console.error("💥 Erreur critique dans le job hebdomadaire:", error);

    return NextResponse.json(
      {
        success: false,
        error: "Erreur interne du serveur",
        message: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}





// Vercel Cron appelle en GET, donc on redirige vers la logique POST
export async function GET(request: NextRequest) {
  if (process.env.NODE_ENV === 'development') {
  console.log("🔄 Vercel Cron appelé en GET, redirection vers la logique POST");
  }
  return await POST(request);
}
