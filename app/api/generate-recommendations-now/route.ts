import { NextRequest, NextResponse } from "next/server";
import { generateRecommendationsForUser } from "@/app/actions/user";

/**
 * API Route simple pour déclencher manuellement la génération de recommandations
 * Utilise l'ID utilisateur fourni en paramètre
 */
export async function POST(request: NextRequest) {
  try {
    // Récupérer l'ID utilisateur depuis les paramètres de la requête
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get("userId");

    if (!userId) {
      return NextResponse.json(
        { error: "userId requis en paramètre" },
        { status: 400 }
      );
    }

    if (process.env.NODE_ENV === 'development') {
    console.log(`🎵 Génération manuelle de recommandations pour l'utilisateur ${userId}`);
    }

    // Générer les recommandations
    const result = await generateRecommendationsForUser(userId);

    return NextResponse.json({
      success: true,
      message: "Recommandations générées avec succès",
      result,
    });

  } catch (error) {
    console.error("💥 Erreur lors de la génération manuelle:", error);
    
    return NextResponse.json(
      {
        success: false,
        error: "Erreur lors de la génération",
        message: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}

export async function GET() {
  return NextResponse.json({ 
    message: "Utilisez POST pour générer des recommandations",
    usage: "POST /api/generate-recommendations-now?userId=YOUR_USER_ID"
  });
}
