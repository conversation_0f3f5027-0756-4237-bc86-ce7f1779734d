import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { sql } from "drizzle-orm";

/**
 * Migration pour ajouter la table user_fcm_tokens
 * Epic 6 - Notifications Utilisateur
 */
export async function POST(request: NextRequest) {
  try {
    if (process.env.NODE_ENV === 'development') {
    console.log("🔄 Début de la migration pour ajouter la table user_fcm_tokens...");
    }

    // Vérification de sécurité basique (en développement seulement)
    if (process.env.NODE_ENV === "production") {
      return NextResponse.json(
        { error: "Migration non autorisée en production" },
        { status: 403 }
      );
    }

    // Vérifier si la table existe déjà
    const checkTable = await db.execute(sql`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_name = 'user_fcm_tokens'
    `);

    if (checkTable.length > 0) {
      if (process.env.NODE_ENV === 'development') {
      console.log("✅ La table user_fcm_tokens existe déjà");
      }
      return NextResponse.json({
        success: true,
        message: "La table user_fcm_tokens existe déjà",
        alreadyExists: true
      });
    }

    // Créer la table user_fcm_tokens
    await db.execute(sql`
      CREATE TABLE user_fcm_tokens (
          id BIGSERIAL PRIMARY KEY,
          "userId" TEXT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
          token TEXT NOT NULL,
          "deviceInfo" TEXT,
          "isActive" BOOLEAN NOT NULL DEFAULT true,
          "createdAt" TIMESTAMPTZ NOT NULL DEFAULT NOW(),
          "lastUsedAt" TIMESTAMPTZ NOT NULL DEFAULT NOW(),
          UNIQUE("userId", token)
      )
    `);

    // Ajouter des index pour les performances
    await db.execute(sql`
      CREATE INDEX idx_user_fcm_tokens_userId ON user_fcm_tokens("userId")
    `);

    await db.execute(sql`
      CREATE INDEX idx_user_fcm_tokens_isActive ON user_fcm_tokens("isActive")
    `);

    await db.execute(sql`
      CREATE INDEX idx_user_fcm_tokens_lastUsedAt ON user_fcm_tokens("lastUsedAt")
    `);

    if (process.env.NODE_ENV === 'development') {
    console.log("✅ Table user_fcm_tokens créée avec succès");
    }

    return NextResponse.json({
      success: true,
      message: "Table user_fcm_tokens créée avec succès",
      created: true
    });

  } catch (error) {
    console.error("❌ Erreur lors de la migration:", error);
    return NextResponse.json(
      { 
        success: false, 
        error: "Erreur lors de la migration",
        details: error instanceof Error ? error.message : "Unknown error"
      },
      { status: 500 }
    );
  }
}

export async function GET() {
  return NextResponse.json({ 
    message: "Utilisez POST pour exécuter la migration de la table user_fcm_tokens" 
  });
}
