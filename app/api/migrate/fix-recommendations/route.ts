import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { sql } from "drizzle-orm";

/**
 * API Route pour corriger la structure de la table recommendations
 * Cette route exécute la migration pour renommer listenCount en listenScore
 */
export async function POST(request: NextRequest) {
  try {
    if (process.env.NODE_ENV === 'development') {
    console.log("🔧 Démarrage de la migration de la table recommendations");
    }

    // Vérification de sécurité basique (en développement seulement)
    if (process.env.NODE_ENV === "production") {
      return NextResponse.json(
        { error: "Migration non autorisée en production" },
        { status: 403 }
      );
    }

    // Exécuter la migration
    await db.execute(sql`
      -- Supprimer la table existante si elle existe
      DROP TABLE IF EXISTS recommendations CASCADE;
    `);

    await db.execute(sql`
      -- Recréer la table avec la bonne structure
      CREATE TABLE recommendations (
          id BIGSERIAL PRIMARY KEY,
          "userId" TEXT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
          "artistName" TEXT NOT NULL,
          "albumTitle" TEXT NOT NULL,
          "albumCoverUrl" TEXT,
          "discogsReleaseId" BIGINT,
          "spotifyAlbumId" TEXT,
          "listenScore" INTEGER NOT NULL,
          "timeframe" TEXT NOT NULL,
          "generatedAt" TIMESTAMPTZ NOT NULL DEFAULT NOW(),
          "isOwned" BOOLEAN NOT NULL DEFAULT FALSE,
          "affiliateLinks" JSONB
      );
    `);

    await db.execute(sql`
      -- Créer l'index pour les performances
      CREATE INDEX IF NOT EXISTS idx_recommendations_userId_generatedAt ON recommendations("userId", "generatedAt" DESC);
    `);

    if (process.env.NODE_ENV === 'development') {
    console.log("✅ Migration de la table recommendations terminée avec succès");
    }

    return NextResponse.json({
      success: true,
      message: "Table recommendations migrée avec succès",
    });

  } catch (error) {
    console.error("💥 Erreur lors de la migration:", error);
    
    return NextResponse.json(
      {
        success: false,
        error: "Erreur lors de la migration",
        message: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}

// Méthodes HTTP non autorisées
export async function GET() {
  return NextResponse.json({ 
    message: "Utilisez POST pour exécuter la migration",
    usage: "POST /api/migrate/fix-recommendations"
  });
}
