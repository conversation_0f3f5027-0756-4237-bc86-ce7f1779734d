import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { sql } from "drizzle-orm";

/**
 * API Route pour corriger les doublons dans la table recommendations
 * et ajouter une contrainte unique pour les éviter à l'avenir
 */
export async function POST(request: NextRequest) {
  try {
    if (process.env.NODE_ENV === 'development') {
    console.log("🔧 Démarrage de la correction des doublons dans recommendations...");
    }

    // Étape 1: Compter les recommandations avant nettoyage
    const countBefore = await db.execute(sql`SELECT COUNT(*) as count FROM recommendations`);
    const totalBefore = countBefore[0]?.count || 0;
    if (process.env.NODE_ENV === 'development') {
    console.log(`📊 Nombre de recommandations avant nettoyage: ${totalBefore}`);
    }

    // Étape 2: Identifier et supprimer les doublons en gardant le plus récent
    // (basé sur userId, spotifyAlbumId, timeframe)
    if (process.env.NODE_ENV === 'development') {
    console.log("🧹 Suppression des doublons avec spotifyAlbumId...");
    }
    
    await db.execute(sql`
      -- Créer une table temporaire avec les IDs des recommandations à conserver
      CREATE TEMP TABLE recommendations_to_keep AS
      SELECT DISTINCT ON ("userId", "spotifyAlbumId", "timeframe") id
      FROM recommendations
      WHERE "spotifyAlbumId" IS NOT NULL
      ORDER BY "userId", "spotifyAlbumId", "timeframe", "generatedAt" DESC
    `);

    const duplicatesWithSpotify = await db.execute(sql`
      DELETE FROM recommendations
      WHERE "spotifyAlbumId" IS NOT NULL
        AND id NOT IN (SELECT id FROM recommendations_to_keep)
      RETURNING id
    `);

    if (process.env.NODE_ENV === 'development') {
    console.log(`✅ ${duplicatesWithSpotify.length} doublons supprimés (avec spotifyAlbumId)`);
    }

    // Étape 3: Gérer les recommandations sans spotifyAlbumId
    if (process.env.NODE_ENV === 'development') {
    console.log("🧹 Suppression des doublons sans spotifyAlbumId...");
    }
    
    await db.execute(sql`
      CREATE TEMP TABLE recommendations_no_spotify_to_keep AS
      SELECT DISTINCT ON ("userId", "artistName", "albumTitle", "timeframe") id
      FROM recommendations
      WHERE "spotifyAlbumId" IS NULL
      ORDER BY "userId", "artistName", "albumTitle", "timeframe", "generatedAt" DESC
    `);

    const duplicatesWithoutSpotify = await db.execute(sql`
      DELETE FROM recommendations
      WHERE "spotifyAlbumId" IS NULL
        AND id NOT IN (SELECT id FROM recommendations_no_spotify_to_keep)
      RETURNING id
    `);

    if (process.env.NODE_ENV === 'development') {
    console.log(`✅ ${duplicatesWithoutSpotify.length} doublons supprimés (sans spotifyAlbumId)`);
    }

    // Étape 4: Ajouter la contrainte unique pour éviter les futurs doublons
    if (process.env.NODE_ENV === 'development') {
    console.log("🔒 Ajout de la contrainte unique...");
    }
    
    try {
      await db.execute(sql`
        ALTER TABLE recommendations 
        ADD CONSTRAINT unique_user_spotify_album_timeframe 
        UNIQUE ("userId", "spotifyAlbumId", "timeframe")
      `);
      if (process.env.NODE_ENV === 'development') {
      console.log("✅ Contrainte unique ajoutée avec succès");
      }
    } catch (error) {
      if (process.env.NODE_ENV === 'development') {
      console.log("⚠️ Contrainte unique déjà existante ou erreur:", error);
      }
    }

    // Étape 5: Créer un index partiel pour les recommandations sans spotifyAlbumId
    if (process.env.NODE_ENV === 'development') {
    console.log("📇 Création de l'index partiel...");
    }
    
    try {
      await db.execute(sql`
        CREATE UNIQUE INDEX IF NOT EXISTS unique_user_artist_album_timeframe_no_spotify
        ON recommendations ("userId", "artistName", "albumTitle", "timeframe")
        WHERE "spotifyAlbumId" IS NULL
      `);
      if (process.env.NODE_ENV === 'development') {
      console.log("✅ Index partiel créé avec succès");
      }
    } catch (error) {
      if (process.env.NODE_ENV === 'development') {
      console.log("⚠️ Index partiel déjà existant ou erreur:", error);
      }
    }

    // Étape 6: Compter les recommandations après nettoyage
    const countAfter = await db.execute(sql`SELECT COUNT(*) as count FROM recommendations`);
    const totalAfter = countAfter[0]?.count || 0;

    const spotifyCount = await db.execute(sql`
      SELECT COUNT(*) as count FROM recommendations WHERE "spotifyAlbumId" IS NOT NULL
    `);
    const withSpotify = spotifyCount[0]?.count || 0;

    const noSpotifyCount = await db.execute(sql`
      SELECT COUNT(*) as count FROM recommendations WHERE "spotifyAlbumId" IS NULL
    `);
    const withoutSpotify = noSpotifyCount[0]?.count || 0;

    const totalRemoved = Number(totalBefore) - Number(totalAfter);

    if (process.env.NODE_ENV === 'development') {
    console.log("🎯 Nettoyage terminé:");
    }
    console.log(`- Recommandations avant: ${totalBefore}`);
    if (process.env.NODE_ENV === 'development') {
    console.log(`- Recommandations après: ${totalAfter}`);
    }
    console.log(`- Doublons supprimés: ${totalRemoved}`);
    if (process.env.NODE_ENV === 'development') {
    console.log(`- Avec Spotify ID: ${withSpotify}`);
    }
    console.log(`- Sans Spotify ID: ${withoutSpotify}`);

    return NextResponse.json({
      success: true,
      message: "Correction des doublons terminée avec succès",
      stats: {
        totalBefore: Number(totalBefore),
        totalAfter: Number(totalAfter),
        duplicatesRemoved: totalRemoved,
        withSpotifyId: Number(withSpotify),
        withoutSpotifyId: Number(withoutSpotify),
        duplicatesWithSpotifyRemoved: duplicatesWithSpotify.length || 0,
        duplicatesWithoutSpotifyRemoved: duplicatesWithoutSpotify.length || 0
      }
    });

  } catch (error) {
    console.error("❌ Erreur lors de la correction des doublons:", error);
    
    return NextResponse.json(
      {
        success: false,
        error: "Erreur lors de la correction des doublons",
        details: error instanceof Error ? error.message : "Unknown error"
      },
      { status: 500 }
    );
  }
}

/**
 * GET pour vérifier l'état des doublons sans les corriger
 */
export async function GET(request: NextRequest) {
  try {
    // Compter les doublons potentiels
    const duplicatesWithSpotify = await db.execute(sql`
      SELECT "userId", "spotifyAlbumId", "timeframe", COUNT(*) as count
      FROM recommendations
      WHERE "spotifyAlbumId" IS NOT NULL
      GROUP BY "userId", "spotifyAlbumId", "timeframe"
      HAVING COUNT(*) > 1
    `);

    const duplicatesWithoutSpotify = await db.execute(sql`
      SELECT "userId", "artistName", "albumTitle", "timeframe", COUNT(*) as count
      FROM recommendations
      WHERE "spotifyAlbumId" IS NULL
      GROUP BY "userId", "artistName", "albumTitle", "timeframe"
      HAVING COUNT(*) > 1
    `);

    const totalCount = await db.execute(sql`SELECT COUNT(*) as count FROM recommendations`);

    return NextResponse.json({
      success: true,
      stats: {
        totalRecommendations: Number(totalCount[0]?.count || 0),
        duplicateGroupsWithSpotify: duplicatesWithSpotify.length || 0,
        duplicateGroupsWithoutSpotify: duplicatesWithoutSpotify.length || 0,
        duplicatesWithSpotify: duplicatesWithSpotify,
        duplicatesWithoutSpotify: duplicatesWithoutSpotify
      }
    });

  } catch (error) {
    console.error("❌ Erreur lors de la vérification des doublons:", error);
    
    return NextResponse.json(
      {
        success: false,
        error: "Erreur lors de la vérification des doublons",
        details: error instanceof Error ? error.message : "Unknown error"
      },
      { status: 500 }
    );
  }
}
