import { NextResponse } from "next/server";
import { db } from "@/lib/db";
import { sql } from "drizzle-orm";

/**
 * Migration pour Epic 16 US 16.4: Analytics et métriques de partage
 * Ajoute la table public_list_analytics pour tracker les vues et conversions
 */
export async function POST() {
  try {
    if (process.env.NODE_ENV === 'development') {
    console.log("🔄 Début de la migration: ajout de la table analytics");
    }

    // Créer la table public_list_analytics
    await db.execute(sql`
      CREATE TABLE IF NOT EXISTS "public_list_analytics" (
        "id" TEXT PRIMARY KEY DEFAULT gen_random_uuid(),
        "userId" TEXT NOT NULL REFERENCES "users"("id") ON DELETE CASCADE,
        "publicListId" TEXT NOT NULL,
        "eventType" TEXT NOT NULL,
        "eventData" JSONB,
        "ipAddress" TEXT,
        "userAgent" TEXT,
        "referrer" TEXT,
        "timestamp" TIMESTAMP DEFAULT NOW() NOT NULL
      );
    `);

    // Créer les index pour les performances
    await db.execute(sql`
      CREATE INDEX IF NOT EXISTS "idx_analytics_userId" ON "public_list_analytics"("userId");
    `);

    await db.execute(sql`
      CREATE INDEX IF NOT EXISTS "idx_analytics_publicListId" ON "public_list_analytics"("publicListId");
    `);

    await db.execute(sql`
      CREATE INDEX IF NOT EXISTS "idx_analytics_eventType" ON "public_list_analytics"("eventType");
    `);

    await db.execute(sql`
      CREATE INDEX IF NOT EXISTS "idx_analytics_timestamp" ON "public_list_analytics"("timestamp");
    `);

    // Ajouter des commentaires pour la documentation
    await db.execute(sql`
      COMMENT ON TABLE "public_list_analytics" IS 'Analytics pour les listes publiques - Epic 16 US 16.4';
    `);

    await db.execute(sql`
      COMMENT ON COLUMN "public_list_analytics"."eventType" IS 'Type d''événement: view, share, signup_click, signup_conversion';
    `);

    await db.execute(sql`
      COMMENT ON COLUMN "public_list_analytics"."eventData" IS 'Données supplémentaires en JSON (timeframe, album cliqué, etc.)';
    `);

    if (process.env.NODE_ENV === 'development') {
    console.log("✅ Migration analytics terminée avec succès");
    }

    return NextResponse.json({
      success: true,
      message: "Table analytics créée avec succès"
    });

  } catch (error) {
    console.error("❌ Erreur lors de la migration analytics:", error);
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : "Erreur inconnue" 
      },
      { status: 500 }
    );
  }
}
