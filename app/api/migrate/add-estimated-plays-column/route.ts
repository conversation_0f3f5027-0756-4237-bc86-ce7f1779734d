import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { sql } from "drizzle-orm";

/**
 * Migration pour ajouter la colonne estimatedPlays à la table recommendations
 * Pour afficher le vrai nombre d'écoutes estimé au lieu du score pondéré
 */
export async function POST(request: NextRequest) {
  try {
    if (process.env.NODE_ENV === 'development') {
    console.log("🔄 Début de la migration: ajout de la colonne estimatedPlays");
    }

    // Vérifier si la colonne existe déjà
    const checkColumns = await db.execute(sql`
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'recommendations' 
      AND column_name = 'estimatedPlays'
    `);

    if (checkColumns.length > 0) {
      if (process.env.NODE_ENV === 'development') {
      console.log("⏭️ La colonne estimatedPlays existe déjà");
      }
      return NextResponse.json({
        success: true,
        message: "La colonne estimatedPlays existe déjà",
        alreadyExists: true
      });
    }

    // Ajouter la colonne estimatedPlays
    if (process.env.NODE_ENV === 'development') {
    console.log("➕ Ajout de la colonne estimatedPlays...");
    }
    await db.execute(sql`
      ALTER TABLE recommendations 
      ADD COLUMN "estimatedPlays" INTEGER
    `);

    if (process.env.NODE_ENV === 'development') {
    console.log("✅ Colonne estimatedPlays ajoutée avec succès");
    }

    // Calculer les valeurs estimatedPlays pour les recommandations existantes
    // Utiliser une formule basée sur le listenScore existant
    if (process.env.NODE_ENV === 'development') {
    console.log("🔄 Calcul des valeurs estimatedPlays pour les recommandations existantes...");
    }
    await db.execute(sql`
      UPDATE recommendations 
      SET "estimatedPlays" = GREATEST(1, ROUND("listenScore" * 2.5))
      WHERE "estimatedPlays" IS NULL
    `);

    if (process.env.NODE_ENV === 'development') {
    console.log("✅ Valeurs estimatedPlays calculées pour les recommandations existantes");
    }

    // Vérifier que la colonne a été ajoutée
    const verifyColumns = await db.execute(sql`
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'recommendations' 
      AND column_name = 'estimatedPlays'
    `);

    if (verifyColumns.length === 0) {
      throw new Error("La colonne estimatedPlays n'a pas été créée correctement");
    }

    return NextResponse.json({
      success: true,
      message: "Migration réussie: colonne estimatedPlays ajoutée et valeurs calculées",
      details: {
        columnAdded: "estimatedPlays",
        type: "INTEGER",
        nullable: true,
        backfilled: true
      }
    });

  } catch (error) {
    console.error("❌ Erreur lors de la migration:", error);
    
    return NextResponse.json(
      {
        success: false,
        error: "Erreur lors de la migration",
        message: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}

/**
 * GET pour vérifier l'état de la migration
 */
export async function GET(request: NextRequest) {
  try {
    // Vérifier si la colonne estimatedPlays existe
    const checkColumns = await db.execute(sql`
      SELECT column_name, data_type, is_nullable
      FROM information_schema.columns 
      WHERE table_name = 'recommendations' 
      AND column_name = 'estimatedPlays'
    `);

    const hasColumn = checkColumns.length > 0;

    // Statistiques sur les valeurs estimatedPlays
    let stats = null;
    if (hasColumn) {
      const statsResult = await db.execute(sql`
        SELECT 
          COUNT(*) as total,
          COUNT("estimatedPlays") as with_estimated_plays,
          AVG("estimatedPlays") as avg_estimated_plays,
          MIN("estimatedPlays") as min_estimated_plays,
          MAX("estimatedPlays") as max_estimated_plays
        FROM recommendations
      `);
      stats = statsResult[0];
    }

    return NextResponse.json({
      success: true,
      message: "État de la colonne estimatedPlays",
      data: {
        hasColumn,
        columnInfo: hasColumn ? {
          name: checkColumns[0].column_name,
          type: checkColumns[0].data_type,
          nullable: checkColumns[0].is_nullable === 'YES'
        } : null,
        stats
      }
    });

  } catch (error) {
    console.error("❌ Erreur lors de la vérification:", error);
    
    return NextResponse.json(
      {
        success: false,
        error: "Erreur lors de la vérification",
        message: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}
