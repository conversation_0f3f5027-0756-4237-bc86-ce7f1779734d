import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { sql } from "drizzle-orm";

/**
 * Migration pour ajouter les colonnes du titre phare (US 3.6)
 * Ajoute topTrackName, topTrackPreviewUrl, topTrackListenScore à la table recommendations
 */
export async function POST(request: NextRequest) {
  try {
    if (process.env.NODE_ENV === 'development') {
    console.log("🔄 Début de la migration pour ajouter les colonnes du titre phare...");
    }

    // Vérifier si les colonnes existent déjà
    const checkColumns = await db.execute(sql`
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'recommendations' 
      AND column_name IN ('topTrackName', 'topTrackPreviewUrl', 'topTrackListenScore')
    `);

    const existingColumns = checkColumns.map((row: any) => row.column_name);
    if (process.env.NODE_ENV === 'development') {
    console.log(`📊 Colonnes existantes: ${existingColumns.join(', ')}`);
    }

    // Ajouter les colonnes manquantes
    const columnsToAdd = [
      { name: 'topTrackName', type: 'TEXT' },
      { name: 'topTrackPreviewUrl', type: 'TEXT' },
      { name: 'topTrackListenScore', type: 'INTEGER' }
    ];

    let addedColumns = 0;

    for (const column of columnsToAdd) {
      if (!existingColumns.includes(column.name)) {
        if (process.env.NODE_ENV === 'development') {
        console.log(`➕ Ajout de la colonne ${column.name}...`);
        }
        
        await db.execute(sql.raw(`
          ALTER TABLE recommendations 
          ADD COLUMN "${column.name}" ${column.type}
        `));
        
        addedColumns++;
        if (process.env.NODE_ENV === 'development') {
        console.log(`✅ Colonne ${column.name} ajoutée avec succès`);
        }
      } else {
        if (process.env.NODE_ENV === 'development') {
        console.log(`⏭️ Colonne ${column.name} existe déjà`);
        }
      }
    }

    // Vérifier que les colonnes ont été ajoutées
    const finalCheck = await db.execute(sql`
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'recommendations' 
      AND column_name IN ('topTrackName', 'topTrackPreviewUrl', 'topTrackListenScore')
    `);

    const finalColumns = finalCheck.map((row: any) => row.column_name);
    if (process.env.NODE_ENV === 'development') {
    console.log(`📊 Colonnes finales: ${finalColumns.join(', ')}`);
    }

    if (process.env.NODE_ENV === 'development') {
    console.log("✅ Migration terminée avec succès");
    }

    return NextResponse.json({
      success: true,
      message: "Migration des colonnes du titre phare terminée",
      data: {
        columnsAdded: addedColumns,
        existingColumns: existingColumns,
        finalColumns: finalColumns,
        allColumnsPresent: finalColumns.length === 3
      }
    });

  } catch (error) {
    console.error("❌ Erreur lors de la migration:", error);
    
    return NextResponse.json(
      {
        success: false,
        error: "Erreur lors de la migration",
        message: error instanceof Error ? error.message : "Unknown error"
      },
      { status: 500 }
    );
  }
}
