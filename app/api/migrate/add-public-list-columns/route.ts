import { NextResponse } from "next/server";
import { db } from "@/lib/db";
import { sql } from "drizzle-orm";

/**
 * Migration pour Epic 16: Partage Public des Recommandations
 * Ajoute les colonnes publicListEnabled et publicListId à la table users
 */
export async function POST() {
  try {
    if (process.env.NODE_ENV === 'development') {
    console.log("🔄 Début de la migration: ajout des colonnes pour le partage public");
    }

    // Ajouter la colonne publicListEnabled (par défaut false pour la confidentialité)
    await db.execute(sql`
      ALTER TABLE users ADD COLUMN IF NOT EXISTS "publicListEnabled" BOOLEAN NOT NULL DEFAULT FALSE;
    `);

    // Ajouter la colonne publicListId avec un UUID unique
    await db.execute(sql`
      ALTER TABLE users ADD COLUMN IF NOT EXISTS "publicListId" TEXT UNIQUE DEFAULT gen_random_uuid();
    `);

    // Créer un index sur publicListId pour les performances
    await db.execute(sql`
      CREATE INDEX IF NOT EXISTS idx_users_publicListId ON users("publicListId");
    `);

    // Ajouter des commentaires pour la documentation
    await db.execute(sql`
      COMMENT ON COLUMN users."publicListEnabled" IS 'Indique si la liste de recommandations de l''utilisateur est publique (Epic 16)';
    `);

    await db.execute(sql`
      COMMENT ON COLUMN users."publicListId" IS 'Identifiant unique pour l''URL publique de la liste de recommandations (Epic 16)';
    `);

    if (process.env.NODE_ENV === 'development') {
    console.log("✅ Migration terminée avec succès");
    }

    return NextResponse.json({
      success: true,
      message: "Colonnes pour le partage public ajoutées avec succès"
    });

  } catch (error) {
    console.error("❌ Erreur lors de la migration:", error);
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : "Erreur inconnue" 
      },
      { status: 500 }
    );
  }
}
