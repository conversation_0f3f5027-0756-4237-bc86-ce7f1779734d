import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { sql } from "drizzle-orm";

/**
 * Migration pour ajouter la colonne topTrackId à la table recommendations
 * Nécessaire pour l'US 3.6 avec Spotify Embed
 */
export async function POST(request: NextRequest) {
  try {
    if (process.env.NODE_ENV === 'development') {
    console.log("🔄 Début de la migration: ajout de la colonne topTrackId");
    }

    // Vérifier si la colonne existe déjà
    const checkColumns = await db.execute(sql`
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'recommendations' 
      AND column_name = 'topTrackId'
    `);

    if (checkColumns.length > 0) {
      if (process.env.NODE_ENV === 'development') {
      console.log("⏭️ La colonne topTrackId existe déjà");
      }
      return NextResponse.json({
        success: true,
        message: "La colonne topTrackId existe déjà",
        alreadyExists: true
      });
    }

    // Ajouter la colonne topTrackId
    if (process.env.NODE_ENV === 'development') {
    console.log("➕ Ajout de la colonne topTrackId...");
    }
    await db.execute(sql`
      ALTER TABLE recommendations 
      ADD COLUMN "topTrackId" TEXT
    `);

    if (process.env.NODE_ENV === 'development') {
    console.log("✅ Colonne topTrackId ajoutée avec succès");
    }

    // Vérifier que la colonne a été ajoutée
    const verifyColumns = await db.execute(sql`
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'recommendations' 
      AND column_name = 'topTrackId'
    `);

    if (verifyColumns.length === 0) {
      throw new Error("La colonne topTrackId n'a pas été créée correctement");
    }

    return NextResponse.json({
      success: true,
      message: "Migration réussie: colonne topTrackId ajoutée",
      details: {
        columnAdded: "topTrackId",
        type: "TEXT",
        nullable: true
      }
    });

  } catch (error) {
    console.error("❌ Erreur lors de la migration:", error);
    
    return NextResponse.json(
      {
        success: false,
        error: "Erreur lors de la migration",
        message: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}

/**
 * GET pour vérifier l'état de la migration
 */
export async function GET(request: NextRequest) {
  try {
    // Vérifier si la colonne topTrackId existe
    const checkColumns = await db.execute(sql`
      SELECT column_name, data_type, is_nullable
      FROM information_schema.columns 
      WHERE table_name = 'recommendations' 
      AND column_name IN ('topTrackId', 'topTrackName', 'topTrackPreviewUrl', 'topTrackListenScore')
      ORDER BY column_name
    `);

    const columns = checkColumns.map((row: any) => ({
      name: row.column_name,
      type: row.data_type,
      nullable: row.is_nullable === 'YES'
    }));

    return NextResponse.json({
      success: true,
      message: "État des colonnes topTrack",
      data: {
        columns,
        hasTopTrackId: columns.some(col => col.name === 'topTrackId'),
        allColumnsPresent: ['topTrackId', 'topTrackName', 'topTrackPreviewUrl', 'topTrackListenScore']
          .every(colName => columns.some(col => col.name === colName))
      }
    });

  } catch (error) {
    console.error("❌ Erreur lors de la vérification:", error);
    
    return NextResponse.json(
      {
        success: false,
        error: "Erreur lors de la vérification",
        message: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}
