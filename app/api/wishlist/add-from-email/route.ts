import { NextRequest, NextResponse } from "next/server";
import { addToWishlistByAlbum } from "@/app/actions/wishlist";

/**
 * Route API pour ajouter un album à la wishlist depuis un email
 * Utilisée par les boutons cœur dans les emails de recommandations
 * Redirige vers /recommendations après l'ajout
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const artistName = searchParams.get('artist');
    const albumTitle = searchParams.get('album');

    // Vérifier que les paramètres requis sont présents
    if (!artistName || !albumTitle) {
      return NextResponse.redirect(
        new URL('/recommendations?error=missing-params', request.url)
      );
    }

    // Décoder les paramètres URL
    const decodedArtist = decodeURIComponent(artistName);
    const decodedAlbum = decodeURIComponent(albumTitle);

    // Ajouter l'album à la wishlist
    const result = await addToWishlistByAlbum(decodedArtist, decodedAlbum);

    if (result.success) {
      // Rediriger vers /recommendations avec un message de succès
      return NextResponse.redirect(
        new URL('/recommendations?wishlist=added', request.url)
      );
    } else {
      // Rediriger vers /recommendations avec un message d'erreur
      const errorParam = result.error?.includes('déjà') ? 'already-in-wishlist' : 'error';
      return NextResponse.redirect(
        new URL(`/recommendations?wishlist=${errorParam}`, request.url)
      );
    }

  } catch (error) {
    console.error('Erreur lors de l\'ajout à la wishlist depuis l\'email:', error);
    
    // Rediriger vers /recommendations avec un message d'erreur générique
    return NextResponse.redirect(
      new URL('/recommendations?wishlist=error', request.url)
    );
  }
}
