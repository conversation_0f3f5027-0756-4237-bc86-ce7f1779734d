import { NextRequest, NextResponse } from "next/server";
import { getSession } from "@/lib/auth";
import { getPublicListMetrics } from "@/lib/analytics";

/**
 * API pour récupérer les métriques d'une liste publique
 * Accessible uniquement au propriétaire de la liste
 */
export async function GET(request: NextRequest) {
  try {
    // Vérifier l'authentification
    const session = await getSession();
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: "Authentification requise" },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const publicListId = searchParams.get("publicListId");

    if (!publicListId) {
      return NextResponse.json(
        { error: "publicListId requis" },
        { status: 400 }
      );
    }

    // Récupérer les métriques (avec vérification de propriété)
    const metrics = await getPublicListMetrics(session.user.id, publicListId);

    return NextResponse.json(metrics);

  } catch (error) {
    console.error("Erreur lors de la récupération des métriques:", error);
    
    if (error instanceof Error && error.message.includes('non autorisé')) {
      return NextResponse.json(
        { error: "Accès non autorisé à cette liste" },
        { status: 403 }
      );
    }

    return NextResponse.json(
      { error: "Erreur serveur" },
      { status: 500 }
    );
  }
}
