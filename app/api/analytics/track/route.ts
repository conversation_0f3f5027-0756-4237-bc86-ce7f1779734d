import { NextRequest, NextResponse } from "next/server";
import { trackPublicListEvent, AnalyticsEventType } from "@/lib/analytics";

/**
 * API pour tracker les événements analytics côté client
 * Utilisée pour les clics d'inscription, partages, etc.
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { publicListId, eventType, eventData } = body;

    // Validation des paramètres
    if (!publicListId || !eventType) {
      return NextResponse.json(
        { error: "publicListId et eventType sont requis" },
        { status: 400 }
      );
    }

    // Validation du type d'événement
    const validEventTypes: AnalyticsEventType[] = ['view', 'share', 'signup_click', 'signup_conversion'];
    if (!validEventTypes.includes(eventType)) {
      return NextResponse.json(
        { error: "Type d'événement invalide" },
        { status: 400 }
      );
    }

    // Enregistrer l'événement
    const result = await trackPublicListEvent(publicListId, eventType, eventData);

    if (!result) {
      return NextResponse.json(
        { error: "Impossible d'enregistrer l'événement" },
        { status: 400 }
      );
    }

    return NextResponse.json({
      success: true,
      eventId: result.id,
    });

  } catch (error) {
    console.error("Erreur lors du tracking analytics:", error);
    return NextResponse.json(
      { error: "Erreur serveur" },
      { status: 500 }
    );
  }
}
