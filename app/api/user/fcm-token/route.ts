/**
 * API Route pour la gestion des tokens FCM
 * Epic 6 - Notifications Utilisateur
 */

import { NextRequest, NextResponse } from "next/server";
import { getSession } from "@/lib/auth";
import { db } from "@/lib/db";
import { userFCMTokens } from "@/lib/db/schema";
import { eq, and } from "drizzle-orm";

/**
 * POST - Sauvegarde ou met à jour un token FCM pour l'utilisateur connecté
 */
export async function POST(request: NextRequest) {
  try {
    const session = await getSession();
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: "Non autorisé" },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { token, deviceInfo } = body;

    if (!token || typeof token !== 'string') {
      return NextResponse.json(
        { error: "Token FCM requis" },
        { status: 400 }
      );
    }

    if (process.env.NODE_ENV === 'development') {
    console.log(`💾 Sauvegarde du token FCM pour l'utilisateur ${session.user.id}`);
    }

    // Vérifier si ce token existe déjà pour cet utilisateur
    const existingToken = await db.query.userFCMTokens.findFirst({
      where: and(
        eq(userFCMTokens.userId, session.user.id),
        eq(userFCMTokens.token, token)
      )
    });

    if (existingToken) {
      // Mettre à jour le token existant (lastUsedAt et deviceInfo)
      await db.update(userFCMTokens)
        .set({
          lastUsedAt: new Date(),
          deviceInfo: deviceInfo || existingToken.deviceInfo,
          isActive: true
        })
        .where(eq(userFCMTokens.id, existingToken.id));

      if (process.env.NODE_ENV === 'development') {
      console.log(`✅ Token FCM mis à jour pour l'utilisateur ${session.user.id}`);
      }
    } else {
      // Créer un nouveau token
      await db.insert(userFCMTokens).values({
        userId: session.user.id,
        token,
        deviceInfo: deviceInfo || null,
        isActive: true,
        createdAt: new Date(),
        lastUsedAt: new Date()
      });

      if (process.env.NODE_ENV === 'development') {
      console.log(`✅ Nouveau token FCM créé pour l'utilisateur ${session.user.id}`);
      }
    }

    return NextResponse.json({
      success: true,
      message: "Token FCM sauvegardé avec succès"
    });

  } catch (error) {
    console.error("❌ Erreur lors de la sauvegarde du token FCM:", error);
    return NextResponse.json(
      { error: "Erreur interne du serveur" },
      { status: 500 }
    );
  }
}

/**
 * GET - Récupère les tokens FCM actifs de l'utilisateur connecté
 */
export async function GET(request: NextRequest) {
  try {
    const session = await getSession();
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: "Non autorisé" },
        { status: 401 }
      );
    }

    const tokens = await db.query.userFCMTokens.findMany({
      where: and(
        eq(userFCMTokens.userId, session.user.id),
        eq(userFCMTokens.isActive, true)
      ),
      columns: {
        id: true,
        token: true,
        deviceInfo: true,
        createdAt: true,
        lastUsedAt: true
      },
      orderBy: (tokens, { desc }) => [desc(tokens.lastUsedAt)]
    });

    // Masquer une partie du token pour la sécurité
    const maskedTokens = tokens.map(token => ({
      ...token,
      token: token.token.substring(0, 20) + '...' + token.token.substring(token.token.length - 10)
    }));

    return NextResponse.json({
      success: true,
      tokens: maskedTokens,
      count: tokens.length
    });

  } catch (error) {
    console.error("❌ Erreur lors de la récupération des tokens FCM:", error);
    return NextResponse.json(
      { error: "Erreur interne du serveur" },
      { status: 500 }
    );
  }
}

/**
 * DELETE - Désactive un token FCM spécifique
 */
export async function DELETE(request: NextRequest) {
  try {
    const session = await getSession();
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: "Non autorisé" },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const tokenId = searchParams.get('tokenId');

    if (!tokenId) {
      return NextResponse.json(
        { error: "ID du token requis" },
        { status: 400 }
      );
    }

    // Vérifier que le token appartient à l'utilisateur
    const token = await db.query.userFCMTokens.findFirst({
      where: and(
        eq(userFCMTokens.id, parseInt(tokenId)),
        eq(userFCMTokens.userId, session.user.id)
      )
    });

    if (!token) {
      return NextResponse.json(
        { error: "Token non trouvé" },
        { status: 404 }
      );
    }

    // Désactiver le token
    await db.update(userFCMTokens)
      .set({ isActive: false })
      .where(eq(userFCMTokens.id, parseInt(tokenId)));

    if (process.env.NODE_ENV === 'development') {
    console.log(`🗑️ Token FCM désactivé pour l'utilisateur ${session.user.id}`);
    }

    return NextResponse.json({
      success: true,
      message: "Token FCM désactivé avec succès"
    });

  } catch (error) {
    console.error("❌ Erreur lors de la désactivation du token FCM:", error);
    return NextResponse.json(
      { error: "Erreur interne du serveur" },
      { status: 500 }
    );
  }
}

/**
 * PUT - Nettoie les anciens tokens inactifs (admin uniquement)
 */
export async function PUT(request: NextRequest) {
  try {
    // Vérifier l'authentification admin
    const authHeader = request.headers.get("authorization");
    const expectedAuth = `Bearer ${process.env.CRON_SECRET}`;
    
    if (authHeader !== expectedAuth) {
      return NextResponse.json(
        { error: "Non autorisé" },
        { status: 401 }
      );
    }

    // Supprimer les tokens inactifs de plus de 30 jours
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const result = await db.delete(userFCMTokens)
      .where(and(
        eq(userFCMTokens.isActive, false),
        // Note: Drizzle ORM syntax pour les dates peut varier
      ));

    if (process.env.NODE_ENV === 'development') {
    console.log(`🧹 Nettoyage des anciens tokens FCM effectué`);
    }

    return NextResponse.json({
      success: true,
      message: "Nettoyage des anciens tokens effectué"
    });

  } catch (error) {
    console.error("❌ Erreur lors du nettoyage des tokens FCM:", error);
    return NextResponse.json(
      { error: "Erreur interne du serveur" },
      { status: 500 }
    );
  }
}
