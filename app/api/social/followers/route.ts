import { NextRequest, NextResponse } from 'next/server';
import { getFollowers } from '@/app/actions/social';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const targetUserId = searchParams.get('userId');

    if (!targetUserId) {
      return NextResponse.json(
        { success: false, error: 'ID utilisateur manquant' },
        { status: 400 }
      );
    }

    const followers = await getFollowers(targetUserId);

    // ✅ CORRECTION: Wrapper le résultat dans un objet avec success
    return NextResponse.json({
      success: true,
      followers: followers
    });

  } catch (error) {
    console.error('Erreur API followers:', error);
    return NextResponse.json(
      { success: false, error: 'Erreur serveur' },
      { status: 500 }
    );
  }
} 