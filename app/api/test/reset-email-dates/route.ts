import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { users } from "@/lib/db/schema";
import { eq, or } from "drizzle-orm";

/**
 * Endpoint de test pour réinitialiser les dates d'envoi d'email
 * Uniquement disponible en développement
 */
export async function POST(request: NextRequest) {
  // Seulement en développement
  if (process.env.NODE_ENV !== 'development') {
    return NextResponse.json(
      { error: "Not available in production" },
      { status: 403 }
    );
  }

  try {
    console.log('🧪 Réinitialisation des dates d\'email pour les tests');

    // Réinitialiser les dates pour les utilisateurs de test
    const result = await db
      .update(users)
      .set({ 
        lastEmailSent: null 
      })
      .where(
        or(
          eq(users.email, '<EMAIL>'),
          eq(users.email, '<EMAIL>')
        )
      );

    console.log('✅ Dates d\'email réinitialisées');

    // Vérifier le résultat
    const updatedUsers = await db.query.users.findMany({
      where: or(
        eq(users.email, '<EMAIL>'),
        eq(users.email, '<EMAIL>')
      ),
      columns: {
        id: true,
        name: true,
        email: true,
        emailFrequency: true,
        lastEmailSent: true,
        firstRecommendationEmailSent: true
      }
    });

    return NextResponse.json({
      success: true,
      message: "Dates d'email réinitialisées",
      users: updatedUsers
    });

  } catch (error) {
    console.error('❌ Erreur lors de la réinitialisation:', error);
    return NextResponse.json(
      { error: "Erreur lors de la réinitialisation" },
      { status: 500 }
    );
  }
}
