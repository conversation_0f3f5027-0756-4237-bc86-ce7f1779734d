import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { sql } from "drizzle-orm";

/**
 * API Admin pour vérifier TOUTES les tables Epic Social V1
 * Vérifie users, followers, notifications et ENUMs
 */
export async function GET(request: NextRequest) {
  try {
    console.log("🔍 Vérification complète du schéma Epic Social V1...");

    const environment = process.env.VERCEL_ENV || process.env.NODE_ENV || 'unknown';
    
    // Structure attendue pour Epic Social V1
    const expectedSchema = {
      enums: [
        { name: 'profile_visibility', values: ['private', 'users_only', 'public'] },
        { name: 'notification_type', values: ['new_follower'] }
      ],
      tables: {
                 users: [
           'profile_visibility',
           'share_recommendations', 
           'share_wishlist',
           'share_collection',
           'email_on_new_follower'
         ],
        followers: [
          'follower_id',
          'following_id', 
          'created_at'
        ],
        notifications: [
          'id',
          'recipient_id',
          'actor_id',
          'type',
          'is_read',
          'created_at'
        ]
      }
    };

    const results = {
      environment,
      timestamp: new Date().toISOString(),
      enums: {} as Record<string, any>,
      tables: {} as Record<string, any>,
      summary: {
        allTablesExist: true,
        allColumnsExist: true,
        allEnumsExist: true,
        migrationNeeded: false,
        missingItems: [] as string[]
      }
    };

    // 1. Vérifier les ENUMs
    console.log("🔍 Vérification des ENUMs...");
    for (const enumDef of expectedSchema.enums) {
      try {
        const enumCheck = await db.execute(sql`
          SELECT enumlabel 
          FROM pg_enum 
          JOIN pg_type ON pg_enum.enumtypid = pg_type.oid 
          WHERE pg_type.typname = ${enumDef.name};
        `);
        
        const existingValues = enumCheck.map((row: any) => row.enumlabel);
        const missingValues = enumDef.values.filter(val => !existingValues.includes(val));
        
        results.enums[enumDef.name] = {
          exists: enumCheck.length > 0,
          expectedValues: enumDef.values,
          existingValues,
          missingValues,
          isComplete: missingValues.length === 0
        };

        if (!results.enums[enumDef.name].exists) {
          results.summary.allEnumsExist = false;
          results.summary.missingItems.push(`ENUM ${enumDef.name}`);
        }
      } catch (error) {
        results.enums[enumDef.name] = {
          exists: false,
          error: String(error),
          expectedValues: enumDef.values
        };
        results.summary.allEnumsExist = false;
        results.summary.missingItems.push(`ENUM ${enumDef.name} (error)`);
      }
    }

    // 2. Vérifier les tables et colonnes
    console.log("🔍 Vérification des tables...");
    for (const [tableName, expectedColumns] of Object.entries(expectedSchema.tables)) {
      try {
        // Vérifier si la table existe
        const tableExists = await db.execute(sql`
          SELECT table_name 
          FROM information_schema.tables 
          WHERE table_schema = 'public' 
          AND table_name = ${tableName};
        `);

        if (tableExists.length === 0) {
          results.tables[tableName] = {
            exists: false,
            expectedColumns,
            existingColumns: [],
            missingColumns: expectedColumns
          };
          results.summary.allTablesExist = false;
          results.summary.missingItems.push(`TABLE ${tableName}`);
          continue;
        }

        // Récupérer les colonnes existantes
        const columnsInfo = await db.execute(sql`
          SELECT column_name, data_type, is_nullable, column_default
          FROM information_schema.columns 
          WHERE table_name = ${tableName}
          AND table_schema = 'public'
          ORDER BY ordinal_position;
        `);

        const existingColumns = columnsInfo.map((row: any) => row.column_name);
        const missingColumns = expectedColumns.filter(col => !existingColumns.includes(col));

        results.tables[tableName] = {
          exists: true,
          expectedColumns,
          existingColumns,
          missingColumns,
          isComplete: missingColumns.length === 0,
          allColumns: columnsInfo.map((row: any) => ({
            name: row.column_name,
            type: row.data_type,
            nullable: row.is_nullable === 'YES',
            default: row.column_default
          }))
        };

        if (missingColumns.length > 0) {
          results.summary.allColumnsExist = false;
          missingColumns.forEach(col => {
            results.summary.missingItems.push(`COLUMN ${tableName}.${col}`);
          });
        }

      } catch (error) {
        results.tables[tableName] = {
          exists: false,
          error: String(error),
          expectedColumns
        };
        results.summary.allTablesExist = false;
        results.summary.missingItems.push(`TABLE ${tableName} (error)`);
      }
    }

    // 3. Résumé global
    results.summary.migrationNeeded = !results.summary.allTablesExist || 
                                      !results.summary.allColumnsExist || 
                                      !results.summary.allEnumsExist;

    console.log(`✅ Vérification terminée - ${results.summary.missingItems.length} éléments manquants`);

    return NextResponse.json(results);

  } catch (error) {
    console.error("❌ Erreur lors de la vérification complète:", error);
    return NextResponse.json(
      { 
        error: "Erreur lors de la vérification complète du schéma", 
        details: error instanceof Error ? error.message : String(error),
        environment: process.env.VERCEL_ENV || process.env.NODE_ENV || 'unknown'
      },
      { status: 500 }
    );
  }
} 