import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { sql } from "drizzle-orm";

/**
 * Route pour diagnostiquer la structure exacte de la table accounts
 */
export async function GET(request: NextRequest) {
  try {
    if (process.env.NODE_ENV === 'development') {
    if (process.env.NODE_ENV === 'development') {
    if (process.env.NODE_ENV === 'development') {
    console.log("🔍 Diagnostic complet de la table accounts...");
    }
    }
    }

    // 1. Structure des colonnes
    const columns = await db.execute(sql`
      SELECT 
        column_name, 
        data_type, 
        is_nullable, 
        column_default,
        ordinal_position
      FROM information_schema.columns 
      WHERE table_name = 'accounts' 
      AND table_schema = 'public'
      ORDER BY ordinal_position
    `);

    // 2. Contraintes et clés primaires
    const constraints = await db.execute(sql`
      SELECT 
        tc.constraint_name,
        tc.constraint_type,
        kcu.column_name,
        tc.table_name
      FROM information_schema.table_constraints tc
      JOIN information_schema.key_column_usage kcu 
        ON tc.constraint_name = kcu.constraint_name
      WHERE tc.table_name = 'accounts'
      AND tc.table_schema = 'public'
    `);

    // 3. Index existants
    const indexes = await db.execute(sql`
      SELECT 
        indexname,
        indexdef
      FROM pg_indexes 
      WHERE tablename = 'accounts'
      AND schemaname = 'public'
    `);

    // 4. Données existantes
    const existingData = await db.execute(sql`
      SELECT 
        provider, 
        "providerAccountId", 
        "userId",
        COUNT(*) as count
      FROM accounts 
      GROUP BY provider, "providerAccountId", "userId"
      ORDER BY count DESC
    `);

    // 5. Comptes Spotify spécifiques
    const spotifyAccounts = await db.execute(sql`
      SELECT 
        "userId", 
        "providerAccountId", 
        "refresh_token" IS NOT NULL as has_refresh_token,
        "access_token" IS NOT NULL as has_access_token
      FROM accounts 
      WHERE provider = 'spotify'
    `);

    return NextResponse.json({
      success: true,
      message: "Diagnostic de la table accounts terminé",
      results: {
        columns: columns,
        constraints: constraints,
        indexes: indexes,
        existingData: existingData,
        spotifyAccounts: spotifyAccounts,
        totalAccounts: existingData.reduce((sum, row) => sum + Number(row.count), 0)
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error("❌ Erreur lors du diagnostic:", error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : "Erreur inconnue",
      stack: error instanceof Error ? error.stack : undefined
    }, { status: 500 });
  }
}
