import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { sql } from "drizzle-orm";

/**
 * Route temporaire pour initialiser la base de données (SANS AUTHENTIFICATION)
 * À SUPPRIMER après utilisation !
 */
export async function GET(request: NextRequest) {
  try {
    if (process.env.NODE_ENV === 'development') {
    console.log("🚀 Initialisation de la base de données (route temporaire)...");
    }

    // Test de connexion
    if (process.env.NODE_ENV === 'development') {
    if (process.env.NODE_ENV === 'development') {
    if (process.env.NODE_ENV === 'development') {
    console.log("🔍 Test de connexion...");
    }
    }
    }
    await db.execute(sql`SELECT 1 as test`);
    if (process.env.NODE_ENV === 'development') {
    console.log("✅ Connexion réussie");
    }

    // Créer toutes les tables
    if (process.env.NODE_ENV === 'development') {
    console.log("📋 Création des tables...");
    }
    
    // 1. Table users
    if (process.env.NODE_ENV === 'development') {
    console.log("  - Création de la table users...");
    }
    await db.execute(sql`
      CREATE TABLE IF NOT EXISTS users (
        id TEXT PRIMARY KEY,
        name TEXT,
        email TEXT UNIQUE,
        "emailVerified" TIMESTAMP,
        image TEXT,
        "preferredLanguage" TEXT DEFAULT 'fr' NOT NULL,
        "emailFrequency" TEXT DEFAULT 'weekly' NOT NULL,
        "pushFrequency" TEXT DEFAULT 'weekly' NOT NULL,
        "firstRecommendationEmailSent" BOOLEAN DEFAULT false NOT NULL
      )
    `);

    // 2. Table accounts
    if (process.env.NODE_ENV === 'development') {
    console.log("  - Création de la table accounts...");
    }
    await db.execute(sql`
      CREATE TABLE IF NOT EXISTS accounts (
        "userId" TEXT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        type TEXT NOT NULL,
        provider TEXT NOT NULL,
        "providerAccountId" TEXT NOT NULL,
        refresh_token TEXT,
        access_token TEXT,
        access_token_secret TEXT,
        expires_at INTEGER,
        token_type TEXT,
        scope TEXT,
        id_token TEXT,
        session_state TEXT,
        PRIMARY KEY (provider, "providerAccountId")
      )
    `);

    // 3. Table sessions
    if (process.env.NODE_ENV === 'development') {
    console.log("  - Création de la table sessions...");
    }
    await db.execute(sql`
      CREATE TABLE IF NOT EXISTS sessions (
        "sessionToken" TEXT PRIMARY KEY,
        "userId" TEXT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        expires TIMESTAMP NOT NULL
      )
    `);

    // 4. Table verification_tokens
    if (process.env.NODE_ENV === 'development') {
    console.log("  - Création de la table verification_tokens...");
    }
    await db.execute(sql`
      CREATE TABLE IF NOT EXISTS verification_tokens (
        identifier TEXT NOT NULL,
        token TEXT NOT NULL UNIQUE,
        expires TIMESTAMP NOT NULL,
        PRIMARY KEY (identifier, token)
      )
    `);

    if (process.env.NODE_ENV === 'development') {
    console.log("✅ Tables NextAuth créées avec succès");
    }

    // Créer les index pour optimiser les performances
    if (process.env.NODE_ENV === 'development') {
    if (process.env.NODE_ENV === 'development') {
    if (process.env.NODE_ENV === 'development') {
    console.log("🔍 Création des index...");
    }
    }
    }
    
    await db.execute(sql`CREATE INDEX IF NOT EXISTS idx_accounts_user_id ON accounts("userId")`);
    await db.execute(sql`CREATE INDEX IF NOT EXISTS idx_sessions_user_id ON sessions("userId")`);
    
    if (process.env.NODE_ENV === 'development') {
    console.log("✅ Index créés avec succès");
    }

    // Vérifier que tout est en place
    if (process.env.NODE_ENV === 'development') {
    if (process.env.NODE_ENV === 'development') {
    if (process.env.NODE_ENV === 'development') {
    console.log("🔍 Vérification finale...");
    }
    }
    }
    const tables = await db.execute(sql`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      AND table_name IN ('users', 'accounts', 'sessions', 'verification_tokens')
      ORDER BY table_name
    `);
    
    const tableNames = tables.map((table: any) => table.table_name);

    return NextResponse.json({
      success: true,
      message: "Tables NextAuth créées avec succès",
      tables: tableNames,
      note: "Cette route temporaire doit être supprimée après utilisation",
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error("❌ Erreur lors de l'initialisation:", error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : "Erreur inconnue",
      stack: error instanceof Error ? error.stack : undefined
    }, { status: 500 });
  }
}
