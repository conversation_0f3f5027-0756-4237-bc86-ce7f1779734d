import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { sql } from "drizzle-orm";

/**
 * Route pour corriger la structure de la table accounts
 * Ajoute les colonnes manquantes pour NextAuth
 */
export async function GET(request: NextRequest) {
  try {
    // Ajouter les colonnes manquantes une par une
    
    // refresh_token
    try {
      await db.execute(sql`ALTER TABLE accounts ADD COLUMN IF NOT EXISTS refresh_token TEXT`);
      if (process.env.NODE_ENV === 'development') {
        console.log("✅ refresh_token ajouté");
      }
    } catch (e) { 
      if (process.env.NODE_ENV === 'development') {
        console.log("ℹ️ refresh_token existe déjà"); 
      }
    }

    // access_token
    try {
      await db.execute(sql`ALTER TABLE accounts ADD COLUMN IF NOT EXISTS access_token TEXT`);
      if (process.env.NODE_ENV === 'development') {
        console.log("✅ access_token ajouté");
      }
    } catch (e) { 
      if (process.env.NODE_ENV === 'development') {
        console.log("ℹ️ access_token existe déjà"); 
      }
    }

    // access_token_secret
    try {
      await db.execute(sql`ALTER TABLE accounts ADD COLUMN IF NOT EXISTS access_token_secret TEXT`);
      if (process.env.NODE_ENV === 'development') {
        console.log("✅ access_token_secret ajouté");
      }
    } catch (e) { 
      if (process.env.NODE_ENV === 'development') {
        console.log("ℹ️ access_token_secret existe déjà"); 
      }
    }

    // expires_at
    try {
      await db.execute(sql`ALTER TABLE accounts ADD COLUMN IF NOT EXISTS expires_at INTEGER`);
      if (process.env.NODE_ENV === 'development') {
        console.log("✅ expires_at ajouté");
      }
    } catch (e) { 
      if (process.env.NODE_ENV === 'development') {
        console.log("ℹ️ expires_at existe déjà"); 
      }
    }

    // token_type
    try {
      await db.execute(sql`ALTER TABLE accounts ADD COLUMN IF NOT EXISTS token_type TEXT`);
      if (process.env.NODE_ENV === 'development') {
        console.log("✅ token_type ajouté");
      }
    } catch (e) { 
      if (process.env.NODE_ENV === 'development') {
        console.log("ℹ️ token_type existe déjà"); 
      }
    }

    // scope
    try {
      await db.execute(sql`ALTER TABLE accounts ADD COLUMN IF NOT EXISTS scope TEXT`);
      if (process.env.NODE_ENV === 'development') {
        console.log("✅ scope ajouté");
      }
    } catch (e) { 
      if (process.env.NODE_ENV === 'development') {
        console.log("ℹ️ scope existe déjà"); 
      }
    }

    // id_token
    try {
      await db.execute(sql`ALTER TABLE accounts ADD COLUMN IF NOT EXISTS id_token TEXT`);
      if (process.env.NODE_ENV === 'development') {
        console.log("✅ id_token ajouté");
      }
    } catch (e) { 
      if (process.env.NODE_ENV === 'development') {
        console.log("ℹ️ id_token existe déjà"); 
      }
    }

    // session_state
    try {
      await db.execute(sql`ALTER TABLE accounts ADD COLUMN IF NOT EXISTS session_state TEXT`);
      if (process.env.NODE_ENV === 'development') {
        console.log("✅ session_state ajouté");
      }
    } catch (e) { 
      if (process.env.NODE_ENV === 'development') {
        console.log("ℹ️ session_state existe déjà"); 
      }
    }

    // Vérifier la structure finale
    if (process.env.NODE_ENV === 'development') {
      console.log("🔍 Vérification de la structure finale...");
    }
    
    const result = await db.execute(sql`
      SELECT column_name, data_type, is_nullable 
      FROM information_schema.columns 
      WHERE table_name = 'accounts' 
      ORDER BY ordinal_position
    `);
    
    if (process.env.NODE_ENV === 'development') {
      console.log("📊 Structure de la table accounts:", result);
    }

    return NextResponse.json({ 
      success: true, 
      message: "Structure de la table accounts corrigée avec succès",
      columns: result
    });

  } catch (error) {
    console.error("Erreur lors de la correction de la table accounts:", error);
    return NextResponse.json({ 
      success: false, 
      error: error instanceof Error ? error.message : "Erreur inconnue" 
    }, { status: 500 });
  }
}
