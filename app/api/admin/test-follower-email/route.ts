import { NextRequest, NextResponse } from "next/server";
import { getSession } from "@/lib/auth";

/**
 * API Admin pour tester l'envoi d'email de nouveau follower
 * Envoie un email de test à l'utilisateur connecté
 */
export async function POST(request: NextRequest) {
  try {
    console.log("🧪 Test envoi email nouveau follower...");

    const session = await getSession();
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: "Non autorisé" },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const testRecipientEmail = searchParams.get('testEmail') || session.user.email;
    const testRecipientName = searchParams.get('testName') || session.user.name || 'Utilisateur Test';
    const testActorName = searchParams.get('actorName') || 'Alice Test';
    const testLocale = searchParams.get('locale') || 'fr';

    if (!testRecipientEmail) {
      return NextResponse.json(
        { error: "Email destinataire requis" },
        { status: 400 }
      );
    }

    const environment = process.env.VERCEL_ENV || process.env.NODE_ENV || 'unknown';
    
    const result = {
      environment,
      timestamp: new Date().toISOString(),
      testData: {
        recipientEmail: testRecipientEmail,
        recipientName: testRecipientName,
        actorName: testActorName,
        locale: testLocale,
      },
      emailResult: null as any,
      success: false,
      error: null as string | null
    };

    try {
      // Importer et utiliser la fonction d'envoi d'email
      const { sendNewFollowerEmail } = await import('@/lib/email');
      
      console.log(`📧 Envoi email test follower à ${testRecipientEmail}`);
      
      const emailResult = await sendNewFollowerEmail({
        recipientEmail: testRecipientEmail,
        recipientName: testRecipientName,
        actorName: testActorName,
        locale: testLocale,
      });

      result.emailResult = emailResult;
      result.success = emailResult.success;
      
      if (!emailResult.success) {
        result.error = typeof emailResult.error === 'string' ? emailResult.error : JSON.stringify(emailResult.error);
      }

      console.log(`${emailResult.success ? '✅' : '❌'} Test email follower: ${emailResult.success ? 'succès' : result.error}`);

    } catch (emailError) {
      result.error = emailError instanceof Error ? emailError.message : String(emailError);
      console.error("❌ Erreur test email follower:", emailError);
    }

    return NextResponse.json(result);

  } catch (error) {
    console.error("❌ Erreur lors du test email follower:", error);
    return NextResponse.json(
      { 
        error: "Erreur lors du test email follower", 
        details: error instanceof Error ? error.message : String(error),
        environment: process.env.VERCEL_ENV || process.env.NODE_ENV || 'unknown'
      },
      { status: 500 }
    );
  }
} 