import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { sql } from "drizzle-orm";

/**
 * Route pour nettoyer les comptes en double et les données orphelines
 */
export async function GET(request: NextRequest) {
  try {
    if (process.env.NODE_ENV === 'development') {
    console.log("🧹 Nettoyage des comptes en double...");
    }

    // 1. Vérifier les comptes existants
    if (process.env.NODE_ENV === 'development') {
    if (process.env.NODE_ENV === 'development') {
    if (process.env.NODE_ENV === 'development') {
    console.log("🔍 Vérification des comptes existants...");
    }
    }
    }
    const existingAccounts = await db.execute(sql`
      SELECT provider, "providerAccountId", "userId", COUNT(*) as count
      FROM accounts 
      GROUP BY provider, "providerAccountId", "userId"
      HAVING COUNT(*) > 1
    `);

    if (process.env.NODE_ENV === 'development') {
    console.log("📊 Comptes en double trouvés:", existingAccounts.length);
    }

    // 2. Supprimer les comptes Spotify en double pour l'utilisateur **********
    if (process.env.NODE_ENV === 'development') {
    console.log("🗑️ Suppression des comptes Spotify en double...");
    }
    const deletedSpotify = await db.execute(sql`
      DELETE FROM accounts 
      WHERE provider = 'spotify' 
      AND "providerAccountId" = '**********'
    `);

    if (process.env.NODE_ENV === 'development') {
    console.log("✅ Comptes Spotify supprimés:", deletedSpotify);
    }

    // 3. Vérifier les utilisateurs orphelins
    if (process.env.NODE_ENV === 'development') {
    if (process.env.NODE_ENV === 'development') {
    if (process.env.NODE_ENV === 'development') {
    console.log("🔍 Vérification des utilisateurs orphelins...");
    }
    }
    }
    const orphanUsers = await db.execute(sql`
      SELECT u.id, u.email, u.name
      FROM users u
      LEFT JOIN accounts a ON u.id = a."userId"
      WHERE a."userId" IS NULL
    `);

    if (process.env.NODE_ENV === 'development') {
    console.log("👤 Utilisateurs orphelins trouvés:", orphanUsers.length);
    }

    // 4. Supprimer les utilisateurs orphelins (optionnel)
    if (orphanUsers.length > 0) {
      if (process.env.NODE_ENV === 'development') {
      console.log("🗑️ Suppression des utilisateurs orphelins...");
      }
      for (const user of orphanUsers) {
        await db.execute(sql`DELETE FROM users WHERE id = ${user.id}`);
        if (process.env.NODE_ENV === 'development') {
        if (process.env.NODE_ENV === 'development') {
        if (process.env.NODE_ENV === 'development') {
        console.log(`✅ Utilisateur supprimé: ${user.email}`);
        }
        }
        }
      }
    }

    // 5. Vérifier les sessions orphelines
    if (process.env.NODE_ENV === 'development') {
    if (process.env.NODE_ENV === 'development') {
    if (process.env.NODE_ENV === 'development') {
    console.log("🔍 Vérification des sessions orphelines...");
    }
    }
    }
    const orphanSessions = await db.execute(sql`
      SELECT s."sessionToken", s."userId"
      FROM sessions s
      LEFT JOIN users u ON s."userId" = u.id
      WHERE u.id IS NULL
    `);

    if (process.env.NODE_ENV === 'development') {
    console.log("🔗 Sessions orphelines trouvées:", orphanSessions.length);
    }

    // 6. Supprimer les sessions orphelines
    if (orphanSessions.length > 0) {
      if (process.env.NODE_ENV === 'development') {
      console.log("🗑️ Suppression des sessions orphelines...");
      }
      for (const session of orphanSessions) {
        await db.execute(sql`DELETE FROM sessions WHERE "sessionToken" = ${session.sessionToken}`);
      }
      if (process.env.NODE_ENV === 'development') {
      console.log("✅ Sessions orphelines supprimées");
      }
    }

    // 7. Statistiques finales
    if (process.env.NODE_ENV === 'development') {
    console.log("📊 Statistiques finales...");
    }
    const finalStats = await db.execute(sql`
      SELECT 
        (SELECT COUNT(*) FROM users) as users_count,
        (SELECT COUNT(*) FROM accounts) as accounts_count,
        (SELECT COUNT(*) FROM sessions) as sessions_count
    `);

    return NextResponse.json({
      success: true,
      message: "Nettoyage des comptes terminé avec succès",
      results: {
        duplicateAccountsFound: existingAccounts.length,
        spotifyAccountsDeleted: deletedSpotify,
        orphanUsersFound: orphanUsers.length,
        orphanSessionsFound: orphanSessions.length,
        finalStats: finalStats[0]
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error("❌ Erreur lors du nettoyage:", error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : "Erreur inconnue",
      stack: error instanceof Error ? error.stack : undefined
    }, { status: 500 });
  }
}
