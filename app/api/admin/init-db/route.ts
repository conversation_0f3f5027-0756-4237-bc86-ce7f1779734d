import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { sql } from "drizzle-orm";

/**
 * Route d'administration pour initialiser la base de données
 * ATTENTION: Cette route doit être protégée en production !
 */
async function initDatabase(request: NextRequest) {
  try {
    if (process.env.NODE_ENV === 'development') {
    console.log("🚀 Initialisation de la base de données...");
    }

    // Vérification de sécurité basique
    const { searchParams } = new URL(request.url);
    const secret = searchParams.get('secret');
    
    if (secret !== process.env.CRON_SECRET) {
      return NextResponse.json({
        success: false,
        error: "Accès non autorisé"
      }, { status: 401 });
    }

    // Test de connexion
    if (process.env.NODE_ENV === 'development') {
    if (process.env.NODE_ENV === 'development') {
    if (process.env.NODE_ENV === 'development') {
    console.log("🔍 Test de connexion...");
    }
    }
    }
    await db.execute(sql`SELECT 1 as test`);
    if (process.env.NODE_ENV === 'development') {
    console.log("✅ Connexion réussie");
    }

    // Créer toutes les tables
    if (process.env.NODE_ENV === 'development') {
    console.log("📋 Création des tables...");
    }
    
    // 1. Table users
    if (process.env.NODE_ENV === 'development') {
    console.log("  - Création de la table users...");
    }
    await db.execute(sql`
      CREATE TABLE IF NOT EXISTS users (
        id TEXT PRIMARY KEY,
        name TEXT,
        email TEXT UNIQUE,
        "emailVerified" TIMESTAMP,
        image TEXT,
        "preferredLanguage" TEXT DEFAULT 'fr' NOT NULL,
        "emailFrequency" TEXT DEFAULT 'weekly' NOT NULL,
        "pushFrequency" TEXT DEFAULT 'weekly' NOT NULL,
        "firstRecommendationEmailSent" BOOLEAN DEFAULT false NOT NULL
      )
    `);

    // 2. Table accounts
    if (process.env.NODE_ENV === 'development') {
    console.log("  - Création de la table accounts...");
    }
    await db.execute(sql`
      CREATE TABLE IF NOT EXISTS accounts (
        "userId" TEXT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        type TEXT NOT NULL,
        provider TEXT NOT NULL,
        "providerAccountId" TEXT NOT NULL,
        refresh_token TEXT,
        access_token TEXT,
        access_token_secret TEXT,
        expires_at INTEGER,
        token_type TEXT,
        scope TEXT,
        id_token TEXT,
        session_state TEXT,
        PRIMARY KEY (provider, "providerAccountId")
      )
    `);

    // 3. Table sessions
    if (process.env.NODE_ENV === 'development') {
    console.log("  - Création de la table sessions...");
    }
    await db.execute(sql`
      CREATE TABLE IF NOT EXISTS sessions (
        "sessionToken" TEXT PRIMARY KEY,
        "userId" TEXT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        expires TIMESTAMP NOT NULL
      )
    `);

    // 4. Table verification_tokens
    if (process.env.NODE_ENV === 'development') {
    console.log("  - Création de la table verification_tokens...");
    }
    await db.execute(sql`
      CREATE TABLE IF NOT EXISTS verification_tokens (
        identifier TEXT NOT NULL,
        token TEXT NOT NULL UNIQUE,
        expires TIMESTAMP NOT NULL,
        PRIMARY KEY (identifier, token)
      )
    `);

    // 5. Table recommendations
    if (process.env.NODE_ENV === 'development') {
    console.log("  - Création de la table recommendations...");
    }
    await db.execute(sql`
      CREATE TABLE IF NOT EXISTS recommendations (
        id SERIAL PRIMARY KEY,
        "userId" TEXT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        "artistName" TEXT NOT NULL,
        "albumTitle" TEXT NOT NULL,
        "albumCoverUrl" TEXT,
        "discogsReleaseId" BIGINT,
        "spotifyAlbumId" TEXT,
        "listenScore" INTEGER NOT NULL,
        "estimatedPlays" INTEGER DEFAULT 0,
        "isOwned" BOOLEAN DEFAULT false,
        "isWishlisted" BOOLEAN DEFAULT false,
        "affiliateLinks" JSONB,
        "generatedAt" TIMESTAMP DEFAULT NOW(),
        "timeframe" TEXT NOT NULL,
        "topTrackName" TEXT,
        "topTrackId" TEXT,
        "topTrackPreviewUrl" TEXT,
        "topTrackListenScore" INTEGER
      )
    `);

    // 6. Table user_discogs_collection
    if (process.env.NODE_ENV === 'development') {
    console.log("  - Création de la table user_discogs_collection...");
    }
    await db.execute(sql`
      CREATE TABLE IF NOT EXISTS user_discogs_collection (
        id SERIAL PRIMARY KEY,
        "userId" TEXT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        "artistName" TEXT NOT NULL,
        "albumTitle" TEXT NOT NULL,
        "discogsReleaseId" BIGINT NOT NULL,
        year INTEGER,
        format TEXT,
        "albumCoverUrl" TEXT,
        "syncedAt" TIMESTAMP DEFAULT NOW()
      )
    `);

    // 7. Table wishlist_items
    if (process.env.NODE_ENV === 'development') {
    console.log("  - Création de la table wishlist_items...");
    }
    await db.execute(sql`
      CREATE TABLE IF NOT EXISTS wishlist_items (
        id SERIAL PRIMARY KEY,
        "userId" TEXT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        "artistName" TEXT NOT NULL,
        "albumTitle" TEXT NOT NULL,
        "createdAt" TIMESTAMP DEFAULT NOW()
      )
    `);

    // 8. Table user_fcm_tokens
    if (process.env.NODE_ENV === 'development') {
    console.log("  - Création de la table user_fcm_tokens...");
    }
    await db.execute(sql`
      CREATE TABLE IF NOT EXISTS user_fcm_tokens (
        id SERIAL PRIMARY KEY,
        "userId" TEXT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        token TEXT NOT NULL UNIQUE,
        "createdAt" TIMESTAMP DEFAULT NOW(),
        "lastUsed" TIMESTAMP DEFAULT NOW()
      )
    `);

    if (process.env.NODE_ENV === 'development') {
    console.log("✅ Toutes les tables créées avec succès");
    }

    // Créer les index pour optimiser les performances
    if (process.env.NODE_ENV === 'development') {
    if (process.env.NODE_ENV === 'development') {
    if (process.env.NODE_ENV === 'development') {
    console.log("🔍 Création des index...");
    }
    }
    }
    
    await db.execute(sql`CREATE INDEX IF NOT EXISTS idx_recommendations_user_id ON recommendations("userId")`);
    await db.execute(sql`CREATE INDEX IF NOT EXISTS idx_recommendations_generated_at ON recommendations("generatedAt")`);
    await db.execute(sql`CREATE INDEX IF NOT EXISTS idx_user_discogs_collection_user_id ON user_discogs_collection("userId")`);
    await db.execute(sql`CREATE INDEX IF NOT EXISTS idx_wishlist_items_user_id ON wishlist_items("userId")`);
    await db.execute(sql`CREATE INDEX IF NOT EXISTS idx_accounts_user_id ON accounts("userId")`);
    await db.execute(sql`CREATE INDEX IF NOT EXISTS idx_sessions_user_id ON sessions("userId")`);
    
    if (process.env.NODE_ENV === 'development') {
    console.log("✅ Index créés avec succès");
    }

    // Vérifier que tout est en place
    if (process.env.NODE_ENV === 'development') {
    if (process.env.NODE_ENV === 'development') {
    if (process.env.NODE_ENV === 'development') {
    console.log("🔍 Vérification finale...");
    }
    }
    }
    const tables = await db.execute(sql`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      ORDER BY table_name
    `);
    
    const tableNames = tables.map((table: any) => table.table_name);

    return NextResponse.json({
      success: true,
      message: "Base de données initialisée avec succès",
      tables: tableNames,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error("❌ Erreur lors de l'initialisation:", error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : "Erreur inconnue",
      stack: error instanceof Error ? error.stack : undefined
    }, { status: 500 });
  }
}

// Supporter les requêtes GET et POST
export async function GET(request: NextRequest) {
  return initDatabase(request);
}

export async function POST(request: NextRequest) {
  return initDatabase(request);
}
