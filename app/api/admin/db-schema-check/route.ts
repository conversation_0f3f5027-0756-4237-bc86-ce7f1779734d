import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { sql } from "drizzle-orm";

/**
 * API Admin pour vérifier la structure de la table users
 * Vérifie si toutes les colonnes Epic Social V1 sont présentes
 */
export async function GET(request: NextRequest) {
  try {
    console.log("🔍 Vérification du schéma de la table users...");

    // Récupérer la structure de la table users
    const tableInfo = await db.execute(sql`
      SELECT column_name, data_type, is_nullable, column_default
      FROM information_schema.columns 
      WHERE table_name = 'users' 
      AND table_schema = 'public'
      ORDER BY ordinal_position;
    `);

    // Colonnes Epic Social V1 attendues (basées sur la vraie migration)
    const epicSocialColumns = [
      'profile_visibility',
      'share_recommendations', 
      'share_wishlist',
      'share_collection',
      'email_on_new_follower' // Ajoutée manuellement car utilisée dans le code auth
    ];

    // Vérifier quelles colonnes Epic Social V1 sont présentes
    const existingColumns = tableInfo.map((row: any) => row.column_name);
    const missingColumns = epicSocialColumns.filter(col => !existingColumns.includes(col));
    const presentColumns = epicSocialColumns.filter(col => existingColumns.includes(col));

    // Informations sur l'environnement
    const environment = process.env.VERCEL_ENV || process.env.NODE_ENV || 'unknown';
    const databaseUrl = process.env.DATABASE_URL ? 'configured' : 'missing';

    const diagnostics = {
      environment,
      databaseUrl,
      tableExists: tableInfo.length > 0,
      totalColumns: tableInfo.length,
      allColumns: tableInfo.map((row: any) => ({
        name: row.column_name,
        type: row.data_type,
        nullable: row.is_nullable === 'YES',
        default: row.column_default
      })),
      epicSocialStatus: {
        requiredColumns: epicSocialColumns,
        presentColumns,
        missingColumns,
        allPresent: missingColumns.length === 0,
        migrationNeeded: missingColumns.length > 0
      },
      recommendations: [] as string[],
      timestamp: new Date().toISOString()
    };

    // Générer des recommandations
    if (missingColumns.length > 0) {
      diagnostics.recommendations.push(
        `❌ MIGRATIONS MANQUANTES: ${missingColumns.length} colonnes Epic Social V1 manquantes`
      );
      diagnostics.recommendations.push(
        `🔧 ACTION REQUISE: Appliquer la migration add-epic-social-v1-schema.sql`
      );
      missingColumns.forEach(col => {
        diagnostics.recommendations.push(`   - Colonne manquante: ${col}`);
      });
    } else {
      diagnostics.recommendations.push(
        `✅ MIGRATIONS OK: Toutes les colonnes Epic Social V1 sont présentes`
      );
    }

    // Vérifier les autres colonnes importantes
    const importantColumns = ['publicListId', 'publicListEnabled', 'emailFrequency'];
    const missingImportantCols = importantColumns.filter(col => !existingColumns.includes(col));
    
    if (missingImportantCols.length > 0) {
      diagnostics.recommendations.push(
        `⚠️ COLONNES IMPORTANTES MANQUANTES: ${missingImportantCols.join(', ')}`
      );
    }

    console.log(`✅ Vérification terminée - ${missingColumns.length} colonnes manquantes`);

    return NextResponse.json(diagnostics);

  } catch (error) {
    console.error("❌ Erreur lors de la vérification du schéma:", error);
    return NextResponse.json(
      { 
        error: "Erreur lors de la vérification du schéma", 
        details: error instanceof Error ? error.message : String(error),
        environment: process.env.VERCEL_ENV || process.env.NODE_ENV || 'unknown'
      },
      { status: 500 }
    );
  }
} 