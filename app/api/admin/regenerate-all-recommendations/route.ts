import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { users, accounts } from "@/lib/db/schema";
import { eq } from "drizzle-orm";
import { generateRecommendationsForUser } from "@/app/actions/user";

/**
 * API d'administration pour régénérer les recommandations de tous les utilisateurs
 * avec enrichissement Rakuten automatique
 */
export async function POST(request: NextRequest) {
  try {
    // Vérifier l'authentification admin (secret)
    const authHeader = request.headers.get("authorization");
    const expectedAuth = `Bearer ${process.env.CRON_SECRET}`;
    
    if (authHeader !== expectedAuth) {
      return NextResponse.json(
        { error: "Non autorisé" },
        { status: 401 }
      );
    }

    if (process.env.NODE_ENV === 'development') {
    console.log("🔄 Régénération de toutes les recommandations avec enrichissement Rakuten");
    }

    // Récupérer tous les utilisateurs avec un compte Spotify
    const usersWithSpotify = await db
      .select({
        userId: users.id,
        userName: users.name,
        userEmail: users.email
      })
      .from(users)
      .innerJoin(accounts, eq(accounts.userId, users.id))
      .where(eq(accounts.provider, "spotify"));

    if (process.env.NODE_ENV === 'development') {
    console.log(`👥 ${usersWithSpotify.length} utilisateurs avec compte Spotify trouvés`);
    }

    if (usersWithSpotify.length === 0) {
      return NextResponse.json({
        success: true,
        message: "Aucun utilisateur avec compte Spotify trouvé",
        data: {
          usersProcessed: 0,
          results: []
        }
      });
    }

    // Traiter chaque utilisateur
    const results = [];
    let successCount = 0;
    let errorCount = 0;

    for (const user of usersWithSpotify) {
      const startTime = Date.now();
      
      try {
        if (process.env.NODE_ENV === 'development') {
        console.log(`🔄 Traitement utilisateur: ${user.userName || user.userEmail} (${user.userId})`);
        }
        
        const result = await generateRecommendationsForUser(user.userId);
        const duration = Date.now() - startTime;

        if (result.status === "success") {
          successCount++;
          results.push({
            userId: user.userId,
            userName: user.userName,
            userEmail: user.userEmail,
            status: "success",
            recommendationsCount: result.recommendationsCount,
            duration: `${duration}ms`
          });
          
          if (process.env.NODE_ENV === 'development') {
          if (process.env.NODE_ENV === 'development') {
          if (process.env.NODE_ENV === 'development') {
          console.log(`✅ ${user.userName || user.userEmail}: ${result.recommendationsCount} recommandations générées en ${duration}ms`);
          }
          }
          }
        } else {
          errorCount++;
          results.push({
            userId: user.userId,
            userName: user.userName,
            userEmail: user.userEmail,
            status: "error",
            error: result.status || "Erreur inconnue",
            duration: `${duration}ms`
          });

          if (process.env.NODE_ENV === 'development') {
          console.log(`❌ ${user.userName || user.userEmail}: Erreur - ${result.status}`);
          }
        }

        // Délai entre les utilisateurs pour éviter la surcharge
        if (usersWithSpotify.indexOf(user) < usersWithSpotify.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 2000)); // 2 secondes
        }

      } catch (error) {
        errorCount++;
        const duration = Date.now() - startTime;
        
        results.push({
          userId: user.userId,
          userName: user.userName,
          userEmail: user.userEmail,
          status: "error",
          error: error instanceof Error ? error.message : "Erreur inconnue",
          duration: `${duration}ms`
        });
        
        console.error(`❌ ${user.userName || user.userEmail}: Exception -`, error);
      }
    }

    const totalDuration = results.reduce((sum, result) => {
      const duration = parseInt(result.duration.replace('ms', ''));
      return sum + duration;
    }, 0);

    if (process.env.NODE_ENV === 'development') {
    console.log(`🎯 Régénération terminée: ${successCount} succès, ${errorCount} erreurs sur ${usersWithSpotify.length} utilisateurs`);
    }

    return NextResponse.json({
      success: true,
      message: `Régénération terminée: ${successCount} succès, ${errorCount} erreurs`,
      data: {
        usersProcessed: usersWithSpotify.length,
        successCount,
        errorCount,
        totalDuration: `${totalDuration}ms`,
        averageDuration: `${Math.round(totalDuration / usersWithSpotify.length)}ms`,
        results: results.map(r => ({
          ...r,
          // Masquer les emails pour la sécurité
          userEmail: r.userEmail ? r.userEmail.substring(0, 3) + "***" : null
        }))
      }
    });

  } catch (error) {
    console.error("💥 Erreur lors de la régénération globale:", error);
    
    return NextResponse.json(
      {
        success: false,
        error: "Erreur lors de la régénération globale",
        message: error instanceof Error ? error.message : "Unknown error"
      },
      { status: 500 }
    );
  }
}
