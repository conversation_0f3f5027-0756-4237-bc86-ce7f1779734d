import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { sql } from "drizzle-orm";

/**
 * API Admin pour appliquer la migration Epic Social V1
 * ⚠️ USAGE: Uniquement sur staging/production avec précautions
 */
export async function POST(request: NextRequest) {
  try {
    const environment = process.env.VERCEL_ENV || process.env.NODE_ENV || 'unknown';
    
    console.log(`🚀 Application migration Epic Social V1 sur ${environment}...`);

    // Sécurité : vérifier l'environnement
    if (environment === 'production') {
      return NextResponse.json(
        { error: "Migration automatique désactivée en production pour sécurité" },
        { status: 403 }
      );
    }

    const results = [];

    // 1. Créer les ENUMs
    try {
      await db.execute(sql`
        DO $$ BEGIN
          CREATE TYPE profile_visibility AS ENUM ('private', 'users_only', 'public');
        EXCEPTION
          WHEN duplicate_object THEN null;
        END $$;
      `);
      results.push("✅ ENUM profile_visibility créé/vérifié");
    } catch (error) {
      results.push(`❌ Erreur ENUM profile_visibility: ${error}`);
    }

    try {
      await db.execute(sql`
        DO $$ BEGIN
          CREATE TYPE notification_type AS ENUM ('new_follower');
        EXCEPTION
          WHEN duplicate_object THEN null;
        END $$;
      `);
      results.push("✅ ENUM notification_type créé/vérifié");
    } catch (error) {
      results.push(`❌ Erreur ENUM notification_type: ${error}`);
    }

    // 2. Ajouter les colonnes à la table users
    const userColumns = [
      { name: 'profile_visibility', sql: `ALTER TABLE users ADD COLUMN IF NOT EXISTS "profile_visibility" profile_visibility NOT NULL DEFAULT 'users_only';` },
      { name: 'share_recommendations', sql: `ALTER TABLE users ADD COLUMN IF NOT EXISTS "share_recommendations" BOOLEAN NOT NULL DEFAULT TRUE;` },
      { name: 'share_wishlist', sql: `ALTER TABLE users ADD COLUMN IF NOT EXISTS "share_wishlist" BOOLEAN NOT NULL DEFAULT TRUE;` },
      { name: 'share_collection', sql: `ALTER TABLE users ADD COLUMN IF NOT EXISTS "share_collection" BOOLEAN NOT NULL DEFAULT TRUE;` }
    ];

    for (const column of userColumns) {
      try {
        await db.execute(sql.raw(column.sql));
        results.push(`✅ Colonne users.${column.name} ajoutée`);
      } catch (error) {
        results.push(`❌ Erreur colonne ${column.name}: ${error}`);
      }
    }

    // 3. Créer la table followers
    try {
      await db.execute(sql`
        CREATE TABLE IF NOT EXISTS followers (
          follower_id TEXT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
          following_id TEXT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
          created_at TIMESTAMP DEFAULT NOW() NOT NULL,
          PRIMARY KEY (follower_id, following_id)
        );
      `);
      results.push("✅ Table followers créée");
    } catch (error) {
      results.push(`❌ Erreur table followers: ${error}`);
    }

    // 4. Créer la table notifications
    try {
      await db.execute(sql`
        CREATE TABLE IF NOT EXISTS notifications (
          id TEXT PRIMARY KEY DEFAULT gen_random_uuid(),
          recipient_id TEXT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
          actor_id TEXT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
          type notification_type NOT NULL,
          is_read BOOLEAN DEFAULT FALSE NOT NULL,
          created_at TIMESTAMP DEFAULT NOW() NOT NULL
        );
      `);
      results.push("✅ Table notifications créée");
    } catch (error) {
      results.push(`❌ Erreur table notifications: ${error}`);
    }

    // 5. Ajouter la colonne emailOnNewFollower (manquante dans la migration originale)
    try {
      await db.execute(sql`
        ALTER TABLE users ADD COLUMN IF NOT EXISTS email_on_new_follower BOOLEAN NOT NULL DEFAULT TRUE;
      `);
      results.push("✅ Colonne users.email_on_new_follower ajoutée");
    } catch (error) {
      results.push(`❌ Erreur colonne email_on_new_follower: ${error}`);
    }

    // 6. Créer les index principaux
    const indexes = [
      `CREATE INDEX IF NOT EXISTS idx_users_profile_visibility ON users(profile_visibility);`,
      `CREATE INDEX IF NOT EXISTS idx_users_share_recommendations ON users(share_recommendations);`,
      `CREATE INDEX IF NOT EXISTS idx_followers_follower_id ON followers(follower_id);`,
      `CREATE INDEX IF NOT EXISTS idx_followers_following_id ON followers(following_id);`,
      `CREATE INDEX IF NOT EXISTS idx_notifications_recipient_id ON notifications(recipient_id);`,
      `CREATE INDEX IF NOT EXISTS idx_notifications_is_read ON notifications(is_read);`
    ];

    for (const indexSql of indexes) {
      try {
        await db.execute(sql.raw(indexSql));
        results.push(`✅ Index créé`);
      } catch (error) {
        results.push(`❌ Erreur index: ${error}`);
      }
    }

    console.log(`✅ Migration Epic Social V1 appliquée sur ${environment}`);

    return NextResponse.json({
      success: true,
      environment,
      message: "Migration Epic Social V1 appliquée avec succès",
      results,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error("❌ Erreur lors de l'application de la migration:", error);
    return NextResponse.json(
      { 
        error: "Erreur lors de l'application de la migration", 
        details: error instanceof Error ? error.message : String(error),
        environment: process.env.VERCEL_ENV || process.env.NODE_ENV || 'unknown'
      },
      { status: 500 }
    );
  }
} 