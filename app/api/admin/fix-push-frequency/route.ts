import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { sql } from "drizzle-orm";

/**
 * Route pour corriger spécifiquement la colonne pushFrequency manquante
 */
export async function GET(request: NextRequest) {
  try {
    if (process.env.NODE_ENV === 'development') {
    console.log("🔧 Correction de la colonne pushFrequency...");
    }

    // Vérifier si la colonne existe
    const columns = await db.execute(sql`
      SELECT column_name
      FROM information_schema.columns 
      WHERE table_name = 'users' 
      AND table_schema = 'public'
      AND column_name = 'pushFrequency'
    `);

    if (columns.length === 0) {
      if (process.env.NODE_ENV === 'development') {
      console.log("➕ Ajout de la colonne pushFrequency...");
      }
      
      // Ajouter la colonne manquante
      await db.execute(sql`
        ALTER TABLE users 
        ADD COLUMN "pushFrequency" TEXT DEFAULT 'weekly' NOT NULL
      `);
      
      if (process.env.NODE_ENV === 'development') {
      console.log("✅ Colonne pushFrequency ajoutée avec succès");
      }
      
      return NextResponse.json({
        success: true,
        message: "Colonne pushFrequency ajoutée avec succès",
        action: "Colonne ajoutée",
        timestamp: new Date().toISOString()
      });
    } else {
      return NextResponse.json({
        success: true,
        message: "Colonne pushFrequency existe déjà",
        action: "Aucune action nécessaire",
        timestamp: new Date().toISOString()
      });
    }

  } catch (error) {
    console.error("❌ Erreur lors de la correction:", error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : "Erreur inconnue",
      stack: error instanceof Error ? error.stack : undefined
    }, { status: 500 });
  }
}
