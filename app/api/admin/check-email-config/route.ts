import { NextRequest, NextResponse } from "next/server";

/**
 * API Admin pour vérifier la configuration email
 * Vérifie les variables d'environnement et les adresses utilisées
 */
export async function GET(request: NextRequest) {
  try {
    console.log("🔍 Vérification configuration email...");

    const environment = process.env.VERCEL_ENV || process.env.NODE_ENV || 'unknown';
    
    // Vérifier les variables d'environnement
    const emailConfig = {
      environment,
      timestamp: new Date().toISOString(),
      variables: {
        RESEND_API_KEY: process.env.RESEND_API_KEY ? `${process.env.RESEND_API_KEY.substring(0, 8)}***` : 'NOT_SET',
        RESEND_FROM_EMAIL: process.env.RESEND_FROM_EMAIL || 'NOT_SET',
        RESEND_REPLY_TO: process.env.RESEND_REPLY_TO || 'NOT_SET',
      },
      defaultConfig: {
        fromDefault: 'Stream2Spin <<EMAIL>>',
        replyToDefault: '<EMAIL>',
      },
      actualConfig: {
        from: process.env.RESEND_FROM_EMAIL || 'Stream2Spin <<EMAIL>>',
        replyTo: process.env.RESEND_REPLY_TO || '<EMAIL>',
      },
      validation: {
        hasApiKey: !!process.env.RESEND_API_KEY,
        apiKeyValid: process.env.RESEND_API_KEY?.startsWith('re_') || false,
        fromEmailSet: !!process.env.RESEND_FROM_EMAIL,
        usesVerifiedDomain: false, // sera calculé ci-dessous
      },
      recommendations: [] as string[]
    };

    // Vérifier si on utilise le bon domaine
    const fromEmail = emailConfig.actualConfig.from;
    if (fromEmail.includes('mails.stream2spin.com')) {
      emailConfig.validation.usesVerifiedDomain = true;
      emailConfig.recommendations.push('✅ Utilise le domaine vérifié mails.stream2spin.com');
    } else if (fromEmail.includes('stream2spin.com')) {
      emailConfig.recommendations.push('⚠️ Utilise stream2spin.com - vérifier si ce domaine est configuré chez Resend');
    } else if (fromEmail.includes('resend.dev')) {
      emailConfig.recommendations.push('🧪 Utilise le domaine de test resend.dev (normal en développement)');
    } else {
      emailConfig.recommendations.push('❌ Utilise un domaine non reconnu - vérifier la configuration');
    }

    // Vérifications additionnelles
    if (!emailConfig.validation.hasApiKey) {
      emailConfig.recommendations.push('❌ RESEND_API_KEY manquante');
    } else if (!emailConfig.validation.apiKeyValid) {
      emailConfig.recommendations.push('❌ RESEND_API_KEY invalide (doit commencer par re_)');
    } else {
      emailConfig.recommendations.push('✅ RESEND_API_KEY configurée correctement');
    }

    if (!emailConfig.validation.fromEmailSet) {
      emailConfig.recommendations.push('ℹ️ RESEND_FROM_EMAIL non définie - utilise la valeur par défaut');
    } else {
      emailConfig.recommendations.push('✅ RESEND_FROM_EMAIL définie en variable d\'environnement');
    }

    // Test de la fonction de vérification existante
    try {
      const { checkWelcomeEmailConfiguration } = await import('@/lib/email');
      const configCheck = checkWelcomeEmailConfiguration();
      emailConfig.recommendations.push(`📧 Test configuration: ${configCheck.message}`);
    } catch (error) {
      emailConfig.recommendations.push(`❌ Erreur test configuration: ${error}`);
    }

    console.log(`✅ Vérification email terminée - utilise: ${fromEmail}`);

    return NextResponse.json(emailConfig);

  } catch (error) {
    console.error("❌ Erreur lors de la vérification email:", error);
    return NextResponse.json(
      { 
        error: "Erreur lors de la vérification email", 
        details: error instanceof Error ? error.message : String(error),
        environment: process.env.VERCEL_ENV || process.env.NODE_ENV || 'unknown'
      },
      { status: 500 }
    );
  }
} 