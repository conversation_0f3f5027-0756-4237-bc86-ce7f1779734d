import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { accounts } from "@/lib/db/schema";
import { eq, and, sql } from "drizzle-orm";

/**
 * API pour exécuter la migration des tokens Discogs
 */
export async function POST(request: NextRequest) {
  try {
    // Vérification de sécurité
    const authHeader = request.headers.get("authorization");
    const expectedSecret = `Bearer ${process.env.CRON_SECRET}`;
    
    if (!authHeader || authHeader !== expectedSecret) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    if (process.env.NODE_ENV === 'development') {
    console.log("🔄 Début de la migration des tokens Discogs...");
    }

    // 1. Ajouter la colonne access_token_secret si elle n'existe pas
    if (process.env.NODE_ENV === 'development') {
    console.log("📝 Ajout de la colonne access_token_secret...");
    }
    await db.execute(sql`
      ALTER TABLE accounts 
      ADD COLUMN IF NOT EXISTS access_token_secret TEXT
    `);

    // 2. Vérifier les comptes Discogs existants
    const discogsAccounts = await db.query.accounts.findMany({
      where: eq(accounts.provider, "discogs"),
    });

    if (process.env.NODE_ENV === 'development') {
    if (process.env.NODE_ENV === 'development') {
    if (process.env.NODE_ENV === 'development') {
    console.log(`🔍 ${discogsAccounts.length} comptes Discogs trouvés`);
    }
    }
    }

    // 3. Migrer les données si nécessaire
    let migratedCount = 0;
    const migrationDetails = [];

    for (const account of discogsAccounts) {
      // Si access_token_secret est vide mais qu'on a des données dans session_state
      if (!account.access_token_secret && account.session_state) {
        await db.update(accounts)
          .set({ access_token_secret: account.session_state })
          .where(and(
            eq(accounts.userId, account.userId),
            eq(accounts.provider, "discogs")
          ));
        migratedCount++;
        migrationDetails.push({
          userId: account.userId,
          action: "migrated_token_secret"
        });
        if (process.env.NODE_ENV === 'development') {
        if (process.env.NODE_ENV === 'development') {
        if (process.env.NODE_ENV === 'development') {
        console.log(`✅ Migré le token pour l'utilisateur ${account.userId}`);
        }
        }
        }
      }
    }

    // 4. Nettoyer les comptes temporaires
    const tempAccounts = await db.query.accounts.findMany({
      where: eq(accounts.provider, "discogs_temp"),
    });

    if (tempAccounts.length > 0) {
      await db.delete(accounts).where(eq(accounts.provider, "discogs_temp"));
      if (process.env.NODE_ENV === 'development') {
      console.log(`🗑️ ${tempAccounts.length} comptes temporaires supprimés`);
      }
    }

    // 5. Vérifier le résultat
    const updatedAccounts = await db.query.accounts.findMany({
      where: eq(accounts.provider, "discogs"),
    });

    const validAccounts = updatedAccounts.filter(acc => 
      acc.access_token && acc.access_token_secret
    );

    const invalidAccounts = updatedAccounts.filter(acc => 
      !acc.access_token || !acc.access_token_secret
    );

    if (process.env.NODE_ENV === 'development') {
    if (process.env.NODE_ENV === 'development') {
    if (process.env.NODE_ENV === 'development') {
    console.log(`✅ Migration terminée:`);
    }
    }
    }
    if (process.env.NODE_ENV === 'development') {
    console.log(`   - ${migratedCount} comptes migrés`);
    }
    console.log(`   - ${validAccounts.length}/${updatedAccounts.length} comptes Discogs valides`);
    if (process.env.NODE_ENV === 'development') {
    console.log(`   - ${tempAccounts.length} comptes temporaires supprimés`);
    }

    return NextResponse.json({
      success: true,
      message: "Migration des tokens Discogs terminée",
      data: {
        migratedCount,
        validAccounts: validAccounts.length,
        invalidAccounts: invalidAccounts.length,
        totalAccounts: updatedAccounts.length,
        tempAccountsRemoved: tempAccounts.length,
        migrationDetails,
        invalidAccountsDetails: invalidAccounts.map(acc => ({
          userId: acc.userId,
          hasAccessToken: !!acc.access_token,
          hasAccessTokenSecret: !!acc.access_token_secret,
          hasSessionState: !!acc.session_state
        }))
      }
    });

  } catch (error) {
    console.error("❌ Erreur lors de la migration:", error);
    
    return NextResponse.json(
      {
        success: false,
        error: "Erreur lors de la migration",
        message: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}

/**
 * Vérifier l'état des tokens Discogs
 */
export async function GET(request: NextRequest) {
  try {
    // Vérification de sécurité
    const authHeader = request.headers.get("authorization");
    const expectedSecret = `Bearer ${process.env.CRON_SECRET}`;
    
    if (!authHeader || authHeader !== expectedSecret) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Vérifier les comptes Discogs
    const discogsAccounts = await db.query.accounts.findMany({
      where: eq(accounts.provider, "discogs"),
    });

    const validAccounts = discogsAccounts.filter(acc => 
      acc.access_token && acc.access_token_secret
    );

    const invalidAccounts = discogsAccounts.filter(acc => 
      !acc.access_token || !acc.access_token_secret
    );

    // Vérifier les comptes temporaires
    const tempAccounts = await db.query.accounts.findMany({
      where: eq(accounts.provider, "discogs_temp"),
    });

    return NextResponse.json({
      success: true,
      data: {
        totalAccounts: discogsAccounts.length,
        validAccounts: validAccounts.length,
        invalidAccounts: invalidAccounts.length,
        tempAccounts: tempAccounts.length,
        accounts: discogsAccounts.map(acc => ({
          userId: acc.userId,
          hasAccessToken: !!acc.access_token,
          hasAccessTokenSecret: !!acc.access_token_secret,
          hasSessionState: !!acc.session_state,
          isValid: !!(acc.access_token && acc.access_token_secret)
        }))
      }
    });

  } catch (error) {
    console.error("❌ Erreur lors de la vérification:", error);
    
    return NextResponse.json(
      {
        success: false,
        error: "Erreur lors de la vérification",
        message: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}
