import { NextRequest, NextResponse } from 'next/server';
import { sendWelcomeEmail } from '@/lib/email';
import { sendRecommendationsEmail } from '@/lib/resend';
import { readFileSync } from 'fs';
import { join } from 'path';

/**
 * API Route pour envoyer des emails de test avec les vrais templates
 */
export async function POST(request: NextRequest) {
  try {
    // Vérification de sécurité basique
    const authHeader = request.headers.get("authorization");
    const expectedSecret = `Bearer ${process.env.CRON_SECRET || 'test-secret'}`;
    
    if (!authHeader || authHeader !== expectedSecret) {
      return NextResponse.json(
        { error: "Accès non autorisé" },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { email, name, language = 'fr' } = body;

    if (!email || !name) {
      return NextResponse.json(
        { error: "Email et nom requis" },
        { status: 400 }
      );
    }

    console.log(`📧 Envoi des emails de test à ${email} (${name}) en ${language}`);

    // Charger les messages de traduction (import direct pour Vercel)
    let messages: any;
    try {
      if (language === 'en') {
        const module = await import('@/messages/en.json');
        messages = module.default || module;
      } else {
        const module = await import('@/messages/fr.json');
        messages = module.default || module;
      }
    } catch (error) {
      console.warn(`⚠️ Impossible de charger les traductions pour ${language}, utilisation du français par défaut`);
      const module = await import('@/messages/fr.json');
      messages = module.default || module;
    }

    // Créer la fonction de traduction
    function t(key: string, values: Record<string, any> = {}): string {
      const keys = key.split('.');
      let translation: any = messages;
      
      for (const k of keys) {
        if (translation && typeof translation === 'object') {
          translation = translation[k];
        } else {
          return key;
        }
      }
      
      if (typeof translation !== 'string') {
        return key;
      }
      
      // Remplacer les variables simples {name}
      let result = translation;
      for (const [varName, varValue] of Object.entries(values)) {
        result = result.replace(new RegExp(`{${varName}}`, 'g'), String(varValue));
      }
      
      // Gestion simplifiée des pluriels pour totalCount
      if (result.includes('plural')) {
        const totalCount = values.totalCount || 1;
        if (totalCount === 1) {
          result = result.replace(/{totalCount, plural, =1 \{([^}]+)\} other \{([^}]+)\}}/g, '$1');
        } else {
          result = result.replace(/{totalCount, plural, =1 \{([^}]+)\} other \{([^}]+)\}}/g, '$2');
        }
        result = result.replace(/{totalCount}/g, String(totalCount));
      }
      
      return result;
    }

    const results = {
      welcome: false,
      recommendations: false,
      details: {
        welcome: null as any,
        recommendations: null as any
      }
    };

    // 1. Envoyer l'email de bienvenue avec le vrai template localisé
    console.log('📬 Envoi de l\'email de bienvenue...');
    try {
      // Créer une version localisée de l'email de bienvenue
      const { render } = await import('@react-email/render');
      const { WelcomeEmail } = await import('@/emails/welcome-email');
      const { Resend } = await import('resend');
      
      const resend = new Resend(process.env.RESEND_API_KEY);
      
      const emailHtml = await render(
        WelcomeEmail({
          name,
          userEmail: email,
          t
        })
      );
      
      const subject = t('emails.welcome.preview');
      
      const welcomeResult = await resend.emails.send({
        from: process.env.RESEND_FROM_EMAIL || 'Stream2Spin <<EMAIL>>',
        to: email,
        subject: `[${language.toUpperCase()}] ${subject}`,
        html: emailHtml,
        tags: [
          { name: 'type', value: 'welcome' },
          { name: 'language', value: language },
          { name: 'test', value: 'multilang' }
        ]
      });

      if (welcomeResult.error) {
        console.log(`❌ Erreur email de bienvenue: ${welcomeResult.error.message}`);
        results.details.welcome = {
          success: false,
          error: welcomeResult.error.message
        };
      } else {
        console.log(`✅ Email de bienvenue envoyé ! ID: ${welcomeResult.data?.id}`);
        results.welcome = true;
        results.details.welcome = {
          success: true,
          messageId: welcomeResult.data?.id
        };
      }
    } catch (error) {
      console.error('❌ Erreur lors de l\'envoi de l\'email de bienvenue:', error);
      results.details.welcome = {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }

    // Attendre un peu entre les envois
    await new Promise(resolve => setTimeout(resolve, 2000));

    // 2. Envoyer l'email de recommandations avec le vrai template
    console.log('📬 Envoi de l\'email de recommandations...');
    try {
      // Données de test pour les recommandations
      const testUser = {
        id: 'test-user-123',
        name,
        email,
        preferredLanguage: language
      };

      // Recommandations de test avec des données réalistes
      const testRecommendations = [
        {
          artistName: 'Daft Punk',
          albumTitle: 'Random Access Memories',
          albumCoverUrl: 'https://i.scdn.co/image/ab67616d0000b273da54d1a8b11e1b7f4f13e8a2',
          listenScore: 85,
          affiliateLinks: [
            {
              vendor: 'Amazon',
              url: 'https://amazon.fr/daft-punk-ram',
              price: 29.99,
              currency: 'EUR'
            }
          ],
          isOwned: false
        },
        {
          artistName: 'Radiohead',
          albumTitle: 'OK Computer',
          albumCoverUrl: 'https://i.scdn.co/image/ab67616d0000b273c8b444df094279e70d0ed856',
          listenScore: 92,
          affiliateLinks: [
            {
              vendor: 'Amazon',
              url: 'https://amazon.fr/radiohead-ok-computer',
              price: 24.99,
              currency: 'EUR'
            }
          ],
          isOwned: false
        },
        {
          artistName: 'Pink Floyd',
          albumTitle: 'The Dark Side of the Moon',
          albumCoverUrl: 'https://i.scdn.co/image/ab67616d0000b273ea7caaff71dea1051d49b2fe',
          listenScore: 88,
          affiliateLinks: [
            {
              vendor: 'Amazon',
              url: 'https://amazon.fr/pink-floyd-dark-side',
              price: 32.99,
              currency: 'EUR'
            }
          ],
          isOwned: false
        }
      ];

      // Créer une version localisée de l'email de recommandations
      const { RecommendationsEmailTemplate } = await import('@/emails/recommendations-email');
      
      const baseUrl = process.env.NEXTAUTH_URL || 'http://localhost:3000';
      const unsubscribeUrl = `${baseUrl}/account?tab=notifications`;
      const viewRecommendationsUrl = `${baseUrl}/recommendations`;
      
      const { render } = await import('@react-email/render');
      const recommendationsEmailHtml = await render(
        RecommendationsEmailTemplate({
          user: testUser,
          recommendations: testRecommendations,
          totalCount: testRecommendations.length,
          unsubscribeUrl,
          viewRecommendationsUrl,
          t
        })
      );
      
      const recommendationsSubject = t('emails.recommendations.preview', { totalCount: testRecommendations.length });
      
      const { Resend } = await import('resend');
      const resendClient = new Resend(process.env.RESEND_API_KEY);
      const recommendationsResult = await resendClient.emails.send({
        from: process.env.RESEND_FROM_EMAIL || 'Stream2Spin <<EMAIL>>',
        to: email,
        subject: `[${language.toUpperCase()}] ${recommendationsSubject}`,
        html: recommendationsEmailHtml,
        tags: [
          { name: 'type', value: 'recommendations' },
          { name: 'language', value: language },
          { name: 'test', value: 'multilang' },
          { name: 'count', value: testRecommendations.length.toString() }
        ]
      });

      if (recommendationsResult.error) {
        console.log(`❌ Erreur email de recommandations: ${recommendationsResult.error.message}`);
        results.details.recommendations = {
          success: false,
          error: recommendationsResult.error.message
        };
      } else {
        console.log(`✅ Email de recommandations envoyé ! ID: ${recommendationsResult.data?.id}`);
        results.recommendations = true;
        results.details.recommendations = {
          success: true,
          messageId: recommendationsResult.data?.id,
          recommendationsCount: testRecommendations.length
        };
      }
    } catch (error) {
      console.error('❌ Erreur lors de l\'envoi de l\'email de recommandations:', error);
      results.details.recommendations = {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }

    const totalSuccess = Object.values(results).filter(Boolean).length - 1; // -1 pour exclure details
    const totalEmails = 2;

    console.log(`📊 Résumé: ${totalSuccess}/${totalEmails} emails envoyés avec succès`);

    return NextResponse.json({
      success: totalSuccess === totalEmails,
      message: `${totalSuccess}/${totalEmails} emails envoyés avec succès`,
      results,
      email,
      name
    });

  } catch (error) {
    console.error('💥 Erreur critique lors de l\'envoi des emails de test:', error);
    
    return NextResponse.json(
      {
        success: false,
        error: "Erreur interne du serveur",
        message: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}

/**
 * GET - Informations sur l'endpoint
 */
export async function GET() {
  return NextResponse.json({
    endpoint: '/api/admin/send-test-emails',
    method: 'POST',
    description: 'Envoie des emails de test avec les vrais templates React',
    parameters: {
      email: 'string (required) - Email de destination',
      name: 'string (required) - Nom du destinataire'
    },
    headers: {
      authorization: 'Bearer TOKEN - Token d\'authentification'
    }
  });
} 