import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { users, accounts, recommendations, userDiscogsCollection } from "@/lib/db/schema";
import { sql, eq, isNull } from "drizzle-orm";

/**
 * API Route pour nettoyer les données orphelines en base de données
 * Supprime les enregistrements qui référencent des utilisateurs qui n'existent plus
 */
export async function POST(request: NextRequest) {
  try {
    if (process.env.NODE_ENV === 'development') {
    console.log("🧹 Début du nettoyage des données orphelines");
    }

    // Récupérer tous les IDs d'utilisateurs existants
    const existingUsers = await db.select({ id: users.id }).from(users);
    const existingUserIds = existingUsers.map(u => u.id);

    if (process.env.NODE_ENV === 'development') {
    console.log(`👥 ${existingUserIds.length} utilisateurs trouvés en base`);
    }

    // Analyser les données orphelines
    const allDiscogs = await db.select({
      id: userDiscogsCollection.id,
      userId: userDiscogsCollection.userId
    }).from(userDiscogsCollection);

    const allRecommendations = await db.select({
      id: recommendations.id,
      userId: recommendations.userId
    }).from(recommendations);

    const allAccounts = await db.select({
      userId: accounts.userId
    }).from(accounts);

    // Identifier les orphelins
    const orphanedDiscogs = allDiscogs.filter(d => !existingUserIds.includes(d.userId));
    const orphanedRecommendations = allRecommendations.filter(r => !existingUserIds.includes(r.userId));
    const orphanedAccounts = allAccounts.filter(a => !existingUserIds.includes(a.userId));

    if (process.env.NODE_ENV === 'development') {
    if (process.env.NODE_ENV === 'development') {
    if (process.env.NODE_ENV === 'development') {
    console.log(`🔍 Données orphelines trouvées: ${orphanedDiscogs.length} Discogs, ${orphanedRecommendations.length} recommandations, ${orphanedAccounts.length} comptes`);
    }
    }
    }

    // 4. Supprimer les données orphelines
    let cleanupResults = {
      discogsDeleted: 0,
      recommendationsDeleted: 0,
      accountsDeleted: 0
    };

    // Supprimer les collections Discogs orphelines
    if (orphanedDiscogs.length > 0) {
      const orphanedDiscogsIds = orphanedDiscogs.map(d => d.id);
      for (const id of orphanedDiscogsIds) {
        await db.delete(userDiscogsCollection).where(eq(userDiscogsCollection.id, id));
        cleanupResults.discogsDeleted++;
      }
      if (process.env.NODE_ENV === 'development') {
      if (process.env.NODE_ENV === 'development') {
      if (process.env.NODE_ENV === 'development') {
      console.log(`✅ ${cleanupResults.discogsDeleted} enregistrements Discogs orphelins supprimés`);
      }
      }
      }
    }

    // Supprimer les recommandations orphelines
    if (orphanedRecommendations.length > 0) {
      const orphanedRecommendationsIds = orphanedRecommendations.map(r => r.id);
      for (const id of orphanedRecommendationsIds) {
        await db.delete(recommendations).where(eq(recommendations.id, id));
        cleanupResults.recommendationsDeleted++;
      }
      if (process.env.NODE_ENV === 'development') {
      if (process.env.NODE_ENV === 'development') {
      if (process.env.NODE_ENV === 'development') {
      console.log(`✅ ${cleanupResults.recommendationsDeleted} recommandations orphelines supprimées`);
      }
      }
      }
    }

    // Supprimer les comptes orphelins
    if (orphanedAccounts.length > 0) {
      const orphanedAccountsUserIds = [...new Set(orphanedAccounts.map(a => a.userId))];
      for (const userId of orphanedAccountsUserIds) {
        await db.delete(accounts).where(eq(accounts.userId, userId));
        cleanupResults.accountsDeleted++;
      }
      if (process.env.NODE_ENV === 'development') {
      if (process.env.NODE_ENV === 'development') {
      if (process.env.NODE_ENV === 'development') {
      console.log(`✅ ${cleanupResults.accountsDeleted} comptes orphelins supprimés`);
      }
      }
      }
    }

    if (process.env.NODE_ENV === 'development') {
    console.log("🧹 Nettoyage des données orphelines terminé");
    }

    return NextResponse.json({
      success: true,
      message: "Nettoyage des données orphelines terminé",
      data: {
        orphanedDataFound: {
          discogs: orphanedDiscogs.length,
          recommendations: orphanedRecommendations.length,
          accounts: orphanedAccounts.length
        },
        cleanupResults
      }
    });

  } catch (error) {
    console.error("💥 Erreur lors du nettoyage des données orphelines:", error);
    
    return NextResponse.json(
      {
        success: false,
        error: "Erreur lors du nettoyage",
        message: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}

/**
 * GET pour analyser les données orphelines sans les supprimer
 */
export async function GET(request: NextRequest) {
  try {
    if (process.env.NODE_ENV === 'development') {
    if (process.env.NODE_ENV === 'development') {
    if (process.env.NODE_ENV === 'development') {
    console.log("🔍 Analyse des données orphelines");
    }
    }
    }

    // Récupérer tous les IDs d'utilisateurs existants
    const existingUsers = await db.select({ id: users.id }).from(users);
    const existingUserIds = existingUsers.map(u => u.id);

    if (process.env.NODE_ENV === 'development') {
    console.log(`👥 ${existingUserIds.length} utilisateurs trouvés en base`);
    }

    // Analyser les données orphelines
    const allDiscogs = await db.select({ userId: userDiscogsCollection.userId }).from(userDiscogsCollection);
    const allRecommendations = await db.select({ userId: recommendations.userId }).from(recommendations);
    const allAccounts = await db.select({ userId: accounts.userId }).from(accounts);

    // Compter les orphelins
    const orphanedDiscogs = allDiscogs.filter(d => !existingUserIds.includes(d.userId));
    const orphanedRecommendations = allRecommendations.filter(r => !existingUserIds.includes(r.userId));
    const orphanedAccounts = allAccounts.filter(a => !existingUserIds.includes(a.userId));

    const totalOrphanedDiscogs = orphanedDiscogs.length;
    const totalOrphanedRecommendations = orphanedRecommendations.length;
    const totalOrphanedAccounts = orphanedAccounts.length;

    if (process.env.NODE_ENV === 'development') {
    if (process.env.NODE_ENV === 'development') {
    if (process.env.NODE_ENV === 'development') {
    console.log(`🔍 Données orphelines trouvées: ${totalOrphanedDiscogs} Discogs, ${totalOrphanedRecommendations} recommandations, ${totalOrphanedAccounts} comptes`);
    }
    }
    }

    return NextResponse.json({
      success: true,
      message: "Analyse des données orphelines terminée",
      data: {
        orphanedData: {
          discogs: {
            totalRecords: totalOrphanedDiscogs
          },
          recommendations: {
            totalRecords: totalOrphanedRecommendations
          },
          accounts: {
            totalRecords: totalOrphanedAccounts
          }
        },
        summary: {
          hasOrphanedData: totalOrphanedDiscogs > 0 || totalOrphanedRecommendations > 0 || totalOrphanedAccounts > 0,
          totalOrphanedRecords: totalOrphanedDiscogs + totalOrphanedRecommendations + totalOrphanedAccounts
        }
      }
    });

  } catch (error) {
    console.error("💥 Erreur lors de l'analyse des données orphelines:", error);
    
    return NextResponse.json(
      {
        success: false,
        error: "Erreur lors de l'analyse",
        message: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}
