import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { sql } from "drizzle-orm";

/**
 * API Admin pour corriger la nomenclature des colonnes Epic Social V1
 * Corrige l'incohérence entre dev (snake_case) et staging (camelCase)
 */
export async function POST(request: NextRequest) {
  try {
    console.log("🔧 Correction nomenclature colonnes Epic Social V1...");

    const environment = process.env.VERCEL_ENV || process.env.NODE_ENV || 'unknown';
    
    const results = {
      environment,
      timestamp: new Date().toISOString(),
      operations: [] as any[],
      summary: {
        success: true,
        operationsCount: 0,
        errors: [] as string[]
      }
    };

    // Vérifier d'abord si la colonne problématique existe
    console.log("🔍 Vérification colonne emailOnNewFollower...");
    
    const columnCheck = await db.execute(sql`
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'users' 
      AND table_schema = 'public'
      AND column_name IN ('emailOnNewFollower', 'email_on_new_follower');
    `);

    const existingColumns = columnCheck.map((row: any) => row.column_name);
    console.log("📊 Colonnes existantes:", existingColumns);

    results.operations.push({
      operation: "column_check",
      existingColumns,
      status: "completed"
    });

    // Si emailOnNewFollower existe mais pas email_on_new_follower, on corrige
    if (existingColumns.includes('emailOnNewFollower') && !existingColumns.includes('email_on_new_follower')) {
      console.log("🔄 Renommage emailOnNewFollower → email_on_new_follower...");
      
      try {
        await db.execute(sql`
          ALTER TABLE users 
          RENAME COLUMN "emailOnNewFollower" TO "email_on_new_follower";
        `);

        results.operations.push({
          operation: "rename_column",
          from: "emailOnNewFollower",
          to: "email_on_new_follower",
          status: "success"
        });
        
        results.summary.operationsCount++;
        console.log("✅ Colonne renommée avec succès");

      } catch (error) {
        const errorMsg = `Erreur renommage colonne: ${error}`;
        console.error("❌", errorMsg);
        results.summary.errors.push(errorMsg);
        results.summary.success = false;
        
        results.operations.push({
          operation: "rename_column",
          from: "emailOnNewFollower", 
          to: "email_on_new_follower",
          status: "error",
          error: errorMsg
        });
      }
    } else if (existingColumns.includes('email_on_new_follower')) {
      console.log("✅ Colonne email_on_new_follower déjà correcte");
      results.operations.push({
        operation: "check_column",
        column: "email_on_new_follower",
        status: "already_correct"
      });
    } else {
      const errorMsg = "Aucune des colonnes emailOnNewFollower/email_on_new_follower trouvée";
      console.error("❌", errorMsg);
      results.summary.errors.push(errorMsg);
      results.summary.success = false;
      
      results.operations.push({
        operation: "check_column",
        status: "error",
        error: errorMsg
      });
    }

    // Vérification finale
    console.log("🔍 Vérification finale...");
    const finalCheck = await db.execute(sql`
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'users' 
      AND table_schema = 'public'
      AND column_name = 'email_on_new_follower';
    `);

    const hasCorrectColumn = finalCheck.length > 0;
    results.operations.push({
      operation: "final_verification",
      column: "email_on_new_follower",
      exists: hasCorrectColumn,
      status: hasCorrectColumn ? "success" : "missing"
    });

    if (!hasCorrectColumn && results.summary.success) {
      results.summary.success = false;
      results.summary.errors.push("Colonne email_on_new_follower toujours manquante après correction");
    }

    console.log(`${results.summary.success ? '✅' : '❌'} Correction terminée - ${results.summary.operationsCount} opérations`);

    return NextResponse.json(results);

  } catch (error) {
    console.error("❌ Erreur lors de la correction nomenclature:", error);
    return NextResponse.json(
      { 
        error: "Erreur lors de la correction nomenclature", 
        details: error instanceof Error ? error.message : String(error),
        environment: process.env.VERCEL_ENV || process.env.NODE_ENV || 'unknown'
      },
      { status: 500 }
    );
  }
} 