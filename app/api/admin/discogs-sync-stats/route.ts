import { NextRequest, NextResponse } from "next/server";
import { DiscogsSyncLogger } from "@/lib/sync-logger";

/**
 * API Route pour consulter les statistiques et logs de synchronisation Discogs
 * Utile pour le monitoring et le debugging
 */
export async function GET(request: NextRequest) {
  try {
    // Vérification de sécurité basique (peut être améliorée avec une vraie auth admin)
    const authHeader = request.headers.get("authorization");
    const expectedSecret = `Bearer ${process.env.CRON_SECRET}`;
    
    if (!authHeader || authHeader !== expectedSecret) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const action = searchParams.get("action") || "stats";
    const userId = searchParams.get("userId");
    const since = searchParams.get("since");

    switch (action) {
      case "stats":
        const sinceDate = since ? new Date(since) : new Date(Date.now() - 7 * 24 * 60 * 60 * 1000); // 7 jours par défaut
        const stats = DiscogsSyncLogger.getStats(sinceDate);
        
        return NextResponse.json({
          success: true,
          data: {
            period: {
              since: sinceDate.toISOString(),
              until: new Date().toISOString()
            },
            stats
          }
        });

      case "logs":
        const limit = parseInt(searchParams.get("limit") || "50");
        
        if (userId) {
          const userLogs = DiscogsSyncLogger.getUserLogs(userId, limit);
          return NextResponse.json({
            success: true,
            data: {
              userId,
              logs: userLogs
            }
          });
        } else {
          const recentLogs = DiscogsSyncLogger.getRecentLogs(limit);
          return NextResponse.json({
            success: true,
            data: {
              logs: recentLogs
            }
          });
        }

      case "cleanup":
        const daysOld = parseInt(searchParams.get("days") || "30");
        const cutoffDate = new Date(Date.now() - daysOld * 24 * 60 * 60 * 1000);
        DiscogsSyncLogger.cleanup(cutoffDate);
        
        return NextResponse.json({
          success: true,
          message: `Logs antérieurs à ${cutoffDate.toISOString()} supprimés`
        });

      default:
        return NextResponse.json(
          { error: "Action non supportée. Utilisez: stats, logs, cleanup" },
          { status: 400 }
        );
    }

  } catch (error) {
    console.error("Erreur lors de la récupération des stats Discogs:", error);
    
    return NextResponse.json(
      {
        success: false,
        error: "Erreur interne du serveur",
        message: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}

/**
 * Endpoint pour déclencher manuellement une synchronisation
 */
export async function POST(request: NextRequest) {
  try {
    // Vérification de sécurité
    const authHeader = request.headers.get("authorization");
    const expectedSecret = `Bearer ${process.env.CRON_SECRET}`;
    
    if (!authHeader || authHeader !== expectedSecret) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { userId, action } = body;

    if (!userId || !action) {
      return NextResponse.json(
        { error: "userId et action requis" },
        { status: 400 }
      );
    }

    switch (action) {
      case "sync_user":
        const { syncDiscogsCollection } = await import('@/app/actions/user');
        const result = await syncDiscogsCollection(userId);
        
        return NextResponse.json({
          success: true,
          message: "Synchronisation déclenchée",
          result
        });

      case "sync_all":
        const { syncAllDiscogsCollections } = await import('@/app/actions/user');
        const allResult = await syncAllDiscogsCollections();
        
        return NextResponse.json({
          success: true,
          message: "Synchronisation globale déclenchée",
          result: allResult
        });

      default:
        return NextResponse.json(
          { error: "Action non supportée. Utilisez: sync_user, sync_all" },
          { status: 400 }
        );
    }

  } catch (error) {
    console.error("Erreur lors du déclenchement de la synchronisation:", error);
    
    return NextResponse.json(
      {
        success: false,
        error: "Erreur interne du serveur",
        message: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}
