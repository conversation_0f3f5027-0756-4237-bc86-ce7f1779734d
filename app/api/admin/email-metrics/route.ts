/**
 * API Route pour consulter les métriques de délivrabilité des emails
 * Accessible uniquement aux administrateurs
 */

import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/db';
import { users, emailEvents, emailDeliverabilityMetrics } from '@/lib/db/schema';
import { eq, gte, lte, desc, count, sql } from 'drizzle-orm';
import { auth } from '@/auth';

/**
 * GET - Récupère les métriques de délivrabilité
 */
export async function GET(request: NextRequest) {
  try {
    // Vérifier l'authentification et les permissions admin
    const session = await auth();
    if (!session?.user?.email) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // TODO: Ajouter une vérification des permissions admin
    // Pour l'instant, on limite aux emails spécifiques
    const adminEmails = ['<EMAIL>']; // À configurer via env
    if (!adminEmails.includes(session.user.email)) {
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 }
      );
    }

    const { searchParams } = new URL(request.url);
    const days = parseInt(searchParams.get('days') || '30');
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    // Métriques globales récentes
    const globalMetrics = await getGlobalMetrics(startDate);
    
    // Métriques par type d'événement
    const eventMetrics = await getEventMetrics(startDate);
    
    // Top des domaines problématiques
    const problematicDomains = await getProblematicDomains(startDate);
    
    // Utilisateurs avec des problèmes de délivrabilité
    const problematicUsers = await getProblematicUsers();
    
    // Tendances quotidiennes
    const dailyTrends = await getDailyTrends(startDate);

    return NextResponse.json({
      success: true,
      period: {
        days,
        startDate: startDate.toISOString(),
        endDate: new Date().toISOString()
      },
      metrics: {
        global: globalMetrics,
        events: eventMetrics,
        problematicDomains,
        problematicUsers,
        dailyTrends
      }
    });

  } catch (error) {
    console.error('❌ Erreur lors de la récupération des métriques email:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

/**
 * Récupère les métriques globales
 */
async function getGlobalMetrics(startDate: Date) {
  const totalUsers = await db
    .select({ count: count() })
    .from(users)
    .where(eq(users.emailNotificationsEnabled, true));

  const recentEvents = await db
    .select({
      eventType: emailEvents.eventType,
      count: count()
    })
    .from(emailEvents)
    .where(gte(emailEvents.createdAt, startDate))
    .groupBy(emailEvents.eventType);

  const eventCounts = recentEvents.reduce((acc, event) => {
    acc[event.eventType] = event.count;
    return acc;
  }, {} as Record<string, number>);

  const sent = eventCounts.sent || 0;
  const delivered = eventCounts.delivered || 0;
  const bounced = eventCounts.bounced || 0;
  const complained = eventCounts.complained || 0;
  const opened = eventCounts.opened || 0;
  const clicked = eventCounts.clicked || 0;

  return {
    totalActiveUsers: totalUsers[0]?.count || 0,
    emailsSent: sent,
    emailsDelivered: delivered,
    emailsBounced: bounced,
    emailsComplained: complained,
    emailsOpened: opened,
    emailsClicked: clicked,
    deliverabilityRate: sent > 0 ? ((delivered / sent) * 100).toFixed(2) : '0',
    bounceRate: sent > 0 ? ((bounced / sent) * 100).toFixed(2) : '0',
    complaintRate: sent > 0 ? ((complained / sent) * 100).toFixed(2) : '0',
    openRate: delivered > 0 ? ((opened / delivered) * 100).toFixed(2) : '0',
    clickRate: delivered > 0 ? ((clicked / delivered) * 100).toFixed(2) : '0'
  };
}

/**
 * Récupère les métriques par type d'événement
 */
async function getEventMetrics(startDate: Date) {
  return await db
    .select({
      eventType: emailEvents.eventType,
      count: count(),
      lastOccurrence: sql<Date>`MAX(${emailEvents.createdAt})`
    })
    .from(emailEvents)
    .where(gte(emailEvents.createdAt, startDate))
    .groupBy(emailEvents.eventType)
    .orderBy(desc(count()));
}

/**
 * Récupère les domaines problématiques
 */
async function getProblematicDomains(startDate: Date) {
  const bouncedEmails = await db
    .select({
      userId: emailEvents.userId,
      eventData: emailEvents.eventData
    })
    .from(emailEvents)
    .where(
      sql`${emailEvents.eventType} IN ('bounced', 'complained') AND ${emailEvents.createdAt} >= ${startDate}`
    );

  const domainCounts: Record<string, { bounces: number; complaints: number }> = {};

  for (const event of bouncedEmails) {
    const user = await db
      .select({ email: users.email })
      .from(users)
      .where(eq(users.id, event.userId))
      .limit(1);

    if (user[0]?.email) {
      const domain = user[0].email.split('@')[1];
      if (!domainCounts[domain]) {
        domainCounts[domain] = { bounces: 0, complaints: 0 };
      }
      
      const eventData = event.eventData as any;
      if (eventData?.bounce) {
        domainCounts[domain].bounces++;
      }
      if (eventData?.complaint) {
        domainCounts[domain].complaints++;
      }
    }
  }

  return Object.entries(domainCounts)
    .map(([domain, counts]) => ({
      domain,
      ...counts,
      total: counts.bounces + counts.complaints
    }))
    .sort((a, b) => b.total - a.total)
    .slice(0, 10);
}

/**
 * Récupère les utilisateurs avec des problèmes de délivrabilité
 */
async function getProblematicUsers() {
  return await db
    .select({
      id: users.id,
      email: users.email,
      emailDeliverabilityScore: users.emailDeliverabilityScore,
      lastEmailBounce: users.lastEmailBounce,
      emailBounceReason: users.emailBounceReason,
      lastEmailComplaint: users.lastEmailComplaint,
      emailComplaintReason: users.emailComplaintReason,
      emailNotificationsEnabled: users.emailNotificationsEnabled
    })
    .from(users)
    .where(
      sql`${users.emailDeliverabilityScore} < 70 OR ${users.lastEmailBounce} IS NOT NULL OR ${users.lastEmailComplaint} IS NOT NULL`
    )
    .orderBy(users.emailDeliverabilityScore)
    .limit(20);
}

/**
 * Récupère les tendances quotidiennes
 */
async function getDailyTrends(startDate: Date) {
  return await db
    .select({
      date: sql<string>`DATE(${emailEvents.createdAt})`,
      eventType: emailEvents.eventType,
      count: count()
    })
    .from(emailEvents)
    .where(gte(emailEvents.createdAt, startDate))
    .groupBy(sql`DATE(${emailEvents.createdAt})`, emailEvents.eventType)
    .orderBy(sql`DATE(${emailEvents.createdAt}) DESC`);
}

/**
 * POST - Recalcule les scores de délivrabilité
 */
export async function POST(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user?.email) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const adminEmails = ['<EMAIL>'];
    if (!adminEmails.includes(session.user.email)) {
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 }
      );
    }

    // Recalculer les scores de délivrabilité pour tous les utilisateurs
    const allUsers = await db.select({ id: users.id }).from(users);
    
    let updated = 0;
    for (const user of allUsers) {
      // Calculer le nouveau score basé sur les événements récents
      const recentEvents = await db
        .select({ eventType: emailEvents.eventType })
        .from(emailEvents)
        .where(
          sql`${emailEvents.userId} = ${user.id} AND ${emailEvents.createdAt} >= ${new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)}`
        );

      let score = 100;
      
      // Pénalités
      const bounces = recentEvents.filter(e => e.eventType === 'bounced').length;
      const complaints = recentEvents.filter(e => e.eventType === 'complained').length;
      
      score -= bounces * 10;
      score -= complaints * 50;
      
      // Bonus pour l'engagement
      const opens = recentEvents.filter(e => e.eventType === 'opened').length;
      const clicks = recentEvents.filter(e => e.eventType === 'clicked').length;
      
      score += Math.min(20, opens * 2 + clicks * 5);
      
      // Limiter entre 0 et 100
      score = Math.max(0, Math.min(100, score));

      await db
        .update(users)
        .set({ emailDeliverabilityScore: score })
        .where(eq(users.id, user.id));
      
      updated++;
    }

    return NextResponse.json({
      success: true,
      message: `Scores de délivrabilité recalculés pour ${updated} utilisateurs`
    });

  } catch (error) {
    console.error('❌ Erreur lors du recalcul des scores:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Autres méthodes HTTP non autorisées
export async function PUT() {
  return NextResponse.json({ error: 'Method not allowed' }, { status: 405 });
}

export async function DELETE() {
  return NextResponse.json({ error: 'Method not allowed' }, { status: 405 });
}

export async function PATCH() {
  return NextResponse.json({ error: 'Method not allowed' }, { status: 405 });
}
