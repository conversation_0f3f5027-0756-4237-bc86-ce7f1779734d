import { NextRequest, NextResponse } from "next/server";
import { getSession } from "@/lib/auth";
import { db } from "@/lib/db";
import { userDiscogsCollection, recommendations } from "@/lib/db/schema";
import { eq } from "drizzle-orm";
import { markOwnedAlbums, markWishlistedAlbums } from "@/lib/album-matching";

/**
 * API pour recalculer le statut isOwned de toutes les recommandations
 * Utile après avoir corrigé la logique de correspondance (ex: support cyrillique)
 */
export async function POST(request: NextRequest) {
  try {
    // Vérifier que l'utilisateur est connecté
    const session = await getSession();
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: "Utilisateur non connecté" },
        { status: 401 }
      );
    }

    const userId = session.user.id;
    if (process.env.NODE_ENV === 'development') {
    console.log(`🔄 Recalcul du statut isOwned pour l'utilisateur: ${userId}`);
    }

    // 1. Récupérer toutes les recommandations de l'utilisateur
    const userRecommendations = await db.query.recommendations.findMany({
      where: eq(recommendations.userId, userId),
    });

    if (userRecommendations.length === 0) {
      return NextResponse.json({
        success: true,
        message: "Aucune recommandation trouvée",
        stats: {
          totalRecommendations: 0,
          updated: 0,
          newlyOwned: 0,
          newlyNotOwned: 0
        }
      });
    }

    // 2. Récupérer la collection Discogs de l'utilisateur
    const discogsCollection = await db.query.userDiscogsCollection.findMany({
      where: eq(userDiscogsCollection.userId, userId),
    });

    if (process.env.NODE_ENV === 'development') {
    console.log(`📊 Données trouvées:`);
    }
    console.log(`  - Recommandations: ${userRecommendations.length}`);
    if (process.env.NODE_ENV === 'development') {
    console.log(`  - Albums Discogs: ${discogsCollection.length}`);
    }

    // 3. Recalculer le statut isOwned avec la nouvelle logique
    // Mapper les données pour convertir null en undefined pour la compatibilité de type
    const mappedRecommendations = userRecommendations.map(rec => ({
      artistName: rec.artistName,
      albumTitle: rec.albumTitle,
      spotifyAlbumId: rec.spotifyAlbumId || undefined,
      albumCoverUrl: rec.albumCoverUrl || undefined,
      listenScore: rec.listenScore,
      timeframe: rec.timeframe
    }));

    const recalculatedRecommendations = markOwnedAlbums(mappedRecommendations, discogsCollection);

    // 4. Identifier les changements
    let updatedCount = 0;
    let newlyOwnedCount = 0;
    let newlyNotOwnedCount = 0;
    const changes: Array<{
      id: number;
      artistName: string;
      albumTitle: string;
      oldStatus: boolean;
      newStatus: boolean;
    }> = [];

    for (let i = 0; i < userRecommendations.length; i++) {
      const original = userRecommendations[i];
      const recalculated = recalculatedRecommendations[i];

      if (original.isOwned !== recalculated.isOwned) {
        updatedCount++;
        
        if (recalculated.isOwned) {
          newlyOwnedCount++;
        } else {
          newlyNotOwnedCount++;
        }

        changes.push({
          id: original.id,
          artistName: original.artistName,
          albumTitle: original.albumTitle,
          oldStatus: original.isOwned,
          newStatus: recalculated.isOwned
        });

        // 5. Mettre à jour en base de données
        await db.update(recommendations)
          .set({ isOwned: recalculated.isOwned })
          .where(eq(recommendations.id, original.id));
      }
    }

    if (process.env.NODE_ENV === 'development') {
    if (process.env.NODE_ENV === 'development') {
    if (process.env.NODE_ENV === 'development') {
    console.log(`✅ Recalcul terminé:`);
    }
    }
    }
    if (process.env.NODE_ENV === 'development') {
    console.log(`  - Recommandations mises à jour: ${updatedCount}`);
    }
    console.log(`  - Nouvellement possédées: ${newlyOwnedCount}`);
    if (process.env.NODE_ENV === 'development') {
    console.log(`  - Nouvellement non possédées: ${newlyNotOwnedCount}`);
    }

    // Afficher les changements détaillés
    if (changes.length > 0) {
      if (process.env.NODE_ENV === 'development') {
      console.log(`📝 Détail des changements:`);
      }
      changes.forEach(change => {
        const status = change.newStatus ? "✅ POSSÉDÉ" : "❌ NON POSSÉDÉ";
        if (process.env.NODE_ENV === 'development') {
        console.log(`  - "${change.artistName}" - "${change.albumTitle}": ${status}`);
        }
      });
    }

    return NextResponse.json({
      success: true,
      message: `Recalcul terminé avec succès`,
      stats: {
        totalRecommendations: userRecommendations.length,
        discogsAlbums: discogsCollection.length,
        updated: updatedCount,
        newlyOwned: newlyOwnedCount,
        newlyNotOwned: newlyNotOwnedCount
      },
      changes: changes.map(change => ({
        artistName: change.artistName,
        albumTitle: change.albumTitle,
        oldStatus: change.oldStatus ? "possédé" : "non possédé",
        newStatus: change.newStatus ? "possédé" : "non possédé",
        action: change.newStatus ? "Marqué comme possédé" : "Marqué comme non possédé"
      }))
    });

  } catch (error) {
    console.error("💥 Erreur lors du recalcul:", error);
    
    return NextResponse.json(
      {
        success: false,
        error: "Erreur lors du recalcul",
        message: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}
