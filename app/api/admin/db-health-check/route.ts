import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { sql } from "drizzle-orm";

/**
 * Route complète de diagnostic et correction automatique de la base de données
 */
async function performHealthCheck(autoFix: boolean = false) {
  const results = {
    connection: false,
    tables: {} as Record<string, any>,
    corrections: [] as string[],
    errors: [] as string[],
    summary: {
      tablesChecked: 0,
      tablesCreated: 0,
      columnsAdded: 0,
      indexesCreated: 0
    }
  };

  try {
    if (process.env.NODE_ENV === 'development') {
    console.log("🏥 Diagnostic complet de la base de données...");
    }

    // 1. Test de connexion
    if (process.env.NODE_ENV === 'development') {
    if (process.env.NODE_ENV === 'development') {
    if (process.env.NODE_ENV === 'development') {
    console.log("🔍 Test de connexion...");
    }
    }
    }
    await db.execute(sql`SELECT 1 as test`);
    results.connection = true;
    if (process.env.NODE_ENV === 'development') {
    console.log("✅ Connexion réussie");
    }

    // 2. Définition des schémas attendus
    const expectedTables = {
      users: {
        columns: [
          { name: 'id', type: 'text', nullable: false, primary: true },
          { name: 'name', type: 'text', nullable: true },
          { name: 'email', type: 'text', nullable: true },
          { name: 'emailVerified', type: 'timestamp', nullable: true },
          { name: 'image', type: 'text', nullable: true },
          { name: 'preferredLanguage', type: 'text', nullable: false, default: 'fr' },
          { name: 'emailFrequency', type: 'text', nullable: false, default: 'weekly' },
          { name: 'pushFrequency', type: 'text', nullable: false, default: 'weekly' },
          { name: 'firstRecommendationEmailSent', type: 'boolean', nullable: false, default: false }
        ],
        indexes: []
      },
      accounts: {
        columns: [
          { name: 'userId', type: 'text', nullable: false },
          { name: 'type', type: 'text', nullable: false },
          { name: 'provider', type: 'text', nullable: false },
          { name: 'providerAccountId', type: 'text', nullable: false },
          { name: 'refresh_token', type: 'text', nullable: true },
          { name: 'access_token', type: 'text', nullable: true },
          { name: 'access_token_secret', type: 'text', nullable: true },
          { name: 'expires_at', type: 'integer', nullable: true },
          { name: 'token_type', type: 'text', nullable: true },
          { name: 'scope', type: 'text', nullable: true },
          { name: 'id_token', type: 'text', nullable: true },
          { name: 'session_state', type: 'text', nullable: true }
        ],
        indexes: ['idx_accounts_user_id']
      },
      sessions: {
        columns: [
          { name: 'sessionToken', type: 'text', nullable: false, primary: true },
          { name: 'userId', type: 'text', nullable: false },
          { name: 'expires', type: 'timestamp', nullable: false }
        ],
        indexes: ['idx_sessions_user_id']
      },
      verification_tokens: {
        columns: [
          { name: 'identifier', type: 'text', nullable: false },
          { name: 'token', type: 'text', nullable: false },
          { name: 'expires', type: 'timestamp', nullable: false }
        ],
        indexes: []
      }
    };

    // 3. Vérification et correction de chaque table
    for (const [tableName, schema] of Object.entries(expectedTables)) {
      if (process.env.NODE_ENV === 'development') {
      console.log(`\n📋 Vérification de la table ${tableName}...`);
      }
      results.summary.tablesChecked++;

      // Vérifier si la table existe
      const tableExists = await db.execute(sql`
        SELECT EXISTS (
          SELECT FROM information_schema.tables 
          WHERE table_schema = 'public' 
          AND table_name = ${tableName}
        )
      `);

      if (!tableExists[0].exists) {
        if (autoFix) {
          if (process.env.NODE_ENV === 'development') {
          console.log(`❌ Table ${tableName} manquante, création...`);
          }
          await createTable(tableName, schema);
          results.corrections.push(`Table ${tableName} créée`);
          results.summary.tablesCreated++;
        } else {
          results.errors.push(`Table ${tableName} manquante`);
        }
      }

      // Vérifier les colonnes
      const existingColumns = await db.execute(sql`
        SELECT column_name, data_type, is_nullable, column_default
        FROM information_schema.columns 
        WHERE table_name = ${tableName}
        AND table_schema = 'public'
      `);

      const existingColumnNames = existingColumns.map((col: any) => col.column_name);
      
      for (const expectedCol of schema.columns) {
        if (!existingColumnNames.includes(expectedCol.name)) {
          if (autoFix) {
            if (process.env.NODE_ENV === 'development') {
            console.log(`➕ Ajout de la colonne ${tableName}.${expectedCol.name}...`);
            }
            await addColumn(tableName, expectedCol);
            results.corrections.push(`Colonne ${tableName}.${expectedCol.name} ajoutée`);
            results.summary.columnsAdded++;
          } else {
            results.errors.push(`Colonne ${tableName}.${expectedCol.name} manquante`);
          }
        }
      }

      // Vérifier les index
      for (const indexName of schema.indexes) {
        const indexExists = await db.execute(sql`
          SELECT EXISTS (
            SELECT FROM pg_indexes 
            WHERE tablename = ${tableName}
            AND indexname = ${indexName}
          )
        `);

        if (!indexExists[0].exists) {
          if (autoFix) {
            if (process.env.NODE_ENV === 'development') {
            if (process.env.NODE_ENV === 'development') {
            if (process.env.NODE_ENV === 'development') {
            console.log(`🔍 Création de l'index ${indexName}...`);
            }
            }
            }
            await createIndex(tableName, indexName);
            results.corrections.push(`Index ${indexName} créé`);
            results.summary.indexesCreated++;
          } else {
            results.errors.push(`Index ${indexName} manquant`);
          }
        }
      }

      // Stocker les informations de la table
      results.tables[tableName] = {
        exists: true,
        columns: existingColumns.length,
        expectedColumns: schema.columns.length
      };
    }

    // 4. Vérification des contraintes de clés étrangères
    if (process.env.NODE_ENV === 'development') {
    console.log("\n🔗 Vérification des contraintes...");
    }
    if (autoFix) {
      await ensureForeignKeys();
      results.corrections.push("Contraintes de clés étrangères vérifiées");
    }

    // 5. Test final avec une requête NextAuth typique
    if (process.env.NODE_ENV === 'development') {
    console.log("\n🧪 Test final avec requête NextAuth...");
    }
    try {
      await db.execute(sql`
        SELECT u.id, u.email, a.provider 
        FROM users u 
        LEFT JOIN accounts a ON u.id = a."userId" 
        LIMIT 1
      `);
      results.corrections.push("Test de requête NextAuth réussi");
    } catch (error) {
      results.errors.push(`Erreur test NextAuth: ${error}`);
    }

    if (process.env.NODE_ENV === 'development') {
    console.log("✅ Diagnostic et corrections terminés");
    }

    return NextResponse.json({
      success: true,
      message: "Diagnostic et corrections de la base de données terminés",
      results,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error("❌ Erreur lors du diagnostic:", error);
    results.errors.push(error instanceof Error ? error.message : "Erreur inconnue");
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : "Erreur inconnue",
      results,
      stack: error instanceof Error ? error.stack : undefined
    }, { status: 500 });
  }
}

// Routes GET et POST
export async function GET(request: NextRequest) {
  return performHealthCheck(false); // Diagnostic seulement
}

export async function POST(request: NextRequest) {
  return performHealthCheck(true); // Diagnostic + corrections automatiques
}

// Fonctions utilitaires
async function createTable(tableName: string, schema: any) {
  const createStatements: Record<string, string> = {
    users: `
      CREATE TABLE users (
        id TEXT PRIMARY KEY,
        name TEXT,
        email TEXT UNIQUE,
        "emailVerified" TIMESTAMP,
        image TEXT,
        "preferredLanguage" TEXT DEFAULT 'fr' NOT NULL,
        "emailFrequency" TEXT DEFAULT 'weekly' NOT NULL,
        "pushFrequency" TEXT DEFAULT 'weekly' NOT NULL,
        "firstRecommendationEmailSent" BOOLEAN DEFAULT false NOT NULL
      )
    `,
    accounts: `
      CREATE TABLE accounts (
        "userId" TEXT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        type TEXT NOT NULL,
        provider TEXT NOT NULL,
        "providerAccountId" TEXT NOT NULL,
        refresh_token TEXT,
        access_token TEXT,
        access_token_secret TEXT,
        expires_at INTEGER,
        token_type TEXT,
        scope TEXT,
        id_token TEXT,
        session_state TEXT,
        PRIMARY KEY (provider, "providerAccountId")
      )
    `,
    sessions: `
      CREATE TABLE sessions (
        "sessionToken" TEXT PRIMARY KEY,
        "userId" TEXT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        expires TIMESTAMP NOT NULL
      )
    `,
    verification_tokens: `
      CREATE TABLE verification_tokens (
        identifier TEXT NOT NULL,
        token TEXT NOT NULL UNIQUE,
        expires TIMESTAMP NOT NULL,
        PRIMARY KEY (identifier, token)
      )
    `
  };

  if (createStatements[tableName]) {
    await db.execute(sql.raw(createStatements[tableName]));
  }
}

async function addColumn(tableName: string, column: any) {
  let columnDef = `${column.name} ${column.type.toUpperCase()}`;
  
  if (!column.nullable) {
    columnDef += ' NOT NULL';
  }
  
  if (column.default !== undefined) {
    if (typeof column.default === 'string') {
      columnDef += ` DEFAULT '${column.default}'`;
    } else {
      columnDef += ` DEFAULT ${column.default}`;
    }
  }

  await db.execute(sql.raw(`ALTER TABLE ${tableName} ADD COLUMN IF NOT EXISTS ${columnDef}`));
}

async function createIndex(tableName: string, indexName: string) {
  const indexStatements: Record<string, string> = {
    'idx_accounts_user_id': 'CREATE INDEX IF NOT EXISTS idx_accounts_user_id ON accounts("userId")',
    'idx_sessions_user_id': 'CREATE INDEX IF NOT EXISTS idx_sessions_user_id ON sessions("userId")'
  };

  if (indexStatements[indexName]) {
    await db.execute(sql.raw(indexStatements[indexName]));
  }
}

async function ensureForeignKeys() {
  // Vérifier et créer les contraintes de clés étrangères si nécessaire
  try {
    await db.execute(sql`
      ALTER TABLE accounts 
      ADD CONSTRAINT IF NOT EXISTS fk_accounts_user_id 
      FOREIGN KEY ("userId") REFERENCES users(id) ON DELETE CASCADE
    `);
  } catch (e) {
    if (process.env.NODE_ENV === 'development') {
    console.log("ℹ️ Contrainte accounts->users existe déjà");
    }
  }

  try {
    await db.execute(sql`
      ALTER TABLE sessions 
      ADD CONSTRAINT IF NOT EXISTS fk_sessions_user_id 
      FOREIGN KEY ("userId") REFERENCES users(id) ON DELETE CASCADE
    `);
  } catch (e) {
    if (process.env.NODE_ENV === 'development') {
    console.log("ℹ️ Contrainte sessions->users existe déjà");
    }
  }
}
