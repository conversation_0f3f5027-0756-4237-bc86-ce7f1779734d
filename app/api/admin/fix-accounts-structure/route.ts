import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { sql } from "drizzle-orm";

/**
 * Route pour corriger définitivement la structure de la table accounts
 */
export async function GET(request: NextRequest) {
  try {
    if (process.env.NODE_ENV === 'development') {
    console.log("🔧 Correction de la structure de la table accounts...");
    }

    // 1. Sauvegarder les données existantes (s'il y en a)
    if (process.env.NODE_ENV === 'development') {
    console.log("💾 Sauvegarde des données existantes...");
    }
    const existingData = await db.execute(sql`
      SELECT * FROM accounts
    `);
    if (process.env.NODE_ENV === 'development') {
    console.log(`📊 ${existingData.length} comptes existants trouvés`);
    }

    // 2. Supprimer la table existante
    if (process.env.NODE_ENV === 'development') {
    console.log("🗑️ Suppression de la table accounts existante...");
    }
    await db.execute(sql`DROP TABLE IF EXISTS accounts CASCADE`);
    if (process.env.NODE_ENV === 'development') {
    console.log("✅ Table accounts supprimée");
    }

    // 3. Recréer la table avec la structure correcte pour NextAuth
    if (process.env.NODE_ENV === 'development') {
    console.log("🏗️ Création de la nouvelle table accounts...");
    }
    await db.execute(sql`
      CREATE TABLE accounts (
        "userId" TEXT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        type TEXT NOT NULL,
        provider TEXT NOT NULL,
        "providerAccountId" TEXT NOT NULL,
        refresh_token TEXT,
        access_token TEXT,
        access_token_secret TEXT,
        expires_at INTEGER,
        token_type TEXT,
        scope TEXT,
        id_token TEXT,
        session_state TEXT,
        PRIMARY KEY (provider, "providerAccountId")
      )
    `);
    if (process.env.NODE_ENV === 'development') {
    console.log("✅ Nouvelle table accounts créée avec clé primaire composite");
    }

    // 4. Créer l'index sur userId
    if (process.env.NODE_ENV === 'development') {
    if (process.env.NODE_ENV === 'development') {
    if (process.env.NODE_ENV === 'development') {
    console.log("🔍 Création de l'index sur userId...");
    }
    }
    }
    await db.execute(sql`
      CREATE INDEX IF NOT EXISTS idx_accounts_user_id ON accounts("userId")
    `);
    if (process.env.NODE_ENV === 'development') {
    console.log("✅ Index créé");
    }

    // 5. Restaurer les données si nécessaire (en adaptant la structure)
    if (existingData.length > 0) {
      if (process.env.NODE_ENV === 'development') {
      console.log("📥 Restauration des données existantes...");
      }
      for (const account of existingData) {
        try {
          await db.execute(sql`
            INSERT INTO accounts (
              "userId", type, provider, "providerAccountId",
              refresh_token, access_token, access_token_secret,
              expires_at, token_type, scope, id_token, session_state
            ) VALUES (
              ${account.userId}, ${account.type}, ${account.provider}, ${account.providerAccountId},
              ${account.refresh_token}, ${account.access_token}, ${account.access_token_secret},
              ${account.expires_at}, ${account.token_type}, ${account.scope}, 
              ${account.id_token}, ${account.session_state}
            )
            ON CONFLICT (provider, "providerAccountId") DO NOTHING
          `);
        } catch (error) {
          if (process.env.NODE_ENV === 'development') {
          if (process.env.NODE_ENV === 'development') {
          if (process.env.NODE_ENV === 'development') {
          console.log(`⚠️ Erreur lors de la restauration du compte ${account.provider}:${account.providerAccountId}:`, error);
          }
          }
          }
        }
      }
      if (process.env.NODE_ENV === 'development') {
      console.log("✅ Données restaurées");
      }
    }

    // 6. Vérifier la nouvelle structure
    if (process.env.NODE_ENV === 'development') {
    if (process.env.NODE_ENV === 'development') {
    if (process.env.NODE_ENV === 'development') {
    console.log("🔍 Vérification de la nouvelle structure...");
    }
    }
    }
    const newConstraints = await db.execute(sql`
      SELECT 
        tc.constraint_name,
        tc.constraint_type,
        kcu.column_name
      FROM information_schema.table_constraints tc
      JOIN information_schema.key_column_usage kcu 
        ON tc.constraint_name = kcu.constraint_name
      WHERE tc.table_name = 'accounts'
      AND tc.table_schema = 'public'
      ORDER BY tc.constraint_type, kcu.ordinal_position
    `);

    return NextResponse.json({
      success: true,
      message: "Structure de la table accounts corrigée avec succès",
      results: {
        dataBackedUp: existingData.length,
        newConstraints: newConstraints,
        message: "Table accounts recréée avec clé primaire composite (provider, providerAccountId)"
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error("❌ Erreur lors de la correction:", error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : "Erreur inconnue",
      stack: error instanceof Error ? error.stack : undefined
    }, { status: 500 });
  }
}
