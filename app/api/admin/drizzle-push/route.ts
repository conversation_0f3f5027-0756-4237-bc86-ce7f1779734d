import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { sql } from "drizzle-orm";

/**
 * Route pour appliquer le schéma Drizzle en staging
 * Équivalent de `drizzle-kit push` mais via une route API
 */
export async function GET(request: NextRequest) {
  try {
    if (process.env.NODE_ENV === 'development') {
    console.log("🚀 Application du schéma Drizzle en staging...");
    }

    // 1. Sauvegarder les données existantes
    if (process.env.NODE_ENV === 'development') {
    console.log("💾 Sauvegarde des données existantes...");
    }
    let existingUsers: any[] = [];
    let existingAccounts: any[] = [];
    let existingSessions: any[] = [];

    try {
      existingUsers = await db.execute(sql`SELECT * FROM users`);
      if (process.env.NODE_ENV === 'development') {
      console.log(`👥 ${existingUsers.length} utilisateurs sauvegardés`);
      }
    } catch (e) {
      if (process.env.NODE_ENV === 'development') {
      console.log("ℹ️ Table users n'existe pas encore");
      }
    }

    try {
      existingAccounts = await db.execute(sql`SELECT * FROM accounts`);
      if (process.env.NODE_ENV === 'development') {
      console.log(`🔗 ${existingAccounts.length} comptes sauvegardés`);
      }
    } catch (e) {
      if (process.env.NODE_ENV === 'development') {
      console.log("ℹ️ Table accounts n'existe pas encore");
      }
    }

    try {
      existingSessions = await db.execute(sql`SELECT * FROM sessions`);
      if (process.env.NODE_ENV === 'development') {
      console.log(`🎫 ${existingSessions.length} sessions sauvegardées`);
      }
    } catch (e) {
      if (process.env.NODE_ENV === 'development') {
      console.log("ℹ️ Table sessions n'existe pas encore");
      }
    }

    // 2. Supprimer les tables existantes (dans le bon ordre)
    if (process.env.NODE_ENV === 'development') {
    console.log("🗑️ Suppression des tables existantes...");
    }
    await db.execute(sql`DROP TABLE IF EXISTS sessions CASCADE`);
    await db.execute(sql`DROP TABLE IF EXISTS accounts CASCADE`);
    await db.execute(sql`DROP TABLE IF EXISTS verification_tokens CASCADE`);
    await db.execute(sql`DROP TABLE IF EXISTS users CASCADE`);
    if (process.env.NODE_ENV === 'development') {
    console.log("✅ Tables supprimées");
    }

    // 3. Recréer les tables avec le schéma Drizzle exact
    if (process.env.NODE_ENV === 'development') {
    console.log("🏗️ Création des tables avec le schéma Drizzle...");
    }

    // Table users
    await db.execute(sql`
      CREATE TABLE users (
        id TEXT PRIMARY KEY,
        name TEXT,
        email TEXT UNIQUE,
        "emailVerified" TIMESTAMP,
        image TEXT,
        "preferredLanguage" TEXT DEFAULT 'fr' NOT NULL,
        "emailFrequency" TEXT DEFAULT 'weekly' NOT NULL,
        "pushFrequency" TEXT DEFAULT 'weekly' NOT NULL,
        "firstRecommendationEmailSent" BOOLEAN DEFAULT false NOT NULL
      )
    `);
    if (process.env.NODE_ENV === 'development') {
    console.log("✅ Table users créée");
    }

    // Table accounts avec clé primaire composite
    await db.execute(sql`
      CREATE TABLE accounts (
        "userId" TEXT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        type TEXT NOT NULL,
        provider TEXT NOT NULL,
        "providerAccountId" TEXT NOT NULL,
        refresh_token TEXT,
        access_token TEXT,
        access_token_secret TEXT,
        expires_at INTEGER,
        token_type TEXT,
        scope TEXT,
        id_token TEXT,
        session_state TEXT,
        PRIMARY KEY (provider, "providerAccountId")
      )
    `);
    if (process.env.NODE_ENV === 'development') {
    console.log("✅ Table accounts créée avec clé primaire composite");
    }

    // Table sessions
    await db.execute(sql`
      CREATE TABLE sessions (
        "sessionToken" TEXT PRIMARY KEY,
        "userId" TEXT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        expires TIMESTAMP NOT NULL
      )
    `);
    if (process.env.NODE_ENV === 'development') {
    console.log("✅ Table sessions créée");
    }

    // Table verification_tokens
    await db.execute(sql`
      CREATE TABLE verification_tokens (
        identifier TEXT NOT NULL,
        token TEXT NOT NULL UNIQUE,
        expires TIMESTAMP NOT NULL,
        PRIMARY KEY (identifier, token)
      )
    `);
    if (process.env.NODE_ENV === 'development') {
    console.log("✅ Table verification_tokens créée");
    }

    // 4. Créer les index
    if (process.env.NODE_ENV === 'development') {
    if (process.env.NODE_ENV === 'development') {
    if (process.env.NODE_ENV === 'development') {
    console.log("🔍 Création des index...");
    }
    }
    }
    await db.execute(sql`CREATE INDEX IF NOT EXISTS idx_accounts_user_id ON accounts("userId")`);
    await db.execute(sql`CREATE INDEX IF NOT EXISTS idx_sessions_user_id ON sessions("userId")`);
    if (process.env.NODE_ENV === 'development') {
    console.log("✅ Index créés");
    }

    // 5. Restaurer les données (en adaptant la structure)
    if (process.env.NODE_ENV === 'development') {
    console.log("📥 Restauration des données...");
    }
    
    // Restaurer les utilisateurs
    for (const user of existingUsers) {
      try {
        await db.execute(sql`
          INSERT INTO users (id, name, email, "emailVerified", image, "preferredLanguage", "emailFrequency", "pushFrequency", "firstRecommendationEmailSent")
          VALUES (${user.id}, ${user.name}, ${user.email}, ${user.emailVerified}, ${user.image}, 
                  ${user.preferredLanguage || 'fr'}, ${user.emailFrequency || 'weekly'}, 
                  ${user.pushFrequency || 'weekly'}, ${user.firstRecommendationEmailSent || false})
          ON CONFLICT (id) DO NOTHING
        `);
      } catch (error) {
        if (process.env.NODE_ENV === 'development') {
        if (process.env.NODE_ENV === 'development') {
        if (process.env.NODE_ENV === 'development') {
        console.log(`⚠️ Erreur lors de la restauration de l'utilisateur ${user.id}:`, error);
        }
        }
        }
      }
    }
    if (process.env.NODE_ENV === 'development') {
    if (process.env.NODE_ENV === 'development') {
    if (process.env.NODE_ENV === 'development') {
    console.log(`✅ ${existingUsers.length} utilisateurs restaurés`);
    }
    }
    }

    // Restaurer les comptes (en ignorant la colonne id si elle existe)
    for (const account of existingAccounts) {
      try {
        await db.execute(sql`
          INSERT INTO accounts ("userId", type, provider, "providerAccountId", refresh_token, access_token, access_token_secret, expires_at, token_type, scope, id_token, session_state)
          VALUES (${account.userId}, ${account.type}, ${account.provider}, ${account.providerAccountId},
                  ${account.refresh_token}, ${account.access_token}, ${account.access_token_secret},
                  ${account.expires_at}, ${account.token_type}, ${account.scope}, ${account.id_token}, ${account.session_state})
          ON CONFLICT (provider, "providerAccountId") DO NOTHING
        `);
      } catch (error) {
        if (process.env.NODE_ENV === 'development') {
        if (process.env.NODE_ENV === 'development') {
        if (process.env.NODE_ENV === 'development') {
        console.log(`⚠️ Erreur lors de la restauration du compte ${account.provider}:${account.providerAccountId}:`, error);
        }
        }
        }
      }
    }
    if (process.env.NODE_ENV === 'development') {
    if (process.env.NODE_ENV === 'development') {
    if (process.env.NODE_ENV === 'development') {
    console.log(`✅ ${existingAccounts.length} comptes restaurés`);
    }
    }
    }

    // Restaurer les sessions
    for (const session of existingSessions) {
      try {
        await db.execute(sql`
          INSERT INTO sessions ("sessionToken", "userId", expires)
          VALUES (${session.sessionToken}, ${session.userId}, ${session.expires})
          ON CONFLICT ("sessionToken") DO NOTHING
        `);
      } catch (error) {
        if (process.env.NODE_ENV === 'development') {
        if (process.env.NODE_ENV === 'development') {
        if (process.env.NODE_ENV === 'development') {
        console.log(`⚠️ Erreur lors de la restauration de la session ${session.sessionToken}:`, error);
        }
        }
        }
      }
    }
    if (process.env.NODE_ENV === 'development') {
    if (process.env.NODE_ENV === 'development') {
    if (process.env.NODE_ENV === 'development') {
    console.log(`✅ ${existingSessions.length} sessions restaurées`);
    }
    }
    }

    // 6. Vérification finale
    if (process.env.NODE_ENV === 'development') {
    if (process.env.NODE_ENV === 'development') {
    if (process.env.NODE_ENV === 'development') {
    console.log("🔍 Vérification finale...");
    }
    }
    }
    const finalStats = await db.execute(sql`
      SELECT 
        (SELECT COUNT(*) FROM users) as users_count,
        (SELECT COUNT(*) FROM accounts) as accounts_count,
        (SELECT COUNT(*) FROM sessions) as sessions_count
    `);

    return NextResponse.json({
      success: true,
      message: "Schéma Drizzle appliqué avec succès en staging",
      results: {
        dataBackedUp: {
          users: existingUsers.length,
          accounts: existingAccounts.length,
          sessions: existingSessions.length
        },
        finalStats: finalStats[0],
        message: "Base de données maintenant conforme au schéma Drizzle"
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error("❌ Erreur lors de l'application du schéma Drizzle:", error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : "Erreur inconnue",
      stack: error instanceof Error ? error.stack : undefined
    }, { status: 500 });
  }
}
