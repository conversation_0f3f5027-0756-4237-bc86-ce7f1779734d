import { ImageResponse } from 'next/og';
import { NextRequest } from 'next/server';
import { db } from "@/lib/db";
import { users, recommendations } from "@/lib/db/schema";
import { eq, desc, and } from "drizzle-orm";

export const runtime = 'nodejs';

/**
 * Génère une image Open Graph dynamique pour les listes publiques
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const publicListId = searchParams.get('publicListId');

    if (!publicListId) {
      return new Response('publicListId manquant', { status: 400 });
    }

    // Récupérer les données de l'utilisateur et ses recommandations
    const user = await db.query.users.findFirst({
      where: and(
        eq(users.publicListId, publicListId),
        eq(users.publicListEnabled, true)
      ),
      columns: {
        id: true,
        name: true,
        image: true,
      },
    });

    if (!user) {
      return new Response('Liste introuvable', { status: 404 });
    }

    // Récupérer quelques recommandations pour l'affichage
    const userRecommendations = await db.query.recommendations.findMany({
      where: eq(recommendations.userId, user.id),
      orderBy: [desc(recommendations.listenScore)],
      limit: 6, // Afficher 6 albums max sur l'image
      columns: {
        artistName: true,
        albumTitle: true,
        albumCoverUrl: true,
        listenScore: true,
      },
    });

    const userName = user.name || "Un mélomane";
    const albumCount = userRecommendations.length;

    return new ImageResponse(
      (
        <div
          style={{
            height: '100%',
            width: '100%',
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            backgroundColor: '#0f172a',
            backgroundImage: 'linear-gradient(135deg, #0f172a 0%, #1e293b 100%)',
            fontFamily: 'Inter, sans-serif',
          }}
        >
          {/* Header avec logo et titre */}
          <div
            style={{
              display: 'flex',
              alignItems: 'center',
              marginBottom: '40px',
            }}
          >
            {/* Logo Stream2Spin */}
            <div
              style={{
                width: '60px',
                height: '60px',
                borderRadius: '50%',
                backgroundColor: '#6366f1',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                marginRight: '20px',
              }}
            >
              <div
                style={{
                  width: '30px',
                  height: '30px',
                  backgroundColor: 'white',
                  borderRadius: '50%',
                }}
              />
            </div>
            <div
              style={{
                fontSize: '32px',
                fontWeight: 'bold',
                color: 'white',
              }}
            >
              Stream2Spin
            </div>
          </div>

          {/* Titre principal */}
          <div
            style={{
              fontSize: '48px',
              fontWeight: 'bold',
              color: 'white',
              textAlign: 'center',
              marginBottom: '20px',
              maxWidth: '800px',
            }}
          >
            Découvertes de {userName}
          </div>

          {/* Sous-titre */}
          <div
            style={{
              fontSize: '24px',
              color: '#94a3b8',
              textAlign: 'center',
              marginBottom: '40px',
            }}
          >
            {albumCount} recommandations d'albums personnalisées
          </div>

          {/* Grille d'albums */}
          {userRecommendations.length > 0 && (
            <div
              style={{
                display: 'flex',
                flexWrap: 'wrap',
                gap: '20px',
                justifyContent: 'center',
                maxWidth: '600px',
              }}
            >
              {userRecommendations.slice(0, 6).map((rec, index) => (
                <div
                  key={index}
                  style={{
                    width: '120px',
                    height: '120px',
                    borderRadius: '12px',
                    backgroundColor: '#334155',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    position: 'relative',
                    overflow: 'hidden',
                  }}
                >
                  {rec.albumCoverUrl ? (
                    <img
                      src={rec.albumCoverUrl}
                      alt={rec.albumTitle}
                      style={{
                        width: '100%',
                        height: '100%',
                        objectFit: 'cover',
                      }}
                    />
                  ) : (
                    <div
                      style={{
                        fontSize: '40px',
                        color: '#64748b',
                      }}
                    >
                      ♪
                    </div>
                  )}
                  
                  {/* Score overlay */}
                  <div
                    style={{
                      position: 'absolute',
                      top: '8px',
                      right: '8px',
                      backgroundColor: 'rgba(0, 0, 0, 0.8)',
                      color: 'white',
                      fontSize: '12px',
                      padding: '4px 8px',
                      borderRadius: '12px',
                      fontWeight: 'bold',
                    }}
                  >
                    {rec.listenScore}
                  </div>
                </div>
              ))}
            </div>
          )}

          {/* Call-to-action */}
          <div
            style={{
              marginTop: '40px',
              fontSize: '20px',
              color: '#e2e8f0',
              textAlign: 'center',
            }}
          >
            Créez votre propre liste de recommandations
          </div>
        </div>
      ),
      {
        width: 1200,
        height: 630,
      }
    );

  } catch (error) {
    console.error('Erreur lors de la génération de l\'image OG:', error);
    return new Response('Erreur lors de la génération de l\'image', { status: 500 });
  }
}
