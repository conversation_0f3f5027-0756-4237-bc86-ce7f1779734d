import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { recommendations } from "@/lib/db/schema";
import { and, eq, count } from "drizzle-orm";

/**
 * API pour récupérer le nombre de recommandations par timeframe
 * Utilisé pour désactiver les filtres de timeframe vides
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');

    if (!userId) {
      return NextResponse.json(
        { error: "userId requis" },
        { status: 400 }
      );
    }

    // Récupérer le nombre de recommandations pour chaque timeframe
    const timeframes = ['short_term', 'medium_term', 'long_term'];
    const counts: Record<string, number> = {};

    for (const timeframe of timeframes) {
      const result = await db
        .select({ count: count() })
        .from(recommendations)
        .where(and(
          eq(recommendations.userId, userId),
          eq(recommendations.timeframe, timeframe)
        ));

      counts[timeframe] = result[0]?.count || 0;
    }

    if (process.env.NODE_ENV === 'development') {
    console.log(`📊 Comptes de recommandations pour l'utilisateur ${userId}:`, counts);
    }

    return NextResponse.json({
      success: true,
      counts,
      hasAnyRecommendations: Object.values(counts).some(count => count > 0)
    });

  } catch (error) {
    console.error("❌ Erreur lors de la récupération des comptes de recommandations:", error);
    return NextResponse.json(
      { 
        error: "Erreur lors de la récupération des comptes",
        details: error instanceof Error ? error.message : "Erreur inconnue"
      },
      { status: 500 }
    );
  }
}
