import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { recommendations, wishlistItems } from "@/lib/db/schema";
import { and, eq, desc } from "drizzle-orm";
import { getUserDiscogsCollection, markOwnedAlbums } from "@/lib/album-matching";

/**
 * API pour récupérer les recommandations filtrées avec priorisation des timeframes
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');
    const requestedTimeframe = searchParams.get('timeframe') || 'short_term';
    const hideOwned = searchParams.get('hideOwned') === 'true'; // Par défaut false (ne pas masquer les vinyles possédés)
    const withOffers = searchParams.get('withOffers') === 'true';

    if (!userId) {
      return NextResponse.json(
        { error: "userId requis" },
        { status: 400 }
      );
    }

    // Valider le timeframe
    const validTimeframes = ['short_term', 'medium_term', 'long_term'];
    const selectedTimeframe = validTimeframes.includes(requestedTimeframe) ? requestedTimeframe : 'short_term';

    // Récupérer les recommandations avec priorisation des timeframes
    const { recommendations: allRecommendations, actualTimeframe } = await getRecommendationsWithPrioritization(
      userId,
      selectedTimeframe,
      hideOwned,
      withOffers
    );

    // Les recommandations sont déjà filtrées et traitées par la fonction de priorisation
    const userRecommendations = allRecommendations;

    // Mettre à jour le timeframe sélectionné si il a été changé par la priorisation
    if (actualTimeframe !== selectedTimeframe) {
      if (process.env.NODE_ENV === 'development') {
      console.log(`🔄 Timeframe changé de ${selectedTimeframe} vers ${actualTimeframe} en raison de la priorisation`);
      }
    }

    return NextResponse.json({
      success: true,
      recommendations: userRecommendations,
      filters: {
        timeframe: actualTimeframe,
        requestedTimeframe: selectedTimeframe,
        hideOwned,
        withOffers
      },
      counts: {
        total: allRecommendations.length,
        filtered: userRecommendations.length
      }
    });

  } catch (error) {
    console.error("❌ Erreur lors de la récupération des recommandations filtrées:", error);
    return NextResponse.json(
      {
        error: "Erreur lors de la récupération des recommandations",
        details: error instanceof Error ? error.message : "Erreur inconnue"
      },
      { status: 500 }
    );
  }
}

/**
 * Récupère les recommandations avec priorisation des timeframes
 * Si le timeframe demandé n'a pas de recommandations, essaie les autres dans l'ordre de priorité
 */
async function getRecommendationsWithPrioritization(
  userId: string,
  requestedTimeframe: string,
  hideOwned: boolean,
  withOffers: boolean
): Promise<{ recommendations: any[], actualTimeframe: string }> {
  // Ordre de priorité : shortterm -> mediumterm -> longterm
  const timeframePriority = ['short_term', 'medium_term', 'long_term'];

  // Commencer par le timeframe demandé, puis essayer les autres selon la priorité
  let timeframesToTry = [requestedTimeframe];

  // Ajouter les autres timeframes selon la priorité si ce n'est pas déjà le timeframe demandé
  for (const tf of timeframePriority) {
    if (tf !== requestedTimeframe) {
      timeframesToTry.push(tf);
    }
  }

  for (const timeframe of timeframesToTry) {
    if (process.env.NODE_ENV === 'development') {
    console.log(`🔍 Recherche de recommandations pour le timeframe: ${timeframe}`);
    }

    // Récupérer les recommandations pour ce timeframe
    const allRecommendations = await db.query.recommendations.findMany({
      where: and(
        eq(recommendations.userId, userId),
        eq(recommendations.timeframe, timeframe)
      ),
      orderBy: [desc(recommendations.listenScore), desc(recommendations.generatedAt)],
      limit: 50,
    });

    if (allRecommendations.length === 0) {
      if (process.env.NODE_ENV === 'development') {
      console.log(`⚠️ Aucune recommandation trouvée pour ${timeframe}, essai du timeframe suivant`);
      }
      continue;
    }

    // Récupérer les albums en wishlist
    const wishlistedItems = await db.query.wishlistItems.findMany({
      where: eq(wishlistItems.userId, userId),
      columns: {
        artistName: true,
        albumTitle: true,
      },
    });

    const wishlistedSet = new Set(
      wishlistedItems.map(item => `${item.artistName}|${item.albumTitle}`)
    );

    // Marquer les albums possédés via Discogs
    let recommendationsWithCorrectOwnership = allRecommendations;
    try {
      const discogsCollection = await getUserDiscogsCollection(userId, db);

      if (discogsCollection.length > 0) {
        // Recalculer le statut isOwned en temps réel
        const recalculatedRecommendations = markOwnedAlbums(
          allRecommendations.map(rec => ({
            artistName: rec.artistName,
            albumTitle: rec.albumTitle,
            spotifyAlbumId: rec.spotifyAlbumId || undefined,
            albumCoverUrl: rec.albumCoverUrl || undefined,
            listenScore: rec.listenScore,
            timeframe: rec.timeframe
          })),
          discogsCollection
        );

        // Fusionner les données recalculées avec les données originales
        recommendationsWithCorrectOwnership = allRecommendations.map((original, index) => ({
          ...original,
          isOwned: recalculatedRecommendations[index].isOwned
        }));
      }
    } catch (discogsError) {
      if (process.env.NODE_ENV === 'development') {
      console.warn("⚠️ Erreur lors du recalcul du statut isOwned, utilisation des données en base:", discogsError);
      }
    }

    // Appliquer les filtres côté serveur
    let filteredRecommendations = recommendationsWithCorrectOwnership;

    // Filtre pour masquer les vinyles possédés (si hideOwned est true)
    if (hideOwned) {
      filteredRecommendations = filteredRecommendations.filter(rec => !rec.isOwned);
    }

    // Filtre pour afficher uniquement les recommandations avec offres
    if (withOffers) {
      filteredRecommendations = filteredRecommendations.filter(rec =>
        rec.affiliateLinks && Array.isArray(rec.affiliateLinks) && rec.affiliateLinks.length > 0
      );
    }

    // Si on a des recommandations après filtrage, les retourner
    if (filteredRecommendations.length > 0) {
      if (process.env.NODE_ENV === 'development') {
      console.log(`✅ ${filteredRecommendations.length} recommandations trouvées pour ${timeframe}`);
      }

      // Ajouter l'information isWishlisted à chaque recommandation
      const recommendationsWithWishlist = filteredRecommendations.map(rec => ({
        ...rec,
        isWishlisted: wishlistedSet.has(`${rec.artistName}|${rec.albumTitle}`),
      }));

      return {
        recommendations: recommendationsWithWishlist,
        actualTimeframe: timeframe
      };
    }

    if (process.env.NODE_ENV === 'development') {
    console.log(`⚠️ Aucune recommandation après filtrage pour ${timeframe}, essai du timeframe suivant`);
    }
  }

  // Aucune recommandation trouvée dans aucun timeframe
  if (process.env.NODE_ENV === 'development') {
  console.log(`❌ Aucune recommandation trouvée dans aucun timeframe pour l'utilisateur ${userId}`);
  }
  return {
    recommendations: [],
    actualTimeframe: requestedTimeframe
  };
}
