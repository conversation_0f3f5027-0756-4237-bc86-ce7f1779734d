import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { recommendations, wishlistItems } from "@/lib/db/schema";
import { eq, desc } from "drizzle-orm";
import { getUserDiscogsCollection, markOwnedAlbums } from "@/lib/album-matching";

/**
 * API Route pour vérifier les recommandations d'un utilisateur
 * Utilisée par le polling côté client
 * Recalcule le statut isOwned en temps réel pour garantir la cohérence
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get("userId");

    if (!userId) {
      return NextResponse.json(
        { error: "userId requis en paramètre" },
        { status: 400 }
      );
    }

    // Récupérer les recommandations de l'utilisateur
    const userRecommendations = await db.query.recommendations.findMany({
      where: eq(recommendations.userId, userId),
      orderBy: [desc(recommendations.listenScore), desc(recommendations.generatedAt)],
      limit: 50,
    });

    // Récupérer les albums en wishlist
    const wishlistedItems = await db.query.wishlistItems.findMany({
      where: eq(wishlistItems.userId, userId),
    });

    // Créer un Set avec la clé composite "artistName|albumTitle"
    const wishlistedSet = new Set(
      wishlistedItems.map(item => `${item.artistName}|${item.albumTitle}`)
    );

    // Récupérer la collection Discogs pour recalculer le statut isOwned
    let recommendationsWithCorrectOwnership = userRecommendations;

    try {
      const discogsCollection = await getUserDiscogsCollection(userId, db);

      if (discogsCollection.length > 0) {
        // Recalculer le statut isOwned en temps réel
        const recalculatedRecommendations = markOwnedAlbums(
          userRecommendations.map(rec => ({
            artistName: rec.artistName,
            albumTitle: rec.albumTitle,
            spotifyAlbumId: rec.spotifyAlbumId || undefined,
            albumCoverUrl: rec.albumCoverUrl || undefined,
            listenScore: rec.listenScore,
            timeframe: rec.timeframe
          })),
          discogsCollection
        );

        // Fusionner les données recalculées avec les données originales
        recommendationsWithCorrectOwnership = userRecommendations.map((original, index) => ({
          ...original,
          isOwned: recalculatedRecommendations[index].isOwned
        }));
      }
    } catch (discogsError) {
      if (process.env.NODE_ENV === 'development') {
      console.warn("⚠️ Erreur lors du recalcul du statut isOwned, utilisation des données en base:", discogsError);
      }
      // En cas d'erreur, utiliser les données existantes en base
    }

    return NextResponse.json({
      success: true,
      count: recommendationsWithCorrectOwnership.length,
      recommendations: recommendationsWithCorrectOwnership.map(rec => ({
        ...rec,
        isWishlisted: wishlistedSet.has(`${rec.artistName}|${rec.albumTitle}`),
      })),
    });

  } catch (error) {
    console.error("💥 Erreur lors de la vérification des recommandations:", error);
    
    return NextResponse.json(
      {
        success: false,
        error: "Erreur lors de la vérification",
        message: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}
