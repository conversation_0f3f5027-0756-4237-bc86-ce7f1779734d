import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { sql } from "drizzle-orm";

/**
 * API TEMPORAIRE pour appliquer les migrations Epic Social V1 en production
 * À SUPPRIMER après migration réussie
 */
export async function POST(request: NextRequest) {
  try {
    console.log("🚀 Application migrations Epic Social V1 en production...");

    const environment = process.env.VERCEL_ENV || process.env.NODE_ENV || 'unknown';
    
    // Vérifier que c'est bien la production
    if (environment !== 'production') {
      return NextResponse.json(
        { error: "Cette API est uniquement pour la production" },
        { status: 403 }
      );
    }

    const result = {
      environment,
      timestamp: new Date().toISOString(),
      operations: [] as any[],
      success: false,
      error: null as string | null
    };

    try {
      // Exécuter la migration Epic Social V1
      console.log("📄 Lecture du fichier de migration...");
      
      // 1. C<PERSON>er les ENUMs
      console.log("🔧 Création des ENUMs...");
      await db.execute(sql`
        DO $$ BEGIN
            CREATE TYPE profile_visibility AS ENUM ('private', 'users_only', 'public');
        EXCEPTION
            WHEN duplicate_object THEN null;
        END $$;
      `);
      result.operations.push("✅ ENUM profile_visibility créé");

      await db.execute(sql`
        DO $$ BEGIN
            CREATE TYPE notification_type AS ENUM ('new_follower');
        EXCEPTION
            WHEN duplicate_object THEN null;
        END $$;
      `);
      result.operations.push("✅ ENUM notification_type créé");

      // 2. Ajouter les colonnes users
      console.log("📋 Ajout des colonnes Epic Social V1...");
      
      await db.execute(sql`
        ALTER TABLE users ADD COLUMN IF NOT EXISTS "profile_visibility" profile_visibility NOT NULL DEFAULT 'users_only';
      `);
      result.operations.push("✅ Colonne profile_visibility ajoutée");

      await db.execute(sql`
        ALTER TABLE users ADD COLUMN IF NOT EXISTS "share_recommendations" BOOLEAN NOT NULL DEFAULT TRUE;
      `);
      result.operations.push("✅ Colonne share_recommendations ajoutée");

      await db.execute(sql`
        ALTER TABLE users ADD COLUMN IF NOT EXISTS "share_wishlist" BOOLEAN NOT NULL DEFAULT TRUE;
      `);
      result.operations.push("✅ Colonne share_wishlist ajoutée");

      await db.execute(sql`
        ALTER TABLE users ADD COLUMN IF NOT EXISTS "share_collection" BOOLEAN NOT NULL DEFAULT TRUE;
      `);
      result.operations.push("✅ Colonne share_collection ajoutée");

      await db.execute(sql`
        ALTER TABLE users ADD COLUMN IF NOT EXISTS "email_on_new_follower" BOOLEAN NOT NULL DEFAULT TRUE;
      `);
      result.operations.push("✅ Colonne email_on_new_follower ajoutée");

      // 3. Créer la table followers
      console.log("🔗 Création table followers...");
      await db.execute(sql`
        CREATE TABLE IF NOT EXISTS followers (
            id TEXT PRIMARY KEY DEFAULT gen_random_uuid(),
            follower_id TEXT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
            following_id TEXT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
            created_at TIMESTAMP DEFAULT NOW() NOT NULL,
            UNIQUE(follower_id, following_id),
            CHECK (follower_id != following_id)
        );
      `);
      result.operations.push("✅ Table followers créée");

      // 4. Créer la table notifications
      console.log("🔔 Création table notifications...");
      await db.execute(sql`
        CREATE TABLE IF NOT EXISTS notifications (
            id TEXT PRIMARY KEY DEFAULT gen_random_uuid(),
            recipient_id TEXT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
            actor_id TEXT REFERENCES users(id) ON DELETE SET NULL,
            type notification_type NOT NULL,
            is_read BOOLEAN NOT NULL DEFAULT FALSE,
            created_at TIMESTAMP DEFAULT NOW() NOT NULL
        );
      `);
      result.operations.push("✅ Table notifications créée");

      // 5. Créer les index de performance
      console.log("⚡ Création des index...");
      await db.execute(sql`CREATE INDEX IF NOT EXISTS idx_users_profile_visibility ON users(profile_visibility);`);
      await db.execute(sql`CREATE INDEX IF NOT EXISTS idx_users_share_recommendations ON users(share_recommendations);`);
      await db.execute(sql`CREATE INDEX IF NOT EXISTS idx_followers_follower_id ON followers(follower_id);`);
      await db.execute(sql`CREATE INDEX IF NOT EXISTS idx_followers_following_id ON followers(following_id);`);
      await db.execute(sql`CREATE INDEX IF NOT EXISTS idx_followers_created_at ON followers(created_at);`);
      await db.execute(sql`CREATE INDEX IF NOT EXISTS idx_notifications_recipient_id ON notifications(recipient_id);`);
      await db.execute(sql`CREATE INDEX IF NOT EXISTS idx_notifications_is_read ON notifications(is_read);`);
      await db.execute(sql`CREATE INDEX IF NOT EXISTS idx_notifications_created_at ON notifications(created_at);`);
      
      result.operations.push("✅ 8 index de performance créés");

      result.success = true;
      console.log("🎉 Migration Epic Social V1 appliquée avec succès en production !");

    } catch (migrationError) {
      result.error = migrationError instanceof Error ? migrationError.message : String(migrationError);
      console.error("❌ Erreur lors de la migration:", migrationError);
    }

    return NextResponse.json(result);

  } catch (error) {
    console.error("❌ Erreur critique lors de la migration:", error);
    return NextResponse.json(
      { 
        error: "Erreur critique lors de la migration", 
        details: error instanceof Error ? error.message : String(error),
        environment: process.env.VERCEL_ENV || process.env.NODE_ENV || 'unknown'
      },
      { status: 500 }
    );
  }
} 