import { redirect } from "next/navigation";
import { getSession } from "@/lib/auth";
import { GeneratingClient } from "@/components/generating/generating-client";
import { db } from "@/lib/db";
import { users } from "@/lib/db/schema";
import { eq } from "drizzle-orm";

/**
 * Page de génération interactive des recommandations
 * Epic 11 - US 11.1: Page dédiée pour l'expérience de génération en temps réel
 */
export default async function GeneratingPage() {
  const session = await getSession();

  if (!session?.user) {
    redirect("/login");
  }

  // Récupérer la langue préférée de l'utilisateur
  const user = await db.query.users.findFirst({
    where: eq(users.id, session.user.id),
    columns: {
      preferredLanguage: true
    }
  });

  const userLanguage = user?.preferredLanguage || 'en';

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800">
      <GeneratingClient userId={session.user.id} userLanguage={userLanguage} />
    </div>
  );
}
