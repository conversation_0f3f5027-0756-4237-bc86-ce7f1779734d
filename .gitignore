# ==================================
# Dependencies
# ==================================
node_modules/
.pnp
.pnp.js
.yarn/install-state.gz

# ==================================
# Next.js
# ==================================
.next/
/.next/
/out/
out/
next-env.d.ts

# ==================================
# Production builds
# ==================================
/build
/dist

# ==================================
# Environment variables
# ==================================
.env*

# ==================================
# Logs
# ==================================
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# ==================================
# Runtime data
# ==================================
pids
*.pid
*.seed
*.pid.lock

# ==================================
# Coverage directory used by tools like istanbul
# ==================================
coverage/
*.lcov

# ==================================
# nyc test coverage
# ==================================
.nyc_output

# ==================================
# Dependency directories
# ==================================
node_modules/
jspm_packages/

# ==================================
# TypeScript
# ==================================
*.tsbuildinfo

# ==================================
# Optional npm cache directory
# ==================================
.npm

# ==================================
# Optional eslint cache
# ==================================
.eslintcache

# ==================================
# Optional stylelint cache
# ==================================
.stylelintcache

# ==================================
# Microbundle cache
# ==================================
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# ==================================
# Optional REPL history
# ==================================
.node_repl_history

# ==================================
# Output of 'npm pack'
# ==================================
*.tgz

# ==================================
# Yarn Integrity file
# ==================================
.yarn-integrity

# ==================================
# parcel-bundler cache (https://parceljs.org/)
# ==================================
.cache
.parcel-cache

# ==================================
# Stores VSCode versions used for testing VSCode extensions
# ==================================
.vscode-test

# ==================================
# yarn v2
# ==================================
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz
.pnp.*

# ==================================
# IDEs and editors
# ==================================
.vscode/
.idea/
*.swp
*.swo
*~

# ==================================
# OS generated files
# ==================================
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# ==================================
# Database
# ==================================
*.db
*.sqlite
*.sqlite3

# ==================================
# Temporary files
# ==================================
*.tmp
*.temp

# ==================================
# Vercel
# ==================================
.vercel

# ==================================
# Turbo
# ==================================
.turbo

# ==================================
# Sentry
# ==================================
.sentryclirc

# ==================================
# Local development
# ==================================
.env.local.example
test-db.js
test-db-simple.js
test-transaction-pooler.js

# ==================================
# Migration files (keep schema but ignore test files)
# ==================================
migrate-auth-schema.sql
run-migration.js

# ==================================
# Additional Next.js patterns
# ==================================
.next/**/*
.vercel/**/*
.turbo/**/*

# ==================================
# Additional cache directories
# ==================================
.pnpm/

# ==================================
# Large media files (if any get added accidentally)
# ==================================
*.mov
*.mp4
*.avi
*.mkv
*.webm
*.zip
*.tar
*.tar.gz
*.rar
*.7z

# ==================================
# Development artifacts
# ==================================
*.log.*
debug.log
error.log

# ==================================
# Project specifications and docs (private)
# ==================================
specs/
docs/

*storybook.log
storybook-static
