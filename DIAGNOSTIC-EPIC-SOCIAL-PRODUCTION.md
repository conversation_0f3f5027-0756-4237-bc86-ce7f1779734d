# 🔍 Diagnostic Epic Social V1 - Production vs Staging

## 📊 **État Actuel (21/07/2025 - 03:02)**

### ✅ **STAGING - STRUCTURE PARFAITE**
```json
{
  "environment": "staging",
  "email_column": {
    "name": "email_on_new_follower",
    "type": "boolean", 
    "format": "snake_case ✅"
  },
  "tables": {
    "followers": {
      "exists": true,
      "columns": ["follower_id", "following_id", "created_at"],
      "constraints": ["FOREIGN KEY", "PRIMARY KEY", "UNIQUE", "CHECK"]
    },
    "notifications": {
      "exists": true, 
      "columns": ["id", "recipient_id", "actor_id", "type", "is_read", "created_at"],
      "constraints": ["FOREIGN KEY", "PRIMARY KEY"]
    }
  },
  "enums": {
    "notification_type": ["new_follower"]
  }
}
```

### ❌ **PRODUCTION - PROBLÈMES IDENTIFIÉS**

**1. 📧 Email notification non envoyé**
- **Cause probable** : Nomenclature incohérente de la colonne
- **Staging** : `email_on_new_follower` (snake_case) ✅
- **Production supposée** : `emailOnNewFollower` (camelCase) ❌

**2. 🔄 Erreur lors du follow (mais fonctionne après refresh)**
- **Cause probable** : Structure des tables incomplète ou nomenclature
- **Symptômes** : JavaScript error côté client, mais données sauvegardées

**3. 🔍 APIs admin inaccessibles**
- **Problème** : Routage/DNS production
- **Impact** : Impossible de diagnostiquer directement

## 🔧 **Solutions Immédiates**

### **Option 1 : Via Supabase Dashboard (RECOMMANDÉE)**

1. **Se connecter à Supabase**
   ```
   https://supabase.com → Projet production
   ```

2. **Diagnostic SQL**
   ```sql
   -- Vérifier la nomenclature colonne email
   SELECT column_name, data_type 
   FROM information_schema.columns 
   WHERE table_name = 'users' 
   AND column_name LIKE '%email%follower%';
   ```

3. **Si la colonne est `emailOnNewFollower` (camelCase) :**
   ```sql
   -- Renommer vers snake_case
   ALTER TABLE users 
   RENAME COLUMN "emailOnNewFollower" TO "email_on_new_follower";
   ```

4. **Vérifier les tables Epic Social V1**
   ```sql
   -- Vérifier table followers
   SELECT EXISTS (
     SELECT FROM information_schema.tables 
     WHERE table_name = 'followers'
   );
   
   -- Vérifier table notifications  
   SELECT EXISTS (
     SELECT FROM information_schema.tables 
     WHERE table_name = 'notifications'
   );
   ```

5. **Si tables manquantes, exécuter la migration complète** :
   ```sql
   -- Contenu de migrations/add-epic-social-v1-schema.sql
   -- (fichier déjà fourni précédemment)
   ```

### **Option 2 : Via psql Local**
```bash
# Diagnostic
psql $DATABASE_URL_PRODUCTION -c "
SELECT column_name, data_type 
FROM information_schema.columns 
WHERE table_name = 'users' 
AND column_name LIKE '%email%follower%';"

# Correction si nécessaire
psql $DATABASE_URL_PRODUCTION -c "
ALTER TABLE users 
RENAME COLUMN \"emailOnNewFollower\" TO \"email_on_new_follower\";"
```

## 🎯 **Test de Validation**

Après correction, tester :

1. **Follow d'un utilisateur** → Doit fonctionner sans erreur
2. **Email de notification** → Doit être envoyé automatiquement
3. **Code follow dans logs** → Vérifier `emailOnNewFollower` vs `email_on_new_follower`

## 🔍 **Code Impacté**

**Fichier** : `app/actions/social.ts` ligne 93
```typescript
const targetUser = await db.query.users.findFirst({
  where: eq(users.id, targetUserId),
  columns: {
    email: true,
    emailOnNewFollower: true,  // <-- Propriété JavaScript (camelCase)
    name: true,
    preferredLanguage: true,
  },
});
```

**Schéma Drizzle** : `lib/db/schema.ts` ligne 25
```typescript
emailOnNewFollower: boolean('email_on_new_follower').default(true).notNull(),
//      ↑ Propriété JS               ↑ Colonne DB (snake_case)
```

**Le mapping est correct**, mais la base de données doit utiliser `email_on_new_follower`.

## 📋 **Comparaison Finale**

| Aspect | Staging | Production (attendu) |
|--------|---------|---------------------|
| Colonne email | `email_on_new_follower` ✅ | `emailOnNewFollower` ❌ |
| Table followers | ✅ Structure complète | ❓ À vérifier |
| Table notifications | ✅ Structure complète | ❓ À vérifier |
| ENUMs | ✅ Tous présents | ❓ À vérifier |
| Emails | ✅ Fonctionnels | ❌ Non envoyés |
| Follow action | ✅ Sans erreur | ❌ Erreur JS |

## 🚀 **Actions Suivantes**

1. **URGENT** : Corriger la nomenclature `emailOnNewFollower` → `email_on_new_follower`
2. **Valider** : Tester follow + email notification
3. **Monitorer** : Vérifier logs d'erreurs
4. **Nettoyer** : Supprimer APIs temporaires après validation

---

**Date** : 21 juillet 2025, 03:02  
**Statut** : 🔴 **CORRECTION REQUISE**  
**Action** : Renommer colonne email en production via Supabase 