import type { StorybookConfig } from "@storybook/nextjs-vite";

const config: StorybookConfig = {
  // Pointer directement vers notre dossier de composants pour la co-localisation
  stories: ["../components/**/*.stories.@(js|jsx|mjs|ts|tsx)"],
  
  // Configuration minimale pour éviter les conflits de versions
  addons: [
    "@storybook/addon-links",
    "@storybook/addon-docs",
    "@chromatic-com/storybook"
  ],

  framework: {
    name: "@storybook/nextjs-vite",
    options: {},
  },

  // Rendre le dossier public accessible pour les images (logos, etc.)
  staticDirs: ["../public"], 
};
export default config;