# Stream2Spin Assets CDN

Ce projet héberge les assets statiques pour les emails de Stream2Spin.

## Déploiement

1. Créer un nouveau projet Vercel
2. Connecter ce dossier comme source
3. Configurer le domaine `assets.stream2spin.com`
4. Déployer

## URLs des assets

- Logo: `https://assets.stream2spin.com/Stream2Spin_white_logo.svg`
- Icône cœur: `https://assets.stream2spin.com/heart-icon.svg`
- Icône disque: `https://assets.stream2spin.com/disc3-icon.svg`

## Configuration DNS

Ajouter un enregistrement CNAME :
- Nom: `assets`
- Valeur: `cname.vercel-dns.com`
