import { useEffect, useRef, useCallback, useState } from 'react';

export function useAutoSave<T>(
  data: T,
  saveFunction: (data: T) => Promise<{ success: boolean; error?: string }>,
  delay: number = 1000,
  isEqual?: (a: T, b: T) => boolean
) {
  const [isSaving, setIsSaving] = useState(false);
  const lastSavedDataRef = useRef<T>(data);
  const timeoutRef = useRef<NodeJS.Timeout | undefined>(undefined);
  const initialDataRef = useRef<T>(data);

  // Fonction de comparaison par défaut (JSON.stringify)
  const compareData = useCallback((a: T, b: T) => {
    if (isEqual) return isEqual(a, b);
    return JSON.stringify(a) === JSON.stringify(b);
  }, [isEqual]);

  const performSave = useCallback(async (dataToSave: T) => {
    if (compareData(dataToSave, lastSavedDataRef.current)) {
      return; // Pas de changement, ne pas sauvegarder
    }

    setIsSaving(true);
    try {
      const result = await saveFunction(dataToSave);
      if (result.success) {
        lastSavedDataRef.current = dataToSave;
      }
      return result;
    } finally {
      setIsSaving(false);
    }
  }, [saveFunction, compareData]);

  useEffect(() => {
    // Ne pas sauvegarder si les données n'ont pas changé depuis l'initialisation
    if (compareData(data, initialDataRef.current)) {
      return;
    }

    // Annuler le timeout précédent
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    // Programmer une nouvelle sauvegarde
    timeoutRef.current = setTimeout(() => {
      performSave(data);
    }, delay);

    // Nettoyer le timeout
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [data, delay, performSave, compareData]);

  // Nettoyer les timeouts au démontage
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  return {
    isSaving,
    hasUnsavedChanges: !compareData(data, lastSavedDataRef.current),
    forceSave: () => performSave(data),
  };
} 