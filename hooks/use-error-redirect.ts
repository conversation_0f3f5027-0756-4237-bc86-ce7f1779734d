"use client";

import { useEffect, useState } from "react";

interface ErrorRedirectResult {
  ctaText: string;
  ctaLink: string;
}

/**
 * Hook pour gérer la logique de redirection conditionnelle des pages d'erreur
 * selon la spécification US-128
 */
export function useErrorRedirect(fromUrl?: string): ErrorRedirectResult {
  const [redirectInfo, setRedirectInfo] = useState<ErrorRedirectResult>({
    ctaText: "Retourner aux recommandations",
    ctaLink: "/recommendations"
  });

  useEffect(() => {
    let sourceUrl = fromUrl;

    // Si pas de fromUrl fourni, essayer de le récupérer depuis le referrer ou l'URL actuelle
    if (!sourceUrl && typeof window !== "undefined") {
      // Essayer d'abord le referrer
      const referrer = document.referrer;
      const currentOrigin = window.location.origin;
      
      if (referrer && referrer.startsWith(currentOrigin)) {
        sourceUrl = new URL(referrer).pathname;
      } else {
        // Fallback sur l'URL actuelle (moins fiable pour les erreurs)
        sourceUrl = window.location.pathname;
      }
    }

    // Appliquer la logique conditionnelle selon US-128
    if (sourceUrl === "/recommendations") {
      setRedirectInfo({
        ctaText: "Voir mes envies",
        ctaLink: "/mes-envies" // Redirige vers /mes-envies qui redirige vers /wishlist
      });
    } else {
      setRedirectInfo({
        ctaText: "Retourner aux recommandations",
        ctaLink: "/recommendations"
      });
    }
  }, [fromUrl]);

  return redirectInfo;
}

/**
 * Utilitaire pour extraire l'URL source depuis les paramètres de recherche
 */
export function getSourceUrlFromSearchParams(searchParams: URLSearchParams): string | undefined {
  return searchParams.get("from") || undefined;
}

/**
 * Utilitaire pour construire une URL d'erreur avec le contexte source
 */
export function buildErrorUrl(errorType: "permission" | "server", sourceUrl?: string): string {
  const baseUrl = `/error/${errorType}`;
  
  if (sourceUrl) {
    return `${baseUrl}?from=${encodeURIComponent(sourceUrl)}`;
  }
  
  return baseUrl;
}
