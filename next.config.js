const withNextIntl = require('next-intl/plugin')('./i18n.ts');

/** @type {import('next').NextConfig} */
const nextConfig = {
  // App Router est maintenant stable dans Next.js 15

  // Configuration pour le développement local
  allowedDevOrigins: ['127.0.0.1:3000', 'localhost:3000'],

  // Exclure le dossier admin du build principal
  webpack: (config, { isServer }) => {
    config.resolve.alias = {
      ...config.resolve.alias,
    };

    // Ignorer le dossier admin lors du build
    config.watchOptions = {
      ...config.watchOptions,
      ignored: ['**/admin/**', '**/node_modules/**'],
    };

    return config;
  },

  // Configuration des images pour permettre les domaines Spotify et Discogs
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'i.scdn.co',
        port: '',
        pathname: '/image/**',
      },
      {
        protocol: 'https',
        hostname: 'platform-lookaside.fbsbx.com',
        port: '',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'scontent.xx.fbcdn.net',
        port: '',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'i.discogs.com',
        port: '',
        pathname: '/**',
      },
    ],
  },

  // Headers pour empêcher l'indexation
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Robots-Tag',
            value: 'noindex, nofollow, nocache, noarchive, nosnippet, noimageindex'
          },
        ],
      },
    ]
  },

  eslint: {
    ignoreDuringBuilds: true,
  },

  serverExternalPackages: ['sharp'],
};

module.exports = withNextIntl(nextConfig);
