const { Pool } = require('pg');
require('dotenv').config();

(async () => {
  // Connexion à la base de données
  const pool = new Pool({
    connectionString: process.env.DATABASE_URL
  });

  try {
    console.log('🔍 Test de debug de la Server Action followUser...');
    
    const sourceUserId = '33d92260-869f-4d7b-aac2-94ff6b03c240'; // Votre ID
    const targetUserId = '8fb016de-48e9-4687-8384-b4844302ee53'; // ID du profil public
    
    console.log(`📋 Source: ${sourceUserId}`);
    console.log(`📋 Target: ${targetUserId}`);
    
    // 1. Vérifier que les utilisateurs existent
    console.log('\n1. Vérification des utilisateurs...');
    
    const sourceUserResult = await pool.query('SELECT id, name FROM users WHERE id = $1', [sourceUserId]);
    const targetUserResult = await pool.query('SELECT id, name FROM users WHERE id = $1', [targetUserId]);
    
    console.log(`   Source user: ${sourceUserResult.rows.length > 0 ? '✅ Trouvé' : '❌ Non trouvé'} - ${sourceUserResult.rows[0]?.name || 'N/A'}`);
    console.log(`   Target user: ${targetUserResult.rows.length > 0 ? '✅ Trouvé' : '❌ Non trouvé'} - ${targetUserResult.rows[0]?.name || 'N/A'}`);
    
    if (sourceUserResult.rows.length === 0 || targetUserResult.rows.length === 0) {
      console.log('❌ L\'un des utilisateurs n\'existe pas');
      return;
    }
    
    // 2. Vérifier la structure de la table followers
    console.log('\n2. Vérification de la table followers...');
    
    const tableInfoResult = await pool.query(`
      SELECT column_name, data_type, is_nullable
      FROM information_schema.columns 
      WHERE table_name = 'followers'
      ORDER BY ordinal_position;
    `);
    
    console.log('   Colonnes de la table followers:');
    tableInfoResult.rows.forEach(col => {
      console.log(`     - ${col.column_name} (${col.data_type})`);
    });
    
    // 3. Vérifier s'il y a déjà une relation
    console.log('\n3. Vérification relation existante...');
    
    const existingFollowResult = await pool.query(`
      SELECT * FROM followers 
      WHERE follower_id = $1 AND following_id = $2
    `, [sourceUserId, targetUserId]);
    
    console.log(`   Relation existante: ${existingFollowResult.rows.length > 0 ? '✅ Oui' : '❌ Non'}`);
    
    // 4. Tenter l'insertion (simulation de la Server Action)
    console.log('\n4. Test d\'insertion...');
    
    try {
      const insertResult = await pool.query(`
        INSERT INTO followers (follower_id, following_id, created_at) 
        VALUES ($1, $2, NOW())
        ON CONFLICT (follower_id, following_id) DO NOTHING
        RETURNING *;
      `, [sourceUserId, targetUserId]);
      
      console.log('   ✅ Insertion réussie !', insertResult.rows);
      
      // Vérifier que ça a marché
      const verifyResult = await pool.query(`
        SELECT * FROM followers 
        WHERE follower_id = $1 AND following_id = $2
      `, [sourceUserId, targetUserId]);
      
      console.log(`   Relation créée: ${verifyResult.rows.length > 0 ? '✅ Oui' : '❌ Non'}`);
      
    } catch (insertError) {
      console.error('   ❌ Erreur lors de l\'insertion:', insertError.message);
      console.error('   Stack:', insertError.stack);
    }
    
  } catch (error) {
    console.error('❌ Erreur générale:', error.message);
    console.error('Stack:', error.stack);
  } finally {
    await pool.end();
  }
})(); 