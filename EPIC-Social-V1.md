# Epic: Fonctionnalités Sociales V1 - Fe<PERSON>, Suivi et Découverte

## 🎯 Objectif Principal
Transformer Stream2Spin en une plateforme communautaire où les utilisateurs peuvent devenir des influenceurs musicaux. L'objectif est de permettre aux utilisateurs de se connecter, de consulter un feed d'activité pertinent trié par pertinence, et de découvrir de nouveaux profils, tout en étant optimisé pour une faible fréquence de connexion grâce à un rafraîchissement des données "Juste-à-Temps".

## 📈 Indicateurs de Succès
- Augmentation du temps de session moyen et de la fréquence des visites.
- Nombre de relations de suivi (follows) créées.
- Taux d'interaction (clics, ajouts à la wishlist) sur les albums du feed social.
- Taux d'adoption des fonctionnalités de partage et de visibilité.

---

## 📚 User Stories (Structurées par Priorité)

---
### **Priorité 0 : Fondations Techniques (Prérequis)**
*(Ces tâches sont principalement techniques, sans impact visible immédiat pour l'utilisateur, mais essentielles pour la suite.)*

#### **US-01 : Mise à jour du Schéma de la Base de Données**
> **En tant que** développeur, **je veux** mettre à jour le schéma de la base de données **afin de** supporter les nouvelles entités et relations sociales.

##### 🔧 Spécifications Techniques Détaillées

**Fichier à Modifier :** `lib/db/schema.ts`

**1. Définir les nouveaux ENUMs**

Ajouter les ENUMs suivants pour la visibilité du profil et les types de notifications.

```typescript
// lib/db/schema.ts

export const profileVisibilityEnum = pgEnum('profile_visibility', ['private', 'users_only', 'public']);
export const notificationTypeEnum = pgEnum('notification_type', ['new_follower']);
```

**2. Ajouter les Nouvelles Tables**

Ajouter les définitions de table Drizzle suivantes au schéma.

```typescript
// lib/db/schema.ts

// ... après la définition de la table 'publicListAnalytics'

/**
 * Table pour gérer les relations de suivi entre utilisateurs.
 */
export const followers = pgTable('followers', {
  followerId: text('follower_id').notNull().references(() => users.id, { onDelete: 'cascade' }),
  followingId: text('following_id').notNull().references(() => users.id, { onDelete: 'cascade' }),
  createdAt: timestamp('created_at', { mode: 'date' }).defaultNow().notNull(),
}, (table) => ({
  pk: primaryKey({ columns: [table.followerId, table.followingId] }),
}));

/**
 * Table pour les notifications in-app et email.
 */
export const notifications = pgTable('notifications', {
  id: text('id').primaryKey().$defaultFn(() => crypto.randomUUID()),
  recipientId: text('recipient_id').notNull().references(() => users.id, { onDelete: 'cascade' }),
  actorId: text('actor_id').notNull().references(() => users.id, { onDelete: 'cascade' }),
  type: notificationTypeEnum('type').notNull(),
  isRead: boolean('is_read').default(false).notNull(),
  createdAt: timestamp('created_at', { mode: 'date' }).defaultNow().notNull(),
});

/**
 * Table pour archiver toutes les recommandations et nourrir l'algorithme de matching.
 */
export const recommendationHistory = pgTable('recommendation_history', {
  id: bigserial('id', { mode: 'number' }).primaryKey(),
  userId: text('user_id').notNull().references(() => users.id, { onDelete: 'cascade' }),
  artistName: text('artist_name').notNull(),
  albumTitle: text('album_title').notNull(),
  spotifyAlbumId: text('spotify_album_id'),
  listenScore: integer('listen_score').notNull(),
  timeframe: text('timeframe').notNull(),
  generatedAt: timestamp('generated_at', { mode: 'date' }).notNull().defaultNow(),
  // On pourra ajouter ici les caractéristiques audio (danceability, energy, etc.) dans une future itération.
});
```

**3. Mettre à jour la Table `users`**

Modifier la définition de la table `users` pour inclure les nouveaux champs de visibilité.

```typescript
// lib/db/schema.ts

// Dans la définition de la table 'users'
export const users = pgTable('users', {
  // ... colonnes existantes (id, name, email, etc.)
  
  // AJOUTER les colonnes suivantes (par exemple, après 'publicListId')

  // --- Colonnes pour les fonctionnalités sociales (Epic Social V1) ---
  profileVisibility: profileVisibilityEnum('profile_visibility').default('users_only').notNull(),
  shareRecommendations: boolean('share_recommendations').default(true).notNull(),
  shareWishlist: boolean('share_wishlist').default(true).notNull(),
  shareCollection: boolean('share_collection').default(true).notNull(),

  // ... reste des colonnes existantes (publicProfileEnabled, etc.)
});
```
*Note : Les colonnes `public...Enabled` existantes pourraient être fusionnées ou remplacées par cette nouvelle logique à terme, mais pour une V1, nous ajoutons de nouvelles colonnes pour ne pas casser l'existant.*

##### 🔄 Processus de Migration

1.  **Modifier le Schéma** : Appliquer les modifications de code ci-dessus dans le fichier `lib/db/schema.ts`.
2.  **Générer la Migration** : Exécuter la commande `pnpm db:generate` dans le terminal. Drizzle Kit va comparer le schéma actuel de la base de données avec le nouveau schéma défini dans le code et générer un nouveau fichier de migration SQL dans le dossier `migrations/`.
3.  **Vérifier le Script SQL** : Ouvrir le nouveau fichier `.sql` généré. Il doit contenir les instructions `CREATE TABLE` pour `followers`, `notifications`, `recommendation_history` et une instruction `ALTER TABLE "users" ADD COLUMN ...` pour les nouveaux champs de visibilité. Vérifier que les valeurs par défaut sont correctement définies.
4.  **Appliquer la Migration** : Exécuter `pnpm db:migrate` pour appliquer la nouvelle migration à votre base de données de développement locale et valider que tout fonctionne comme prévu.

---
### **Priorité 1 : Noyau de l'Interaction (MVP)**
*(Le socle de l'interaction sociale devient visible et fonctionnel.)*

#### **US-02 : Implémentation du Système de Suivi (Follow/Unfollow)**
> **En tant qu'** utilisateur, **je veux** pouvoir suivre et ne plus suivre d'autres utilisateurs depuis leur profil, **afin de** construire mon réseau social.

##### 🎨 Design UX/UI
- **Page** : `/u/[id]`.
- **Composant** : Un bouton unique et clair sur le profil, qui pourrait être `components/social/FollowButton.tsx`.
    - **État par défaut** : Bouton "Suivre" (style `default` de `components/ui/button`).
    - **État après suivi** : Bouton "Ne plus suivre" (style `secondary` ou `outline`).
    - **Pendant l'action** : Le bouton doit afficher un spinner ou un texte comme "Chargement..." et être désactivé pour éviter les clics multiples.
- **Interaction** : Utiliser une "Optimistic UI". Le bouton change d'état visuel *instantanément* au clic, avant même la confirmation du serveur, pour une sensation de réactivité maximale. En cas d'erreur, l'état initial est restauré et une notification (toast) est affichée.

##### 🔧 Spécifications Techniques Détaillées

**1. Création des Server Actions (`app/actions/social.ts`)**

Créer un nouveau fichier pour héberger les actions liées aux fonctionnalités sociales.

```typescript
// app/actions/social.ts
'use server';

import { db } from '@/lib/db';
import { followers } from '@/lib/db/schema';
import { auth } from '@/auth'; // Assurez-vous que votre config auth exporte `auth`
import { and, eq } from 'drizzle-orm';
import { revalidatePath } from 'next/cache';

export async function followUser(targetUserId: string) {
  const session = await auth();
  if (!session?.user?.id) {
    return { error: 'Non autorisé. Vous devez être connecté.' };
  }

  const sourceUserId = session.user.id;

  if (!targetUserId) {
    return { error: "ID de l'utilisateur cible manquant." };
  }

  if (sourceUserId === targetUserId) {
    return { error: 'Vous ne pouvez pas vous suivre vous-même.' };
  }

  try {
    // Utiliser `onConflictDoNothing` pour gérer élégamment les tentatives de suivi multiples.
    await db.insert(followers).values({
      followerId: sourceUserId,
      followingId: targetUserId,
    }).onConflictDoNothing();

    revalidatePath(`/u/${targetUserId}`);
    revalidatePath('/social'); // Invalider aussi la page sociale pour les suggestions
    return { success: true };

  } catch (error) {
    console.error("Erreur lors du suivi de l'utilisateur:", error);
    return { error: 'Une erreur est survenue. Veuillez réessayer.' };
  }
}

export async function unfollowUser(targetUserId: string) {
  const session = await auth();
  if (!session?.user?.id) {
    return { error: 'Non autorisé. Vous devez être connecté.' };
  }

  const sourceUserId = session.user.id;

  if (!targetUserId) {
    return { error: "ID de l'utilisateur cible manquant." };
  }

  try {
    await db.delete(followers).where(
      and(
        eq(followers.followerId, sourceUserId),
        eq(followers.followingId, targetUserId)
      )
    );

    revalidatePath(`/u/${targetUserId}`);
    revalidatePath('/social');
    return { success: true };

  } catch (error) {
    console.error("Erreur lors de l'arrêt du suivi de l'utilisateur:", error);
    return { error: 'Une erreur est survenue. Veuillez réessayer.' };
  }
}
```

**2. Création du Composant Frontend (`components/social/FollowButton.tsx`)**

Ce composant client gérera l'état de l'interface et appellera les Server Actions.

```typescript
// components/social/FollowButton.tsx
'use client';

import { useState, useTransition } from 'react';
import { Button } from '@/components/ui/button';
import { followUser, unfollowUser } from '@/app/actions/social';
import { toast } from 'sonner'; // Utilisation de sonner pour les toasts
import { useTranslations } from 'next-intl';

interface FollowButtonProps {
  targetUserId: string;
  isFollowingInitial: boolean;
}

export function FollowButton({ targetUserId, isFollowingInitial }: FollowButtonProps) {
  const t = useTranslations('Social');
  const [isFollowing, setIsFollowing] = useState(isFollowingInitial);
  const [isPending, startTransition] = useTransition();

  const handleFollow = () => {
    startTransition(async () => {
      const previousState = isFollowing;
      setIsFollowing(!previousState);

      const action = previousState ? unfollowUser : followUser;
      const result = await action(targetUserId);

      if (result.error) {
        setIsFollowing(previousState);
        toast.error(t('errors.generic')); // Utiliser une clé de traduction pour l'erreur
      }
    });
  };

  return (
    <Button
      onClick={handleFollow}
      disabled={isPending}
      variant={isFollowing ? 'secondary' : 'default'}
      size="sm"
    >
      {isPending ? t('loading') : isFollowing ? t('unfollow') : t('follow')}
    </Button>
  );
}
```

**3. Intégration dans la Page Profil (`app/u/[id]/page.tsx`)**

La page de profil (qui est un Server Component) devra récupérer l'état de suivi initial et le passer en props au `FollowButton`.

```typescript
// app/u/[id]/page.tsx (extrait de la logique)

import { FollowButton } from '@/components/social/FollowButton';
import { auth } from '@/auth';
import { db } from '@/lib/db';
import { followers } from '@/lib/db/schema';
import { and, eq } from 'drizzle-orm';

// Dans la fonction de la page...
export default async function UserProfilePage({ params }: { params: { id: string } }) {
  const session = await auth();
  const profileUserId = params.id;
  // const profileUser = ... (logique pour récupérer les données de l'utilisateur du profil)

  let isFollowing = false;
  // On vérifie la relation de suivi uniquement si un utilisateur est connecté
  if (session?.user?.id) {
    const followRelation = await db.query.followers.findFirst({
      where: and(
        eq(followers.followerId, session.user.id),
        eq(followers.followingId, profileUserId)
      ),
    });
    isFollowing = !!followRelation;
  }

  // ... (reste du JSX de la page)

  return (
    <div>
      {/* ... autres infos du profil (avatar, nom) ... */}

      {/* Le bouton ne s'affiche que si un utilisateur est connecté ET qu'il ne regarde pas son propre profil */}
      {session?.user?.id && session.user.id !== profileUserId && (
        <FollowButton
          targetUserId={profileUserId}
          isFollowingInitial={isFollowing}
        />
      )}
    </div>
  );
}
```

##### 🌍 Internationalisation (i18n)
- **Principe** : Tous les textes visibles par l'utilisateur doivent utiliser le hook `useTranslations` de `next-intl`.
- **Fichiers à modifier** : `messages/fr.json`, `messages/en.json`.
- **Nouvelles clés à ajouter** :
  ```json
  {
    "Social": {
      "follow": "Suivre",
      "unfollow": "Ne plus suivre",
      "loading": "Chargement...",
      "errors": {
        "generic": "Une erreur est survenue. Veuillez réessayer.",
        "unauthorized": "Non autorisé. Vous devez être connecté.",
        "selfFollow": "Vous ne pouvez pas vous suivre vous-même."
      }
    }
  }
  ```
  *(Les traductions anglaises correspondantes doivent aussi être ajoutées)*

##### 🔒 RGPD & Sécurité
- Les Server Actions valident systématiquement la session côté serveur, empêchant toute action non authentifiée.
- La logique métier (ne pas se suivre soi-même) est gérée côté serveur pour être infaillible.
- L'ID de l'utilisateur cible est le seul paramètre passé, limitant la surface d'attaque.
- L'état de suivi initial est déterminé côté serveur, garantissant que le bouton affiche l'état correct au premier chargement.

---
#### **US-03 : Refonte du Profil Public et Modales Sociales**
> **En tant qu'** utilisateur, **je veux** voir les statistiques de suivi sur un profil et cliquer dessus **afin d'** afficher la liste des abonnés et abonnements dans une fenêtre modale.

##### 🎨 Design UX/UI
- **Page** : `/u/[id]`.
- **Déclencheurs** : Les textes affichant le nombre de "followers" et "following" sur la page de profil doivent être rendus comme des éléments interactifs (par exemple, un lien ou un bouton avec un style `link`). Au survol, ils doivent changer d'apparence pour indiquer qu'ils sont cliquables.
- **Interaction** :
    - Un clic sur "followers" ou "following" ouvre une fenêtre modale unique (`Dialog`).
    - La modale doit s'ouvrir avec l'onglet correspondant déjà sélectionné (si je clique sur "followers", l'onglet "Followers" est actif).
- **Design de la Modale** :
    - **Titre** : Le titre de la modale doit être dynamique, par exemple : "Relations de [Nom de l'utilisateur]".
    - **Contenu** : Utiliser un composant `Tabs` avec deux onglets : "Followers" et "Following".
    - **Listes** : Chaque onglet contient une liste verticale et scrollable d'utilisateurs.
    - **Item de la liste** : Chaque ligne de la liste doit afficher :
        1.  L'`Avatar` de l'utilisateur.
        2.  Le `name` de l'utilisateur.
        3.  Le composant `FollowButton` (de l'US-02) pour permettre une action de suivi/arrêt de suivi directement depuis la modale.

##### 🔧 Spécifications Techniques Détaillées

**1. Logique Côté Serveur (Server-Side)**
- **Calcul des Compteurs** : Sur la page de profil (`app/u/[id]/page.tsx`), le serveur doit calculer le nombre total de followers et de followings pour l'utilisateur du profil. Ces comptes seront passés en props à un composant client.
- **Nouvelles Fonctions d'Action** : Dans `app/actions/social.ts`, créer deux nouvelles fonctions asynchrones :
    - `getFollowers(targetUserId: string): Promise<User[]>`
    - `getFollowing(targetUserId: string): Promise<User[]>`
- **Logique des Fonctions** :
    - Ces fonctions doivent requêter la table `followers` pour récupérer les listes correspondantes.
    - **Crucial** : Pour chaque utilisateur retourné dans la liste, la fonction doit aussi déterminer si l'utilisateur *actuellement connecté* le suit déjà. Cette information (`isFollowing`) est indispensable pour que le `FollowButton` dans la modale ait le bon état initial. Cela peut être fait via une sous-requête ou une jointure supplémentaire.
    - Elles doivent retourner un tableau d'objets, chaque objet contenant `id`, `name`, `image`, et `isFollowing` (du point de vue de l'utilisateur connecté).

**2. Structure des Composants (Client-Side)**
- **Composant Déclencheur (`SocialStats.tsx`)** :
    - Créer un composant client qui reçoit les comptes de followers/following en props.
    - Il affiche les textes cliquables.
    - Il gère l'état d'ouverture de la modale (`open`/`setOpen`) et l'onglet actif par défaut (`defaultTab`).
- **Composant Modale (`SocialListModal.tsx`)** :
    - Ce composant client reçoit l'ID de l'utilisateur cible (`targetUserId`) et l'onglet actif par défaut.
    - Il contient les composants `Dialog` et `Tabs` de `shadcn/ui`.
    - **Data Fetching Côté Client** : La modale doit être "intelligente". Elle ne charge les données (via les actions `getFollowers`/`getFollowing`) que lorsque l'onglet correspondant devient actif. Utiliser un hook `useEffect` ou une librairie de data-fetching client comme SWR / TanStack Query est recommandé pour gérer les états de chargement, d'erreur, et la mise en cache.
    - La modale fait le rendu de la liste des utilisateurs et passe les props nécessaires à chaque `FollowButton`.

**3. Intégration**
- La page serveur `/u/[id]/page.tsx` calcule les comptes et les passe au composant client `SocialStats.tsx`.
- `SocialStats.tsx` gère l'affichage des stats et le clic pour ouvrir `SocialListModal.tsx`.
- `SocialListModal.tsx` gère son propre cycle de vie de récupération de données et d'affichage.

##### 🌍 Internationalisation (i18n)
- **Principe** : Tous les textes de l'interface de la modale et des statistiques doivent être traduits.
- **Nouvelles clés à ajouter** :
  ```json
  {
    "Profile": {
      "followersLabel": "{count, plural, =0 {follower} =1 {follower} other {followers}}",
      "followingLabel": "following"
    },
    "Social": {
      "modalTitle": "Connections for {name}",
      "followersTab": "Followers",
      "followingTab": "Following"
    }
  }
  ```
  *(Note : La traduction de "following" est contextuelle en français. On utilisera "abonnements". Pour "followers", "abonnés". Le format ICU `plural` est essentiel.)*

##### 🔒 RGPD & Sécurité
- Les fonctions `getFollowers` et `getFollowing` doivent impérativement vérifier les paramètres de visibilité du `targetUserId` avant de retourner des données. Si le profil est privé, elles doivent retourner une erreur ou un tableau vide.
- Le `FollowButton` ne doit pas être affiché pour l'utilisateur connecté consultant sa propre ligne dans une liste (on ne peut pas se suivre soi-même).
- Toutes les actions de suivi/arrêt de suivi dans la modale sont gérées par les Server Actions sécurisées de l'US-02, garantissant qu'aucune logique métier n'est exposée côté client.

---
#### **US-04 : Implémentation de la Recherche d'Utilisateurs**
> **En tant qu'** utilisateur, **je veux** un champ de recherche pour trouver d'autres utilisateurs par leur nom, **afin de** pouvoir découvrir et suivre des personnes spécifiques.

##### 🎨 Design UX/UI
- **Emplacement** : La barre de recherche doit être un élément proéminent sur la page `/social`, par exemple en haut de la colonne latérale droite ou directement sous le titre de la page.
- **Interaction** :
    - La recherche doit être "live" : les résultats s'affichent et se mettent à jour au fur et à mesure de la frappe de l'utilisateur, sans nécessiter de clic sur un bouton "Rechercher".
    - Un état de chargement (spinner) doit être visible pendant que la recherche est en cours.
    - Si aucun résultat n'est trouvé, un message clair comme "Aucun utilisateur trouvé" doit s'afficher.
- **Résultats** : La liste des résultats doit être simple et efficace, chaque item affichant l'avatar et le nom de l'utilisateur. La liste entière doit être un lien ou chaque item doit être un lien individuel menant au profil public de l'utilisateur (`/u/[id]`).

##### 🔧 Spécifications Techniques et Principes

**1. Composant Frontend (`UserSearch.tsx`)**
- **Type** : Composant client (`'use client'`).
- **Responsabilités** :
    - Gérer l'état du champ de recherche (la `query`).
    - **Debouncing** : Implémenter une logique de "debounce" (par exemple avec un hook `use-debounce`) pour ne déclencher l'appel à l'API que lorsque l'utilisateur a cessé de taper pendant un court instant (ex: 300ms). C'est **essentiel** pour la performance.
    - Gérer les états de l'interface : état initial, état de chargement, état avec résultats, et état vide/sans résultats.
    - Appeler la Server Action de recherche.
    - Afficher la liste des résultats retournés par l'action.

**2. Server Action (`searchUsers`)**
- **Fichier** : `app/actions/social.ts`.
- **Signature du Contrat** :
  ```typescript
  // Définir le type de retour pour la clarté du contrat d'API
  type UserSearchResult = {
    id: string;
    name: string | null;
    image: string | null;
  };

  export async function searchUsers(query: string): Promise<UserSearchResult[]>;
  ```
- **Logique Métier (Règles à implémenter)** :
    - **Sécurité** : L'action doit d'abord vérifier si un utilisateur est connecté. La recherche est une fonctionnalité réservée aux membres.
    - **Validation** : Si la `query` est vide ou trop courte (ex: < 2 caractères), retourner un tableau vide pour éviter des recherches inutiles.
    - **Requête DB** :
        - Utiliser une requête `ILIKE` (insensible à la casse) sur la colonne `name` de la table `users`.
        - **Filtrage de Confidentialité** : Exclure impérativement les utilisateurs dont `profileVisibility` est `'private'`.
        - **Auto-exclusion** : Exclure l'utilisateur qui effectue la recherche de ses propres résultats.
    - **Performance** :
        - **Pagination** : Limiter le nombre de résultats retournés (ex: `LIMIT 10`).
        - **Indexation** : Pour des performances optimales en production, il est recommandé d'ajouter un index sur la colonne `name` de la table `users`. Un index GIN avec `pg_trgm` est idéal pour les recherches `ILIKE`.

##### 🌍 Internationalisation (i18n)
- **Principe** : Les textes du champ de recherche et des états doivent être traduits.
- **Nouvelles clés à ajouter** :
  ```json
  {
    "Social": {
      "searchPlaceholder": "Rechercher un utilisateur...",
      "searchNoResults": "Aucun utilisateur trouvé."
    }
  }
  ```

##### 🔒 RGPD & Sécurité
- **Données Exposées** : L'API ne doit retourner que le minimum d'informations publiques nécessaires à l'affichage : `id`, `name`, `image`. Ne jamais retourner l'email ou d'autres données sensibles.
- **Prévention des Abus** : L'endpoint (même s'il s'agit d'une Server Action) doit être protégé contre les abus. Envisager une limitation de débit (rate limiting) si l'application devait grandir, pour empêcher un acteur malveillant de "scraper" la liste des utilisateurs en testant toutes les combinaisons de lettres.

---
### **Priorité 2 : Cœur de l'Expérience (Feed & Engagement)**
*(L'utilisateur peut maintenant consommer le contenu généré par son réseau.)*

#### **US-05 : Création du Feed Social sur la Page /social**
> **En tant qu'** utilisateur, **je veux** une page `/social` qui affiche un feed des recommandations d'albums des personnes que je suis, **afin de** découvrir de la musique à travers mon réseau.

##### 🎨 Design UX/UI
- **Page** : `/social`.
- **Layout** : La page doit être structurée pour mettre en avant le feed. Une mise en page en deux colonnes sur grand écran est recommandée : le feed occupant l'espace principal (ex: 70-75%), et une colonne latérale pour les modules de recherche (US-04) et de suggestions (future US).
- **Feed** : Le feed doit être conçu comme une liste verticale d'items. Pour la V1, une pagination simple (un bouton "Voir plus" à la fin de la liste) est préférable à un défilement infini, car elle est plus simple à implémenter et plus performante.
- **Carte d'Album (`AlbumCard`)** : La carte d'album existante doit être adaptée pour le contexte social. Elle doit inclure une nouvelle section en haut de la carte indiquant clairement l'origine de la recommandation : `Recommandé par [Avatar] [Nom de l'utilisateur]`. Le nom de l'utilisateur doit être un lien vers son profil (`/u/[id]`).
- **État Vide** : Si l'utilisateur ne suit personne, le feed doit afficher un état vide clair et actionnable, l'invitant à trouver des utilisateurs à suivre via le module de recherche.

##### 🔧 Spécifications Techniques et Principes

**1. Composant Principal de la Page (`app/social/page.tsx`)**
- **Type** : Server Component.
- **Responsabilités** :
    - **Récupération Initiale des Données** : Au premier chargement, ce composant est responsable d'appeler la fonction serveur pour récupérer la première "page" de résultats du feed.
    - **Déclenchement du Rafraîchissement Asynchrone** : C'est ici que la logique de l'US-07 ("Juste-à-Temps") sera initiée. Le composant appellera une action qui, en plus de récupérer le feed, vérifiera la fraîcheur des données des utilisateurs suivis et mettra en file d'attente les mises à jour nécessaires.
    - **Rendu Initial** : Il rendra la structure de la page et passera les données initiales du feed à un composant client.

**2. Logique de Récupération du Feed (`getSocialFeed`)**
- **Fichier** : `app/actions/social.ts`.
- **Signature du Contrat** :
  ```typescript
  // Définir un type de retour clair
  type SocialFeedItem = Recommendation & {
    author: {
      id: string;
      name: string | null;
      image: string | null;
    };
  };

  export async function getSocialFeed(options: { page?: number; limit?: number }): Promise<SocialFeedItem[]>;
  ```
- **Logique Métier (Règles à implémenter)** :
    - **Sécurité** : Vérifier la session de l'utilisateur connecté.
    - **Requête DB** :
        1.  Identifier la liste des `followingId` pour l'utilisateur connecté depuis la table `followers`.
        2.  Récupérer les recommandations (`recommendations`) pour ces `followingId`.
        3.  **Filtrage de Confidentialité** : Joindre la table `users` pour s'assurer que les auteurs des recommandations ont bien activé le partage (`shareRecommendations === true`).
        4.  **Pagination** : Utiliser les paramètres `page` et `limit` pour implémenter la pagination (`OFFSET` et `LIMIT` en SQL).
        5.  **Tri (V1)** : Pour cette première version, trier les résultats par ordre **antéchronologique** basé sur `generatedAt`. L'algorithme de pertinence sera une amélioration future (US-08).
    - **Formatage** : La fonction doit retourner un tableau d'objets où chaque recommandation est enrichie avec les informations de son auteur.

**3. Composant d'Affichage du Feed (`SocialFeed.tsx`)**
- **Type** : Composant client (`'use client'`).
- **Responsabilités** :
    - Recevoir la liste initiale des items du feed en props.
    - Gérer l'état de la pagination (le numéro de la page actuelle).
    - Gérer l'état de chargement lors de la récupération des pages suivantes.
    - Au clic sur "Voir plus", appeler l'action `getSocialFeed` avec le numéro de la page suivante et ajouter les nouveaux résultats à la liste existante.
    - Faire le rendu de la liste des `AlbumCard` modifiées.

##### 🌍 Internationalisation (i18n)
- **Principe** : Les textes du feed (état vide, bouton, etc.) doivent être traduits.
- **Nouvelles clés à ajouter** :
  ```json
  {
    "Social": {
      "feed": {
        "recommendedBy": "Recommandé par {name}",
        "loadMore": "Voir plus",
        "emptyState": {
          "title": "Votre feed est vide",
          "description": "Commencez par suivre des utilisateurs pour voir leurs recommandations ici."
        }
      }
    }
  }
  ```

##### 🔒 RGPD & Sécurité
- La fonction `getSocialFeed` est le point de contrôle principal. Elle doit scrupuleusement respecter les choix de partage de chaque utilisateur. Si un utilisateur désactive le partage de ses recommandations, ses données ne doivent plus jamais apparaître dans le feed de personne.
- Les informations sur l'auteur retournées doivent être limitées au minimum public (`id`, `name`, `image`).

---
#### **US-06 : Intégration du Lecteur Spotify Embed dans l'AlbumCard**
> **En tant qu'** utilisateur, **je veux** voir et utiliser le lecteur Spotify intégré sur chaque carte d'album, **afin de** pouvoir écouter un extrait du titre phare.

##### 🎯 Principe Fondamental
Cette User Story ne consiste **pas** à créer un nouveau lecteur audio, mais à s'assurer que le **lecteur Spotify IFrame Embed existant** est correctement intégré et alimenté en données dans tous les contextes où le composant `AlbumCard` est utilisé, notamment dans le nouveau feed social.

##### 🎨 Design UX/UI
- **Composant** : `AlbumCard`.
- **Affichage** : Le lecteur Spotify Embed standard (iframe) doit être affiché sur la carte.
- **Condition d'Affichage** : Le lecteur ne doit apparaître que si une recommandation possède un `topTrackId` valide. Si `topTrackId` est `null` ou non défini, le lecteur ne doit pas être rendu.
- **Interaction** : L'interaction est entièrement gérée par le widget Spotify Embed lui-même (bouton play/pause, barre de progression, etc.). Il n'y a pas de contrôle personnalisé à développer.

##### 🔧 Spécifications Techniques et Principes

**1. Data Flow (Flux de Données)**
- **Contrat de Données** : Toutes les fonctions qui retournent des données destinées à être affichées dans une `AlbumCard` (comme `getSocialFeed` de l'US-05) **doivent** s'assurer que le champ `topTrackId` est inclus dans les données de chaque recommandation.
- **Exemple de Requête** : La requête de `getSocialFeed` doit sélectionner `recommendations.topTrackId` et le passer au composant final.

**2. Modification du Composant `AlbumCard`**
- **Props** : Le composant `AlbumCard` doit accepter `topTrackId: string | null` comme prop.
- **Logique de Rendu Conditionnel** :
  ```jsx
  // Dans le composant AlbumCard
  {topTrackId && (
    <iframe
      src={`https://open.spotify.com/embed/track/${topTrackId}`}
      width="100%"
      height="80" // ou la hauteur appropriée
      frameBorder="0"
      allow="encrypted-media"
      title={t('playerTitle')} // Utiliser une clé de traduction pour l'accessibilité
    ></iframe>
  )}
  ```
- **Type de Composant** : `AlbumCard` est déjà susceptible d'être un composant client (`'use client'`) en raison d'autres interactions (comme le bouton d'ajout à la wishlist). Cette modification ne change pas ce prérequis.

**3. Gestion de l'État**
- **Aucun État Global Requis** : Il n'est **pas nécessaire** d'implémenter un store global (Zustand) pour cette fonctionnalité. Chaque iframe Spotify Embed gère son propre état de lecture de manière isolée.
- **Problème Connu (Hors Scope pour la V1)** : Le fait que plusieurs lecteurs puissent jouer simultanément est un comportement connu de cette approche. Empêcher cela nécessiterait une intégration plus complexe avec l'API `postMessage` de Spotify, ce qui est considéré comme une amélioration pour une future version et non un requis pour cette US.

##### 🌍 Internationalisation (i18n)
- **Principe** : Le titre de l'iframe doit être traduit pour l'accessibilité.
- **Nouvelles clés à ajouter** :
  ```json
  {
    "AlbumCard": {
      "playerTitle": "Lecteur Spotify"
    }
  }
  ```

##### 🔒 RGPD & Sécurité
- Cette fonctionnalité est sécurisée par nature car elle ne fait qu'afficher du contenu public de Spotify via une iframe.
- Il n'y a aucune transmission de données utilisateur vers Spotify autre que ce que le widget embed gère lui-même.
- Il est important de s'assurer que le `topTrackId` provient d'une source de données fiable (notre base de données).

---
#### **US-07 : Implémentation du Rafraîchissement Asynchrone "Juste-à-Temps"**
> **En tant que** système, **je veux** déclencher le rafraîchissement des données des profils suivis uniquement lorsqu'un utilisateur actif consulte son feed, **afin d'** assurer la fraîcheur des données sans gaspiller de ressources.

##### 🎯 Principe Fondamental
Cette fonctionnalité est la solution au problème clé des utilisateurs qui se connectent rarement. Au lieu d'un cron job coûteux qui s'exécute pour tous les utilisateurs, nous adoptons une approche "event-driven" : la visite d'un utilisateur actif déclenche les mises à jour nécessaires pour les profils qu'il consulte, bénéficiant ainsi à toute la communauté de manière ciblée.

##### 🔧 Spécifications Techniques et Principes

**1. Point de Déclenchement : `getSocialFeed`**
- **Fichier** : `app/actions/social.ts`.
- **Modification de la Logique** : La fonction `getSocialFeed` (de l'US-05) doit être enrichie. En plus de récupérer les données existantes pour le feed, elle doit initier le processus de rafraîchissement.
- **Principe** : La récupération du feed pour l'utilisateur actif et le déclenchement des mises à jour en arrière-plan doivent se faire en parallèle (`Promise.allSettled`) pour ne pas ralentir la réponse initiale.

**2. Logique de Détection des Données "Périmées"**
- **Critère** : Une recommandation est considérée comme "périmée" si sa `generatedAt` date de plus d'un certain seuil (par exemple, 24 ou 48 heures). Ce seuil doit être configurable.
- **Implémentation** :
    1. Dans `getSocialFeed`, après avoir identifié les utilisateurs suivis, effectuer une requête pour trouver la date de la recommandation la plus récente (`MAX(generatedAt)`) pour chacun de ces utilisateurs.
    2. Comparer cette date au seuil pour identifier la liste des `userIds` à rafraîchir.

**3. Système de File d'Attente (Queue)**
- **Principe** : Pour garantir que les rafraîchissements sont traités de manière fiable et sans bloquer le serveur, nous devons utiliser un système de file d'attente.
- **Choix Technologique** :
    - **Option A (Préférée, Vercel-native)** : Utiliser **Vercel KV Queue**. C'est une solution simple et intégrée à l'écosystème Vercel, idéale pour ce cas d'usage.
    - **Option B (Alternative, sans dépendance externe)** : Créer une table SQL `background_jobs` (`id`, `job_type`, `payload`, `status`, `attempts`). Un cron job très léger (`*/5 * * * *`) pourrait alors interroger cette table et traiter les jobs en attente. Cette option est plus complexe à gérer.
- **Contrat du Job** : Chaque job poussé dans la file d'attente doit contenir le minimum d'informations nécessaires, par exemple : `{ type: 'REFRESH_RECOMMENDATIONS', payload: { userId: '...' } }`.

**4. Fonction de Traitement (Worker)**
- **Principe** : Une fonction serveur dédiée, découplée du flux de l'utilisateur, doit consommer les jobs de la file d'attente.
- **Implémentation** :
    - Créer une nouvelle API Route (par exemple, `app/api/queues/refresh-recommendations/route.ts`) qui sera appelée par le service de file d'attente (ex: Vercel KV).
    - Cette route reçoit le payload du job (contenant le `userId`).
    - Elle exécute la logique complète de génération de recommandations pour cet utilisateur (appel à l'API Spotify, scoring, croisement Discogs, sauvegarde en base de données).
- **Robustesse** : La fonction doit inclure une gestion des erreurs et des tentatives multiples (`retries`) pour gérer les échecs potentiels (ex: API Spotify indisponible).

**5. Flux d'Exécution Complet**
1.  L'utilisateur A visite `/social`.
2.  Le composant serveur appelle `getSocialFeed`.
3.  `getSocialFeed` exécute deux logiques en parallèle :
    a. Récupérer le feed actuel avec les données existantes.
    b. Identifier les utilisateurs suivis (B, C) dont les données sont périmées.
4.  Pour B et C, `getSocialFeed` pousse un job dans la file d'attente Vercel KV.
5.  `getSocialFeed` retourne **immédiatement** le feed (a) à l'utilisateur A.
6.  Quelques instants plus tard, Vercel exécute la fonction worker pour les jobs de B et C, qui met à jour leurs recommandations en base de données.
7.  La prochaine fois qu'un utilisateur consultera le profil de B ou C, il verra les données fraîches.

##### 🌍 Internationalisation (i18n)
- Cette fonctionnalité est entièrement technique et n'a pas d'interface utilisateur directe. Aucune traduction n'est nécessaire.

##### 🔒 RGPD & Sécurité
- Le système ne fait que rafraîchir des données pour lesquelles il a déjà le consentement (l'utilisateur a connecté son compte Spotify).
- La fonction worker doit être sécurisée. Si elle est exposée via une API route, elle doit être protégée par un secret ou une signature pour s'assurer qu'elle ne peut être appelée que par le service de file d'attente.
- Les jobs dans la file d'attente ne doivent contenir que des identifiants et non des données personnelles sensibles.

---
### **Priorité 3 : Intelligence et Rétention**
*(Le système devient plus intelligent et proactif.)*

#### **US-08 : Implémentation de l'Algorithme de Pertinence du Feed**
> **En tant qu'** utilisateur, **je veux** que mon feed social soit trié par pertinence plutôt que par date, **afin que** les recommandations les plus susceptibles de me plaire apparaissent en premier.

##### 🎯 Principe Fondamental
L'objectif est de remplacer le tri chronologique simple de l'US-05 par un algorithme de scoring qui évalue chaque recommandation du feed en fonction de sa pertinence pour l'utilisateur *actuel*. Cela garantit que le contenu le plus engageant remonte en haut de la liste, maximisant ainsi la découverte et la rétention.

##### 🔧 Spécifications Techniques et Principes

**1. Prérequis : Enrichissement des Données**
- **Historique des Recommandations** : Cette US dépend de la table `recommendation_history` (créée dans l'US-01), qui doit être peuplée avec toutes les recommandations générées pour tous les utilisateurs.
- **Caractéristiques Audio (Future US)** : Pour un matching encore plus fin, une future US (`US-02` du plan initial, maintenant intégrée ici en tant que concept) devra enrichir les données des pistes avec les caractéristiques audio de l'API Spotify (`danceability`, `energy`, `valence`, etc.). Pour la V1 de cet algorithme, nous nous baserons principalement sur le matching d'artistes/albums.

**2. Modification de la Logique de `getSocialFeed`**
- **Principe** : Au lieu de trier par date en base de données, la fonction `getSocialFeed` va maintenant :
    1. Récupérer les recommandations brutes des utilisateurs suivis (sans tri complexe en SQL).
    2. Pour chaque recommandation, calculer un "Score de Pertinence" en mémoire.
    3. Trier la liste finale des recommandations en fonction de ce score, par ordre décroissant.
- **Performance** : Le calcul en mémoire peut être intensif. Si le nombre d'utilisateurs suivis est élevé, il faudra envisager de pré-calculer les scores de similarité entre utilisateurs via un batch nocturne. Pour la V1, un calcul à la volée est acceptable.

**3. Formule de Scoring de Pertinence (V1)**

Le score de chaque item du feed (`reco_pertinence_score`) sera une somme pondérée de plusieurs facteurs.

`reco_pertinence_score = (W1 * matching_score) + (W2 * social_proof_score) + (W3 * recency_score)`

Où `W1`, `W2`, `W3` sont des poids à ajuster (ex: `W1=0.6`, `W2=0.25`, `W3=0.15`).

- **a. Score de Matching (`matching_score`)**
    - **Objectif** : Mesurer la similarité des goûts entre l'utilisateur actuel (`UserA`) et l'auteur de la recommandation (`UserB`).
    - **Calcul** :
        1. Récupérer l'historique des recommandations de `UserA` et `UserB` depuis `recommendation_history`.
        2. Identifier les artistes en commun.
        3. Calculer un score basé sur le nombre d'artistes communs et l'importance de ces artistes pour chaque utilisateur (somme des `listenScore` pour un artiste donné).
    - **Normalisation** : Le score doit être normalisé (ramené sur une échelle de 0 à 1) pour être comparable.

- **b. Preuve Sociale (`social_proof_score`)**
    - **Objectif** : Donner plus de poids aux recommandations provenant d'utilisateurs influents ou populaires au sein de la plateforme.
    - **Calcul** :
        1. Récupérer le nombre de followers de l'auteur de la recommandation (`UserB`).
    - **Normalisation** : Le nombre de followers doit être normalisé (par exemple, en utilisant une fonction logarithmique pour que l'influence de 10 à 100 followers soit plus significative que de 1000 à 1100).

- **c. Score de Récence (`recency_score`)**
    - **Objectif** : Donner un léger avantage aux recommandations plus récentes pour que le feed ne soit pas statique.
    - **Calcul** : Basé sur la date `generatedAt` de la recommandation. Une fonction de décroissance exponentielle est idéale : une recommandation d'aujourd'hui a un score de 1, une d'hier 0.9, une d'il y a une semaine 0.5, etc.

**4. Implémentation Pratique**
- Créer un nouveau module/service, par exemple `lib/feed-scoring.ts`, qui contiendra toute la logique de calcul.
- La fonction principale, `calculateRelevanceScore(recommendation, currentUser)`, prendra une recommandation du feed et l'utilisateur actuel, et retournera le score final.
- `getSocialFeed` utilisera cette fonction pour trier sa liste de résultats avant de la retourner au client.

##### 🌍 Internationalisation (i18n)
- Cette fonctionnalité est entièrement technique et n'a pas d'interface utilisateur directe. Aucune traduction n'est nécessaire.

##### 🔒 RGPD & Sécurité
- L'analyse des goûts et des historiques doit être faite de manière agrégée. Le système manipule des `userId` et des `artistName`, mais ne doit pas exposer de profils détaillés sans consentement.
- Les scores calculés sont des données internes au système et ne doivent pas être exposés directement à l'utilisateur final.
- Il faut s'assurer que le calcul ne prend en compte que les données des utilisateurs qui ont consenti au partage (`shareRecommendations`).

---
#### **US-09 : Création de l'Algorithme de Suggestion de Profils**
> **En tant qu'** utilisateur, **je veux** que le système me suggère des profils pertinents à suivre, **afin de** développer mon réseau sans effort.

##### 🎯 Principe Fondamental
L'objectif est de fournir à l'utilisateur une liste de profils à suivre qui soit pertinente et personnalisée. Pour la V1, l'algorithme se basera sur une combinaison de similarité de goûts, d'exploration du graphe social, et d'une suggestion éditoriale, afin de ne pas dépendre d'une seule source de données.

##### 🎨 Design UX/UI
- **Emplacement** : Un module dédié "Suggestions pour vous" doit être placé sur la page `/social`, idéalement dans la colonne latérale droite.
- **Affichage** : Le module doit afficher une liste courte et engageante de 3 à 5 profils.
- **Item de Suggestion** : Chaque suggestion doit contenir :
    - L'`Avatar` et le `name` de l'utilisateur suggéré.
    - Une très courte justification de la suggestion (si possible, ex: "A des goûts similaires").
    - Un bouton "Suivre" (`FollowButton` de l'US-02).

##### 🔧 Spécifications Techniques et Principes

**1. Création d'une Action Serveur (`getProfileSuggestions`)**
- **Fichier** : `app/actions/social.ts`.
- **Signature du Contrat** :
  ```typescript
  type ProfileSuggestion = {
    id: string;
    name: string | null;
    image: string | null;
    justification: string; // Ex: "Vous suivez des personnes en commun"
  };

  export async function getProfileSuggestions(): Promise<ProfileSuggestion[]>;
  ```
- **Responsabilités** : Cette fonction est le cœur de l'algorithme. Elle doit orchestrer l'appel aux différentes sources de suggestions et agréger les résultats.

**2. Logique d'Agrégation des Suggestions**
La fonction `getProfileSuggestions` doit suivre ces étapes :
1.  Récupérer la liste des utilisateurs que l'utilisateur actuel suit déjà pour pouvoir les exclure des résultats finaux.
2.  Appeler en parallèle plusieurs "fonctions sources" pour obtenir des candidats.
3.  Agréger, dédoublonner et classer les candidats pour former la liste finale de 3 à 5 suggestions.

**3. Fonctions Sources de Suggestions**

- **a. Suggestion par Similarité de Goûts (`suggestByTaste`)**
    - **Principe** : Suggérer des utilisateurs ayant un score de similarité élevé (calculé dans l'US-08).
    - **Implémentation** :
        1.  Récupérer les scores de similarité pré-calculés (si disponibles) ou les calculer à la volée pour un échantillon d'utilisateurs.
        2.  Retourner les 5 utilisateurs (non déjà suivis) avec le score le plus élevé.
        3.  La `justification` sera "A des goûts similaires".

- **b. Suggestion par Graphe Social (`suggestByGraph`)**
    - **Principe** : Suggérer les "amis de mes amis".
    - **Implémentation** :
        1.  Identifier les utilisateurs que je suis (`following_of_user_A`).
        2.  Identifier les utilisateurs que *eux* suivent (`following_of_following_of_user_A`).
        3.  Filtrer cette liste pour exclure l'utilisateur A lui-même et les personnes qu'il suit déjà.
        4.  Compter les occurrences (combien de mes "amis" suivent cette personne) et retourner les plus fréquentes.
        5.  La `justification` sera "Suivi par [Nom d'un ami en commun]".

- **c. Suggestion Éditoriale (`suggestByEditorial`)**
    - **Principe** : Suggérer un ou plusieurs profils "mis en avant".
    - **Implémentation** :
        1.  Avoir une liste d'IDs de profils à promouvoir (par exemple, dans une variable d'environnement ou une table de configuration).
        2.  **Règle Spécifique** : Le profil `<EMAIL>` doit toujours être un candidat.
        3.  Retourner ces profils s'ils ne sont pas déjà suivis par l'utilisateur.
        4.  La `justification` sera "Suggestion de l'équipe".

**4. Intégration Frontend**
- Créer un composant client `ProfileSuggestions.tsx`.
- Ce composant appellera l'action `getProfileSuggestions` à son chargement.
- Il gérera les états de chargement et d'affichage de la liste des suggestions.
- Chaque item de la liste utilisera le `FollowButton` pour permettre une action directe.

##### 🌍 Internationalisation (i18n)
- **Principe** : Les textes du module de suggestion doivent être traduits.
- **Nouvelles clés à ajouter** :
  ```json
  {
    "Social": {
      "suggestions": {
        "title": "Suggestions pour vous",
        "justification": {
          "taste": "A des goûts similaires",
          "graph": "Suivi par {name}",
          "editorial": "Suggestion de l'équipe"
        }
      }
    }
  }
  ```

##### 🔒 RGPD & Sécurité
- L'algorithme ne doit suggérer que des profils dont la visibilité n'est pas `'private'`.
- Les justifications doivent être formulées de manière à ne pas révéler d'informations sensibles. "Suivi par Simon G." est acceptable, mais "Vous aimez tous les deux l'artiste X" pourrait être perçu comme plus intrusif si les goûts sont considérés comme privés. La première approche est plus sûre.
- Toutes les requêtes se basent sur des relations de suivi et des données de recommandations partagées, respectant ainsi les choix de consentement des utilisateurs.

---
#### **US-10 : Mise en Place des Notifications de Suivi (Email & In-App)**
> **En tant qu'** utilisateur, **je veux** être notifié lorsqu'une personne me suit, **afin d'** être au courant de l'activité de mon profil et de pouvoir potentiellement la suivre en retour.

##### 🎯 Principe Fondamental
Les notifications sont un levier d'engagement clé pour faire revenir les utilisateurs sur l'application. Elles doivent être informatives, non-intrusives, et l'utilisateur doit garder le contrôle sur leur envoi. Pour la V1, nous nous concentrons sur la notification la plus importante : le "nouveau follower".

##### 🎨 Design UX/UI

- **Notification In-App** :
    - **Déclencheur** : Une icône `Bell` dans le `Header` principal de l'application.
    - **Indicateur** : Un petit badge rouge ou un point sur l'icône `Bell` signale la présence de notifications non lues.
    - **Contenu** : Au clic sur l'icône, un `DropdownMenu` ou un `Popover` s'ouvre, affichant une liste des notifications récentes. Chaque notification doit être un lien cliquable.
    - **Texte de la Notification** : `[Avatar] [Nom de l'utilisateur] vous suit maintenant.` Un clic sur la notification doit rediriger vers le profil de cet utilisateur (`/u/[id]`).
- **Notification par Email** :
    - **Template** : Créer un nouveau template d'email (`emails/new-follower-email.tsx`).
    - **Design** : L'email doit être simple, propre et direct, en réutilisant les composants d'email existants (comme `EmailHeader`).
    - **Contenu** : "Bonne nouvelle ! [Nom de l'utilisateur] vous suit maintenant sur Stream2Spin." avec un bouton d'appel à l'action clair : "Voir son profil".

##### 🔧 Spécifications Techniques et Principes

**1. Logique de Création de la Notification**
- **Point d'Origine** : La Server Action `followUser` (de l'US-02) est le point de départ.
- **Modification de `followUser`** : Après avoir inséré avec succès la relation dans la table `followers`, l'action doit appeler une nouvelle fonction `createFollowNotification(sourceUserId, targetUserId)`.
- **Fonction `createFollowNotification`** :
    - **Responsabilités** :
        1.  Créer une entrée dans la table `notifications` avec `recipientId = targetUserId`, `actorId = sourceUserId`, et `type = 'new_follower'`.
        2.  Déclencher l'envoi de l'email de notification de manière asynchrone (pour ne pas ralentir la réponse de l'action `followUser`).

**2. Envoi de l'Email de Notification**
- **Fonction `sendNewFollowerEmail`** :
    - **Déclenchement** : Appelée par `createFollowNotification`.
    - **Logique** :
        1.  Vérifier si l'utilisateur destinataire (`targetUser`) a activé les notifications par email pour ce type d'événement (nécessite d'ajouter une colonne de préférence dans la table `users` ou d'utiliser une colonne existante).
        2.  Si oui, récupérer les informations nécessaires (email du destinataire, nom de l'acteur, langue préférée du destinataire).
        3.  Faire le rendu du template React Email `new-follower-email.tsx` en passant les traductions appropriées.
        4.  Utiliser le service `Resend` pour envoyer l'email.

**3. Notifications In-App**
- **Composant `NotificationBell.tsx`** :
    - **Type** : Composant client (`'use client'`).
    - **Data Fetching** : À son montage, ou périodiquement (via SWR/TanStack Query avec `refetchInterval`), il doit appeler une action `getUnreadNotifications()` pour récupérer le nombre de notifications non lues et leur contenu.
    - **Affichage** : Il gère l'affichage du badge et le contenu du dropdown.
- **Action `getUnreadNotifications`** :
    - Récupère les notifications de l'utilisateur connecté où `isRead` est `false`.
    - Joint la table `users` sur `actorId` pour obtenir le nom et l'avatar de l'acteur.
- **Action `markNotificationsAsRead`** :
    - Appelée lorsque l'utilisateur ouvre le dropdown des notifications.
    - Met à jour le champ `isRead` à `true` pour les notifications de l'utilisateur.

##### 🌍 Internationalisation (i18n)
- **Principe** : Le contenu des notifications in-app et des emails doit être entièrement traduit.
- **Nouvelles clés à ajouter** :
  ```json
  {
    "Notifications": {
      "newFollower": "{name} vous suit maintenant."
    },
    "Emails": {
      "newFollower": {
        "subject": "{name} vous suit sur Stream2Spin !",
        "greeting": "Bonne nouvelle !",
        "body": "{name} vous suit maintenant sur Stream2Spin.",
        "cta": "Voir son profil"
      }
    },
    "Account": {
      "settings": {
        "notifications": {
          "newFollowerLabel": "Quand quelqu'un me suit",
          "newFollowerDescription": "Recevoir un email à chaque nouvel abonné."
        }
      }
    }
  }
  ```

##### 🔒 RGPD & Sécurité
- **Contrôle de l'Utilisateur** : Il est **impératif** d'ajouter une option dans les paramètres du compte (`/account`) permettant à l'utilisateur de désactiver les notifications par email ("Quand quelqu'un me suit"). Cette option doit être activée par défaut.
- **Désinscription** : Chaque email de notification doit contenir un lien de désinscription clair, conformément à la législation.
- **Données dans l'Email** : L'email ne doit contenir que des informations publiques (le nom de l'utilisateur qui a suivi).
- **Sécurité des Actions** : Toutes les actions (`getUnreadNotifications`, `markNotificationsAsRead`) doivent être sécurisées et ne fonctionner que pour l'utilisateur actuellement authentifié.

---
### **Priorité 4 : Finalisation et Contrôles**
*(Finalisation de l'expérience avec des contrôles de confidentialité complets.)*

#### **US-11 : Implémentation de la Modale de Partage et de Visibilité**
> **En tant qu'** utilisateur, **je veux** des contrôles clairs et granulaires sur la visibilité de mon profil et le partage de mes données, **afin de** me sentir en sécurité sur la plateforme et de maîtriser mon empreinte numérique.

##### 🎯 Principe Fondamental
Cette fonctionnalité est la pierre angulaire de la confiance utilisateur et de la conformité RGPD. Elle doit être facile à comprendre, facile à utiliser, et ses effets doivent être immédiats et respectés dans toute l'application. Le contrôle doit être entre les mains de l'utilisateur.

##### 🎨 Design UX/UI
- **Emplacement** : Une nouvelle section dédiée "Visibilité & Partage" sur la page des paramètres du compte (`/account`).
- **Design** :
    - **Titre de Section** : Clair et sans ambiguïté, par exemple "Confidentialité du Profil".
    - **Contrôle Principal (Visibilité)** : Utiliser un composant `RadioGroup` pour le niveau de visibilité global. Chaque option doit être accompagnée d'une courte description explicative.
        - `( `(•)` **Public** : "Tout le monde sur internet peut voir votre profil."
        - `(•)` **Utilisateurs Stream2Spin** (par défaut) : "Seuls les utilisateurs connectés à Stream2Spin peuvent voir votre profil."
        - `( )` **Privé** : "Seul vous pouvez voir votre profil."
    - **Contrôles Granulaires (Partage)** : Utiliser une liste de composants `Checkbox` ou `Switch` pour le partage de chaque section du profil. Ces contrôles ne sont actifs que si la visibilité n'est pas "Privé".
        - `[x]` Partager mes recommandations
        - `[x]` Partager mes envies
        - `[x]` Partager ma collection
    - **Bouton de Sauvegarde** : Un bouton "Enregistrer les modifications" clair, qui est désactivé par défaut et ne s'active que si des changements ont été faits. Au clic, un toast de confirmation "Paramètres enregistrés" doit apparaître.

##### 🔧 Spécifications Techniques et Principes

**1. Composant Frontend (`VisibilitySettingsForm.tsx`)**
- **Type** : Composant client (`'use client'`).
- **Responsabilités** :
    - **Props** : Recevoir les paramètres de visibilité actuels de l'utilisateur depuis la page serveur (`/account/page.tsx`).
    - **Gestion d'État** : Gérer l'état local du formulaire pour refléter les modifications de l'utilisateur avant la soumission.
    - **Interaction** : Activer/désactiver le bouton de sauvegarde en fonction des changements.
    - **Soumission** : Au clic sur "Enregistrer", appeler la Server Action dédiée en passant le nouvel objet de configuration. Gérer les états de chargement et d'erreur.

**2. Server Action (`updateVisibilitySettings`)**
- **Fichier** : `app/actions/user.ts` ou `app/actions/account.ts`.
- **Signature du Contrat** :
  ```typescript
  type VisibilitySettings = {
    profileVisibility: 'public' | 'users_only' | 'private';
    shareRecommendations: boolean;
    shareWishlist: boolean;
    shareCollection: boolean;
  };

  export async function updateVisibilitySettings(settings: VisibilitySettings): Promise<{ success: boolean; error?: string }>;
  ```
- **Logique Métier** :
    - **Sécurité** : Valider la session de l'utilisateur connecté.
    - **Validation des Données** : Utiliser une librairie comme Zod pour valider que l'objet `settings` reçu correspond bien au format attendu. C'est une bonne pratique de sécurité.
    - **Mise à Jour DB** : Mettre à jour les colonnes correspondantes (`profileVisibility`, `shareRecommendations`, etc.) dans la table `users` pour l'utilisateur authentifié.
    - **Revalidation du Cache** : Invalider les données mises en cache qui dépendent de ces paramètres. `revalidatePath('/u/[id]')` et `revalidatePath('/account')` sont un minimum.

**3. Application des Règles de Visibilité (Crucial)**
- **Principe** : C'est la partie la plus importante. Toutes les fonctions et tous les composants qui exposent des données utilisateur doivent être revus pour appliquer ces règles.
- **Points de Contrôle à Vérifier** :
    - **Profil Public (`/u/[id]`)** : La page doit vérifier `profileVisibility` avant de rendre quoi que ce soit. Si le profil est privé, elle doit afficher une page d'erreur ou un message "Ce profil est privé".
    - **Recherche d'Utilisateurs (`searchUsers`)** : Doit exclure les profils privés des résultats.
    - **Feed Social (`getSocialFeed`)** : Doit exclure les recommandations des utilisateurs qui ont `shareRecommendations` à `false`.
    - **Suggestions de Profils (`getProfileSuggestions`)** : Doit exclure les profils privés.
    - **Listes Followers/Following** : L'accès à ces listes doit être conditionné par la visibilité du profil.

##### 🌍 Internationalisation (i18n)
- **Principe** : Tous les labels, descriptions et messages de confirmation de cette section doivent être traduits.
- **Nouvelles clés à ajouter** :
  ```json
  {
    "Account": {
      "settings": {
        "visibility": {
          "title": "Visibilité & Partage",
          "sectionTitle": "Confidentialité du Profil",
          "level": {
            "public": {
              "label": "Public",
              "description": "Tout le monde sur internet peut voir votre profil."
            },
            "users_only": {
              "label": "Utilisateurs Stream2Spin",
              "description": "Seuls les utilisateurs connectés à Stream2Spin peuvent voir votre profil."
            },
            "private": {
              "label": "Privé",
              "description": "Seul vous pouvez voir votre profil."
            }
          },
          "shareRecommendations": "Partager mes recommandations",
          "shareWishlist": "Partager mes envies",
          "shareCollection": "Partager ma collection"
        }
      }
    },
    "Common": {
      "saveChanges": "Enregistrer les modifications",
      "settingsSaved": "Paramètres enregistrés avec succès."
    }
  }
  ```

##### 🔒 RGPD & Sécurité
- **Consentement Granulaire** : Cette US est l'implémentation directe du principe de consentement granulaire du RGPD.
- **Droit à l'Oubli/Portabilité** : Avoir ces contrôles clairs est une première étape vers la conformité avec d'autres droits du RGPD.
- **Défaut Sécurisé** : Les valeurs par défaut dans la base de données (`users_only`, `true`) sont un bon compromis entre fonctionnalité et confidentialité. Le plus sécurisé serait `private`, mais cela tuerait la fonctionnalité sociale par défaut. Le choix de `users_only` est donc un bon équilibre.
- **Validation Serveur** : La validation des `settings` côté serveur avec Zod empêche des injections de valeurs invalides dans la base de données.
