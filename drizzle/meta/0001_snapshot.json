{"id": "d946eca0-721d-4a0f-92de-3855e4edfba1", "prevId": "e4758d21-b01f-4949-a088-54d03f231cb3", "version": "7", "dialect": "postgresql", "tables": {"public.accounts": {"name": "accounts", "schema": "", "columns": {"userId": {"name": "userId", "type": "text", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "text", "primaryKey": false, "notNull": true}, "provider": {"name": "provider", "type": "text", "primaryKey": false, "notNull": true}, "providerAccountId": {"name": "providerAccountId", "type": "text", "primaryKey": false, "notNull": true}, "refresh_token": {"name": "refresh_token", "type": "text", "primaryKey": false, "notNull": false}, "access_token": {"name": "access_token", "type": "text", "primaryKey": false, "notNull": false}, "access_token_secret": {"name": "access_token_secret", "type": "text", "primaryKey": false, "notNull": false}, "expires_at": {"name": "expires_at", "type": "integer", "primaryKey": false, "notNull": false}, "token_type": {"name": "token_type", "type": "text", "primaryKey": false, "notNull": false}, "scope": {"name": "scope", "type": "text", "primaryKey": false, "notNull": false}, "id_token": {"name": "id_token", "type": "text", "primaryKey": false, "notNull": false}, "session_state": {"name": "session_state", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"accounts_userId_users_id_fk": {"name": "accounts_userId_users_id_fk", "tableFrom": "accounts", "tableTo": "users", "columnsFrom": ["userId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {"accounts_provider_providerAccountId_pk": {"name": "accounts_provider_providerAccountId_pk", "columns": ["provider", "providerAccountId"]}}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.email_deliverability_metrics": {"name": "email_deliverability_metrics", "schema": "", "columns": {"id": {"name": "id", "type": "bigserial", "primaryKey": true, "notNull": true}, "date": {"name": "date", "type": "timestamp", "primaryKey": false, "notNull": true}, "emailsSent": {"name": "emailsSent", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "emailsDelivered": {"name": "emailsDelivered", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "emailsBounced": {"name": "emailsBounced", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "emailsComplained": {"name": "emailsComplained", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "emailsOpened": {"name": "emailsOpened", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "emailsClicked": {"name": "emailsClicked", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "deliverabilityRate": {"name": "deliverabilityRate", "type": "text", "primaryKey": false, "notNull": false}, "openRate": {"name": "openRate", "type": "text", "primaryKey": false, "notNull": false}, "clickRate": {"name": "clickRate", "type": "text", "primaryKey": false, "notNull": false}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"idx_email_metrics_date": {"name": "idx_email_metrics_date", "columns": [{"expression": "date", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"unique_email_metrics_date": {"name": "unique_email_metrics_date", "nullsNotDistinct": false, "columns": ["date"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.email_domain_blacklist": {"name": "email_domain_blacklist", "schema": "", "columns": {"id": {"name": "id", "type": "bigserial", "primaryKey": true, "notNull": true}, "domain": {"name": "domain", "type": "text", "primaryKey": false, "notNull": true}, "reason": {"name": "reason", "type": "text", "primaryKey": false, "notNull": false}, "addedAt": {"name": "addedAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "isActive": {"name": "isActive", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"email_domain_blacklist_domain_unique": {"name": "email_domain_blacklist_domain_unique", "nullsNotDistinct": false, "columns": ["domain"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.email_events": {"name": "email_events", "schema": "", "columns": {"id": {"name": "id", "type": "bigserial", "primaryKey": true, "notNull": true}, "userId": {"name": "userId", "type": "text", "primaryKey": false, "notNull": true}, "emailId": {"name": "emailId", "type": "text", "primaryKey": false, "notNull": true}, "eventType": {"name": "eventType", "type": "text", "primaryKey": false, "notNull": true}, "eventData": {"name": "eventData", "type": "jsonb", "primaryKey": false, "notNull": false}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"idx_email_events_userId": {"name": "idx_email_events_userId", "columns": [{"expression": "userId", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_email_events_eventType": {"name": "idx_email_events_eventType", "columns": [{"expression": "eventType", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_email_events_createdAt": {"name": "idx_email_events_createdAt", "columns": [{"expression": "createdAt", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"email_events_userId_users_id_fk": {"name": "email_events_userId_users_id_fk", "tableFrom": "email_events", "tableTo": "users", "columnsFrom": ["userId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.followers": {"name": "followers", "schema": "", "columns": {"follower_id": {"name": "follower_id", "type": "text", "primaryKey": false, "notNull": true}, "following_id": {"name": "following_id", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"followers_follower_id_users_id_fk": {"name": "followers_follower_id_users_id_fk", "tableFrom": "followers", "tableTo": "users", "columnsFrom": ["follower_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "followers_following_id_users_id_fk": {"name": "followers_following_id_users_id_fk", "tableFrom": "followers", "tableTo": "users", "columnsFrom": ["following_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {"followers_follower_id_following_id_pk": {"name": "followers_follower_id_following_id_pk", "columns": ["follower_id", "following_id"]}}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.notifications": {"name": "notifications", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "recipient_id": {"name": "recipient_id", "type": "text", "primaryKey": false, "notNull": true}, "actor_id": {"name": "actor_id", "type": "text", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "notification_type", "typeSchema": "public", "primaryKey": false, "notNull": true}, "is_read": {"name": "is_read", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"notifications_recipient_id_users_id_fk": {"name": "notifications_recipient_id_users_id_fk", "tableFrom": "notifications", "tableTo": "users", "columnsFrom": ["recipient_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "notifications_actor_id_users_id_fk": {"name": "notifications_actor_id_users_id_fk", "tableFrom": "notifications", "tableTo": "users", "columnsFrom": ["actor_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.public_list_analytics": {"name": "public_list_analytics", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "userId": {"name": "userId", "type": "text", "primaryKey": false, "notNull": true}, "publicListId": {"name": "publicListId", "type": "text", "primaryKey": false, "notNull": true}, "eventType": {"name": "eventType", "type": "text", "primaryKey": false, "notNull": true}, "eventData": {"name": "eventData", "type": "jsonb", "primaryKey": false, "notNull": false}, "ipAddress": {"name": "ip<PERSON><PERSON><PERSON>", "type": "text", "primaryKey": false, "notNull": false}, "userAgent": {"name": "userAgent", "type": "text", "primaryKey": false, "notNull": false}, "referrer": {"name": "referrer", "type": "text", "primaryKey": false, "notNull": false}, "timestamp": {"name": "timestamp", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"idx_analytics_userId": {"name": "idx_analytics_userId", "columns": [{"expression": "userId", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_analytics_publicListId": {"name": "idx_analytics_publicListId", "columns": [{"expression": "publicListId", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_analytics_eventType": {"name": "idx_analytics_eventType", "columns": [{"expression": "eventType", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_analytics_timestamp": {"name": "idx_analytics_timestamp", "columns": [{"expression": "timestamp", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"public_list_analytics_userId_users_id_fk": {"name": "public_list_analytics_userId_users_id_fk", "tableFrom": "public_list_analytics", "tableTo": "users", "columnsFrom": ["userId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "public_list_analytics_publicListId_users_publicListId_fk": {"name": "public_list_analytics_publicListId_users_publicListId_fk", "tableFrom": "public_list_analytics", "tableTo": "users", "columnsFrom": ["publicListId"], "columnsTo": ["publicListId"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.recommendation_history": {"name": "recommendation_history", "schema": "", "columns": {"id": {"name": "id", "type": "bigserial", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "artist_name": {"name": "artist_name", "type": "text", "primaryKey": false, "notNull": true}, "album_title": {"name": "album_title", "type": "text", "primaryKey": false, "notNull": true}, "spotify_album_id": {"name": "spotify_album_id", "type": "text", "primaryKey": false, "notNull": false}, "listen_score": {"name": "listen_score", "type": "integer", "primaryKey": false, "notNull": true}, "timeframe": {"name": "timeframe", "type": "text", "primaryKey": false, "notNull": true}, "generated_at": {"name": "generated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"recommendation_history_user_id_users_id_fk": {"name": "recommendation_history_user_id_users_id_fk", "tableFrom": "recommendation_history", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.recommendations": {"name": "recommendations", "schema": "", "columns": {"id": {"name": "id", "type": "bigserial", "primaryKey": true, "notNull": true}, "userId": {"name": "userId", "type": "text", "primaryKey": false, "notNull": true}, "artistName": {"name": "<PERSON><PERSON><PERSON>", "type": "text", "primaryKey": false, "notNull": true}, "albumTitle": {"name": "albumTitle", "type": "text", "primaryKey": false, "notNull": true}, "albumCoverUrl": {"name": "albumCoverUrl", "type": "text", "primaryKey": false, "notNull": false}, "discogsReleaseId": {"name": "discogsReleaseId", "type": "bigint", "primaryKey": false, "notNull": false}, "spotifyAlbumId": {"name": "spotifyAlbumId", "type": "text", "primaryKey": false, "notNull": false}, "listenScore": {"name": "listenScore", "type": "integer", "primaryKey": false, "notNull": true}, "estimatedPlays": {"name": "estimatedPlays", "type": "integer", "primaryKey": false, "notNull": false}, "timeframe": {"name": "timeframe", "type": "text", "primaryKey": false, "notNull": true}, "generatedAt": {"name": "generatedAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "isOwned": {"name": "isOwned", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "affiliateLinks": {"name": "affiliateLinks", "type": "jsonb", "primaryKey": false, "notNull": false}, "topTrackName": {"name": "topTrackName", "type": "text", "primaryKey": false, "notNull": false}, "topTrackId": {"name": "topTrackId", "type": "text", "primaryKey": false, "notNull": false}, "topTrackPreviewUrl": {"name": "topTrackPreviewUrl", "type": "text", "primaryKey": false, "notNull": false}, "topTrackListenScore": {"name": "topTrackListenScore", "type": "integer", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"recommendations_userId_users_id_fk": {"name": "recommendations_userId_users_id_fk", "tableFrom": "recommendations", "tableTo": "users", "columnsFrom": ["userId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"recommendations_userId_spotifyAlbumId_timeframe_unique": {"name": "recommendations_userId_spotifyAlbumId_timeframe_unique", "nullsNotDistinct": false, "columns": ["userId", "spotifyAlbumId", "timeframe"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.sessions": {"name": "sessions", "schema": "", "columns": {"sessionToken": {"name": "sessionToken", "type": "text", "primaryKey": true, "notNull": true}, "userId": {"name": "userId", "type": "text", "primaryKey": false, "notNull": true}, "expires": {"name": "expires", "type": "timestamp", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"sessions_userId_users_id_fk": {"name": "sessions_userId_users_id_fk", "tableFrom": "sessions", "tableTo": "users", "columnsFrom": ["userId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.user_discogs_collection": {"name": "user_discogs_collection", "schema": "", "columns": {"id": {"name": "id", "type": "bigserial", "primaryKey": true, "notNull": true}, "userId": {"name": "userId", "type": "text", "primaryKey": false, "notNull": true}, "discogsReleaseId": {"name": "discogsReleaseId", "type": "bigint", "primaryKey": false, "notNull": true}, "artistName": {"name": "<PERSON><PERSON><PERSON>", "type": "text", "primaryKey": false, "notNull": true}, "albumTitle": {"name": "albumTitle", "type": "text", "primaryKey": false, "notNull": true}, "albumCoverUrl": {"name": "albumCoverUrl", "type": "text", "primaryKey": false, "notNull": false}, "year": {"name": "year", "type": "integer", "primaryKey": false, "notNull": false}, "format": {"name": "format", "type": "text", "primaryKey": false, "notNull": false}, "syncedAt": {"name": "syncedAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"user_discogs_collection_userId_users_id_fk": {"name": "user_discogs_collection_userId_users_id_fk", "tableFrom": "user_discogs_collection", "tableTo": "users", "columnsFrom": ["userId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"user_discogs_collection_userId_discogsReleaseId_unique": {"name": "user_discogs_collection_userId_discogsReleaseId_unique", "nullsNotDistinct": false, "columns": ["userId", "discogsReleaseId"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.user_fcm_tokens": {"name": "user_fcm_tokens", "schema": "", "columns": {"id": {"name": "id", "type": "bigserial", "primaryKey": true, "notNull": true}, "userId": {"name": "userId", "type": "text", "primaryKey": false, "notNull": true}, "token": {"name": "token", "type": "text", "primaryKey": false, "notNull": true}, "deviceInfo": {"name": "deviceInfo", "type": "text", "primaryKey": false, "notNull": false}, "isActive": {"name": "isActive", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "lastUsedAt": {"name": "lastUsedAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"user_fcm_tokens_userId_users_id_fk": {"name": "user_fcm_tokens_userId_users_id_fk", "tableFrom": "user_fcm_tokens", "tableTo": "users", "columnsFrom": ["userId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"user_fcm_tokens_userId_token_unique": {"name": "user_fcm_tokens_userId_token_unique", "nullsNotDistinct": false, "columns": ["userId", "token"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.users": {"name": "users", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": false}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": false}, "emailVerified": {"name": "emailVerified", "type": "timestamp", "primaryKey": false, "notNull": false}, "image": {"name": "image", "type": "text", "primaryKey": false, "notNull": false}, "preferredLanguage": {"name": "preferredLanguage", "type": "text", "primaryKey": false, "notNull": true, "default": "'fr'"}, "emailFrequency": {"name": "emailFrequency", "type": "text", "primaryKey": false, "notNull": true, "default": "'weekly'"}, "pushFrequency": {"name": "pushFrequency", "type": "text", "primaryKey": false, "notNull": true, "default": "'weekly'"}, "firstRecommendationEmailSent": {"name": "firstRecommendationEmailSent", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "publicListEnabled": {"name": "publicListEnabled", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "publicListId": {"name": "publicListId", "type": "text", "primaryKey": false, "notNull": false}, "profile_visibility": {"name": "profile_visibility", "type": "profile_visibility", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'users_only'"}, "share_recommendations": {"name": "share_recommendations", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "share_wishlist": {"name": "share_wishlist", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "share_collection": {"name": "share_collection", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "email_on_new_follower": {"name": "email_on_new_follower", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "publicProfileEnabled": {"name": "publicProfileEnabled", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "publicRecommendationsEnabled": {"name": "publicRecommendationsEnabled", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "publicWishlistEnabled": {"name": "publicWishlistEnabled", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "publicCollectionEnabled": {"name": "publicCollectionEnabled", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "email_notifications_enabled": {"name": "email_notifications_enabled", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "last_email_sent": {"name": "last_email_sent", "type": "timestamp", "primaryKey": false, "notNull": false}, "last_email_bounce": {"name": "last_email_bounce", "type": "timestamp", "primaryKey": false, "notNull": false}, "email_bounce_reason": {"name": "email_bounce_reason", "type": "text", "primaryKey": false, "notNull": false}, "last_email_complaint": {"name": "last_email_complaint", "type": "timestamp", "primaryKey": false, "notNull": false}, "email_complaint_reason": {"name": "email_complaint_reason", "type": "text", "primaryKey": false, "notNull": false}, "last_email_delivered": {"name": "last_email_delivered", "type": "timestamp", "primaryKey": false, "notNull": false}, "last_email_opened": {"name": "last_email_opened", "type": "timestamp", "primaryKey": false, "notNull": false}, "last_email_clicked": {"name": "last_email_clicked", "type": "timestamp", "primaryKey": false, "notNull": false}, "email_deliverability_score": {"name": "email_deliverability_score", "type": "integer", "primaryKey": false, "notNull": false, "default": 100}, "timezone": {"name": "timezone", "type": "text", "primaryKey": false, "notNull": false, "default": "'Europe/Paris'"}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"users_email_unique": {"name": "users_email_unique", "nullsNotDistinct": false, "columns": ["email"]}, "users_publicListId_unique": {"name": "users_publicListId_unique", "nullsNotDistinct": false, "columns": ["publicListId"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.verificationTokens": {"name": "verificationTokens", "schema": "", "columns": {"identifier": {"name": "identifier", "type": "text", "primaryKey": false, "notNull": true}, "token": {"name": "token", "type": "text", "primaryKey": false, "notNull": true}, "expires": {"name": "expires", "type": "timestamp", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"verificationTokens_identifier_token_pk": {"name": "verificationTokens_identifier_token_pk", "columns": ["identifier", "token"]}}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.wishlist_items": {"name": "wishlist_items", "schema": "", "columns": {"userId": {"name": "userId", "type": "text", "primaryKey": false, "notNull": true}, "artistName": {"name": "<PERSON><PERSON><PERSON>", "type": "text", "primaryKey": false, "notNull": true}, "albumTitle": {"name": "albumTitle", "type": "text", "primaryKey": false, "notNull": true}, "album_cover_url": {"name": "album_cover_url", "type": "text", "primaryKey": false, "notNull": false}, "spotify_album_id": {"name": "spotify_album_id", "type": "text", "primaryKey": false, "notNull": false}, "discogs_release_id": {"name": "discogs_release_id", "type": "bigint", "primaryKey": false, "notNull": false}, "affiliate_links": {"name": "affiliate_links", "type": "jsonb", "primaryKey": false, "notNull": false}, "top_track_name": {"name": "top_track_name", "type": "text", "primaryKey": false, "notNull": false}, "top_track_id": {"name": "top_track_id", "type": "text", "primaryKey": false, "notNull": false}, "top_track_preview_url": {"name": "top_track_preview_url", "type": "text", "primaryKey": false, "notNull": false}, "top_track_listen_score": {"name": "top_track_listen_score", "type": "integer", "primaryKey": false, "notNull": false}, "original_user_name": {"name": "original_user_name", "type": "text", "primaryKey": false, "notNull": false}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"wishlist_items_userId_users_id_fk": {"name": "wishlist_items_userId_users_id_fk", "tableFrom": "wishlist_items", "tableTo": "users", "columnsFrom": ["userId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {"wishlist_items_userId_artistName_albumTitle_pk": {"name": "wishlist_items_userId_artistName_albumTitle_pk", "columns": ["userId", "<PERSON><PERSON><PERSON>", "albumTitle"]}}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {"public.notification_type": {"name": "notification_type", "schema": "public", "values": ["new_follower"]}, "public.profile_visibility": {"name": "profile_visibility", "schema": "public", "values": ["private", "users_only", "public"]}}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}