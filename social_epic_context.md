Je souhaite faire évoluer la fonctionnalité de profil public. 
Aide moi à bien penser l'expérience UX et l'UI. Une fois que nous serons d'accord, nous rédigerons l'epic pour Cursor. 
Je souhaite que dans la sidebar, une nouvelle entrée "Social" soit présente.
Quand je clique dessus, j'arrive sur une page où on peut faire plusieurs choses : 
- Rechercher le profil d'un autre utilisateur en tapant son nom, prénom ou pseudo spotify
- Voir la liste des profils qu'on suit déjà 
- Followers (n) / Following (n) : afficher la liste des followers ou following dans une modale au clic sur l'un des éléments. La liste permet de suivre ou d'aller voir le profil
- Followers (n) / Following (n) : doivent être affichés sur le profil près de la photo de profil et du nom prénom
- Quand quelqu'un me suit, je reçois une notification in-app et par email
- Sur Social, ce que je vois en premier est un feed des recommendations des personnes que je follow
- Vérifier l'API de spotify pour voir si on peut connaitre qui l'utilisateur a dans ses "amis" Spotify, afin de lui fournir des recommandations de profil à suivre
- Ouvrir un profil et accéder à ce qu'il partage (recommandations, envies, collection), voir la liste des gens qu'ils suit et des gens qui le suivent
- le profil de l'utilisateur utilise les pages /u/{id}. Les paramètres de visibilité de ces pages évoluent. Par défaut, mon profil est visible par tous les utilisateurs connectés et mes recommandations, mes envies et ma collection sont partagés. Quand on clique sur "partager", je peux choisir le niveau de visibilité : Moi uniquement ; Les autres utilisateurs uniquement ; publique. Par défaut, l'option "Les autres utilisateurs uniquement" est cochée.
- Dans les albumcards de /social il doit y avoir User's Top track avec le player spotify
- Pour l'algorithme de recommandation de profils à suivre, nous implémenterons un algo qui combinera à la fois les suggestions A, B et C. Afin d'avoir un algorithme plus efficace, faut-il stocker en bdd toutes les recommendations que l'utilisateur a obtenu au fil du temps, tout cela sans altérer la liste actuelle des recommendations sur /recommendations ? 
Il faut aussi sans doute récupérer des informations sur le style musical de chaque musique écoutée (style, danceability, etc). 
- Par défaut, le profil de l'utilisateur <EMAIL> sera toujours proposé en suggestion.
- Partons du principe que les utilisateurs se connectent rarement

Précision, mes recommandations sont personnelles, elles se basent sur mon historique personnel d'écoutes Spotify et sont affichées sur /recommendations.
Nous cherchons actuellement à rendre visible les recommendations des personne que l'on suit, en étant triées via un algo de matching pour montrer les plus intéressantes en premier.
Donc le côté social reprose bien sur le rafraichissement des recommendations des users que l'on suit et pas sur ses propres recommendations personnelles

 "je me connecte, donc je déclenche une mise à jour des données dont j'ai besoin pour avoir une bonne expérience".
Voici comment cela fonctionne, étape par étape :
Le Déclencheur : La Visite du Feed Social (/social)
Un utilisateur (appelons-le Alex) se connecte. C'est peut-être le seul utilisateur actif de la journée. Alex va sur la page /social.
L'Action : Une Analyse Ciblée de la Stagnation
Au moment où Alex charge son feed, le système ne se contente pas de lui servir les données existantes. Il identifie d'abord les personnes qu'Alex suit (disons, Béatrice, Charles et David).
Il vérifie instantanément la date de dernière génération (generatedAt) des recommandations de Béatrice, Charles et David.
Disons que les données de Béatrice et Charles datent du dernier cron job (il y a 5 jours), mais que celles de David sont plus récentes.
La Magie : Le Lancement de Tâches Asynchrones pour les Autres
Le système déclenche immédiatement et en arrière-plan une nouvelle génération de recommandations pour Béatrice et Charles.
Ces tâches sont ajoutées à une file d'attente (queue) pour être exécutées par des fonctions serverless. Alex, lui, n'attend absolument rien.
L'Expérience d'Alex : Transparente et en Deux Temps
Temps 1 (Instantané) : Pour ne pas faire attendre Alex, la page /social se charge immédiatement avec les données actuellement disponibles (même si elles sont un peu anciennes). Son algorithme de feed personnel tourne sur ces données et lui propose déjà le meilleur classement possible.
Temps 2 (Différé) : Quelques instants plus tard, une fois que les tâches de rafraîchissement pour Béatrice et Charles sont terminées, la page d'Alex peut se mettre à jour. La manière la plus simple et la plus robuste est que la mise à jour soit visible lors de sa prochaine visite de la page /social ou après un rafraîchissement manuel de la page. On peut aussi envisager un toast subtil : "De nouvelles découvertes sont disponibles dans votre feed."



