# Référence de Contexte - Guide pour les Agents IA

## Objectif
Quand un agent IA n'a pas de contexte sur le projet Stream2Spin, il doit se référer au fichier de documentation technique complète pour comprendre l'architecture, les fonctionnalités et les patterns de développement.

## Fichier de Référence Principal

### Documentation Technique Complète
[ai-kickstart-context.md](mdc:Stream2Spin/ai-kickstart-context.md) contient toutes les informations essentielles pour comprendre le projet Stream2Spin.

## Quand Utiliser cette Référence

### Situations où consulter le fichier de contexte :
- **Première interaction** avec le projet
- **Manque d'informations** sur l'architecture
- **Questions** sur les fonctionnalités implémentées
- **Besoin de comprendre** les patterns de développement
- **Recherche** de composants ou d'utilitaires existants
- **Vérification** de l'état des épics et fonctionnalités

## Informations Clés Disponibles

### 🎯 Vue d'ensemble du Projet
- **Objectif** : Transformation des habitudes Spotify en recommandations vinyles
- **Stack technique** : Next.js 15, React 19, PostgreSQL, Drizzle ORM
- **Authentification** : Spotify OAuth uniquement
- **Monétisation** : Liens affiliés Amazon

### 📋 État des Fonctionnalités
- **Implémentées** : Profils publics (Epic 16), Storybook, Sécurité
- **En développement** : Fonctionnalités sociales (Epic 17)
- **Architecture** : App Router, Server Components, TypeScript strict

### 🗄️ Base de Données
- **Schéma complet** : Tables users, recommendations, wishlist, etc.
- **Migrations** : État des migrations appliquées
- **Relations** : Clés étrangères et contraintes

### 🧠 Algorithme de Recommandation
- **Scoring** : Position-based (track #1 = 50 points)
- **Croisement Discogs** : Évitement des doublons
- **Enrichissement** : Liens affiliés Amazon

### 🎨 Interface Utilisateur
- **Design system** : Glassmorphism, Tailwind CSS
- **Composants** : Structure organisée par catégories
- **Storybook** : Documentation complète des composants

## Processus de Consultation

### 1. Lecture Rapide
```markdown
# Commencer par la section "Guide de Démarrage Rapide"
- Checklist de compréhension
- Setup rapide pour développement
- Workflow principal
```

### 2. Analyse Détaillée
```markdown
# Consulter les sections selon le besoin
- Architecture Technique : Stack et structure
- Base de Données : Schéma et relations
- Algorithme : Logique de recommandation
- Fonctionnalités : État d'implémentation
```

### 3. Vérification de Cohérence
```markdown
# S'assurer que les informations sont à jour
- Vérifier les versions des dépendances
- Confirmer l'état des fonctionnalités
- Valider les patterns de code
```

## Questions Fréquentes

### Q: Comment démarrer le projet ?
**R:** Consulter la section "Setup Rapide pour Développement" dans [ai-kickstart-context.md](mdc:Stream2Spin/ai-kickstart-context.md)

### Q: Quelles fonctionnalités sont disponibles ?
**R:** Vérifier la "Checklist de Compréhension" et l'état des épics dans le fichier de contexte

### Q: Comment fonctionne l'algorithme de recommandation ?
**R:** Section "Algorithme de Recommandation" avec formules de scoring et exemples de code

### Q: Où trouver les composants existants ?
**R:** Section "Structure du Projet" et "Composants Documentés" pour Storybook

### Q: Comment gérer l'authentification ?
**R:** Section "Système d'Authentification" avec configuration NextAuth.js

## Patterns de Référence

### Pour les Questions d'Architecture
```markdown
1. Consulter "Architecture Technique"
2. Vérifier "Structure du Projet"
3. Analyser "Stack Technologique"
```

### Pour les Questions de Fonctionnalités
```markdown
1. Vérifier "Checklist de Compréhension"
2. Consulter "État des Fonctionnalités"
3. Analyser les épics mentionnées
```

### Pour les Questions de Code
```markdown
1. Consulter les exemples de code dans le fichier
2. Vérifier les patterns établis
3. Référencer les composants existants
```

### Pour les Questions de Base de Données
```markdown
1. Analyser "Schéma Principal"
2. Vérifier les migrations appliquées
3. Consulter les relations entre tables
```

## Intégration avec le Développement

### Avant de Proposer des Solutions
1. **Lire** le fichier de contexte pour comprendre l'état actuel
2. **Vérifier** que la solution proposée est cohérente
3. **Référencer** les patterns et composants existants
4. **Respecter** l'architecture établie

### Pour les Nouvelles Fonctionnalités
1. **Consulter** l'état des épics existantes
2. **Vérifier** les fonctionnalités déjà implémentées
3. **Proposer** des solutions cohérentes avec l'architecture
4. **Documenter** dans le fichier de contexte si nécessaire

### Pour les Corrections et Améliorations
1. **Comprendre** le contexte avant de modifier
2. **Respecter** les patterns établis
3. **Maintenir** la cohérence avec l'existant
4. **Mettre à jour** la documentation si nécessaire

## Validation de Contexte

### Vérifications Essentielles
- **Stack technique** : Next.js 15, React 19, TypeScript
- **Authentification** : Spotify OAuth uniquement
- **Base de données** : PostgreSQL avec Drizzle ORM
- **UI/UX** : Glassmorphism, Tailwind CSS
- **Déploiement** : Vercel avec cron jobs

### Cohérence avec l'Existant
- **Composants** : Utiliser ceux existants dans `components/`
- **Patterns** : Respecter les conventions établies
- **Architecture** : Suivre la structure App Router
- **Sécurité** : Appliquer les protections existantes

Cette règle garantit que tout agent IA peut rapidement comprendre le projet Stream2Spin en consultant le fichier de contexte approprié, évitant ainsi les erreurs de compréhension et proposant des solutions cohérentes avec l'architecture existante.
