# Système de Wishlist - INDÉPENDANTE des Recommandations

## 🚨 PRINCIPE FONDAMENTAL
⚠️ **LA WISHLIST EST TOTALEMENT INDÉPENDANTE DES RECOMMANDATIONS** ⚠️

### ❌ Ancien Bug (CORRIGÉ)
- Albums disparaissaient de la wishlist quand plus recommandés
- Affichage dégradé sans pochette/liens/player

### ✅ Nouveau Comportement (depuis Jan 2025)
- Albums PERSISTENT dans la wishlist même après disparition
- Toutes les données enrichies sont sauvegardées
- Synchronisation automatique lors de la réapparition

## 🔧 Implémentation Technique

### Table `wishlist_items`
```sql
-- Clé primaire composite (pas de foreign key vers recommendations!)
PRIMARY KEY (userId, artistName, albumTitle)

-- Données persistantes OBLIGATOIRES
albumCoverUrl          -- Pochette
affiliateLinks         -- Liens d'achat Amazon  
topTrackName          -- Player Spotify
topTrackId            -- Player Spotify
topTrackPreviewUrl    -- Player Spotify
spotifyAlbumId        -- Identifiant Spotify
```

### Fonction `addToWishlist()` 
```typescript
// ✅ OBLIGATOIRE: Sauvegarder TOUTES les données enrichies
const recommendation = await db.query.recommendations.findFirst({
  columns: {
    artistName: true,
    albumTitle: true,
    albumCoverUrl: true,        // ← CRITIQUE
    affiliateLinks: true,       // ← CRITIQUE  
    topTrackName: true,         // ← CRITIQUE
    topTrackId: true,           // ← CRITIQUE
    // ... TOUS les champs enrichis
  }
});
```

### Synchronisation Automatique
```typescript
// ✅ Appelée automatiquement lors de la génération
await syncWishlistWithRecommendations(userId, newRecommendations);
```

## 🧪 Tests de Validation
1. Ajouter album à la wishlist
2. Supprimer album des recommandations  
3. ✅ Vérifier que l'album reste INTACT dans la wishlist
4. Réajouter album aux recommandations
5. ✅ Vérifier synchronisation des nouvelles données

Voir: [Guide de test](mdc:docs/test-wishlist-persistence-guide.md)
