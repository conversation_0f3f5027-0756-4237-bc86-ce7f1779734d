# Logique de Composants - Cohérence et Réutilisabilité

## Objectif
Développer avec une logique de composants cohérente pour éviter le code dupliqué et maintenir une architecture propre à travers l'application Stream2Spin.

## Architecture des Composants

### Hiérarchie des Composants
```
components/
├── ui/                    # Composants de base (atomes)
│   ├── button.tsx        # Boutons avec variantes
│   ├── input.tsx         # Champs de saisie
│   ├── card.tsx          # Conteneurs
│   ├── avatar.tsx        # Avatars utilisateur
│   └── badge.tsx         # Badges et étiquettes
├── layout/               # Composants de mise en page
│   ├── header.tsx        # En-tête de l'application
│   ├── sidebar.tsx       # Barre latérale
│   └── user-nav.tsx      # Navigation utilisateur
├── recommendations/      # Composants métier
│   ├── album-card.tsx    # Carte d'album
│   └── availability-filter.tsx
├── public/               # Composants pour profils publics
├── account/              # Composants de gestion de compte
├── auth/                 # Composants d'authentification
├── audio/                # Composants audio
├── error/                # Gestion d'erreurs
├── generating/           # Expérience de génération
└── wishlist/             # Composants de wishlist
```

## Règles de Développement

### 1. Réutilisabilité des Composants
- **Toujours vérifier** s'il existe déjà un composant similaire
- **Créer des variantes** plutôt que de dupliquer du code
- **Utiliser les props** pour personnaliser le comportement
- **Éviter** les composants trop spécifiques à une page

### 2. Structure des Composants
```typescript
// Pattern recommandé pour tous les composants
interface ComponentProps {
  // Props obligatoires
  requiredProp: string;
  // Props optionnelles avec valeurs par défaut
  optionalProp?: string;
  // Props de style
  className?: string;
  // Props d'événements
  onClick?: () => void;
}

export function Component({ 
  requiredProp, 
  optionalProp = 'default',
  className,
  onClick,
  ...props 
}: ComponentProps) {
  // Logique du composant
  return (
    <div className={cn("base-classes", className)} onClick={onClick} {...props}>
      {/* Contenu du composant */}
    </div>
  );
}
```

### 3. Utilisation des Composants UI de Base
- **Toujours utiliser** les composants de [components/ui/](mdc:Stream2Spin/components/ui/) en priorité
- **Étendre** les composants existants plutôt que d'en créer de nouveaux
- **Respecter** les patterns établis dans [button.tsx](mdc:Stream2Spin/components/ui/button.tsx)

### 4. Gestion des États
```typescript
// Pattern pour les composants avec état
export function StatefulComponent({ data }: Props) {
  const [state, setState] = useState(initialState);
  
  // Logique métier centralisée
  const handleAction = useCallback(() => {
    // Action avec gestion d'erreur
  }, [dependencies]);
  
  return (
    <div>
      {/* Rendu conditionnel basé sur l'état */}
    </div>
  );
}
```

### 5. Props et Interface
- **Définir** des interfaces TypeScript strictes
- **Utiliser** des props optionnelles avec valeurs par défaut
- **Éviter** les props trop nombreuses (max 5-6 props principales)
- **Utiliser** des objets pour les props complexes

### 6. Styling et Classes
```typescript
// Utiliser la fonction cn pour combiner les classes
import { cn } from "@/lib/utils";

// Pattern pour les classes conditionnelles
<div className={cn(
  "base-classes",
  variant && "variant-classes",
  className
)}>
```

### 7. Gestion des Erreurs
```typescript
// Pattern pour la gestion d'erreurs dans les composants
export function SafeComponent({ data }: Props) {
  if (!data) {
    return <EmptyState />;
  }
  
  if (error) {
    return <ErrorState error={error} />;
  }
  
  return <MainContent data={data} />;
}
```

## Patterns de Réutilisabilité

### 1. Composants de Liste
```typescript
// Pattern réutilisable pour les listes
interface ListProps<T> {
  items: T[];
  renderItem: (item: T, index: number) => ReactNode;
  emptyState?: ReactNode;
  className?: string;
}

export function List<T>({ items, renderItem, emptyState, className }: ListProps<T>) {
  if (items.length === 0) {
    return emptyState || <DefaultEmptyState />;
  }
  
  return (
    <div className={cn("list-container", className)}>
      {items.map((item, index) => renderItem(item, index))}
    </div>
  );
}
```

### 2. Composants de Formulaire
```typescript
// Pattern pour les formulaires réutilisables
interface FormFieldProps {
  label: string;
  error?: string;
  required?: boolean;
  children: ReactNode;
}

export function FormField({ label, error, required, children }: FormFieldProps) {
  return (
    <div className="form-field">
      <label className="form-label">
        {label} {required && <span className="required">*</span>}
      </label>
      {children}
      {error && <span className="error-message">{error}</span>}
    </div>
  );
}
```

### 3. Composants de Loading
```typescript
// Pattern pour les états de chargement
export function LoadingState({ size = "default" }: { size?: "sm" | "default" | "lg" }) {
  return (
    <div className={cn("loading-container", `loading-${size}`)}>
      <Spinner size={size} />
    </div>
  );
}
```

## Vérification de Cohérence

### Avant de Créer un Nouveau Composant
1. **Rechercher** dans [components/](mdc:Stream2Spin/components/) s'il existe déjà
2. **Vérifier** les composants UI de base dans [components/ui/](mdc:Stream2Spin/components/ui/)
3. **Considérer** si c'est une variante d'un composant existant
4. **Évaluer** si c'est vraiment nécessaire

### Avant d'Utiliser un Composant
1. **Vérifier** la documentation dans Storybook
2. **Tester** les props disponibles
3. **S'assurer** que le composant correspond au besoin
4. **Considérer** les performances et la réutilisabilité

### Maintenance Continue
1. **Refactoriser** le code dupliqué en composants réutilisables
2. **Mettre à jour** les composants existants plutôt que d'en créer de nouveaux
3. **Documenter** les nouveaux composants dans Storybook
4. **Tester** la cohérence visuelle et fonctionnelle

## Exemples de Bonnes Pratiques

### ✅ Bon - Composant Réutilisable
```typescript
// components/ui/data-card.tsx
interface DataCardProps {
  title: string;
  value: string | number;
  icon?: ReactNode;
  trend?: 'up' | 'down' | 'neutral';
  className?: string;
}

export function DataCard({ title, value, icon, trend, className }: DataCardProps) {
  return (
    <Card className={cn("data-card", className)}>
      <CardHeader>
        <div className="flex items-center gap-2">
          {icon}
          <CardTitle>{title}</CardTitle>
        </div>
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">{value}</div>
        {trend && <TrendIndicator trend={trend} />}
      </CardContent>
    </Card>
  );
}
```

### ❌ Éviter - Code Dupliqué
```typescript
// ❌ Ne pas faire - Code dupliqué dans plusieurs composants
<div className="bg-white rounded-lg p-4 shadow-sm">
  <h3 className="text-lg font-semibold">{title}</h3>
  <p className="text-2xl font-bold">{value}</p>
</div>
```

## Intégration avec Storybook

### Documentation des Composants
- **Créer** une story pour chaque nouveau composant
- **Documenter** tous les cas d'usage
- **Tester** les variantes et états
- **Maintenir** la cohérence avec le design system

### Exemple de Story
```typescript
// components/ui/data-card.stories.tsx
const meta = {
  title: 'Design System/UI/DataCard',
  component: DataCard,
  parameters: { layout: 'centered' },
  tags: ['autodocs'],
} satisfies Meta<typeof DataCard>;

export default meta;

export const Default: Story = {
  args: {
    title: 'Utilisateurs actifs',
    value: '1,234',
  },
};

export const WithIcon: Story = {
  args: {
    title: 'Recommandations',
    value: '42',
    icon: <Users className="h-4 w-4" />,
  },
};
```

Cette règle garantit une architecture de composants cohérente, réutilisable et maintenable dans l'application Stream2Spin.
