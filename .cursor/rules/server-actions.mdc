# Server Actions Stream2Spin

## 📁 Organisation
Toutes les mutations de données utilisent des Server Actions dans [app/actions/](mdc:app/actions/)

### Fichiers principaux:
- [user.ts](mdc:app/actions/user.ts) - Gestion utilisateur et recommandations
- [wishlist.ts](mdc:app/actions/wishlist.ts) - Wishlist persistante
- [profile.ts](mdc:app/actions/profile.ts) - Profils publics
- [recommendations.ts](mdc:app/actions/recommendations.ts) - Actions recommandations

## 🔒 Patterns de Sécurité
- **Session obligatoire**: Toujours vérifier `getSession()` 
- **Validation des données**: Utiliser Zod pour la validation
- **Revalidation**: `revalidatePath()` après les mutations
- **Gestion d'erreurs**: Try/catch avec messages utilisateur

## 🎯 Exemples Clés

### Wishlist Persistante
```typescript
// ✅ Bon: Sauvegarder TOUTES les données enrichies
await db.insert(wishlistItems).values({
  userId,
  artistName,
  albumTitle,
  albumCoverUrl,     // Pochette persistante
  affiliateLinks,    // Liens d'achat persistants  
  topTrackName,      // Player persistant
  // ... autres données enrichies
});
```

### Génération Recommandations
```typescript
// ✅ Bon: Synchroniser automatiquement la wishlist
await saveRecommendations(userId, recommendations);
await syncWishlistWithRecommendations(userId, recommendations);
```

## ⚠️ Règles Importantes
- **JAMAIS** de mutations directes côté client
- **TOUJOURS** invalider le cache après mutations
- **Wishlist**: Indépendante des recommandations (données persistantes)
- **Performance**: Utiliser transactions pour opérations multiples
