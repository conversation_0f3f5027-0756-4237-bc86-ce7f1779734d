# Base de Données Stream2Spin

## 🗄️ Configuration
- **ORM**: Drizzle avec PostgreSQL
- **Schéma**: [lib/db/schema.ts](mdc:lib/db/schema.ts)
- **Migrations**: Dossier [migrations/](mdc:migrations/)
- **Config**: [drizzle.config.ts](mdc:drizzle.config.ts)

## 📊 Tables Principales

### `users` - Utilisateurs
- Authentification NextAuth.js uniquement
- Champs: id, name, email, image, spotifyAccessToken, etc.
- Relations: recommendations, wishlistItems, userDiscogsCollection

### `recommendations` - Recommandations d'albums
- Algorithme de scoring basé sur position dans top tracks
- Champs: artistName, albumTitle, listenScore, isOwned, affiliateLinks
- Timeframes: short_term, medium_term, long_term
- Contrainte unique: userId + spotifyAlbumId + timeframe

### `wishlist_items` - Liste d'envies (PERSISTANTE)
⚠️ **IMPORTANT**: La wishlist est indépendante des recommandations !
- Clé primaire composite: userId + artistName + albumTitle
- Données persistées: albumCoverUrl, affiliateLinks, topTrack*, etc.
- Synchronisation automatique avec nouvelles recommandations

### `user_discogs_collection` - Collection Discogs
- Évite les doublons dans les recommandations
- Champs: userId, discogsReleaseId, artistName, albumTitle
- Sync automatique périodique

## 🔄 Patterns de Requêtes
- **Utiliser le cache** pour les requêtes fréquentes (getUserWishlist)
- **Pagination** pour les listes longues
- **Jointures optimisées** avec Drizzle relations
- **Transactions** pour les opérations multi-tables

## 🚀 Migrations
- Migrations SQL manuelles dans [migrations/](mdc:migrations/)
- Script d'application: `node apply-migration.js`
- **Dernier ajout**: Wishlist enrichie avec données persistantes
