# Architecture Stream2Spin

## 🏗️ Stack Technique
- **Framework**: Next.js 15 avec App Router
- **Base de données**: PostgreSQL avec Drizzle ORM
- **Authentification**: NextAuth.js (Spotify OAuth uniquement)
- **Déploiement**: Vercel avec cron jobs
- **Styling**: Tailwind CSS + Glassmorphism
- **Intégrations**: Spotify API, Discogs API, Amazon Affiliate

## 📁 Structure des Dossiers Clés
- `app/` - Pages et API routes (App Router)
- `app/actions/` - Server Actions pour les mutations
- `app/api/` - API Routes pour les intégrations externes
- `lib/` - Utilitaires, DB schema, intégrations APIs
- `components/` - Composants React réutilisables
- `migrations/` - Migrations SQL manuelles
- `scripts/` - Scripts de maintenance et déploiement

## 🔄 Patterns de Code Importants
- **Server Actions** pour toutes les mutations de données
- **Streaming SSE** pour la génération de recommandations
- **Cache en mémoire** pour optimiser les performances DB
- **Middleware** pour l'authentification et la protection des routes
- **Wishlist persistante** indépendante des recommandations

## 🎵 Workflow Principal
1. Connexion Spotify → Récupération top tracks (3 timeframes)
2. Analyse algorithmique → Scoring par position + estimation écoutes  
3. Croisement Discogs → Évitement des doublons
4. Génération liens Amazon → Liens affiliés avec tag
5. Sauvegarde DB → Recommandations persistantes
6. Notifications → Emails + push notifications

Consultez [cursor.md](mdc:cursor.md) pour la documentation technique complète.
