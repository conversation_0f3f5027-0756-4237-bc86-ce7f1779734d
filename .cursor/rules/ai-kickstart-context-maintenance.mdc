# Maintenance du fichier ai-kickstart-context.md

## Objectif
Maintenir à jour le fichier [ai-kickstart-context.md](mdc:Stream2Spin/ai-kickstart-context.md) pour qu'il reflète fidèlement l'état actuel du projet Stream2Spin.

## Règles de mise à jour

### 1. Fonctionnalités implémentées vs en développement
- **Marquer clairement** les fonctionnalités implémentées avec ✅ ou "(implémenté)"
- **Marquer les fonctionnalités en développement** avec 🔄 ou "(en développement)"
- **Commenter le code** des fonctionnalités non encore implémentées

### 2. Structure du projet
- **Mettre à jour** la structure des dossiers dans la section "Structure du Projet"
- **Ajouter/supprimer** les dossiers et fichiers selon l'état actuel
- **Vérifier** que les routes API et pages correspondent à l'implémentation

### 3. Base de données
- **Actualiser** le schéma des tables selon les migrations appliquées
- **Commenter** les tables en développement (ex: Epic 17)
- **Vérifier** que les colonnes correspondent aux migrations réelles

### 4. Stack technologique
- **Mettre à jour** les versions des dépendances selon [package.json](mdc:Stream2Spin/package.json)
- **Ajouter** les nouvelles technologies intégrées
- **Supprimer** les technologies abandonnées

### 5. Scripts et commandes
- **Synchroniser** les scripts avec [package.json](mdc:Stream2Spin/package.json)
- **Ajouter** les nouveaux scripts de développement
- **Mettre à jour** les commandes de déploiement

### 6. Variables d'environnement
- **Vérifier** que toutes les variables nécessaires sont listées
- **Ajouter** les nouvelles variables selon les besoins
- **Supprimer** les variables obsolètes

### 7. Fonctionnalités sociales
- **Distinguer** Epic 16 (profils publics) implémentée vs Epic 17 (suivi, feed) en développement
- **Commenter** le code des fonctionnalités non encore disponibles
- **Préciser** l'état d'implémentation dans les descriptions

### 8. Storybook
- **Maintenir** la liste des composants documentés
- **Ajouter** les nouvelles stories créées
- **Mettre à jour** la configuration selon [.storybook/main.ts](mdc:Stream2Spin/.storybook/main.ts)

### 9. Sécurité
- **Actualiser** les scripts de sécurité selon [scripts/](mdc:Stream2Spin/scripts/)
- **Vérifier** que les protections de debug sont documentées
- **Maintenir** les exemples de code de sécurité

### 10. Monitoring et déploiement
- **Synchroniser** avec [vercel.json](mdc:Stream2Spin/vercel.json)
- **Mettre à jour** les cron jobs selon l'implémentation
- **Vérifier** les métriques et analytics

## Processus de mise à jour

### Avant chaque modification majeure
1. **Lire** le fichier [ai-kickstart-context.md](mdc:Stream2Spin/ai-kickstart-context.md)
2. **Identifier** les sections à mettre à jour
3. **Vérifier** l'état actuel du code
4. **Mettre à jour** de manière cohérente

### Après chaque implémentation
1. **Mettre à jour** la checklist de compréhension
2. **Actualiser** la structure du projet si nécessaire
3. **Synchroniser** le schéma de base de données
4. **Vérifier** que les exemples de code correspondent

### Validation
- **Tester** que les commandes listées fonctionnent
- **Vérifier** que les chemins de fichiers sont corrects
- **S'assurer** que l'état des fonctionnalités est précis

## Exemples de mise à jour

### Ajout d'une nouvelle fonctionnalité
```markdown
### 📋 Checklist de Compréhension
- [ ] **Nouvelle fonctionnalité** : Description (implémenté/en développement)
```

### Mise à jour du schéma DB
```typescript
// Nouvelle table implémentée
export const newTable = pgTable('new_table', {
  // ... colonnes
});

// Table en développement (commentée)
// export const futureTable = pgTable('future_table', {
//   // ... colonnes
// });
```

### Ajout d'un nouveau script
```json
{
  "scripts": {
    "nouveau-script": "commande",
    // ... autres scripts
  }
}
```

Cette règle garantit que le fichier [ai-kickstart-context.md](mdc:Stream2Spin/ai-kickstart-context.md) reste une référence fiable et à jour pour tout développeur IA travaillant sur le projet Stream2Spin.
