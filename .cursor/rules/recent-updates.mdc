# 🚨 MISES À JOUR CRITIQUES - Janvier 2025

## ⚠️ RÈGLES ABSOLUES
**Toute IA travaillant sur ce projet DOIT connaître ces changements récents !**

### 1. Wishlist Persistante - BUG MAJEUR CORRIGÉ
**Date:** 3 janvier 2025  
**Impact:** Critique - Fonctionnalité principale  
**Statut:** ✅ Corrigé en staging

#### Problème (RÉSOLU)
- Albums disparaissaient de la wishlist quand plus recommandés
- Affichage dégradé sans pochette/liens/player

#### Solution Implémentée
```typescript
// ✅ OBLIGATOIRE: Sauvegarder TOUTES les données enrichies
await db.insert(wishlistItems).values({
  userId,
  artistName,
  albumTitle,
  albumCoverUrl,       // ← CRITIQUE
  affiliateLinks,      // ← CRITIQUE
  topTrackName,        // ← CRITIQUE
  topTrackId,          // ← CRITIQUE
  topTrackPreviewUrl,  // ← CRITIQUE
  // ... TOUS les champs
});
```

#### ⚠️ INTERDICTIONS ABSOLUES
1. **JAMAIS** toucher à `addToWishlist()` sans sauvegarder toutes les données
2. **JAMAIS** créer de foreign key entre wishlist et recommendations
3. **JAMAIS** oublier `syncWishlistWithRecommendations()` après génération

#### ✅ Tests Obligatoires
1. Ajouter album à wishlist
2. Supprimer des recommandations
3. Vérifier que l'album reste INTACT dans wishlist
4. Réajouter aux recommandations
5. Vérifier synchronisation

### 2. Migration DB - APPLIQUÉE
- Nouvelles colonnes dans `wishlist_items`
- Index GIN sur `affiliate_links`
- Script: `migrations/add-wishlist-enriched-data.sql`

### 3. Documentation Mise à Jour
- [cursor.md](mdc:cursor.md) - Section wishlist enrichie
- [Guide de test](mdc:docs/test-wishlist-persistence-guide.md)
- [Rapport de déploiement](mdc:docs/DEPLOYMENT-WISHLIST-PERSISTENCE.md)

### 4. Environnements
- ✅ Staging: https://stream2spin-staging.vercel.app
- 🔄 Production: En attente de validation

---

**Si vous travaillez sur la wishlist, lisez OBLIGATOIREMENT:**
[Guide de Test](mdc:docs/test-wishlist-persistence-guide.md)
