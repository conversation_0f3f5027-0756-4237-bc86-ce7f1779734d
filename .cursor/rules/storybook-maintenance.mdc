# Maintenance de Storybook au fil du développement

## Objectif
Maintenir à jour la bibliothèque Storybook du projet Stream2Spin pour qu'elle documente fidèlement tous les composants et serve de référence pour le développement.

## Configuration Storybook

### Fichiers de configuration
- **Configuration principale** : [.storybook/main.ts](mdc:Stream2Spin/.storybook/main.ts)
- **Configuration TypeScript** : [.storybook/tsconfig.json](mdc:Stream2Spin/.storybook/tsconfig.json)
- **Dépendances** : Vérifier [package.json](mdc:Stream2Spin/package.json) pour les addons Storybook

### Structure des stories
```
components/
├── ui/
│   ├── button.stories.tsx          # Composants de base
│   ├── input.stories.tsx
│   ├── card.stories.tsx
│   ├── avatar.stories.tsx
│   └── badge.stories.tsx
├── layout/
│   ├── header.stories.tsx          # Composants de layout
│   ├── sidebar.stories.tsx
│   └── user-nav.stories.tsx
└── recommendations/
    └── album-card.stories.tsx      # Composants métier
```

## Règles de maintenance

### 1. Création de nouvelles stories
- **Toujours créer** une story pour chaque nouveau composant
- **Suivre le pattern** établi dans [button.stories.tsx](mdc:Stream2Spin/components/ui/button.stories.tsx)
- **Utiliser les métadonnées** appropriées (title, component, parameters)
- **Ajouter les contrôles** interactifs avec argTypes

### 2. Organisation des stories
- **Hiérarchie claire** : `Design System/UI/ComponentName`
- **Groupement logique** : UI de base, Layout, Composants métier
- **Noms explicites** : `Default`, `WithImage`, `Disabled`, etc.

### 3. Documentation des composants
- **Décrire** le rôle et l'usage de chaque composant
- **Documenter** toutes les props et variantes
- **Inclure** des exemples d'utilisation
- **Ajouter** des notes sur les bonnes pratiques

### 4. Gestion des dépendances
- **Mocker** les dépendances externes (Next.js Image, etc.)
- **Utiliser** des décorateurs pour simuler les contextes
- **Éviter** les imports directs de composants complexes

### 5. Données de test
- **Créer** des données mock réalistes
- **Varier** les cas d'usage (états vides, erreurs, etc.)
- **Maintenir** la cohérence avec les vraies données

### 6. Contrôles interactifs
- **Exposer** les props importantes via argTypes
- **Permettre** de tester les variantes
- **Faciliter** les tests visuels

## Processus de mise à jour

### Avant d'ajouter un nouveau composant
1. **Vérifier** s'il existe déjà une story
2. **Déterminer** la catégorie appropriée
3. **Préparer** les données mock nécessaires

### Après avoir créé un composant
1. **Créer** immédiatement la story correspondante
2. **Tester** tous les états et variantes
3. **Documenter** l'usage et les props
4. **Vérifier** que Storybook démarre correctement

### Maintenance continue
1. **Synchroniser** les stories avec les changements de composants
2. **Mettre à jour** les données mock si nécessaire
3. **Vérifier** que les contrôles fonctionnent
4. **Tester** les nouvelles fonctionnalités dans Storybook

## Exemples de patterns

### Story de base (composant UI)
```typescript
// components/ui/component.stories.tsx
import type { Meta, StoryObj } from '@storybook/nextjs-vite';
import { Component } from './component';

const meta = {
  title: 'Design System/UI/Component',
  component: Component,
  parameters: { layout: 'centered' },
  tags: ['autodocs'],
  argTypes: {
    // Contrôles interactifs
  },
} satisfies Meta<typeof Component>;

export default meta;

export const Default: Story = {
  args: {
    // Props par défaut
  },
};
```

### Story avec données mock
```typescript
// Données mock réalistes
const mockData = {
  // Données cohérentes avec l'API
};

export const WithData: Story = {
  args: {
    data: mockData,
  },
};
```

### Story avec décorateur
```typescript
// Pour les composants avec contexte
const meta = {
  // ... configuration
  decorators: [
    (Story) => (
      <ContextProvider>
        <Story />
      </ContextProvider>
    ),
  ],
};
```

## Commandes Storybook

### Développement
```bash
pnpm storybook          # Démarre Storybook en mode dev
pnpm build-storybook    # Build de production
```

### Ports
- **Développement** : Port 6006 (avec fallback 6007)
- **Production** : Build statique

## Intégration avec le développement

### Workflow recommandé
1. **Développer** le composant
2. **Créer** la story immédiatement
3. **Tester** dans Storybook
4. **Documenter** l'usage
5. **Intégrer** dans l'application

### Validation
- **Vérifier** que toutes les variantes s'affichent
- **Tester** les interactions utilisateur
- **S'assurer** que la documentation est claire
- **Valider** que les contrôles fonctionnent

## Ressources Storybook

### Documentation
- **URL locale** : http://localhost:6006
- **Structure** : Organisée par catégories
- **Recherche** : Fonctionnelle sur tous les composants

### Addons disponibles
- **@storybook/addon-docs** : Documentation automatique
- **@storybook/addon-links** : Navigation entre stories
- **@chromatic-com/storybook** : Tests visuels

Cette règle garantit que Storybook reste une ressource fiable et complète pour le développement de l'interface utilisateur de Stream2Spin.
