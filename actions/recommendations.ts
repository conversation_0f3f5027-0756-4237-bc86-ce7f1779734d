'use server';

import { revalidatePath } from 'next/cache';
import { redirect } from 'next/navigation';

/**
 * Server Action pour forcer la revalidation des données de recommandations
 * et rediriger l'utilisateur vers la page des recommandations
 * 
 * Cette action est utilisée après la génération de recommandations
 * pour s'assurer que l'utilisateur voit immédiatement toutes les recommandations
 * générées sans cache stale.
 */
export async function revalidateAndRedirectToRecommendations() {
  try {
    // Forcer la revalidation de la page des recommandations
    revalidatePath('/recommendations');
    
    // Revalider également les données de l'utilisateur qui pourraient être utilisées
    revalidatePath('/account');
    
    // Rediriger l'utilisateur vers les recommandations
    redirect('/recommendations?from=generating');
  } catch (error) {
    console.error('❌ Erreur lors de la revalidation et redirection:', error);
    // En cas d'erreur, rediriger quand même vers les recommandations
    redirect('/recommendations?from=generating');
  }
}

/**
 * Server Action pour forcer uniquement la revalidation des recommandations
 * sans redirection (utilisée pour les rafraîchissements)
 */
export async function revalidateRecommendations() {
  try {
    revalidatePath('/recommendations');
    return { success: true };
  } catch (error) {
    console.error('❌ Erreur lors de la revalidation des recommandations:', error);
    return { success: false, error: 'Erreur lors de la revalidation' };
  }
} 