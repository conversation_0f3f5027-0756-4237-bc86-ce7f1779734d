import { withAuth } from "next-auth/middleware";
import { NextResponse } from "next/server";

export default withAuth(
  async function middleware(req) {
    const { nextUrl } = req;
    const token = req.nextauth.token;
    const isLoggedIn = !!token;

    // Lai<PERSON> passer les requêtes OPTIONS sans traitement avec headers CORS appropriés
    if (req.method === 'OPTIONS') {
      const response = new NextResponse(null, { status: 200 });
      response.headers.set('Access-Control-Allow-Origin', '*');
      response.headers.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
      response.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization');
      return response;
    }

    // Debug logs conditionnels (seulement en développement)
    if (process.env.NODE_ENV === 'development') {
      console.log(`🔍 Middleware - Path: ${nextUrl.pathname}, Logged in: ${isLoggedIn}, Token exists: ${!!token}`);
    }

    // Éviter les boucles de redirection en vérifiant les paramètres d'erreur
    const hasError = nextUrl.searchParams.has('error');
    const hasCallbackUrl = nextUrl.searchParams.has('callbackUrl');

    // Définir les types de routes
    const isAuthRoute = nextUrl.pathname === '/login'; // Seulement la page de login exacte
    const isLoginRedirectRoute = nextUrl.pathname === '/login-redirect';
    const isProtectedRoute = nextUrl.pathname.startsWith('/recommendations') ||
                            nextUrl.pathname.startsWith('/account') ||
                            nextUrl.pathname.startsWith('/collection') ||
                            nextUrl.pathname.startsWith('/statistics') ||
                            nextUrl.pathname.startsWith('/generating') ||
                            nextUrl.pathname.startsWith('/wishlist') ||
                            nextUrl.pathname.startsWith('/mes-envies') ||
                            nextUrl.pathname.startsWith('/social') ||
                            isLoginRedirectRoute;
    const isPublicRoute = nextUrl.pathname === '/' ||
                         nextUrl.pathname.startsWith('/terms') ||
                         nextUrl.pathname.startsWith('/privacy') ||
                         nextUrl.pathname.startsWith('/api/auth') ||
                         nextUrl.pathname.startsWith('/error') ||
                         nextUrl.pathname.startsWith('/public/') ||
                         nextUrl.pathname.startsWith('/u/') ||
                         nextUrl.pathname === '/not-found';

    // Ne pas rediriger si on est en cours de traitement d'une erreur OAuth
    if (hasError) {
      if (process.env.NODE_ENV === 'development') {
        console.log(`⚠️ OAuth error detected, allowing access to ${nextUrl.pathname}`);
      }
      return NextResponse.next();
    }

    // Permettre l'accès à /login-redirect pour les utilisateurs connectés (pas de redirection)
    if (isLoginRedirectRoute && isLoggedIn) {
      if (process.env.NODE_ENV === 'development') {
        console.log(`➡️ Allowing access to /login-redirect for logged user`);
      }
      return NextResponse.next();
    }

    // 1. Rediriger les utilisateurs connectés loin de la page de connexion
    if (isAuthRoute && isLoggedIn && !hasCallbackUrl && !hasError) {
      if (process.env.NODE_ENV === 'development') {
        console.log(`✅ Redirecting logged user from ${nextUrl.pathname} to /login-redirect`);
      }
      return NextResponse.redirect(new URL('/login-redirect', nextUrl));
    }

    // 2. Protéger les routes qui nécessitent une connexion
    if (isProtectedRoute && !isLoggedIn) {
      if (process.env.NODE_ENV === 'development') {
        console.log(`🔒 Protecting route ${nextUrl.pathname}, redirecting to /login`);
      }
      const loginUrl = new URL('/login', nextUrl);
      return NextResponse.redirect(loginUrl);
    }

    // 3. Rediriger la page d'accueil selon l'état de l'utilisateur
    if (nextUrl.pathname === '/' && isLoggedIn) {
      if (process.env.NODE_ENV === 'development') {
        console.log(`🏠 Redirecting home to /login-redirect`);
      }
      return NextResponse.redirect(new URL('/login-redirect', nextUrl));
    }

    // Laisser passer toutes les autres requêtes
    if (process.env.NODE_ENV === 'development') {
      console.log(`➡️ Allowing access to ${nextUrl.pathname}`);
    }
    return NextResponse.next();
  },
  {
    callbacks: {
      authorized: ({ token, req }) => {
        // Permettre l'accès aux routes publiques sans token
        const { pathname } = req.nextUrl;

        const isPublicRoute = pathname === '/' ||
                             pathname.startsWith('/login') ||
                             pathname.startsWith('/terms') ||
                             pathname.startsWith('/privacy') ||
                             pathname.startsWith('/api/auth') ||
                             pathname.startsWith('/error') ||
                             pathname.startsWith('/public/') ||
                             pathname.startsWith('/u/') ||
                             pathname === '/not-found';

        if (isPublicRoute) {
          return true;
        }

        // Pour les routes protégées, vérifier le token
        return !!token;
      },
    },
  }
);

export const config = {
  matcher: ['/((?!api|_next/static|_next/image|favicon.ico|.*\\.jpg|.*\\.png|.*\\.gif|.*\\.svg|.*\\.ico).*)'],
};
