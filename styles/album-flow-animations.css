/* Animations pour la grille d'arrière-plan - Nouvelles spécifications Epic 11 */

/* Animation "Pop" pour l'apparition de nouvelles pochettes */
@keyframes album-pop {
  0% {
    opacity: 0;
    transform: scale(0.8);
  }
  50% {
    opacity: 0.9;
    transform: scale(1.05);
  }
  100% {
    opacity: 0.7;
    transform: scale(1);
  }
}

/* Animation "Flip" 3D pour le remplacement de pochettes */
@keyframes album-flip {
  0% {
    transform: rotateY(0deg);
    opacity: 0.7;
  }
  50% {
    transform: rotateY(90deg);
    opacity: 0.3;
  }
  100% {
    transform: rotateY(0deg);
    opacity: 0.7;
  }
}

/* Animation pour le texte qui glisse vers le haut */
@keyframes slide-up {
  0% {
    opacity: 0;
    transform: translateY(100%);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Animation pour les points de chargement */
@keyframes bounce-dots {
  0%, 80%, 100% {
    transform: scale(0);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Classes d'animation selon les spécifications Epic 11 */
.animate-album-pop {
  animation: album-pop 0.6s ease-out forwards;
  will-change: transform, opacity;
}

.animate-album-flip {
  animation: album-flip 0.8s ease-in-out forwards;
  will-change: transform, opacity;
  transform-style: preserve-3d;
}

.animate-slide-up {
  animation: slide-up 0.3s ease-out forwards;
  will-change: transform, opacity;
}

/* Animation pour les points de chargement avec délais */
.animate-bounce-dots {
  animation: bounce-dots 1.4s infinite ease-in-out;
}

.animate-bounce-dots:nth-child(1) {
  animation-delay: -0.32s;
}

.animate-bounce-dots:nth-child(2) {
  animation-delay: -0.16s;
}

.animate-bounce-dots:nth-child(3) {
  animation-delay: 0s;
}

.animate-fade-in-album {
  animation: fade-in-album 1s ease-out forwards;
}

.animate-slide-up {
  animation: slide-up 0.5s ease-out forwards;
}

.animate-pulse-slow {
  animation: pulse-slow 3s ease-in-out infinite;
}

@keyframes pulse-slow {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.3;
  }
}

@keyframes bounce-dots {
  0%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-8px);
  }
}

@keyframes flip {
  0% {
    transform: rotateY(0deg);
  }
  50% {
    transform: rotateY(90deg);
  }
  100% {
    transform: rotateY(0deg);
  }
}

@keyframes pop {
  0% {
    transform: scale(0);
    opacity: 0;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.8;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

.animate-bounce-dots {
  animation: bounce-dots 1.5s ease-in-out infinite;
}

.animate-flip {
  animation: flip 1.2s ease-in-out;
  transform-style: preserve-3d;
}

.animate-pop {
  animation: pop 0.6s ease-out;
}

/* Optimisations de performance */
.album-flow-container {
  will-change: transform;
  backface-visibility: hidden;
  perspective: 1000px;
  transform: translateZ(0);
  contain: layout style paint;
}

.album-flow-item {
  will-change: transform, opacity;
  transform: translate3d(0, 0, 0);
  backface-visibility: hidden;
  contain: layout style paint;
}

/* Optimisation spécifique pour éviter les glitches lors des ajouts */
.animate-scroll-left,
.animate-scroll-right,
.animate-scroll-left-slow {
  contain: layout style paint;
  isolation: isolate;
}
