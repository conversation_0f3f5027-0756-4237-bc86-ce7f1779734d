require('dotenv').config({ path: '.env.local' });
const { getSocialFeed } = require('./app/actions/social.ts');

async function testQueueTrigger() {
  console.log('--- Début du test de déclenchement de la file d\'attente ---');
  
  // Simuler une session utilisateur
  // Note: getSocialFeed utilise getSession() qui lira la session de l'environnement de test si disponible,
  // mais pour ce test, nous nous assurons surtout que la logique interne est appelée.
  // Nous avons besoin d'au moins un utilisateur suivi pour que la logique se déclenche.
  
  // Pour ce test, nous allons assumer que l'utilisateur a des personnes qu'il suit.
  // La fonction `getSocialFeed` va récupérer ces personnes et appeler `queueStaleProfileRefreshes`.
  
  console.log('Appel de getSocialFeed pour simuler une visite sur /social...');
  await getSocialFeed();
  
  console.log('--- Fin du test ---');
  console.log('Veuillez vérifier les logs du serveur (dev.log) pour voir si des jobs ont été ajoutés à la file.');
}

testQueueTrigger();

