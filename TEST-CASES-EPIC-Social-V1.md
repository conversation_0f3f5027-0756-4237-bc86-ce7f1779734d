# Cahier de Test Utilisateur - Epic "Social V1"

## 🎯 Objectif
Ce document décrit les scénarios de test à effectuer par un utilisateur pour valider le bon fonctionnement, l'ergonomie et la robustesse des fonctionnalités sociales introduites par l'Epic "Social V1".

## 🧑‍🤝‍🧑 Profils de Test
Pour réaliser ces tests, il est recommandé d'utiliser au moins trois comptes utilisateurs distincts :
- **Utilisateur A (Testeur Principal)**
- **Utilisateur B**
- **Utilisateur C**

---

## 🧪 Scénarios de Test par User Story

### US-02 & US-03 : Système de Suivi et Modales Sociales

#### Scénario 2.1 : Suivre un autre utilisateur
1.  **Prérequis** : L'Utilisateur A n'est pas en train de suivre l'Utilisateur B.
2.  **Action** : Connecté en tant qu'Utilisateur A, naviguer vers le profil de l'Utilisateur B (`/u/[id_de_B]`).
3.  **Vérification** :
    - Le bouton "Suivre" est visible et cliquable.
    - Cliquer sur "Suivre".
    - **Résultat Attendu** : Le bouton se transforme immédiatement en "Ne plus suivre". Le compteur "followers" sur le profil de B augmente de 1. Le compteur "following" sur le profil de A augmente de 1.

#### Scénario 2.2 : Ne plus suivre un utilisateur
1.  **Prérequis** : L'Utilisateur A suit l'Utilisateur B.
2.  **Action** : Connecté en tant qu'Utilisateur A, naviguer vers le profil de l'Utilisateur B.
3.  **Vérification** :
    - Le bouton "Ne plus suivre" est visible.
    - Cliquer sur "Ne plus suivre".
    - **Résultat Attendu** : Le bouton se transforme immédiatement en "Suivre". Les compteurs "followers" et "following" sont mis à jour en conséquence.

#### Scénario 2.3 : Consultation des modales "Followers" et "Following"
1.  **Prérequis** : L'Utilisateur B suit l'Utilisateur C. L'Utilisateur A suit l'Utilisateur B.
2.  **Action** : Connecté en tant qu'Utilisateur A, naviguer vers le profil de l'Utilisateur B.
3.  **Vérification** :
    - Cliquer sur le compteur "following".
    - **Résultat Attendu** : Une modale s'ouvre sur l'onglet "Following". L'Utilisateur C doit apparaître dans la liste.
    - Cliquer sur l'onglet "Followers" dans la même modale.
    - **Résultat Attendu** : L'Utilisateur A doit apparaître dans la liste.
    - Dans la liste "Following", le bouton à côté de l'Utilisateur C doit être "Suivre" (car A ne suit pas C). Cliquer dessus.
    - **Résultat Attendu** : Le bouton devient "Ne plus suivre".

#### Scénario 2.4 : Cas Limites
1.  **Action** : Connecté en tant qu'Utilisateur A, naviguer vers son propre profil (`/account` ou `/u/[id_de_A]`).
2.  **Vérification** :
    - **Résultat Attendu** : Le bouton "Suivre" ne doit pas être visible.
    - Tenter de se suivre soi-même via une manipulation (si possible).
    - **Résultat Attendu** : Le système doit l'empêcher avec une erreur.

---

### US-04 & US-09 : Recherche et Suggestion d'Utilisateurs

#### Scénario 4.1 : Recherche d'un utilisateur existant
1.  **Action** : Connecté en tant qu'Utilisateur A, naviguer vers la page `/social`.
2.  **Vérification** :
    - Dans le champ de recherche, taper les premières lettres du nom de l'Utilisateur B.
    - **Résultat Attendu** : L'Utilisateur B apparaît dans les résultats de recherche. Un clic sur son nom redirige vers son profil.

#### Scénario 4.2 : Recherche d'un utilisateur inexistant
1.  **Action** : Sur la page `/social`, taper un nom aléatoire (ex: "XyzAbc").
2.  **Vérification** :
    - **Résultat Attendu** : Un message "Aucun utilisateur trouvé" s'affiche.

#### Scénario 9.1 : Vérification des suggestions
1.  **Prérequis** : L'Utilisateur A ne suit pas `<EMAIL>`.
2.  **Action** : Connecté en tant qu'Utilisateur A, naviguer vers la page `/social`.
3.  **Vérification** :
    - **Résultat Attendu** : Le module "Suggestions pour vous" est visible. Le profil de `<EMAIL>` doit être dans la liste avec la justification "Suggestion de l'équipe".

---

### US-05 & US-06 : Feed Social et Lecteur Audio

#### Scénario 5.1 : Affichage du feed
1.  **Prérequis** : L'Utilisateur A suit l'Utilisateur B. L'Utilisateur B a des recommandations récentes.
2.  **Action** : Connecté en tant qu'Utilisateur A, naviguer vers la page `/social`.
3.  **Vérification** :
    - **Résultat Attendu** : Le feed affiche les recommandations de l'Utilisateur B. Chaque carte d'album indique "Recommandé par [Avatar] Utilisateur B".

#### Scénario 5.2 : État vide du feed
1.  **Prérequis** : L'Utilisateur C ne suit personne.
2.  **Action** : Connecté en tant qu'Utilisateur C, naviguer vers la page `/social`.
3.  **Vérification** :
    - **Résultat Attendu** : Le feed affiche un message clair indiquant qu'il est vide et invitant à suivre des utilisateurs.

#### Scénario 6.1 : Utilisation du lecteur Spotify Embed
1.  **Action** : Sur le feed de l'Utilisateur A, trouver une carte d'album avec un lecteur.
2.  **Vérification** :
    - Cliquer sur le bouton "Play" du lecteur.
    - **Résultat Attendu** : L'extrait audio de la piste se lance.
    - Lancer la lecture sur une deuxième carte d'album.
    - **Résultat Attendu** : La deuxième piste se lance. La première piste peut continuer à jouer (comportement attendu pour la V1).

---

### US-10 : Notifications

#### Scénario 10.1 : Réception d'une notification
1.  **Prérequis** : L'Utilisateur B a les notifications par email activées.
2.  **Action** : Connecté en tant qu'Utilisateur A, suivre l'Utilisateur B.
3.  **Vérification** :
    - **In-App** : L'Utilisateur B, en rafraîchissant son application, doit voir un badge sur l'icône "Cloche". En cliquant, il voit la notification "Utilisateur A vous suit maintenant.".
    - **Email** : L'Utilisateur B doit recevoir un email l'informant que l'Utilisateur A le suit.
    - Cliquer sur la notification in-app.
    - **Résultat Attendu** : Redirection vers le profil de l'Utilisateur A.

---

### US-11 : Contrôles de Visibilité et Confidentialité

#### Scénario 11.1 : Profil Privé
1.  **Action** : Connecté en tant qu'Utilisateur B, aller dans `/account` et régler la visibilité du profil sur "Privé".
2.  **Vérification** :
    - Connecté en tant qu'Utilisateur A, essayer d'accéder au profil de l'Utilisateur B (`/u/[id_de_B]`).
    - **Résultat Attendu** : L'accès est refusé, un message "Ce profil est privé" s'affiche.
    - Dans la recherche d'utilisateurs, taper le nom de l'Utilisateur B.
    - **Résultat Attendu** : L'Utilisateur B n'apparaît pas dans les résultats.

#### Scénario 11.2 : Partage granulaire
1.  **Action** : Connecté en tant qu'Utilisateur B, régler la visibilité sur "Utilisateurs Stream2Spin", mais décocher "Partager mes recommandations".
2.  **Vérification** :
    - Connecté en tant qu'Utilisateur A (qui suit B), naviguer vers la page `/social`.
    - **Résultat Attendu** : Les recommandations de l'Utilisateur B n'apparaissent plus dans le feed de l'Utilisateur A.
    - Naviguer vers le profil de l'Utilisateur B.
    - **Résultat Attendu** : L'onglet "Recommandations" sur son profil doit être vide ou afficher un message indiquant que l'utilisateur ne partage pas cette section.

#### Scénario 11.3 : Test de la langue
1.  **Action** : Connecté en tant qu'Utilisateur A, régler la langue préférée sur "Anglais" dans `/account`.
2.  **Vérification** :
    - **Résultat Attendu** : Tous les nouveaux textes de l'interface sociale (boutons "Follow", titres des modales, etc.) doivent s'afficher en anglais.
    - Demander à l'Utilisateur C de suivre l'Utilisateur A.
    - **Résultat Attendu** : L'email de notification reçu par l'Utilisateur A doit être entièrement en anglais.
