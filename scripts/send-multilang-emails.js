/**
 * Script pour envoyer des emails dans les deux langues avec les vraies données utilisateur
 * Version JavaScript simple avec chargement correct des variables d'environnement
 */

// Charger les variables d'environnement en premier
require('dotenv').config({ path: '.env.local' });

const { render } = require('@react-email/render');
const { Resend } = require('resend');
const fs = require('fs');
const path = require('path');

// Configuration
const TARGET_EMAIL = '<EMAIL>';
const LANGUAGES = ['fr', 'en'];

// Vérifier la configuration
if (!process.env.RESEND_API_KEY) {
  console.error('❌ RESEND_API_KEY manquante dans .env.local');
  process.exit(1);
}

if (!process.env.DATABASE_URL) {
  console.error('❌ DATABASE_URL manquante dans .env.local');
  process.exit(1);
}

// Initialiser Resend
const resend = new Resend(process.env.RESEND_API_KEY);

// Configuration des emails
const EMAIL_CONFIG = {
  from: process.env.RESEND_FROM_EMAIL || 'Stream2Spin <<EMAIL>>',
  replyTo: process.env.RESEND_REPLY_TO || '<EMAIL>',
};

/**
 * Charge les messages de traduction
 */
async function loadTranslations() {
  const translations = {};
  
  for (const lang of LANGUAGES) {
    const messagesPath = path.join(__dirname, '..', 'messages', `${lang}.json`);
    const messagesContent = fs.readFileSync(messagesPath, 'utf-8');
    translations[lang] = JSON.parse(messagesContent);
  }
  
  return translations;
}

/**
 * Crée une fonction de traduction pour une langue donnée
 */
function createTranslator(messages, lang) {
  return function t(key, values = {}) {
    const keys = key.split('.');
    let translation = messages;
    
    for (const k of keys) {
      if (translation && typeof translation === 'object') {
        translation = translation[k];
      } else {
        return key; // Retourner la clé si traduction non trouvée
      }
    }
    
    if (typeof translation !== 'string') {
      return key;
    }
    
    // Remplacer les variables simples {name}
    let result = translation;
    for (const [varName, varValue] of Object.entries(values)) {
      result = result.replace(new RegExp(`{${varName}}`, 'g'), String(varValue));
    }
    
    // Gestion simplifiée des pluriels pour totalCount
    if (result.includes('plural')) {
      const totalCount = values.totalCount || 1;
      if (totalCount === 1) {
        result = result.replace(/{totalCount, plural, =1 \{([^}]+)\} other \{([^}]+)\}}/g, '$1');
      } else {
        result = result.replace(/{totalCount, plural, =1 \{([^}]+)\} other \{([^}]+)\}}/g, '$2');
      }
      result = result.replace(/{totalCount}/g, String(totalCount));
    }
    
    return result;
  };
}

/**
 * Récupère les données utilisateur depuis la base de données
 */
async function getUserData() {
  console.log(`🔍 Recherche de l'utilisateur ${TARGET_EMAIL}...`);
  
  // Importer les modules DB après que les variables d'environnement soient chargées
  const { db } = await import('../lib/db');
  const { users, recommendations, accounts } = await import('../lib/db/schema');
  const { eq, and, desc } = await import('drizzle-orm');
  
  const user = await db.query.users.findFirst({
    where: eq(users.email, TARGET_EMAIL),
    columns: {
      id: true,
      name: true,
      email: true,
      preferredLanguage: true,
      emailFrequency: true,
      image: true,
    }
  });
  
  if (!user) {
    throw new Error(`Utilisateur ${TARGET_EMAIL} non trouvé`);
  }
  
  console.log(`✅ Utilisateur trouvé: ${user.name} (${user.id})`);
  console.log(`🗣️  Langue préférée: ${user.preferredLanguage}`);
  
  return user;
}

/**
 * Récupère les recommandations réelles de l'utilisateur
 */
async function getUserRecommendations(userId) {
  console.log(`🎵 Récupération des recommandations pour l'utilisateur ${userId}...`);
  
  const { db } = await import('../lib/db');
  const { recommendations } = await import('../lib/db/schema');
  const { eq, and, desc } = await import('drizzle-orm');
  
  const userRecommendations = await db.query.recommendations.findMany({
    where: and(
      eq(recommendations.userId, userId),
      eq(recommendations.timeframe, 'short_term'),
      eq(recommendations.isOwned, false)
    ),
    orderBy: [desc(recommendations.listenScore)],
    limit: 10,
    columns: {
      artistName: true,
      albumTitle: true,
      albumCoverUrl: true,
      listenScore: true,
      affiliateLinks: true,
      isOwned: true,
      topTrackName: true,
      topTrackId: true,
    }
  });
  
  console.log(`📊 ${userRecommendations.length} recommandations trouvées`);
  
  // Filtrer celles avec des liens d'achat
  const recommendationsWithLinks = userRecommendations.filter(rec => 
    rec.affiliateLinks && 
    Array.isArray(rec.affiliateLinks) && 
    rec.affiliateLinks.length > 0
  );
  
  console.log(`💰 ${recommendationsWithLinks.length} recommandations avec liens d'achat`);
  
  return recommendationsWithLinks.slice(0, 5); // Limiter à 5 pour l'email
}

/**
 * Vérifie si l'utilisateur a un compte Discogs connecté
 */
async function checkDiscogsConnection(userId) {
  const { db } = await import('../lib/db');
  const { accounts } = await import('../lib/db/schema');
  const { eq, and } = await import('drizzle-orm');
  
  const discogsAccount = await db.query.accounts.findFirst({
    where: and(
      eq(accounts.userId, userId),
      eq(accounts.provider, 'discogs')
    )
  });
  
  return !!discogsAccount;
}

/**
 * Envoie l'email de bienvenue dans une langue donnée
 */
async function sendWelcomeEmailInLanguage(user, t, lang) {
  console.log(`📧 Envoi de l'email de bienvenue en ${lang}...`);
  
  const { WelcomeEmail } = await import('../emails/welcome-email');
  
  const emailHtml = await render(
    WelcomeEmail({
      name: user.name,
      userEmail: user.email,
      t
    })
  );
  
  const subject = t('emails.welcome.preview');
  
  const result = await resend.emails.send({
    from: EMAIL_CONFIG.from,
    to: user.email,
    replyTo: EMAIL_CONFIG.replyTo,
    subject: `[${lang.toUpperCase()}] ${subject}`,
    html: emailHtml,
    tags: [
      { name: 'type', value: 'welcome' },
      { name: 'language', value: lang },
      { name: 'test', value: 'multilang' }
    ]
  });
  
  if (result.error) {
    throw new Error(`Erreur Resend (${lang}): ${result.error.message}`);
  }
  
  console.log(`✅ Email de bienvenue envoyé en ${lang} (ID: ${result.data?.id})`);
  return result.data?.id;
}

/**
 * Envoie l'email de recommandations dans une langue donnée
 */
async function sendRecommendationsEmailInLanguage(user, userRecommendations, t, lang) {
  console.log(`📧 Envoi de l'email de recommandations en ${lang}...`);
  
  const { RecommendationsEmailTemplate } = await import('../emails/recommendations-email');
  
  const baseUrl = process.env.NEXTAUTH_URL || 'http://localhost:3000';
  const unsubscribeUrl = `${baseUrl}/account?tab=notifications`;
  const viewRecommendationsUrl = `${baseUrl}/recommendations`;
  
  const emailHtml = await render(
    RecommendationsEmailTemplate({
      user: {
        id: user.id,
        name: user.name,
        email: user.email,
        preferredLanguage: lang
      },
      recommendations: userRecommendations,
      totalCount: userRecommendations.length,
      unsubscribeUrl,
      viewRecommendationsUrl,
      t
    })
  );
  
  const subject = t('emails.recommendations.preview', { totalCount: userRecommendations.length });
  
  const result = await resend.emails.send({
    from: EMAIL_CONFIG.from,
    to: user.email,
    replyTo: EMAIL_CONFIG.replyTo,
    subject: `[${lang.toUpperCase()}] ${subject}`,
    html: emailHtml,
    tags: [
      { name: 'type', value: 'recommendations' },
      { name: 'language', value: lang },
      { name: 'test', value: 'multilang' },
      { name: 'count', value: userRecommendations.length.toString() }
    ]
  });
  
  if (result.error) {
    throw new Error(`Erreur Resend (${lang}): ${result.error.message}`);
  }
  
  console.log(`✅ Email de recommandations envoyé en ${lang} (ID: ${result.data?.id})`);
  return result.data?.id;
}

/**
 * Fonction principale
 */
async function main() {
  try {
    console.log('🚀 Envoi des emails multilingues avec vraies données utilisateur');
    console.log(`📧 Destination: ${TARGET_EMAIL}`);
    console.log(`🌍 Langues: ${LANGUAGES.join(', ')}`);
    console.log();
    
    // Charger les traductions
    console.log('📚 Chargement des traductions...');
    const translations = await loadTranslations();
    console.log('✅ Traductions chargées');
    
    // Récupérer les données utilisateur
    const user = await getUserData();
    
    // Récupérer les recommandations
    const userRecommendations = await getUserRecommendations(user.id);
    
    if (userRecommendations.length === 0) {
      console.log('⚠️  Aucune recommandation avec liens d\'achat trouvée');
      console.log('📧 Envoi uniquement des emails de bienvenue...');
    }
    
    // Vérifier la connexion Discogs
    const hasDiscogs = await checkDiscogsConnection(user.id);
    console.log(`🎵 Compte Discogs connecté: ${hasDiscogs ? 'Oui' : 'Non'}`);
    
    console.log();
    
    const results = {
      welcome: {},
      recommendations: {}
    };
    
    // Envoyer les emails dans chaque langue
    for (const lang of LANGUAGES) {
      console.log(`🌍 === ENVOI EN ${lang.toUpperCase()} ===`);
      
      // Créer la fonction de traduction pour cette langue
      const t = createTranslator(translations[lang], lang);
      
      try {
        // 1. Email de bienvenue
        const welcomeId = await sendWelcomeEmailInLanguage(user, t, lang);
        results.welcome[lang] = { success: true, messageId: welcomeId };
        
        // Attendre un peu
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        // 2. Email de recommandations (si on a des recommandations)
        if (userRecommendations.length > 0) {
          const recId = await sendRecommendationsEmailInLanguage(user, userRecommendations, t, lang);
          results.recommendations[lang] = { success: true, messageId: recId };
        } else {
          results.recommendations[lang] = { success: false, reason: 'Pas de recommandations' };
        }
        
      } catch (error) {
        console.error(`❌ Erreur pour la langue ${lang}:`, error.message);
        results.welcome[lang] = { success: false, error: error.message };
        results.recommendations[lang] = { success: false, error: error.message };
      }
      
      console.log();
    }
    
    // Résumé final
    console.log('📊 === RÉSUMÉ FINAL ===');
    console.log(`👤 Utilisateur: ${user.name} (${user.email})`);
    console.log(`🎵 Recommandations: ${userRecommendations.length}`);
    console.log(`🎵 Discogs connecté: ${hasDiscogs ? 'Oui' : 'Non'}`);
    console.log();
    
    for (const lang of LANGUAGES) {
      console.log(`🌍 ${lang.toUpperCase()}:`);
      console.log(`  Welcome: ${results.welcome[lang].success ? '✅' : '❌'} ${results.welcome[lang].messageId || results.welcome[lang].error}`);
      console.log(`  Recommendations: ${results.recommendations[lang].success ? '✅' : '❌'} ${results.recommendations[lang].messageId || results.recommendations[lang].reason || results.recommendations[lang].error}`);
    }
    
    console.log();
    console.log('🎉 Processus terminé ! Vérifiez votre boîte mail.');
    
  } catch (error) {
    console.error('❌ Erreur globale:', error.message);
    process.exit(1);
  }
}

// Exécuter le script
main(); 