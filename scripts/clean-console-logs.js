#!/usr/bin/env node

/**
 * Script pour nettoyer les console.log sensibles
 * Remplace les console.log par des logs conditionnels en développement
 */

const fs = require('fs');
const path = require('path');

// Patterns de console.log à sécuriser
const SENSITIVE_PATTERNS = [
  /console\.log\(`🧪/g,
  /console\.log\(`🔍/g,
  /console\.log\(`⚠️/g,
  /console\.log\(`✅/g,
  /console\.log\(`🔒/g,
  /console\.log\(`🏠/g,
  /console\.log\(`➡️/g,
  /console\.log\("🧪/g,
  /console\.log\("🔍/g,
  /console\.log\("Session debug:/g,
  /console\.log\("Test/g,
];

// Répertoires à traiter
const DIRECTORIES = [
  'app/api/test',
  'app/api/debug',
  'app/api/public',
  'app/api/admin',
  'lib'
];

function shouldSecureLog(line) {
  return SENSITIVE_PATTERNS.some(pattern => pattern.test(line));
}

function secureLogLine(line) {
  // Si c'est déjà conditionné, ne pas modifier
  if (line.includes("process.env.NODE_ENV === 'development'")) {
    return line;
  }
  
  // Ajouter la condition de développement
  const indent = line.match(/^(\s*)/)[1];
  return `${indent}if (process.env.NODE_ENV === 'development') {\n${line}\n${indent}}`;
}

function processFile(filePath) {
  if (!filePath.endsWith('.ts') && !filePath.endsWith('.js')) {
    return;
  }
  
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const lines = content.split('\n');
    let modified = false;
    const newLines = [];
    
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      
      if (shouldSecureLog(line)) {
        console.log(`Sécurisation: ${filePath}:${i + 1}`);
        newLines.push(secureLogLine(line));
        modified = true;
      } else {
        newLines.push(line);
      }
    }
    
    if (modified) {
      fs.writeFileSync(filePath, newLines.join('\n'));
      console.log(`✅ Fichier sécurisé: ${filePath}`);
    }
  } catch (error) {
    console.error(`❌ Erreur traitement ${filePath}:`, error.message);
  }
}

function processDirectory(dirPath) {
  if (!fs.existsSync(dirPath)) {
    console.log(`⚠️ Répertoire non trouvé: ${dirPath}`);
    return;
  }
  
  const items = fs.readdirSync(dirPath);
  
  for (const item of items) {
    const fullPath = path.join(dirPath, item);
    const stat = fs.statSync(fullPath);
    
    if (stat.isDirectory()) {
      processDirectory(fullPath);
    } else {
      processFile(fullPath);
    }
  }
}

function main() {
  console.log('🧹 Nettoyage des console.log sensibles...\n');
  
  for (const dir of DIRECTORIES) {
    console.log(`📁 Traitement du répertoire: ${dir}`);
    processDirectory(dir);
  }
  
  console.log('\n✅ Nettoyage terminé !');
  console.log('\n📝 Actions recommandées :');
  console.log('1. Vérifier les modifications avec git diff');
  console.log('2. Tester l\'application en local');
  console.log('3. Commit les changements si tout fonctionne');
}

if (require.main === module) {
  main();
}

module.exports = { processFile, processDirectory };
