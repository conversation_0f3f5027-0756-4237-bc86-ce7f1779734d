import { drizzle } from 'drizzle-orm/postgres-js';
import { migrate } from 'drizzle-orm/postgres-js/migrator';
import postgres from 'postgres';

/**
 * Script pour migrer la base de données en production
 * Usage: npx tsx scripts/migrate-production.ts
 */
async function main() {
  const databaseUrl = process.env.DATABASE_URL;
  
  if (!databaseUrl) {
    throw new Error('DATABASE_URL is required');
  }

  if (process.env.NODE_ENV === 'development') {
  console.log('🔄 Connecting to database...');
  }
  const sql = postgres(databaseUrl, { max: 1 });
  const db = drizzle(sql);

  if (process.env.NODE_ENV === 'development') {
  console.log('🚀 Running migrations...');
  }
  await migrate(db, { migrationsFolder: './drizzle' });

  if (process.env.NODE_ENV === 'development') {
  console.log('✅ Migrations completed successfully');
  }
  await sql.end();
}

main().catch((error) => {
  console.error('❌ Migration failed:', error);
  process.exit(1);
});
