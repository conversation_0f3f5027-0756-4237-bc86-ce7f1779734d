/**
 * Script de test pour valider la persistance de la wishlist
 * Scénario de test décrit par l'utilisateur :
 * 1. Ajout d'un album à la wishlist depuis /recommendations
 * 2. Vérification que l'album s'affiche correctement dans /wishlist
 * 3. Simulation de la disparition de l'album des recommandations
 * 4. Validation que l'album reste intact dans /wishlist
 * 5. Simulation de la réapparition de l'album
 * 6. Validation de la synchronisation
 */

const { db } = require('../lib/db');
const { recommendations, wishlistItems } = require('../lib/db/schema');
const { eq, and } = require('drizzle-orm');

class WishlistPersistenceTest {
  constructor(userId) {
    this.userId = userId;
    this.testAlbum = {
      artistName: "Test Artist",
      albumTitle: "Test Album",
      albumCoverUrl: "https://example.com/cover.jpg",
      spotifyAlbumId: "test-spotify-id",
      discogsReleaseId: 12345,
      affiliateLinks: [{
        vendor: 'Amazon',
        url: 'https://amazon.fr/test-album',
        price: null,
        currency: 'EUR',
        productName: 'Test Artist - Test Album',
        inStock: null
      }],
      topTrackName: "Test Track",
      topTrackId: "test-track-id",
      topTrackPreviewUrl: "https://example.com/preview.mp3",
      topTrackListenScore: 85,
      listenScore: 92,
      estimatedPlays: 150,
      timeframe: 'short_term',
      isOwned: false,
      generatedAt: new Date()
    };
  }

  async step1_addAlbumToRecommendations() {
    console.log("📝 ÉTAPE 1: Ajout de l'album de test aux recommandations");
    
    // Ajouter l'album aux recommandations
    const [insertedRec] = await db.insert(recommendations).values({
      userId: this.userId,
      ...this.testAlbum
    }).returning();

    console.log(`✅ Album ajouté aux recommandations avec ID: ${insertedRec.id}`);
    this.recommendationId = insertedRec.id;
    return insertedRec;
  }

  async step2_addToWishlist() {
    console.log("📝 ÉTAPE 2: Ajout de l'album à la wishlist");
    
    // Simuler l'ajout à la wishlist via la fonction addToWishlist
    const { addToWishlist } = require('../app/actions/wishlist');
    
    // Note: Pour ce test, on simule la session utilisateur
    const mockSession = { user: { id: this.userId } };
    
    // Simuler l'appel avec l'ID de recommandation
    const result = await addToWishlist(this.recommendationId);
    
    if (result.success) {
      console.log("✅ Album ajouté à la wishlist avec succès");
    } else {
      console.error("❌ Échec de l'ajout à la wishlist:", result.error);
      return false;
    }
    
    return true;
  }

  async step3_verifyWishlistContent() {
    console.log("📝 ÉTAPE 3: Vérification du contenu de la wishlist");
    
    const wishlistItem = await db.query.wishlistItems.findFirst({
      where: and(
        eq(wishlistItems.userId, this.userId),
        eq(wishlistItems.artistName, this.testAlbum.artistName),
        eq(wishlistItems.albumTitle, this.testAlbum.albumTitle)
      )
    });

    if (!wishlistItem) {
      console.error("❌ Album non trouvé dans la wishlist");
      return false;
    }

    console.log("✅ Album trouvé dans la wishlist");
    
    // Vérifier que toutes les données enrichies sont présentes
    const checks = [
      { field: 'albumCoverUrl', expected: this.testAlbum.albumCoverUrl },
      { field: 'spotifyAlbumId', expected: this.testAlbum.spotifyAlbumId },
      { field: 'discogsReleaseId', expected: this.testAlbum.discogsReleaseId },
      { field: 'topTrackName', expected: this.testAlbum.topTrackName },
      { field: 'topTrackId', expected: this.testAlbum.topTrackId },
      { field: 'topTrackPreviewUrl', expected: this.testAlbum.topTrackPreviewUrl },
      { field: 'topTrackListenScore', expected: this.testAlbum.topTrackListenScore }
    ];

    let allDataPresent = true;
    for (const check of checks) {
      if (wishlistItem[check.field] !== check.expected) {
        console.error(`❌ Champ ${check.field} incorrect. Attendu: ${check.expected}, Reçu: ${wishlistItem[check.field]}`);
        allDataPresent = false;
      } else {
        console.log(`✅ Champ ${check.field} correct`);
      }
    }

    // Vérifier les liens d'affiliation
    if (wishlistItem.affiliateLinks) {
      const links = typeof wishlistItem.affiliateLinks === 'string' 
        ? JSON.parse(wishlistItem.affiliateLinks) 
        : wishlistItem.affiliateLinks;
      
      if (links.length > 0 && links[0].vendor === 'Amazon') {
        console.log("✅ Liens d'affiliation présents");
      } else {
        console.error("❌ Liens d'affiliation manquants ou incorrects");
        allDataPresent = false;
      }
    } else {
      console.error("❌ Liens d'affiliation manquants");
      allDataPresent = false;
    }

    return allDataPresent;
  }

  async step4_simulateDisappearance() {
    console.log("📝 ÉTAPE 4: Simulation de la disparition de l'album des recommandations");
    
    // Supprimer l'album des recommandations
    await db.delete(recommendations).where(eq(recommendations.id, this.recommendationId));
    
    console.log("✅ Album supprimé des recommandations");
    
    // Vérifier que l'album n'est plus dans les recommandations
    const remainingRec = await db.query.recommendations.findFirst({
      where: eq(recommendations.id, this.recommendationId)
    });

    if (remainingRec) {
      console.error("❌ Album toujours présent dans les recommandations");
      return false;
    }

    console.log("✅ Album bien supprimé des recommandations");
    return true;
  }

  async step5_verifyPersistence() {
    console.log("📝 ÉTAPE 5: Validation de la persistance dans la wishlist");
    
    // Vérifier que l'album est toujours dans la wishlist avec toutes ses données
    const wishlistItem = await db.query.wishlistItems.findFirst({
      where: and(
        eq(wishlistItems.userId, this.userId),
        eq(wishlistItems.artistName, this.testAlbum.artistName),
        eq(wishlistItems.albumTitle, this.testAlbum.albumTitle)
      )
    });

    if (!wishlistItem) {
      console.error("❌ ÉCHEC CRITIQUE: Album supprimé de la wishlist");
      return false;
    }

    console.log("✅ Album toujours présent dans la wishlist");

    // Vérifier que toutes les données enrichies sont toujours présentes
    const hasAllData = wishlistItem.albumCoverUrl && 
                      wishlistItem.spotifyAlbumId && 
                      wishlistItem.topTrackName && 
                      wishlistItem.affiliateLinks;

    if (hasAllData) {
      console.log("✅ SUCCÈS: Toutes les données enrichies sont persistées");
      return true;
    } else {
      console.error("❌ ÉCHEC: Données enrichies manquantes");
      console.log("Données présentes:", {
        albumCoverUrl: !!wishlistItem.albumCoverUrl,
        spotifyAlbumId: !!wishlistItem.spotifyAlbumId,
        topTrackName: !!wishlistItem.topTrackName,
        affiliateLinks: !!wishlistItem.affiliateLinks
      });
      return false;
    }
  }

  async step6_simulateReappearance() {
    console.log("📝 ÉTAPE 6: Simulation de la réapparition avec nouvelle topTrack");
    
    // Créer une nouvelle recommandation avec une topTrack différente
    const newTestAlbum = {
      ...this.testAlbum,
      topTrackName: "New Test Track",
      topTrackId: "new-test-track-id",
      topTrackPreviewUrl: "https://example.com/new-preview.mp3",
      topTrackListenScore: 95,
      listenScore: 96,
      generatedAt: new Date()
    };

    const [reinsertedRec] = await db.insert(recommendations).values({
      userId: this.userId,
      ...newTestAlbum
    }).returning();

    console.log(`✅ Album réajouté aux recommandations avec nouvelle topTrack: ${newTestAlbum.topTrackName}`);
    this.newRecommendationId = reinsertedRec.id;
    
    // Simuler la synchronisation
    const { syncWishlistWithRecommendations } = require('../app/actions/wishlist');
    await syncWishlistWithRecommendations(this.userId, [reinsertedRec]);
    
    console.log("✅ Synchronisation de la wishlist effectuée");
    return true;
  }

  async step7_verifySynchronization() {
    console.log("📝 ÉTAPE 7: Validation de la synchronisation");
    
    const wishlistItem = await db.query.wishlistItems.findFirst({
      where: and(
        eq(wishlistItems.userId, this.userId),
        eq(wishlistItems.artistName, this.testAlbum.artistName),
        eq(wishlistItems.albumTitle, this.testAlbum.albumTitle)
      )
    });

    if (!wishlistItem) {
      console.error("❌ Album non trouvé dans la wishlist");
      return false;
    }

    // Vérifier que la topTrack a été mise à jour
    if (wishlistItem.topTrackName === "New Test Track") {
      console.log("✅ SUCCÈS: TopTrack mise à jour dans la wishlist");
      return true;
    } else {
      console.error(`❌ ÉCHEC: TopTrack non mise à jour. Attendu: "New Test Track", Reçu: ${wishlistItem.topTrackName}`);
      return false;
    }
  }

  async cleanup() {
    console.log("📝 NETTOYAGE: Suppression des données de test");
    
    // Supprimer les données de test
    await db.delete(recommendations).where(
      and(
        eq(recommendations.userId, this.userId),
        eq(recommendations.artistName, this.testAlbum.artistName),
        eq(recommendations.albumTitle, this.testAlbum.albumTitle)
      )
    );

    await db.delete(wishlistItems).where(
      and(
        eq(wishlistItems.userId, this.userId),
        eq(wishlistItems.artistName, this.testAlbum.artistName),
        eq(wishlistItems.albumTitle, this.testAlbum.albumTitle)
      )
    );

    console.log("✅ Nettoyage terminé");
  }

  async runFullTest() {
    console.log("🚀 DÉBUT DU TEST DE PERSISTANCE DE LA WISHLIST");
    console.log("=" * 50);

    try {
      // Étape 1: Ajouter l'album aux recommandations
      await this.step1_addAlbumToRecommendations();

      // Étape 2: Ajouter à la wishlist
      const addedToWishlist = await this.step2_addToWishlist();
      if (!addedToWishlist) {
        throw new Error("Échec de l'ajout à la wishlist");
      }

      // Étape 3: Vérifier le contenu initial
      const initialContentOk = await this.step3_verifyWishlistContent();
      if (!initialContentOk) {
        throw new Error("Données initiales de la wishlist incorrectes");
      }

      // Étape 4: Simuler la disparition
      const disappeared = await this.step4_simulateDisappearance();
      if (!disappeared) {
        throw new Error("Échec de la simulation de disparition");
      }

      // Étape 5: Vérifier la persistance (TEST CRITIQUE)
      const persisted = await this.step5_verifyPersistence();
      if (!persisted) {
        throw new Error("ÉCHEC CRITIQUE: La wishlist n'est pas persistante");
      }

      // Étape 6: Simuler la réapparition
      const reappeared = await this.step6_simulateReappearance();
      if (!reappeared) {
        throw new Error("Échec de la simulation de réapparition");
      }

      // Étape 7: Vérifier la synchronisation
      const synchronized = await this.step7_verifySynchronization();
      if (!synchronized) {
        throw new Error("Échec de la synchronisation");
      }

      console.log("\n" + "=" * 50);
      console.log("🎉 SUCCÈS: Tous les tests sont passés!");
      console.log("La wishlist est maintenant totalement indépendante des recommandations");
      console.log("=" * 50);

      return true;

    } catch (error) {
      console.error("\n" + "=" * 50);
      console.error("❌ ÉCHEC DU TEST:", error.message);
      console.error("=" * 50);
      return false;
    } finally {
      await this.cleanup();
    }
  }
}

// Fonction utilitaire pour exécuter le test
async function runWishlistPersistenceTest(userId) {
  const test = new WishlistPersistenceTest(userId);
  return await test.runFullTest();
}

// Export pour utilisation dans d'autres scripts
module.exports = {
  WishlistPersistenceTest,
  runWishlistPersistenceTest
};

// Exécution directe si le script est appelé
if (require.main === module) {
  const userId = process.argv[2];
  if (!userId) {
    console.error("Usage: node test-wishlist-persistence.js <userId>");
    process.exit(1);
  }

  runWishlistPersistenceTest(userId)
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error("Erreur lors du test:", error);
      process.exit(1);
    });
} 