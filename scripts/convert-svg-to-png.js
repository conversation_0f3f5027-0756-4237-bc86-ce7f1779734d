// Script pour convertir les SVG en PNG pour les emails
// Nécessite: npm install sharp

const sharp = require('sharp');
const fs = require('fs');
const path = require('path');

async function convertSvgToPng() {
  const assetsDir = path.join(__dirname, '../assets-cdn/public');
  const svgFiles = [
    'Stream2Spin_white_logo.svg',
    'heart-icon.svg',
    'disc3-icon.svg'
  ];

  for (const svgFile of svgFiles) {
    const svgPath = path.join(assetsDir, svgFile);
    const pngPath = path.join(assetsDir, svgFile.replace('.svg', '.png'));
    
    try {
      await sharp(svgPath)
        .png({ quality: 90 })
        .toFile(pngPath);
      
      if (process.env.NODE_ENV === 'development') {
      console.log(`✅ Converted ${svgFile} to PNG`);
      }
    } catch (error) {
      console.error(`❌ Error converting ${svgFile}:`, error);
    }
  }
}

convertSvgToPng();
