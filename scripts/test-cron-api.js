/**
 * Script pour tester l'API CRON d'envoi d'emails directement
 */

async function testCronAPI() {
  console.log('🧪 Test de l\'API CRON d\'envoi d\'emails');
  console.log('=====================================\n');

  try {
    // Vérifier que le serveur de dev tourne
    console.log('🔍 Vérification du serveur de développement...');
    
    const healthCheck = await fetch('http://localhost:3000/api/health', {
      method: 'GET'
    }).catch(() => null);

    if (!healthCheck) {
      console.log('❌ Le serveur de développement ne semble pas tourner sur le port 3000');
      console.log('💡 Assurez-vous que `pnpm dev` est lancé dans un autre terminal');
      return;
    }

    console.log('✅ Serveur de développement actif\n');

    // Tester l'API CRON
    console.log('📧 Déclenchement du CRON d\'envoi d\'emails...');
    
    const cronResponse = await fetch('http://localhost:3000/api/cron/send-notifications', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${process.env.CRON_SECRET || 'dev-secret'}`
      }
    });

    console.log(`📊 Statut de la réponse: ${cronResponse.status}`);

    if (!cronResponse.ok) {
      const errorText = await cronResponse.text();
      console.log('❌ Erreur de l\'API CRON:');
      console.log(errorText);
      return;
    }

    const result = await cronResponse.json();
    console.log('\n✅ Réponse de l\'API CRON:');
    console.log(JSON.stringify(result, null, 2));

    // Analyser les résultats
    if (result.email) {
      console.log('\n📧 Résultats des emails:');
      console.log(`   Utilisateurs traités: ${result.email.processedUsers}`);
      console.log(`   Succès: ${result.email.successCount}`);
      console.log(`   Erreurs: ${result.email.errorCount}`);
      
      if (result.email.results && result.email.results.length > 0) {
        console.log('\n📋 Détails par utilisateur:');
        result.email.results.forEach((userResult, index) => {
          console.log(`   ${index + 1}. Utilisateur ${userResult.userId}:`);
          console.log(`      Statut: ${userResult.status}`);
          
          if (userResult.status === 'success') {
            console.log(`      Message ID: ${userResult.messageId}`);
            if (userResult.personalRecommendationsCount !== undefined) {
              console.log(`      Recommandations personnelles: ${userResult.personalRecommendationsCount}`);
              console.log(`      Recommandations communautaires: ${userResult.communityRecommendationsCount}`);
              console.log(`      Total: ${userResult.totalRecommendationsCount}`);
              console.log(`      Type d'email: ${userResult.communityRecommendationsCount > 0 ? 'SOCIAL' : 'CLASSIQUE'}`);
            } else if (userResult.recommendationsCount !== undefined) {
              console.log(`      Recommandations: ${userResult.recommendationsCount}`);
              console.log(`      Type d'email: CLASSIQUE (ancienne version)`);
            }
          } else if (userResult.error) {
            console.log(`      Erreur: ${userResult.error}`);
          } else if (userResult.reason) {
            console.log(`      Raison: ${userResult.reason}`);
          }
        });
      }
    }

    if (result.push) {
      console.log('\n📱 Résultats des notifications push:');
      console.log(`   Utilisateurs traités: ${result.push.processedUsers}`);
      console.log(`   Succès: ${result.push.successCount}`);
      console.log(`   Erreurs: ${result.push.errorCount}`);
    }

    console.log('\n🎉 Test terminé avec succès !');

  } catch (error) {
    console.error('❌ Erreur lors du test:', error);
  }
}

// Fonction pour tester avec un utilisateur spécifique (mode debug)
async function testSpecificUser(userId) {
  console.log(`🧪 Test pour l'utilisateur spécifique: ${userId}`);
  
  try {
    const response = await fetch('http://localhost:3000/api/cron/send-notifications', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${process.env.CRON_SECRET || 'dev-secret'}`
      },
      body: JSON.stringify({ 
        testMode: true, 
        testUserId: userId 
      })
    });

    const result = await response.json();
    console.log('Résultat:', JSON.stringify(result, null, 2));
    
  } catch (error) {
    console.error('❌ Erreur:', error);
  }
}

// Exécuter le test
if (require.main === module) {
  const args = process.argv.slice(2);
  
  if (args.length > 0 && args[0] === '--user') {
    const userId = args[1];
    if (!userId) {
      console.log('❌ Veuillez spécifier un ID utilisateur: node test-cron-api.js --user USER_ID');
      process.exit(1);
    }
    testSpecificUser(userId);
  } else {
    testCronAPI();
  }
}

module.exports = { testCronAPI, testSpecificUser };
