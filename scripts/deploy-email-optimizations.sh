#!/bin/bash

# Script de déploiement des optimisations email
# Stream2Spin - Délivrabilité Email v1.0

set -e  # Arrêter en cas d'erreur

# Couleurs pour l'affichage
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Fonctions d'affichage
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Vérifier les prérequis
check_prerequisites() {
    log_info "Vérification des prérequis..."
    
    # Vérifier Node.js
    if ! command -v node &> /dev/null; then
        log_error "Node.js n'est pas installé"
        exit 1
    fi
    
    # Vérifier pnpm
    if ! command -v pnpm &> /dev/null; then
        log_error "pnpm n'est pas installé"
        exit 1
    fi
    
    # Vérifier psql (pour la migration)
    if ! command -v psql &> /dev/null; then
        log_warning "psql n'est pas installé - migration manuelle requise"
    fi
    
    # Vérifier les variables d'environnement critiques
    if [ -z "$DATABASE_URL" ]; then
        log_error "DATABASE_URL n'est pas définie"
        exit 1
    fi
    
    if [ -z "$RESEND_API_KEY" ]; then
        log_error "RESEND_API_KEY n'est pas définie"
        exit 1
    fi
    
    log_success "Prérequis validés"
}

# Sauvegarder la configuration actuelle
backup_current_config() {
    log_info "Sauvegarde de la configuration actuelle..."
    
    BACKUP_DIR="./backups/email-config-$(date +%Y%m%d-%H%M%S)"
    mkdir -p "$BACKUP_DIR"
    
    # Sauvegarder les fichiers modifiés
    if [ -f "lib/email.ts" ]; then
        cp "lib/email.ts" "$BACKUP_DIR/"
    fi
    
    if [ -f "lib/resend.ts" ]; then
        cp "lib/resend.ts" "$BACKUP_DIR/"
    fi
    
    log_success "Configuration sauvegardée dans $BACKUP_DIR"
}

# Installer les dépendances
install_dependencies() {
    log_info "Installation des dépendances..."
    
    # Vérifier si de nouvelles dépendances sont nécessaires
    # (Dans ce cas, toutes les dépendances sont déjà présentes)
    
    pnpm install
    log_success "Dépendances installées"
}

# Exécuter la migration de base de données
run_database_migration() {
    log_info "Exécution de la migration de base de données..."
    
    if command -v psql &> /dev/null; then
        if psql "$DATABASE_URL" -f "migrations/add-email-deliverability-tracking.sql"; then
            log_success "Migration de base de données réussie"
        else
            log_warning "Erreur lors de la migration - vérifiez manuellement"
        fi
    else
        log_warning "psql non disponible - exécutez manuellement :"
        log_warning "psql \$DATABASE_URL -f migrations/add-email-deliverability-tracking.sql"
    fi
}

# Valider la configuration DNS
validate_dns_config() {
    log_info "Validation de la configuration DNS..."
    
    if node scripts/validate-email-dns.js; then
        log_success "Configuration DNS validée"
    else
        log_warning "Configuration DNS incomplète - consultez le guide de configuration"
        log_warning "Voir : docs/EMAIL_DELIVERABILITY_SETUP.md"
    fi
}

# Tester les optimisations
test_optimizations() {
    log_info "Test des optimisations email..."
    
    if node scripts/test-email-optimizations.js; then
        log_success "Tous les tests sont passés"
    else
        log_error "Certains tests ont échoué - vérifiez la configuration"
        return 1
    fi
}

# Tester la délivrabilité
test_deliverability() {
    log_info "Test de la délivrabilité..."
    
    if node scripts/test-email-deliverability.js; then
        log_success "Tests de délivrabilité réussis"
    else
        log_warning "Problèmes de délivrabilité détectés"
    fi
}

# Configurer le monitoring
setup_monitoring() {
    log_info "Configuration du monitoring..."
    
    # Créer le répertoire des rapports
    mkdir -p "./reports"
    
    # Tester le script de monitoring
    if node scripts/monitor-email-health.js; then
        log_success "Monitoring configuré"
    else
        log_warning "Problème avec le monitoring - vérifiez la configuration"
    fi
}

# Déployer sur Vercel (si applicable)
deploy_to_vercel() {
    if command -v vercel &> /dev/null; then
        log_info "Déploiement sur Vercel..."
        
        # Vérifier si c'est un projet Vercel
        if [ -f "vercel.json" ]; then
            if vercel --prod; then
                log_success "Déploiement Vercel réussi"
            else
                log_error "Erreur lors du déploiement Vercel"
                return 1
            fi
        else
            log_warning "Pas de configuration Vercel détectée"
        fi
    else
        log_warning "Vercel CLI non installé - déploiement manuel requis"
    fi
}

# Vérifier le déploiement
verify_deployment() {
    log_info "Vérification du déploiement..."
    
    # Attendre que le déploiement soit actif
    sleep 10
    
    # Tester l'endpoint de webhook
    if [ ! -z "$NEXTAUTH_URL" ]; then
        WEBHOOK_URL="$NEXTAUTH_URL/api/webhooks/resend"
        if curl -s -o /dev/null -w "%{http_code}" "$WEBHOOK_URL" | grep -q "200\|405"; then
            log_success "Endpoint webhook accessible"
        else
            log_warning "Endpoint webhook non accessible : $WEBHOOK_URL"
        fi
        
        # Tester l'endpoint de métriques (nécessite authentification)
        METRICS_URL="$NEXTAUTH_URL/api/admin/email-metrics"
        log_info "Endpoint métriques : $METRICS_URL"
    fi
}

# Afficher le résumé post-déploiement
show_post_deployment_summary() {
    log_info "=== RÉSUMÉ DU DÉPLOIEMENT ==="
    echo
    log_success "✅ Optimisations email déployées avec succès !"
    echo
    log_info "📋 PROCHAINES ÉTAPES :"
    echo "1. Configurer les enregistrements DNS (voir docs/EMAIL_DELIVERABILITY_SETUP.md)"
    echo "2. Configurer les webhooks Resend vers /api/webhooks/resend"
    echo "3. Tester avec mail-tester.com"
    echo "4. Surveiller les métriques sur /admin/email-metrics"
    echo "5. Programmer le monitoring : node scripts/monitor-email-health.js"
    echo
    log_info "📊 MONITORING :"
    echo "- Dashboard : ${NEXTAUTH_URL:-http://localhost:3000}/admin/email-metrics"
    echo "- Webhook : ${NEXTAUTH_URL:-http://localhost:3000}/api/webhooks/resend"
    echo "- Rapports : ./reports/"
    echo
    log_info "🔧 COMMANDES UTILES :"
    echo "- Test DNS : node scripts/validate-email-dns.js"
    echo "- Test optimisations : node scripts/test-email-optimizations.js"
    echo "- Monitoring : node scripts/monitor-email-health.js"
    echo "- Analyse timing : node scripts/optimize-email-timing.js"
    echo
}

# Fonction principale
main() {
    echo "🚀 Déploiement des optimisations email Stream2Spin"
    echo "=================================================="
    echo
    
    # Demander confirmation
    read -p "Continuer avec le déploiement ? (y/N) " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "Déploiement annulé"
        exit 0
    fi
    
    # Exécuter les étapes
    check_prerequisites
    backup_current_config
    install_dependencies
    run_database_migration
    validate_dns_config
    test_optimizations
    test_deliverability
    setup_monitoring
    
    # Déploiement (optionnel)
    if [ "$1" = "--deploy" ]; then
        deploy_to_vercel
        verify_deployment
    fi
    
    show_post_deployment_summary
    
    log_success "🎉 Déploiement terminé avec succès !"
}

# Gestion des erreurs
trap 'log_error "Erreur lors du déploiement à la ligne $LINENO"' ERR

# Exécution
main "$@"
