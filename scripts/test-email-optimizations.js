#!/usr/bin/env node

/**
 * Script de test complet des optimisations email
 * Teste toutes les fonctionnalités d'optimisation de la délivrabilité
 */

// Couleurs pour la console
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(message, color = 'reset') {
  const timestamp = new Date().toISOString();
  console.log(`${colors[color]}[${timestamp}] ${message}${colors.reset}`);
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

function logInfo(message) {
  log(`ℹ️  ${message}`, 'blue');
}

/**
 * Test de la validation des emails
 */
function testEmailValidation() {
  logInfo('Test de la validation des emails...');
  
  // Simuler la fonction de validation (normalement importée)
  function validateEmailForDeliverability(email, subject) {
    const warnings = [];
    let score = 100;

    // Vérifier le domaine
    const domain = email.split('@')[1]?.toLowerCase();
    const riskyDomains = ['tempmail.org', '10minutemail.com'];
    if (riskyDomains.includes(domain)) {
      warnings.push(`Domaine à risque: ${domain}`);
      score -= 30;
    }

    // Vérifier le sujet
    const spamKeywords = ['gratuit', 'urgent', 'cliquez ici'];
    const foundSpam = spamKeywords.filter(keyword => 
      subject.toLowerCase().includes(keyword)
    );
    if (foundSpam.length > 0) {
      warnings.push(`Mots-clés spam: ${foundSpam.join(', ')}`);
      score -= foundSpam.length * 10;
    }

    if (subject.length > 50) {
      warnings.push('Sujet trop long');
      score -= 5;
    }

    return {
      isValid: score >= 70,
      warnings,
      score: Math.max(0, score)
    };
  }

  const testCases = [
    { email: '<EMAIL>', subject: 'Nouvelles recommandations vinyles', expected: true },
    { email: '<EMAIL>', subject: 'Test', expected: false },
    { email: '<EMAIL>', subject: 'URGENT!!! GRATUIT!!!', expected: false }
  ];

  let passed = 0;
  testCases.forEach((test, index) => {
    const result = validateEmailForDeliverability(test.email, test.subject);
    if (result.isValid === test.expected) {
      logSuccess(`Test ${index + 1}: ${test.email} - Score: ${result.score}`);
      passed++;
    } else {
      logError(`Test ${index + 1}: ${test.email} - Score: ${result.score}`);
    }
  });

  return passed === testCases.length;
}

/**
 * Test du timing optimal
 */
function testOptimalTiming() {
  logInfo('Test du timing optimal...');
  
  // Simuler les fonctions de timing
  function getOptimalHours(timezone) {
    const configs = {
      'Europe/Paris': { primary: [9, 10, 14], secondary: [11, 15], avoid: [0, 1, 2, 3, 4, 5, 6, 22, 23] },
      'America/New_York': { primary: [10, 11, 14], secondary: [9, 15], avoid: [0, 1, 2, 3, 4, 5, 6, 22, 23] }
    };
    return configs[timezone] || configs['Europe/Paris'];
  }

  function isGoodTimeToSend(timezone, currentTime) {
    const hour = currentTime.getHours();
    const day = currentTime.getDay();
    const config = getOptimalHours(timezone);
    
    // Éviter weekends
    if (day === 0 || day === 6) return false;
    
    // Éviter heures à éviter
    if (config.avoid.includes(hour)) return false;
    
    // Préférer heures optimales
    return [...config.primary, ...config.secondary].includes(hour);
  }

  const testTimes = [
    { time: new Date('2024-01-15T09:30:00'), timezone: 'Europe/Paris', expected: true },
    { time: new Date('2024-01-15T02:00:00'), timezone: 'Europe/Paris', expected: false },
    { time: new Date('2024-01-13T10:00:00'), timezone: 'Europe/Paris', expected: false }, // Samedi
    { time: new Date('2024-01-15T14:00:00'), timezone: 'America/New_York', expected: true }
  ];

  let passed = 0;
  testTimes.forEach((test, index) => {
    const result = isGoodTimeToSend(test.timezone, test.time);
    const timeStr = test.time.toLocaleString();
    if (result === test.expected) {
      logSuccess(`Test ${index + 1}: ${timeStr} (${test.timezone}) - ${result ? 'Optimal' : 'Non optimal'}`);
      passed++;
    } else {
      logError(`Test ${index + 1}: ${timeStr} (${test.timezone}) - Attendu: ${test.expected}, Reçu: ${result}`);
    }
  });

  return passed === testTimes.length;
}

/**
 * Test du throttling
 */
function testThrottling() {
  logInfo('Test du throttling...');
  
  // Simuler la classe EmailThrottler
  class MockEmailThrottler {
    constructor() {
      this.emailQueue = [];
      this.maxEmailsPerMinute = 50;
    }

    canSendEmail() {
      const now = Date.now();
      const oneMinuteAgo = now - 60 * 1000;
      const recentEmails = this.emailQueue.filter(item => item.timestamp > oneMinuteAgo);
      return recentEmails.length < this.maxEmailsPerMinute;
    }

    recordEmailSent(email) {
      this.emailQueue.push({ email, timestamp: Date.now() });
    }

    getNextAvailableSlot() {
      if (this.canSendEmail()) return new Date();
      const now = Date.now();
      return new Date(now + 60 * 1000);
    }
  }

  const throttler = new MockEmailThrottler();
  let passed = 0;

  // Test 1: Peut envoyer au début
  if (throttler.canSendEmail()) {
    logSuccess('Test 1: Peut envoyer au début');
    passed++;
  } else {
    logError('Test 1: Ne peut pas envoyer au début');
  }

  // Test 2: Enregistrer des envois
  for (let i = 0; i < 45; i++) {
    throttler.recordEmailSent(`test${i}@example.com`);
  }

  if (throttler.canSendEmail()) {
    logSuccess('Test 2: Peut encore envoyer après 45 emails');
    passed++;
  } else {
    logError('Test 2: Ne peut plus envoyer après 45 emails');
  }

  // Test 3: Dépasser la limite
  for (let i = 45; i < 55; i++) {
    throttler.recordEmailSent(`test${i}@example.com`);
  }

  if (!throttler.canSendEmail()) {
    logSuccess('Test 3: Throttling activé après dépassement de limite');
    passed++;
  } else {
    logError('Test 3: Throttling non activé après dépassement');
  }

  return passed === 3;
}

/**
 * Test de la fréquence adaptative
 */
function testAdaptiveFrequency() {
  logInfo('Test de la fréquence adaptative...');
  
  function calculateOptimalFrequency(openRate, clickRate) {
    if (openRate >= 0.3 && clickRate >= 0.1) return 'weekly';
    if (openRate >= 0.15 && clickRate >= 0.05) return 'bi-weekly';
    if (openRate >= 0.05 && clickRate >= 0.01) return 'monthly';
    return 'never';
  }

  const testCases = [
    { openRate: 0.35, clickRate: 0.12, expected: 'weekly' },
    { openRate: 0.20, clickRate: 0.07, expected: 'bi-weekly' },
    { openRate: 0.08, clickRate: 0.02, expected: 'monthly' },
    { openRate: 0.02, clickRate: 0.005, expected: 'never' }
  ];

  let passed = 0;
  testCases.forEach((test, index) => {
    const result = calculateOptimalFrequency(test.openRate, test.clickRate);
    if (result === test.expected) {
      logSuccess(`Test ${index + 1}: Open ${test.openRate}, Click ${test.clickRate} → ${result}`);
      passed++;
    } else {
      logError(`Test ${index + 1}: Attendu ${test.expected}, Reçu ${result}`);
    }
  });

  return passed === testCases.length;
}

/**
 * Test des jours fériés
 */
function testHolidayDetection() {
  logInfo('Test de la détection des jours fériés...');
  
  function isBlackoutDate(date) {
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    const dateString = `${month}-${day}`;
    
    const holidays = ['01-01', '12-25', '12-24'];
    return holidays.includes(dateString);
  }

  const testDates = [
    { date: new Date('2024-01-01'), expected: true }, // Nouvel An
    { date: new Date('2024-12-25'), expected: true }, // Noël
    { date: new Date('2024-06-15'), expected: false }, // Jour normal
    { date: new Date('2024-12-24'), expected: true }  // Veille de Noël
  ];

  let passed = 0;
  testDates.forEach((test, index) => {
    const result = isBlackoutDate(test.date);
    const dateStr = test.date.toLocaleDateString();
    if (result === test.expected) {
      logSuccess(`Test ${index + 1}: ${dateStr} - ${result ? 'Jour férié' : 'Jour normal'}`);
      passed++;
    } else {
      logError(`Test ${index + 1}: ${dateStr} - Attendu: ${test.expected}, Reçu: ${result}`);
    }
  });

  return passed === testDates.length;
}

/**
 * Génère un rapport de test
 */
function generateTestReport(results) {
  logInfo('📊 RAPPORT DE TEST DES OPTIMISATIONS EMAIL');
  logInfo('==============================================');
  
  const totalTests = Object.keys(results).length;
  const passedTests = Object.values(results).filter(Boolean).length;
  const score = Math.round((passedTests / totalTests) * 100);

  Object.entries(results).forEach(([testName, passed]) => {
    const status = passed ? '✅' : '❌';
    const name = testName.replace(/([A-Z])/g, ' $1').toLowerCase();
    log(`${status} ${name}`);
  });

  log('', 'reset');
  log(`Score global: ${score}% (${passedTests}/${totalTests})`, score >= 80 ? 'green' : 'red');

  if (score >= 80) {
    logSuccess('🎉 Toutes les optimisations fonctionnent correctement !');
  } else {
    logWarning('⚠️ Certaines optimisations nécessitent des corrections.');
  }

  // Recommandations
  logInfo('\n📋 PROCHAINES ÉTAPES:');
  log('1. Configurer les enregistrements DNS (SPF, DKIM, DMARC)', 'blue');
  log('2. Mettre à jour les variables d\'environnement', 'blue');
  log('3. Exécuter la migration de base de données', 'blue');
  log('4. Tester en production avec un petit groupe', 'blue');
  log('5. Surveiller les métriques de délivrabilité', 'blue');

  return score;
}

/**
 * Fonction principale
 */
async function main() {
  log('🚀 Test complet des optimisations email Stream2Spin', 'bold');
  log('', 'reset');

  const results = {
    emailValidation: false,
    optimalTiming: false,
    throttling: false,
    adaptiveFrequency: false,
    holidayDetection: false
  };

  // Exécuter tous les tests
  try {
    results.emailValidation = testEmailValidation();
    log('', 'reset');
    
    results.optimalTiming = testOptimalTiming();
    log('', 'reset');
    
    results.throttling = testThrottling();
    log('', 'reset');
    
    results.adaptiveFrequency = testAdaptiveFrequency();
    log('', 'reset');
    
    results.holidayDetection = testHolidayDetection();
    log('', 'reset');

    // Générer le rapport
    const score = generateTestReport(results);

    // Code de sortie
    process.exit(score >= 80 ? 0 : 1);

  } catch (error) {
    logError(`💥 Erreur fatale: ${error.message}`);
    process.exit(1);
  }
}

// Gestion des erreurs
process.on('unhandledRejection', (error) => {
  logError(`Erreur non gérée: ${error.message}`);
  process.exit(1);
});

// Exécution
if (require.main === module) {
  main();
}
