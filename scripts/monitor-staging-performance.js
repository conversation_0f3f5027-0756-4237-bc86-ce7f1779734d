#!/usr/bin/env node

/**
 * Script de monitoring des performances sur staging
 * Vérifie que les optimisations DB fonctionnent correctement
 */

const https = require('https');
const { performance } = require('perf_hooks');

const STAGING_URL = 'https://stream2spin-staging.vercel.app'; // À ajuster selon l'URL réelle
const ENDPOINTS_TO_TEST = [
  '/api/auth/session',
  '/api/auth/providers',
  '/recommendations',
  '/login'
];

if (process.env.NODE_ENV === 'development') {
console.log('🔍 Monitoring des performances sur staging');
}
console.log(`📍 URL de staging: ${STAGING_URL}`);
if (process.env.NODE_ENV === 'development') {
console.log('');
}

/**
 * Teste un endpoint et mesure le temps de réponse
 */
async function testEndpoint(endpoint) {
  return new Promise((resolve) => {
    const startTime = performance.now();
    
    const options = {
      hostname: STAGING_URL.replace('https://', ''),
      path: endpoint,
      method: 'GET',
      headers: {
        'User-Agent': 'Stream2Spin-Performance-Monitor/1.0'
      }
    };

    const req = https.request(options, (res) => {
      const endTime = performance.now();
      const responseTime = Math.round(endTime - startTime);
      
      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        resolve({
          endpoint,
          statusCode: res.statusCode,
          responseTime,
          contentLength: data.length,
          success: res.statusCode >= 200 && res.statusCode < 400
        });
      });
    });

    req.on('error', (error) => {
      const endTime = performance.now();
      const responseTime = Math.round(endTime - startTime);
      
      resolve({
        endpoint,
        statusCode: 0,
        responseTime,
        contentLength: 0,
        success: false,
        error: error.message
      });
    });

    req.setTimeout(10000, () => {
      req.destroy();
      const endTime = performance.now();
      const responseTime = Math.round(endTime - startTime);
      
      resolve({
        endpoint,
        statusCode: 0,
        responseTime,
        contentLength: 0,
        success: false,
        error: 'Timeout (10s)'
      });
    });

    req.end();
  });
}

/**
 * Exécute les tests de performance
 */
async function runPerformanceTests() {
  if (process.env.NODE_ENV === 'development') {
  console.log('🚀 Début des tests de performance...');
  }
  console.log('');

  const results = [];
  
  for (const endpoint of ENDPOINTS_TO_TEST) {
    if (process.env.NODE_ENV === 'development') {
    console.log(`⏱️  Test de ${endpoint}...`);
    }
    
    // Test multiple pour avoir une moyenne
    const tests = [];
    for (let i = 0; i < 3; i++) {
      const result = await testEndpoint(endpoint);
      tests.push(result);
      
      if (result.success) {
        if (process.env.NODE_ENV === 'development') {
        console.log(`   Essai ${i + 1}: ${result.responseTime}ms (${result.statusCode})`);
        }
      } else {
        if (process.env.NODE_ENV === 'development') {
        console.log(`   Essai ${i + 1}: ERREUR - ${result.error || result.statusCode}`);
        }
      }
      
      // Pause entre les tests
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
    
    // Calculer la moyenne des tests réussis
    const successfulTests = tests.filter(t => t.success);
    if (successfulTests.length > 0) {
      const avgResponseTime = Math.round(
        successfulTests.reduce((sum, t) => sum + t.responseTime, 0) / successfulTests.length
      );
      
      results.push({
        endpoint,
        avgResponseTime,
        successRate: (successfulTests.length / tests.length) * 100,
        tests: tests.length
      });
      
      if (process.env.NODE_ENV === 'development') {
      console.log(`   ✅ Moyenne: ${avgResponseTime}ms (${successfulTests.length}/${tests.length} succès)`);
      }
    } else {
      results.push({
        endpoint,
        avgResponseTime: 0,
        successRate: 0,
        tests: tests.length
      });
      
      if (process.env.NODE_ENV === 'development') {
      console.log(`   ❌ Tous les tests ont échoué`);
      }
    }
    
    if (process.env.NODE_ENV === 'development') {
    console.log('');
    }
  }

  return results;
}

/**
 * Analyse les résultats et donne des recommandations
 */
function analyzeResults(results) {
  if (process.env.NODE_ENV === 'development') {
  console.log('📊 Analyse des résultats:');
  }
  console.log('');

  let totalScore = 0;
  let maxScore = 0;

  results.forEach(result => {
    const { endpoint, avgResponseTime, successRate } = result;
    
    // Critères de performance attendus après optimisations
    let performanceScore = 0;
    let maxEndpointScore = 0;
    let status = '';
    
    if (endpoint === '/api/auth/session') {
      // Devrait être <200ms avec cache
      if (avgResponseTime < 200) {
        performanceScore = 100;
        status = '🚀 EXCELLENT (cache actif)';
      } else if (avgResponseTime < 500) {
        performanceScore = 70;
        status = '✅ BON (optimisations actives)';
      } else if (avgResponseTime < 1000) {
        performanceScore = 40;
        status = '⚠️ MOYEN (optimisations partielles)';
      } else {
        performanceScore = 10;
        status = '❌ LENT (optimisations non actives)';
      }
      maxEndpointScore = 100;
    } else if (endpoint.startsWith('/api/')) {
      // APIs devraient être <500ms
      if (avgResponseTime < 500) {
        performanceScore = 100;
        status = '🚀 EXCELLENT';
      } else if (avgResponseTime < 1000) {
        performanceScore = 70;
        status = '✅ BON';
      } else {
        performanceScore = 30;
        status = '⚠️ LENT';
      }
      maxEndpointScore = 100;
    } else {
      // Pages devraient être <2s
      if (avgResponseTime < 1000) {
        performanceScore = 100;
        status = '🚀 EXCELLENT';
      } else if (avgResponseTime < 2000) {
        performanceScore = 70;
        status = '✅ BON';
      } else {
        performanceScore = 30;
        status = '⚠️ LENT';
      }
      maxEndpointScore = 100;
    }

    // Pénalité pour les échecs
    performanceScore = performanceScore * (successRate / 100);
    
    totalScore += performanceScore;
    maxScore += maxEndpointScore;

    if (process.env.NODE_ENV === 'development') {
    console.log(`${endpoint}:`);
    }
    console.log(`   Temps moyen: ${avgResponseTime}ms`);
    if (process.env.NODE_ENV === 'development') {
    console.log(`   Taux de succès: ${successRate}%`);
    }
    console.log(`   Status: ${status}`);
    if (process.env.NODE_ENV === 'development') {
    console.log('');
    }
  });

  const globalScore = Math.round((totalScore / maxScore) * 100);
  
  if (process.env.NODE_ENV === 'development') {
  console.log('🎯 Score global de performance:');
  }
  console.log(`   ${globalScore}/100`);
  
  if (globalScore >= 90) {
    if (process.env.NODE_ENV === 'development') {
    console.log('   🚀 EXCELLENT - Optimisations parfaitement actives !');
    }
  } else if (globalScore >= 70) {
    if (process.env.NODE_ENV === 'development') {
    console.log('   ✅ BON - Optimisations largement actives');
    }
  } else if (globalScore >= 50) {
    if (process.env.NODE_ENV === 'development') {
    console.log('   ⚠️ MOYEN - Optimisations partiellement actives');
    }
  } else {
    if (process.env.NODE_ENV === 'development') {
    console.log('   ❌ FAIBLE - Optimisations non actives ou problème');
    }
  }
  
  if (process.env.NODE_ENV === 'development') {
  console.log('');
  }
  
  // Recommandations
  if (process.env.NODE_ENV === 'development') {
  console.log('💡 Recommandations:');
  }
  
  const sessionResult = results.find(r => r.endpoint === '/api/auth/session');
  if (sessionResult && sessionResult.avgResponseTime > 200) {
    if (process.env.NODE_ENV === 'development') {
    console.log('   - Vérifier que le cache DB est actif (logs "Cache DB hit")');
    }
    console.log('   - Vérifier la configuration du pool de connexions');
  }
  
  if (globalScore < 70) {
    if (process.env.NODE_ENV === 'development') {
    console.log('   - Vérifier que Turbopack est activé');
    }
    console.log('   - Vérifier les variables d\'environnement sur Vercel');
    if (process.env.NODE_ENV === 'development') {
    console.log('   - Exécuter le script SQL d\'optimisation des index');
    }
  }
  
  if (results.some(r => r.successRate < 100)) {
    if (process.env.NODE_ENV === 'development') {
    console.log('   - Vérifier les logs d\'erreur sur Vercel');
    }
    console.log('   - Vérifier la connectivité à Supabase');
  }
}

/**
 * Fonction principale
 */
async function main() {
  try {
    const results = await runPerformanceTests();
    analyzeResults(results);
    
    if (process.env.NODE_ENV === 'development') {
    console.log('');
    }
    console.log('✅ Monitoring terminé');
    if (process.env.NODE_ENV === 'development') {
    console.log('');
    }
    console.log('📋 Prochaines étapes:');
    if (process.env.NODE_ENV === 'development') {
    console.log('1. Vérifier les logs Vercel pour les détails');
    }
    console.log('2. Exécuter le script SQL d\'optimisation si nécessaire');
    if (process.env.NODE_ENV === 'development') {
    console.log('3. Re-tester après 5-10 minutes pour voir l\'effet du cache');
    }
    
  } catch (error) {
    console.error('❌ Erreur lors du monitoring:', error);
    process.exit(1);
  }
}

// Exécuter le monitoring
main();
