#!/bin/bash

# Script de déploiement sur staging avec validation de sécurité
# Déploie les changements sur la branche staging et vérifie la sécurité

set -e  # Arrêter en cas d'erreur

echo "🚀 Déploiement sur staging avec validation de sécurité"
echo "=================================================="

# Vérifier qu'on n'est pas sur main
CURRENT_BRANCH=$(git branch --show-current)
if [ "$CURRENT_BRANCH" = "main" ]; then
    echo "❌ ERREUR: Ne pas déployer directement depuis main"
    echo "   Créez une branche de feature d'abord"
    exit 1
fi

# Vérifier que les changements sont commités
if [ -n "$(git status --porcelain)" ]; then
    echo "⚠️ Changements non commités détectés"
    echo "   Voulez-vous les commiter automatiquement ? (y/n)"
    read -r response
    if [ "$response" = "y" ]; then
        git add .
        git commit -m "feat: security cleanup - remove debug routes for production"
        echo "✅ Changements commités"
    else
        echo "❌ Veuillez commiter vos changements d'abord"
        exit 1
    fi
fi

# Validation de sécurité avant déploiement
echo ""
echo "🔍 Validation de sécurité..."
node scripts/validate-production-ready.js
VALIDATION_EXIT_CODE=$?

if [ $VALIDATION_EXIT_CODE -ne 0 ]; then
    echo ""
    echo "❌ ÉCHEC DE LA VALIDATION DE SÉCURITÉ"
    echo "   Corrigez les problèmes avant de déployer"
    exit 1
fi

echo "✅ Validation de sécurité réussie"

# Pousser vers staging
echo ""
echo "📤 Push vers la branche staging..."

# Créer/mettre à jour la branche staging
git checkout -B staging
git push origin staging --force-with-lease

echo "✅ Code poussé vers staging"

# Attendre le déploiement Vercel
echo ""
echo "⏳ Attente du déploiement Vercel..."
echo "   URL de staging: https://stream2spin-staging.vercel.app"
echo "   Vérifiez le déploiement dans le dashboard Vercel"

# Tests automatiques de sécurité sur staging
echo ""
echo "🧪 Tests de sécurité sur staging..."

# Fonction pour tester une route
test_route() {
    local route=$1
    local expected_status=$2
    local description=$3
    
    echo "   Testing: $route"
    
    # Utiliser curl pour tester la route
    HTTP_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "https://stream2spin-staging.vercel.app$route" || echo "000")
    
    if [ "$HTTP_STATUS" = "$expected_status" ]; then
        echo "   ✅ $description: $HTTP_STATUS"
        return 0
    else
        echo "   ❌ $description: Expected $expected_status, got $HTTP_STATUS"
        return 1
    fi
}

# Attendre que le déploiement soit prêt
echo "   Attente que le déploiement soit prêt (30s)..."
sleep 30

# Tests des routes qui doivent retourner 404
FAILED_TESTS=0

echo ""
echo "🔒 Test des routes de debug (doivent retourner 404):"

test_route "/api/public/user-status" "404" "Route user-status bloquée" || ((FAILED_TESTS++))
test_route "/api/public/reset-email-flag" "404" "Route reset-email-flag bloquée" || ((FAILED_TESTS++))
test_route "/api/test/rakuten-token-public" "404" "Route rakuten-token-public bloquée" || ((FAILED_TESTS++))
test_route "/debug-email" "404" "Page debug-email bloquée" || ((FAILED_TESTS++))
test_route "/test-email" "404" "Page test-email bloquée" || ((FAILED_TESTS++))

echo ""
echo "✅ Test des routes normales (doivent fonctionner):"

test_route "/" "200" "Page d'accueil accessible" || ((FAILED_TESTS++))
test_route "/login" "200" "Page de login accessible" || ((FAILED_TESTS++))

# Résultats des tests
echo ""
if [ $FAILED_TESTS -eq 0 ]; then
    echo "🎉 ✅ TOUS LES TESTS DE SÉCURITÉ RÉUSSIS !"
    echo "   • Toutes les routes de debug sont bloquées"
    echo "   • Les routes normales fonctionnent"
    echo "   • L'application est sécurisée"
    
    echo ""
    echo "📋 Prochaines étapes:"
    echo "   1. Vérifiez manuellement: https://stream2spin-staging.vercel.app"
    echo "   2. Testez les fonctionnalités principales"
    echo "   3. Si tout fonctionne, mergez vers main"
    echo "   4. Déployez en production"
    
    # Créer un rapport de déploiement
    cat > deployment-report.md << EOF
# Rapport de Déploiement Staging

**Date:** $(date)
**Branche:** staging
**Commit:** $(git rev-parse HEAD)

## ✅ Validation de Sécurité
- Toutes les routes de debug supprimées
- Validation automatique réussie
- Tests de sécurité sur staging réussis

## 🧪 Tests Effectués
- Routes de debug bloquées (404)
- Routes normales fonctionnelles (200)
- Application sécurisée pour production

## 🔗 Liens
- Staging: https://stream2spin-staging.vercel.app
- Admin: https://admin-staging.stream2spin.com (à configurer)

## 📝 Actions Suivantes
1. Tests manuels complets
2. Validation équipe
3. Merge vers main si OK
4. Déploiement production
EOF
    
    echo "📄 Rapport créé: deployment-report.md"
    
else
    echo "❌ ÉCHEC DES TESTS DE SÉCURITÉ ($FAILED_TESTS tests échoués)"
    echo "   Vérifiez les routes qui ne sont pas correctement bloquées"
    echo "   Corrigez les problèmes avant de continuer"
    exit 1
fi

echo ""
echo "🎯 Déploiement staging terminé avec succès !"
