#!/usr/bin/env node

const { drizzle } = require('drizzle-orm/postgres-js');
const postgres = require('postgres');
const { migrate } = require('drizzle-orm/postgres-js/migrator');
require('dotenv').config({ path: '.env.local' });

async function applyMigrations() {
  if (process.env.NODE_ENV === 'development') {
  console.log('🚀 Application des migrations...');
  }
  
  const connectionString = process.env.DATABASE_URL;
  if (!connectionString) {
    console.error('❌ DATABASE_URL non trouvée dans .env.local');
    process.exit(1);
  }

  if (process.env.NODE_ENV === 'development') {
  console.log('📡 Connexion à la base de données...');
  }
  const sql = postgres(connectionString, { max: 1 });
  const db = drizzle(sql);

  try {
    if (process.env.NODE_ENV === 'development') {
    console.log('📦 Application des migrations...');
    }
    await migrate(db, { migrationsFolder: './drizzle' });
    if (process.env.NODE_ENV === 'development') {
    console.log('✅ Migrations appliquées avec succès !');
    }
  } catch (error) {
    console.error('❌ Erreur lors de l\'application des migrations:', error);
    process.exit(1);
  } finally {
    await sql.end();
  }
}

applyMigrations();
