-- Script pour réinitialiser les dates d'envoi d'email pour permettre de nouveaux tests
-- À exécuter dans la console Supabase

-- Réinitialiser les dates de dernier email envoyé pour permettre de nouveaux tests
UPDATE users 
SET "last_email_sent" = NULL
WHERE email IN ('<EMAIL>', '<EMAIL>');

-- Vérifier le résultat
SELECT 
  id,
  name,
  email,
  "emailFrequency",
  "last_email_sent",
  "firstRecommendationEmailSent"
FROM users 
WHERE email IN ('<EMAIL>', '<EMAIL>');
