#!/usr/bin/env node

/**
 * Script de validation du déploiement des optimisations sur staging
 */

if (process.env.NODE_ENV === 'development') {
console.log('🚀 Validation du déploiement des optimisations DB sur staging');
}
console.log('');

const fs = require('fs');
const path = require('path');

// Vérifier que tous les fichiers d'optimisation sont présents
const requiredFiles = [
  'lib/db/index.ts',
  'lib/db/query-cache.ts',
  'auth.ts',
  'lib/preload-critical-routes.ts',
  'package.json',
  'scripts/optimize-db-indexes.sql',
  'DEPLOYMENT_STAGING.md',
  'scripts/setup-staging-db-optimizations.md'
];

if (process.env.NODE_ENV === 'development') {
console.log('📁 Vérification des fichiers d\'optimisation:');
}
console.log('');

let allFilesPresent = true;

requiredFiles.forEach(file => {
  const filePath = path.join(process.cwd(), file);
  if (fs.existsSync(filePath)) {
    if (process.env.NODE_ENV === 'development') {
    console.log(`✅ ${file}`);
    }
  } else {
    if (process.env.NODE_ENV === 'development') {
    console.log(`❌ ${file} - MANQUANT`);
    }
    allFilesPresent = false;
  }
});

if (process.env.NODE_ENV === 'development') {
console.log('');
}

if (!allFilesPresent) {
  if (process.env.NODE_ENV === 'development') {
  console.log('❌ Certains fichiers d\'optimisation sont manquants !');
  }
  process.exit(1);
}

// Vérifier le contenu des fichiers critiques
if (process.env.NODE_ENV === 'development') {
console.log('🔍 Vérification du contenu des optimisations:');
}
console.log('');

const checks = [
  {
    file: 'lib/db/index.ts',
    patterns: [
      'max: 10',
      'idle_timeout: 120',
      'connectionPool',
      'globalClient',
      'ultra-optimisée'
    ],
    name: 'Pool de connexions avancé'
  },
  {
    file: 'lib/db/query-cache.ts',
    patterns: [
      'SESSION_TTL = 30 * 60 * 1000',
      'DEFAULT_TTL = 10 * 60 * 1000',
      'switch (queryType)',
      'cacheUserQuery'
    ],
    name: 'Cache intelligent avec TTL'
  },
  {
    file: 'auth.ts',
    patterns: [
      'getCachedUserQuery',
      'cacheUserQuery',
      'Cache DB miss',
      'Cache DB hit',
      '30min'
    ],
    name: 'Session callback optimisé'
  },
  {
    file: 'package.json',
    patterns: [
      '--turbo',
      'dev:fast'
    ],
    name: 'Turbopack activé'
  },
  {
    file: 'lib/preload-critical-routes.ts',
    patterns: [
      'preloadCriticalRoutes',
      'optimizeDNS',
      'dns-prefetch',
      'Modules critiques pré-chargés'
    ],
    name: 'Pré-chargement automatique'
  }
];

let allChecksPass = true;

checks.forEach(check => {
  const filePath = path.join(process.cwd(), check.file);
  
  if (fs.existsSync(filePath)) {
    const content = fs.readFileSync(filePath, 'utf8');
    const missingPatterns = check.patterns.filter(pattern => !content.includes(pattern));
    
    if (missingPatterns.length === 0) {
      if (process.env.NODE_ENV === 'development') {
      console.log(`✅ ${check.name} - Toutes les optimisations présentes`);
      }
    } else {
      if (process.env.NODE_ENV === 'development') {
      console.log(`⚠️ ${check.name} - Patterns manquants: ${missingPatterns.join(', ')}`);
      }
      allChecksPass = false;
    }
  } else {
    if (process.env.NODE_ENV === 'development') {
    console.log(`❌ ${check.name} - Fichier manquant: ${check.file}`);
    }
    allChecksPass = false;
  }
});

if (process.env.NODE_ENV === 'development') {
console.log('');
}

// Vérifier le statut Git
if (process.env.NODE_ENV === 'development') {
console.log('📋 Statut du déploiement:');
}
console.log('');

try {
  const { execSync } = require('child_process');
  
  // Vérifier la branche actuelle
  const currentBranch = execSync('git branch --show-current', { encoding: 'utf8' }).trim();
  if (process.env.NODE_ENV === 'development') {
  console.log(`🌿 Branche actuelle: ${currentBranch}`);
  }
  
  if (currentBranch !== 'staging') {
    if (process.env.NODE_ENV === 'development') {
    console.log('⚠️ Attention: Vous n\'êtes pas sur la branche staging');
    }
  }
  
  // Vérifier le dernier commit
  const lastCommit = execSync('git log -1 --oneline', { encoding: 'utf8' }).trim();
  if (process.env.NODE_ENV === 'development') {
  console.log(`📝 Dernier commit: ${lastCommit}`);
  }
  
  // Vérifier si tout est poussé
  try {
    const unpushedCommits = execSync('git log origin/staging..HEAD --oneline', { encoding: 'utf8' }).trim();
    if (unpushedCommits) {
      if (process.env.NODE_ENV === 'development') {
      console.log('⚠️ Commits non poussés détectés:');
      }
      console.log(unpushedCommits);
    } else {
      if (process.env.NODE_ENV === 'development') {
      console.log('✅ Tous les commits sont poussés sur origin/staging');
      }
    }
  } catch (error) {
    if (process.env.NODE_ENV === 'development') {
    console.log('ℹ️ Impossible de vérifier les commits non poussés');
    }
  }
  
} catch (error) {
  if (process.env.NODE_ENV === 'development') {
  console.log('⚠️ Impossible de vérifier le statut Git:', error.message);
  }
}

if (process.env.NODE_ENV === 'development') {
console.log('');
}

// Résumé final
if (process.env.NODE_ENV === 'development') {
console.log('📊 Résumé de la validation:');
}
console.log('');

if (allFilesPresent && allChecksPass) {
  if (process.env.NODE_ENV === 'development') {
  console.log('🎉 SUCCÈS - Toutes les optimisations sont correctement déployées !');
  }
  console.log('');
  if (process.env.NODE_ENV === 'development') {
  console.log('📋 Prochaines étapes:');
  }
  console.log('1. ✅ Vérifier le déploiement automatique sur Vercel');
  if (process.env.NODE_ENV === 'development') {
  console.log('2. 🗄️ Exécuter le script SQL d\'optimisation sur Supabase Staging');
  }
  console.log('3. 📊 Lancer le monitoring de performance');
  if (process.env.NODE_ENV === 'development') {
  console.log('4. 🧪 Tester l\'application sur staging');
  }
  console.log('');
  if (process.env.NODE_ENV === 'development') {
  console.log('🔧 Commandes utiles:');
  }
  console.log('   node scripts/monitor-staging-performance.js');
  if (process.env.NODE_ENV === 'development') {
  console.log('   cat scripts/setup-staging-db-optimizations.md');
  }
  console.log('');
  if (process.env.NODE_ENV === 'development') {
  console.log('🎯 Performances attendues:');
  }
  console.log('   - Header loading: ~500ms (au lieu de 4+ secondes)');
  if (process.env.NODE_ENV === 'development') {
  console.log('   - Requêtes DB: 50-200ms (au lieu de 1-5 secondes)');
  }
  console.log('   - Cache hit rate: >90% après quelques minutes');
  if (process.env.NODE_ENV === 'development') {
  console.log('   - Compilation: 39-80% plus rapide avec Turbopack');
  }
  
} else {
  if (process.env.NODE_ENV === 'development') {
  console.log('❌ ÉCHEC - Certaines optimisations sont manquantes ou incomplètes');
  }
  console.log('');
  if (process.env.NODE_ENV === 'development') {
  console.log('🔧 Actions requises:');
  }
  
  if (!allFilesPresent) {
    if (process.env.NODE_ENV === 'development') {
    console.log('   - Vérifier que tous les fichiers d\'optimisation sont présents');
    }
  }
  
  if (!allChecksPass) {
    if (process.env.NODE_ENV === 'development') {
    console.log('   - Vérifier le contenu des fichiers d\'optimisation');
    }
    console.log('   - Re-appliquer les optimisations manquantes');
  }
  
  if (process.env.NODE_ENV === 'development') {
  console.log('   - Re-exécuter ce script après corrections');
  }
}

if (process.env.NODE_ENV === 'development') {
console.log('');
}
console.log('📚 Documentation complète: DEPLOYMENT_STAGING.md');
if (process.env.NODE_ENV === 'development') {
console.log('🗄️ Instructions SQL: scripts/setup-staging-db-optimizations.md');
}
