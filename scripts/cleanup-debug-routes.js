#!/usr/bin/env node

/**
 * Script de nettoyage des routes de debug
 * Supprime définitivement toutes les fonctionnalités de debug de l'application principale
 */

const fs = require('fs');
const path = require('path');

// Routes et fichiers à supprimer
const ROUTES_TO_DELETE = [
  // Pages de debug
  'app/debug-email',
  'app/test-email', 
  'app/test-spotify-embed',
  'app/test-error-pages',
  
  // APIs publiques critiques
  'app/api/public/user-status',
  'app/api/public/reset-email-flag',
  'app/api/public-debug',
  
  // APIs de test (toutes)
  'app/api/test',
  
  // APIs de debug
  'app/api/debug',
  
  // Utilitaires de debug
  'lib/sync-logger.ts',
];

// Fichiers à modifier (supprimer imports/références)
const FILES_TO_CLEAN = [
  'middleware.ts',
  'app/layout.tsx',
  'components/navigation.tsx',
];

// Patterns à supprimer dans les fichiers
const PATTERNS_TO_REMOVE = [
  /import.*sync-logger.*/g,
  /import.*debug-protection.*/g,
  /console\.log\(.*🧪.*/g,
  /console\.log\(.*🔍.*/g,
  /console\.log\(.*⚠️.*/g,
  /\/\/ Debug:.*/g,
];

function deleteRouteOrFile(relativePath) {
  const fullPath = path.join(process.cwd(), relativePath);
  
  if (!fs.existsSync(fullPath)) {
    if (process.env.NODE_ENV === 'development') {
    console.log(`⚠️ Chemin non trouvé: ${relativePath}`);
    }
    return false;
  }
  
  try {
    const stats = fs.statSync(fullPath);
    
    if (stats.isDirectory()) {
      // Supprimer récursivement le répertoire
      fs.rmSync(fullPath, { recursive: true, force: true });
      if (process.env.NODE_ENV === 'development') {
      console.log(`🗂️ Répertoire supprimé: ${relativePath}`);
      }
    } else {
      // Supprimer le fichier
      fs.unlinkSync(fullPath);
      if (process.env.NODE_ENV === 'development') {
      console.log(`📄 Fichier supprimé: ${relativePath}`);
      }
    }
    
    return true;
  } catch (error) {
    console.error(`❌ Erreur suppression ${relativePath}:`, error.message);
    return false;
  }
}

function cleanFile(relativePath) {
  const fullPath = path.join(process.cwd(), relativePath);
  
  if (!fs.existsSync(fullPath)) {
    if (process.env.NODE_ENV === 'development') {
    console.log(`⚠️ Fichier non trouvé: ${relativePath}`);
    }
    return false;
  }
  
  try {
    let content = fs.readFileSync(fullPath, 'utf8');
    let modified = false;
    
    // Appliquer tous les patterns de nettoyage
    PATTERNS_TO_REMOVE.forEach(pattern => {
      const originalContent = content;
      content = content.replace(pattern, '');
      if (content !== originalContent) {
        modified = true;
      }
    });
    
    // Nettoyer les lignes vides multiples
    content = content.replace(/\n\s*\n\s*\n/g, '\n\n');
    
    if (modified) {
      fs.writeFileSync(fullPath, content);
      if (process.env.NODE_ENV === 'development') {
      console.log(`🧹 Fichier nettoyé: ${relativePath}`);
      }
      return true;
    } else {
      if (process.env.NODE_ENV === 'development') {
      console.log(`✅ Fichier déjà propre: ${relativePath}`);
      }
      return false;
    }
  } catch (error) {
    console.error(`❌ Erreur nettoyage ${relativePath}:`, error.message);
    return false;
  }
}

function updatePackageJson() {
  const packagePath = path.join(process.cwd(), 'package.json');
  
  try {
    const packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
    
    // Supprimer les scripts de debug s'ils existent
    if (packageJson.scripts) {
      delete packageJson.scripts['debug:email'];
      delete packageJson.scripts['test:routes'];
      delete packageJson.scripts['clean:logs'];
    }
    
    fs.writeFileSync(packagePath, JSON.stringify(packageJson, null, 2));
    if (process.env.NODE_ENV === 'development') {
    console.log('📦 package.json mis à jour');
    }
  } catch (error) {
    console.error('❌ Erreur mise à jour package.json:', error.message);
  }
}

function createCleanupReport() {
  const report = {
    timestamp: new Date().toISOString(),
    deletedRoutes: [],
    cleanedFiles: [],
    summary: {
      totalDeleted: 0,
      totalCleaned: 0,
      success: true
    }
  };
  
  // Sauvegarder le rapport
  const reportPath = path.join(process.cwd(), 'cleanup-report.json');
  fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
  if (process.env.NODE_ENV === 'development') {
  console.log(`📋 Rapport de nettoyage créé: cleanup-report.json`);
  }
  
  return report;
}

function main() {
  if (process.env.NODE_ENV === 'development') {
  console.log('🧹 Démarrage du nettoyage des routes de debug...\n');
  }
  
  let deletedCount = 0;
  let cleanedCount = 0;
  
  // Phase 1: Suppression des routes et fichiers
  if (process.env.NODE_ENV === 'development') {
  console.log('📁 Phase 1: Suppression des routes et fichiers de debug');
  }
  ROUTES_TO_DELETE.forEach(route => {
    if (deleteRouteOrFile(route)) {
      deletedCount++;
    }
  });
  
  if (process.env.NODE_ENV === 'development') {
  console.log(`\n✅ ${deletedCount} éléments supprimés\n`);
  }
  
  // Phase 2: Nettoyage des fichiers existants
  if (process.env.NODE_ENV === 'development') {
  console.log('🧽 Phase 2: Nettoyage des fichiers existants');
  }
  FILES_TO_CLEAN.forEach(file => {
    if (cleanFile(file)) {
      cleanedCount++;
    }
  });
  
  if (process.env.NODE_ENV === 'development') {
  console.log(`\n✅ ${cleanedCount} fichiers nettoyés\n`);
  }
  
  // Phase 3: Mise à jour package.json
  if (process.env.NODE_ENV === 'development') {
  console.log('📦 Phase 3: Mise à jour package.json');
  }
  updatePackageJson();
  
  // Phase 4: Création du rapport
  if (process.env.NODE_ENV === 'development') {
  console.log('\n📋 Phase 4: Création du rapport de nettoyage');
  }
  createCleanupReport();
  
  // Résumé final
  if (process.env.NODE_ENV === 'development') {
  console.log('\n🎉 Nettoyage terminé avec succès !');
  }
  console.log(`📊 Résumé:`);
  if (process.env.NODE_ENV === 'development') {
  console.log(`   • ${deletedCount} routes/fichiers supprimés`);
  }
  console.log(`   • ${cleanedCount} fichiers nettoyés`);
  if (process.env.NODE_ENV === 'development') {
  console.log(`   • Application prête pour la production`);
  }
  
  if (process.env.NODE_ENV === 'development') {
  console.log('\n📝 Actions recommandées :');
  }
  console.log('1. Vérifier que l\'application fonctionne : npm run dev');
  if (process.env.NODE_ENV === 'development') {
  console.log('2. Exécuter les tests : npm run test');
  }
  console.log('3. Commit les changements : git add . && git commit -m "feat: remove debug routes for production"');
  if (process.env.NODE_ENV === 'development') {
  console.log('4. Déployer sur staging pour validation');
  }
}

// Vérification de sécurité
if (process.env.NODE_ENV === 'production') {
  console.error('❌ ERREUR: Ce script ne doit PAS être exécuté en production !');
  process.exit(1);
}

// Demander confirmation
const readline = require('readline');
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

rl.question('⚠️ ATTENTION: Ce script va supprimer définitivement toutes les routes de debug. Continuer ? (oui/non): ', (answer) => {
  if (answer.toLowerCase() === 'oui' || answer.toLowerCase() === 'y') {
    main();
  } else {
    if (process.env.NODE_ENV === 'development') {
    console.log('❌ Nettoyage annulé');
    }
  }
  rl.close();
});

module.exports = { deleteRouteOrFile, cleanFile };
