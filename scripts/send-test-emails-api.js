#!/usr/bin/env node

/**
 * Script pour envoyer des emails de test via l'API locale
 * Utilise le serveur de développement en local
 */

const http = require('http');
const https = require('https');
const querystring = require('querystring');

// Configuration
const LOCAL_URL = 'http://localhost:3000';
const TEST_EMAIL = '<EMAIL>';
const TEST_USER_NAME = 'Simon Gavelle';

// Couleurs pour la console
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(message, color = 'reset') {
  const timestamp = new Date().toISOString();
  console.log(`${colors[color]}[${timestamp}] ${message}${colors.reset}`);
}

function logInfo(message) {
  log(`ℹ️  ${message}`, 'blue');
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

/**
 * Fait une requête HTTP
 */
function makeRequest(options, postData = null) {
  return new Promise((resolve, reject) => {
    const client = options.protocol === 'https:' ? https : http;
    
    const req = client.request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          const parsedData = data ? JSON.parse(data) : {};
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            body: parsedData
          });
        } catch (e) {
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            body: data
          });
        }
      });
    });
    
    req.on('error', (err) => {
      reject(err);
    });
    
    if (postData) {
      req.write(postData);
    }
    
    req.end();
  });
}

/**
 * Démarre le serveur de développement
 */
async function startDevServer() {
  logInfo('Vérification du serveur de développement...');
  
  try {
    const response = await makeRequest({
      hostname: 'localhost',
      port: 3000,
      path: '/api/auth/providers',
      method: 'GET',
      timeout: 3000
    });
    
    if (response.statusCode === 200) {
      logSuccess('Serveur de développement déjà en cours d\'exécution');
      return true;
    }
  } catch (error) {
    logWarning('Serveur de développement non disponible');
    logInfo('Veuillez démarrer le serveur avec: npm run dev');
    logInfo('Puis relancer ce script');
    return false;
  }
  
  return false;
}

/**
 * Envoie un email de test simple
 */
async function sendTestEmail() {
  logInfo('Envoi d\'un email de test simple...');
  
  const testData = {
    toEmail: TEST_EMAIL,
    subject: 'Test Email - Stream2Spin',
    message: 'Ceci est un email de test depuis le script d\'envoi automatique.'
  };
  
  try {
    const response = await makeRequest({
      hostname: 'localhost',
      port: 3000,
      path: '/api/admin/send-test-email',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${process.env.CRON_SECRET || 'test-secret'}`
      }
    }, JSON.stringify(testData));
    
    if (response.statusCode === 200 && response.body.success) {
      logSuccess(`Email de test envoyé ! ID: ${response.body.messageId}`);
      return true;
    } else {
      logError(`Erreur lors de l'envoi de l'email de test: ${response.body.error || 'Unknown error'}`);
      return false;
    }
  } catch (error) {
    logError(`Erreur lors de l'envoi de l'email de test: ${error.message}`);
    return false;
  }
}

/**
 * Simule un utilisateur et envoie des emails de recommandations
 */
async function sendRecommendationsViaAPI() {
  logInfo('Simulation d\'un utilisateur pour l\'envoi de recommandations...');
  
  const testUserData = {
    email: TEST_EMAIL,
    name: TEST_USER_NAME,
    preferredLanguage: 'fr',
    emailFrequency: 'weekly'
  };
  
  try {
    // Simuler une génération de recommandations
    const response = await makeRequest({
      hostname: 'localhost',
      port: 3000,
      path: '/api/admin/test-recommendations',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${process.env.CRON_SECRET || 'test-secret'}`
      }
    }, JSON.stringify(testUserData));
    
    if (response.statusCode === 200 && response.body.success) {
      logSuccess(`Email de recommandations envoyé ! ID: ${response.body.messageId}`);
      return true;
    } else {
      logError(`Erreur lors de l'envoi des recommandations: ${response.body.error || 'Unknown error'}`);
      return false;
    }
  } catch (error) {
    logError(`Erreur lors de l'envoi des recommandations: ${error.message}`);
    return false;
  }
}

/**
 * Utilise les vrais templates React pour envoyer les emails
 */
async function sendEmailsDirectly() {
  logInfo('Envoi des emails avec les vrais templates React...');
  
  try {
    // Charger les variables d'environnement
    require('dotenv').config({ path: '.env.local' });
    
    // Vérifier la configuration
    if (!process.env.RESEND_API_KEY) {
      logError('RESEND_API_KEY manquante dans .env.local');
      return { welcome: false, recommendations: false };
    }
    
    logInfo('Configuration Resend trouvée');
    
    // Utiliser les vraies fonctions d'envoi d'email
    const { sendWelcomeEmail } = require('../lib/email.ts');
    const { sendRecommendationsEmail } = require('../lib/resend.ts');
    
    // 1. Envoyer l'email de bienvenue avec le vrai template
    logInfo('Envoi de l\'email de bienvenue avec le template React...');
    const welcomeResult = await sendWelcomeEmail({
      name: TEST_USER_NAME,
      email: TEST_EMAIL
    });
    
    let welcomeSuccess = false;
    if (welcomeResult.success) {
      logSuccess(`Email de bienvenue envoyé ! ID: ${welcomeResult.messageId}`);
      welcomeSuccess = true;
    } else {
      logError(`Erreur email de bienvenue: ${welcomeResult.error}`);
    }
    
    // Attendre un peu
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // 2. Envoyer l'email de recommandations avec le vrai template
    logInfo('Envoi de l\'email de recommandations avec le template React...');
    
    // Données de test pour les recommandations
    const testUser = {
      id: 'test-user-123',
      name: TEST_USER_NAME,
      email: TEST_EMAIL,
      preferredLanguage: 'fr'
    };

    // Recommandations de test
    const testRecommendations = [
      {
        artistName: 'Daft Punk',
        albumTitle: 'Random Access Memories',
        albumCoverUrl: 'https://example.com/cover1.jpg',
        listenScore: 85,
        affiliateLinks: [
          {
            vendor: 'Amazon',
            url: 'https://amazon.fr/daft-punk-ram',
            price: 29.99,
            currency: 'EUR'
          }
        ],
        isOwned: false
      },
      {
        artistName: 'Radiohead',
        albumTitle: 'OK Computer',
        albumCoverUrl: 'https://example.com/cover2.jpg',
        listenScore: 92,
        affiliateLinks: [
          {
            vendor: 'Amazon',
            url: 'https://amazon.fr/radiohead-ok-computer',
            price: 24.99,
            currency: 'EUR'
          }
        ],
        isOwned: false
      },
      {
        artistName: 'Pink Floyd',
        albumTitle: 'The Dark Side of the Moon',
        albumCoverUrl: 'https://example.com/cover3.jpg',
        listenScore: 88,
        affiliateLinks: [
          {
            vendor: 'Amazon',
            url: 'https://amazon.fr/pink-floyd-dark-side',
            price: 32.99,
            currency: 'EUR'
          }
        ],
        isOwned: false
      }
    ];
    
    const recommendationsResult = await sendRecommendationsEmail(testUser, testRecommendations);
    
    let recommendationsSuccess = false;
    if (recommendationsResult.success) {
      logSuccess(`Email de recommandations envoyé ! ID: ${recommendationsResult.messageId}`);
      recommendationsSuccess = true;
    } else {
      logError(`Erreur email de recommandations: ${recommendationsResult.error}`);
    }
    
    return {
      welcome: welcomeSuccess,
      recommendations: recommendationsSuccess
    };
    
  } catch (error) {
    logError(`Erreur lors de l'envoi avec templates: ${error.message}`);
    return { welcome: false, recommendations: false };
  }
}

/**
 * Fonction principale
 */
async function main() {
  log('🚀 Envoi des emails de test Stream2Spin', 'bold');
  log(`📧 Destination: ${TEST_EMAIL}`, 'blue');
  log('', 'reset');
  
  const results = await sendEmailsDirectly();
  
  log('', 'reset');
  
  // Résumé
  logInfo('=== RÉSUMÉ ===');
  const welcomeStatus = results.welcome ? '✅' : '❌';
  const recommendationsStatus = results.recommendations ? '✅' : '❌';
  
  log(`${welcomeStatus} Email de bienvenue`);
  log(`${recommendationsStatus} Email de recommandations`);
  
  const totalSuccess = Object.values(results).filter(Boolean).length;
  const totalEmails = Object.keys(results).length;
  
  log('', 'reset');
  
  if (totalSuccess === totalEmails) {
    logSuccess(`🎉 Tous les emails ont été envoyés avec succès ! (${totalSuccess}/${totalEmails})`);
    logInfo(`📧 Vérifiez votre boîte mail: ${TEST_EMAIL}`);
  } else {
    logWarning(`⚠️  Seulement ${totalSuccess}/${totalEmails} emails envoyés avec succès`);
  }
  
  process.exit(totalSuccess === totalEmails ? 0 : 1);
}

// Gestion des erreurs
process.on('unhandledRejection', (error) => {
  logError(`Erreur non gérée: ${error.message}`);
  process.exit(1);
});

// Lancement du script
main().catch(error => {
  logError(`Erreur fatale: ${error.message}`);
  process.exit(1);
}); 