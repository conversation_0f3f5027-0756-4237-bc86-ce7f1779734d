-- Script pour configurer un environnement de test pour les emails sociaux
-- À exécuter dans la console Supabase ou via un client SQL

-- 1. Activer les emails pour quelques utilisateurs de test
UPDATE users 
SET 
  "emailFrequency" = 'weekly',
  "firstRecommendationEmailSent" = true,
  "shareRecommendations" = true,
  "profileVisibility" = 'public'
WHERE id IN (
  SELECT id FROM users 
  WHERE email IS NOT NULL 
  LIMIT 3
);

-- 2. Créer des relations de suivi entre utilisateurs
-- (Utilisateur 1 suit Utilisateur 2 et 3)
INSERT INTO followers (id, "followerId", "followingId", "createdAt")
SELECT 
  gen_random_uuid(),
  u1.id,
  u2.id,
  NOW()
FROM 
  (SELECT id FROM users WHERE email IS NOT NULL ORDER BY "createdAt" LIMIT 1) u1,
  (SELECT id FROM users WHERE email IS NOT NULL ORDER BY "createdAt" OFFSET 1 LIMIT 2) u2
ON CONFLICT DO NOTHING;

-- 3. Mettre à jour les dates de génération des recommandations pour qu'elles soient récentes
UPDATE recommendations 
SET "generatedAt" = NOW() - INTERVAL '2 days'
WHERE "userId" IN (
  SELECT id FROM users 
  WHERE email IS NOT NULL 
  LIMIT 3
)
AND "timeframe" = 'short_term';

-- 4. Vérifier la configuration
SELECT 
  u.id,
  u.name,
  u.email,
  u."emailFrequency",
  u."firstRecommendationEmailSent",
  u."shareRecommendations",
  u."profileVisibility",
  (SELECT COUNT(*) FROM followers f WHERE f."followerId" = u.id) as following_count,
  (SELECT COUNT(*) FROM recommendations r WHERE r."userId" = u.id AND r."generatedAt" >= NOW() - INTERVAL '7 days') as recent_recommendations
FROM users u
WHERE u.email IS NOT NULL
ORDER BY u."createdAt"
LIMIT 5;
