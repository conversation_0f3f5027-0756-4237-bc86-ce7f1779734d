#!/usr/bin/env node

/**
 * Script de monitoring de la santé des emails
 * Surveille les métriques de délivrabilité et envoie des alertes si nécessaire
 */

const https = require('https');

// Configuration
const CONFIG = {
  // Seuils d'alerte
  thresholds: {
    deliverabilityRate: 95, // Minimum 95%
    bounceRate: 2, // Maximum 2%
    complaintRate: 0.1, // Maximum 0.1%
    openRate: 15, // Minimum 15%
  },
  
  // Webhook pour les alertes (Slack, Discord, etc.)
  alertWebhook: process.env.ALERT_WEBHOOK_URL,
  
  // Email d'alerte
  alertEmail: process.env.ALERT_EMAIL || '<EMAIL>',
  
  // URL de l'API
  apiUrl: process.env.NEXTAUTH_URL || 'http://localhost:3000'
};

// Couleurs pour la console
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(message, color = 'reset') {
  const timestamp = new Date().toISOString();
  if (process.env.NODE_ENV === 'development') {
  console.log(`${colors[color]}[${timestamp}] ${message}${colors.reset}`);
  }
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

function logInfo(message) {
  log(`ℹ️  ${message}`, 'blue');
}

/**
 * Récupère les métriques depuis l'API
 */
async function fetchMetrics() {
  return new Promise((resolve, reject) => {
    const url = `${CONFIG.apiUrl}/api/admin/email-metrics?days=7`;
    
    https.get(url, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          const parsed = JSON.parse(data);
          if (parsed.success) {
            resolve(parsed.metrics);
          } else {
            reject(new Error(parsed.message || 'Erreur API'));
          }
        } catch (error) {
          reject(new Error(`Erreur de parsing JSON: ${error.message}`));
        }
      });
    }).on('error', (error) => {
      reject(new Error(`Erreur réseau: ${error.message}`));
    });
  });
}

/**
 * Analyse les métriques et détecte les problèmes
 */
function analyzeMetrics(metrics) {
  const issues = [];
  const warnings = [];
  const global = metrics.global;

  // Vérifier le taux de délivrabilité
  const deliverabilityRate = parseFloat(global.deliverabilityRate);
  if (deliverabilityRate < CONFIG.thresholds.deliverabilityRate) {
    issues.push({
      type: 'deliverability',
      message: `Taux de délivrabilité faible: ${deliverabilityRate}% (seuil: ${CONFIG.thresholds.deliverabilityRate}%)`,
      severity: 'high',
      value: deliverabilityRate,
      threshold: CONFIG.thresholds.deliverabilityRate
    });
  }

  // Vérifier le taux de bounce
  const bounceRate = parseFloat(global.bounceRate);
  if (bounceRate > CONFIG.thresholds.bounceRate) {
    issues.push({
      type: 'bounce',
      message: `Taux de bounce élevé: ${bounceRate}% (seuil: ${CONFIG.thresholds.bounceRate}%)`,
      severity: 'high',
      value: bounceRate,
      threshold: CONFIG.thresholds.bounceRate
    });
  }

  // Vérifier le taux de plainte
  const complaintRate = parseFloat(global.complaintRate);
  if (complaintRate > CONFIG.thresholds.complaintRate) {
    issues.push({
      type: 'complaint',
      message: `Taux de plainte élevé: ${complaintRate}% (seuil: ${CONFIG.thresholds.complaintRate}%)`,
      severity: 'critical',
      value: complaintRate,
      threshold: CONFIG.thresholds.complaintRate
    });
  }

  // Vérifier le taux d'ouverture
  const openRate = parseFloat(global.openRate);
  if (openRate < CONFIG.thresholds.openRate) {
    warnings.push({
      type: 'engagement',
      message: `Taux d'ouverture faible: ${openRate}% (seuil: ${CONFIG.thresholds.openRate}%)`,
      severity: 'medium',
      value: openRate,
      threshold: CONFIG.thresholds.openRate
    });
  }

  // Vérifier les domaines problématiques
  if (metrics.problematicDomains.length > 0) {
    warnings.push({
      type: 'domains',
      message: `${metrics.problematicDomains.length} domaine(s) problématique(s) détecté(s)`,
      severity: 'medium',
      details: metrics.problematicDomains.slice(0, 3).map(d => d.domain)
    });
  }

  // Vérifier les utilisateurs problématiques
  if (metrics.problematicUsers.length > 0) {
    warnings.push({
      type: 'users',
      message: `${metrics.problematicUsers.length} utilisateur(s) avec des problèmes de délivrabilité`,
      severity: 'medium',
      details: metrics.problematicUsers.length
    });
  }

  return { issues, warnings };
}

/**
 * Envoie une alerte
 */
async function sendAlert(issues, warnings, metrics) {
  const alertData = {
    timestamp: new Date().toISOString(),
    service: 'Stream2Spin Email Monitoring',
    issues,
    warnings,
    summary: {
      deliverabilityRate: metrics.global.deliverabilityRate,
      bounceRate: metrics.global.bounceRate,
      complaintRate: metrics.global.complaintRate,
      openRate: metrics.global.openRate,
      totalEmails: metrics.global.emailsSent
    }
  };

  // Log local
  logError(`🚨 ALERTE EMAIL DÉTECTÉE:`);
  issues.forEach(issue => {
    logError(`  - ${issue.message}`);
  });
  warnings.forEach(warning => {
    logWarning(`  - ${warning.message}`);
  });

  // Envoyer via webhook si configuré
  if (CONFIG.alertWebhook) {
    try {
      await sendWebhookAlert(alertData);
      logSuccess('Alerte envoyée via webhook');
    } catch (error) {
      logError(`Erreur envoi webhook: ${error.message}`);
    }
  }

  // TODO: Envoyer par email si configuré
  // if (CONFIG.alertEmail) {
  //   await sendEmailAlert(alertData);
  // }
}

/**
 * Envoie une alerte via webhook
 */
function sendWebhookAlert(alertData) {
  return new Promise((resolve, reject) => {
    const url = new URL(CONFIG.alertWebhook);
    const postData = JSON.stringify({
      text: `🚨 Alerte Email Stream2Spin`,
      attachments: [{
        color: alertData.issues.length > 0 ? 'danger' : 'warning',
        title: 'Monitoring Email - Problèmes détectés',
        fields: [
          {
            title: 'Délivrabilité',
            value: `${alertData.summary.deliverabilityRate}%`,
            short: true
          },
          {
            title: 'Taux de Bounce',
            value: `${alertData.summary.bounceRate}%`,
            short: true
          },
          {
            title: 'Taux d\'Ouverture',
            value: `${alertData.summary.openRate}%`,
            short: true
          },
          {
            title: 'Emails Envoyés',
            value: alertData.summary.totalEmails,
            short: true
          }
        ],
        text: [...alertData.issues, ...alertData.warnings]
          .map(item => `• ${item.message}`)
          .join('\n')
      }]
    });

    const options = {
      hostname: url.hostname,
      port: url.port || 443,
      path: url.pathname,
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(postData)
      }
    };

    const req = https.request(options, (res) => {
      if (res.statusCode >= 200 && res.statusCode < 300) {
        resolve();
      } else {
        reject(new Error(`HTTP ${res.statusCode}`));
      }
    });

    req.on('error', reject);
    req.write(postData);
    req.end();
  });
}

/**
 * Génère un rapport de santé
 */
function generateHealthReport(metrics, analysis) {
  const { issues, warnings } = analysis;
  
  logInfo('📊 RAPPORT DE SANTÉ EMAIL');
  logInfo('================================');
  
  // Métriques principales
  logInfo(`Délivrabilité: ${metrics.global.deliverabilityRate}%`);
  logInfo(`Taux de Bounce: ${metrics.global.bounceRate}%`);
  logInfo(`Taux de Plainte: ${metrics.global.complaintRate}%`);
  logInfo(`Taux d'Ouverture: ${metrics.global.openRate}%`);
  logInfo(`Emails Envoyés: ${metrics.global.emailsSent}`);
  
  // Statut global
  if (issues.length === 0 && warnings.length === 0) {
    logSuccess('✅ Tous les indicateurs sont dans les seuils normaux');
  } else {
    if (issues.length > 0) {
      logError(`❌ ${issues.length} problème(s) critique(s) détecté(s)`);
    }
    if (warnings.length > 0) {
      logWarning(`⚠️ ${warnings.length} avertissement(s)`);
    }
  }
  
  // Recommandations
  if (issues.length > 0 || warnings.length > 0) {
    logInfo('\n📋 RECOMMANDATIONS:');
    
    if (issues.some(i => i.type === 'deliverability')) {
      logInfo('• Vérifier la configuration DNS (SPF, DKIM, DMARC)');
      logInfo('• Analyser les bounces récents');
    }
    
    if (issues.some(i => i.type === 'bounce')) {
      logInfo('• Nettoyer la liste des destinataires');
      logInfo('• Vérifier les domaines problématiques');
    }
    
    if (issues.some(i => i.type === 'complaint')) {
      logInfo('• Revoir le contenu des emails');
      logInfo('• Vérifier les listes de désabonnement');
    }
    
    if (warnings.some(w => w.type === 'engagement')) {
      logInfo('• Optimiser les sujets d\'email');
      logInfo('• Personnaliser le contenu');
      logInfo('• Ajuster les horaires d\'envoi');
    }
  }
}

/**
 * Fonction principale
 */
async function main() {
  log('🚀 Démarrage du monitoring email Stream2Spin', 'bold');
  
  try {
    // Récupérer les métriques
    logInfo('Récupération des métriques...');
    const metrics = await fetchMetrics();
    
    // Analyser les métriques
    logInfo('Analyse des métriques...');
    const analysis = analyzeMetrics(metrics);
    
    // Générer le rapport
    generateHealthReport(metrics, analysis);
    
    // Envoyer des alertes si nécessaire
    if (analysis.issues.length > 0 || analysis.warnings.length > 0) {
      await sendAlert(analysis.issues, analysis.warnings, metrics);
    }
    
    // Code de sortie
    const exitCode = analysis.issues.length > 0 ? 1 : 0;
    log(`\n✅ Monitoring terminé (code: ${exitCode})`, 'bold');
    process.exit(exitCode);
    
  } catch (error) {
    logError(`💥 Erreur fatale: ${error.message}`);
    process.exit(1);
  }
}

// Gestion des erreurs
process.on('unhandledRejection', (error) => {
  logError(`Erreur non gérée: ${error.message}`);
  process.exit(1);
});

// Exécution
if (require.main === module) {
  main();
}
