#!/usr/bin/env node

/**
 * Script de diagnostic des problèmes de cron jobs Stream2Spin
 * Identifie les problèmes courants et propose des solutions
 */

const fs = require('fs');
const path = require('path');

if (process.env.NODE_ENV === 'development') {
console.log('🔍 Diagnostic des Cron Jobs Stream2Spin');
}
console.log('==========================================');

// 1. Vérifier la configuration vercel.json
if (process.env.NODE_ENV === 'development') {
console.log('\n📋 1. Vérification de vercel.json');
}
const vercelConfigPath = path.join(__dirname, '..', 'vercel.json');

if (fs.existsSync(vercelConfigPath)) {
  try {
    const vercelConfig = JSON.parse(fs.readFileSync(vercelConfigPath, 'utf8'));
    if (process.env.NODE_ENV === 'development') {
    console.log('✅ vercel.json trouvé');
    }
    
    if (vercelConfig.crons && Array.isArray(vercelConfig.crons)) {
      if (process.env.NODE_ENV === 'development') {
      console.log(`📅 ${vercelConfig.crons.length} cron job(s) configuré(s):`);
      }
      vercelConfig.crons.forEach((cron, index) => {
        if (process.env.NODE_ENV === 'development') {
        console.log(`   ${index + 1}. ${cron.path} - ${cron.schedule}`);
        }
      });
    } else {
      if (process.env.NODE_ENV === 'development') {
      console.log('❌ Aucun cron job configuré dans vercel.json');
      }
    }
  } catch (error) {
    if (process.env.NODE_ENV === 'development') {
    console.log('❌ Erreur lors de la lecture de vercel.json:', error.message);
    }
  }
} else {
  if (process.env.NODE_ENV === 'development') {
  console.log('❌ vercel.json non trouvé');
  }
}

// 2. Vérifier les routes API des cron jobs
if (process.env.NODE_ENV === 'development') {
console.log('\n🔗 2. Vérification des routes API');
}
const cronRoutes = [
  'app/api/cron/generate-recommendations/route.ts',
  'app/api/cron/send-notifications/route.ts'
];

cronRoutes.forEach(routePath => {
  const fullPath = path.join(__dirname, '..', routePath);
  if (fs.existsSync(fullPath)) {
    if (process.env.NODE_ENV === 'development') {
    console.log(`✅ ${routePath} existe`);
    }
    
    // Vérifier la présence de la vérification CRON_SECRET
    const content = fs.readFileSync(fullPath, 'utf8');
    if (content.includes('CRON_SECRET')) {
      if (process.env.NODE_ENV === 'development') {
      console.log(`   ✅ Vérification CRON_SECRET présente`);
      }
    } else {
      if (process.env.NODE_ENV === 'development') {
      console.log(`   ❌ Vérification CRON_SECRET manquante`);
      }
    }
    
    if (content.includes('export async function POST')) {
      if (process.env.NODE_ENV === 'development') {
      console.log(`   ✅ Fonction POST exportée`);
      }
    } else {
      if (process.env.NODE_ENV === 'development') {
      console.log(`   ❌ Fonction POST manquante`);
      }
    }
  } else {
    if (process.env.NODE_ENV === 'development') {
    console.log(`❌ ${routePath} n'existe pas`);
    }
  }
});

// 3. Vérifier les variables d'environnement
if (process.env.NODE_ENV === 'development') {
console.log('\n🔐 3. Vérification des variables d\'environnement');
}
const envFiles = [
  '.env.local',
  '.env.production',
  '.env'
];

envFiles.forEach(envFile => {
  const envPath = path.join(__dirname, '..', envFile);
  if (fs.existsSync(envPath)) {
    if (process.env.NODE_ENV === 'development') {
    console.log(`✅ ${envFile} existe`);
    }
    const content = fs.readFileSync(envPath, 'utf8');
    if (content.includes('CRON_SECRET')) {
      if (process.env.NODE_ENV === 'development') {
      console.log(`   ✅ CRON_SECRET définie dans ${envFile}`);
      }
    } else {
      if (process.env.NODE_ENV === 'development') {
      console.log(`   ❌ CRON_SECRET manquante dans ${envFile}`);
      }
    }
  } else {
    if (process.env.NODE_ENV === 'development') {
    console.log(`❌ ${envFile} n'existe pas`);
    }
  }
});

// 4. Vérifier les dépendances
if (process.env.NODE_ENV === 'development') {
console.log('\n📦 4. Vérification des dépendances');
}
const packageJsonPath = path.join(__dirname, '..', 'package.json');
if (fs.existsSync(packageJsonPath)) {
  try {
    const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
    if (process.env.NODE_ENV === 'development') {
    console.log('✅ package.json valide');
    }
    
    // Vérifier les scripts de test
    if (packageJson.scripts && packageJson.scripts['test:cron']) {
      if (process.env.NODE_ENV === 'development') {
      console.log('   ✅ Script test:cron configuré');
      }
    } else {
      if (process.env.NODE_ENV === 'development') {
      console.log('   ❌ Script test:cron manquant');
      }
    }
  } catch (error) {
    if (process.env.NODE_ENV === 'development') {
    console.log('❌ Erreur lors de la lecture de package.json:', error.message);
    }
  }
}

// 5. Problèmes courants et solutions
if (process.env.NODE_ENV === 'development') {
console.log('\n💡 5. Problèmes courants et solutions');
}
console.log('----------------------------------------');

const commonIssues = [
  {
    issue: 'CRON_SECRET non définie dans Vercel',
    solution: 'Ajouter CRON_SECRET dans les variables d\'environnement Vercel',
    command: 'vercel env add CRON_SECRET'
  },
  {
    issue: 'Cron jobs bloqués par l\'authentification Vercel',
    solution: 'Vérifier que les routes sont accessibles publiquement',
    check: 'Tester avec curl -X POST /api/cron/generate-recommendations'
  },
  {
    issue: 'Erreurs dans les logs Vercel',
    solution: 'Vérifier les logs avec vercel logs <deployment-url>',
    command: 'vercel logs https://stream2spin-6mpsdda8p-lecoutels-projects.vercel.app'
  },
  {
    issue: 'Cron jobs ne s\'exécutent pas aux heures prévues',
    solution: 'Vérifier la syntaxe cron dans vercel.json',
    check: 'Schedule actuel: "30 0 * * 4" (jeudi 00:30)'
  },
  {
    issue: 'Erreurs de base de données dans les cron jobs',
    solution: 'Vérifier DATABASE_URL et les permissions',
    check: 'Tester la connexion DB dans les cron jobs'
  }
];

commonIssues.forEach((issue, index) => {
  if (process.env.NODE_ENV === 'development') {
  console.log(`\n${index + 1}. ${issue.issue}`);
  }
  console.log(`   Solution: ${issue.solution}`);
  if (issue.command) {
    if (process.env.NODE_ENV === 'development') {
    console.log(`   Commande: ${issue.command}`);
    }
  }
  if (issue.check) {
    if (process.env.NODE_ENV === 'development') {
    console.log(`   Vérification: ${issue.check}`);
    }
  }
});

// 6. Recommandations
if (process.env.NODE_ENV === 'development') {
console.log('\n🎯 6. Recommandations');
}
console.log('---------------------');

const recommendations = [
  '✅ Tester les cron jobs localement avec npm run test:cron',
  '✅ Vérifier les logs Vercel pour les erreurs',
  '✅ S\'assurer que CRON_SECRET est identique en local et en production',
  '✅ Tester manuellement les endpoints avec curl',
  '✅ Vérifier que les utilisateurs ont des comptes Spotify connectés',
  '✅ Contrôler que la base de données est accessible',
  '✅ Vérifier les permissions des APIs externes (Spotify, Discogs)'
];

recommendations.forEach((rec, index) => {
  if (process.env.NODE_ENV === 'development') {
  console.log(`${index + 1}. ${rec}`);
  }
});

if (process.env.NODE_ENV === 'development') {
console.log('\n✅ Diagnostic terminé');
}
console.log('\n📝 Prochaines étapes:');
if (process.env.NODE_ENV === 'development') {
console.log('1. Exécuter: npm run test:cron');
}
console.log('2. Vérifier les logs Vercel');
if (process.env.NODE_ENV === 'development') {
console.log('3. Tester manuellement les endpoints');
}
console.log('4. Vérifier les variables d\'environnement'); 