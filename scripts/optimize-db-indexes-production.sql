-- 🚀 Script d'optimisation des index pour PRODUCTION
-- À exécuter dans Supabase SQL Editor (PRODUCTION)
-- ⚠️ ATTENTION : Vérifier que vous êtes sur la base PRODUCTION

-- Vérification de sécurité : afficher le nom de la base
SELECT current_database() as database_name, 
       current_user as user_name,
       version() as postgres_version;

-- 1. Index optimisé pour la table users (requêtes de session)
-- Améliore les performances des requêtes d'authentification
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_id_optimized_prod 
ON users (id) 
INCLUDE (name, email, image);

-- 2. Index pour les requêtes de recommandations
-- Optimise les filtres par utilisateur et timeframe
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_recommendations_user_timeframe_prod 
ON recommendations ("userId", timeframe, "generatedAt" DESC);

-- 3. Index pour la collection Discogs
-- Accélère les vérifications de possession d'albums
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_discogs_collection_user_prod 
ON user_discogs_collection ("userId", "discogsReleaseId");

-- 4. Index pour la wishlist
-- Optimise l'affichage de la wishlist par utilisateur
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_wishlist_user_album_prod 
ON wishlist_items ("userId", "createdAt" DESC);

-- 5. Index pour les comptes (accounts table)
-- Améliore les requêtes de connexion par provider
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_accounts_provider_user_prod 
ON accounts (provider, "userId");

-- 6. Index pour les sessions NextAuth
-- Optimise la gestion des sessions utilisateur
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_sessions_user_expires_prod 
ON sessions ("userId", expires);

-- 7. Index pour les requêtes de recommandations par album
-- Optimise les recherches d'albums spécifiques
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_recommendations_album_prod 
ON recommendations ("albumId", "userId");

-- 8. Index pour les statistiques utilisateur
-- Améliore les requêtes de comptage et statistiques
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_created_at_prod 
ON users ("createdAt" DESC);

-- 9. Statistiques pour l'optimiseur de requêtes
-- Met à jour les statistiques pour de meilleures performances
ANALYZE users;
ANALYZE recommendations;
ANALYZE user_discogs_collection;
ANALYZE wishlist_items;
ANALYZE accounts;
ANALYZE sessions;

-- 10. Vérifier les index créés
SELECT 
    schemaname,
    tablename,
    indexname,
    indexdef
FROM pg_indexes 
WHERE tablename IN ('users', 'recommendations', 'user_discogs_collection', 'wishlist_items', 'accounts', 'sessions')
  AND indexname LIKE '%_prod'
ORDER BY tablename, indexname;

-- 11. Vérifier les performances avec EXPLAIN
-- Test de performance sur une requête utilisateur critique
EXPLAIN (ANALYZE, BUFFERS) 
SELECT id, name, email, image 
FROM users 
WHERE id = (SELECT "userId" FROM recommendations LIMIT 1);

-- 12. Statistiques finales
SELECT 
    schemaname,
    tablename,
    n_tup_ins as inserts,
    n_tup_upd as updates,
    n_tup_del as deletes,
    n_live_tup as live_rows,
    last_analyze
FROM pg_stat_user_tables 
WHERE tablename IN ('users', 'recommendations', 'user_discogs_collection', 'wishlist_items', 'accounts', 'sessions')
ORDER BY tablename;

-- ✅ Script d'optimisation PRODUCTION terminé
-- Les index sont créés avec CONCURRENTLY pour éviter les blocages
-- Surveiller les performances après exécution
