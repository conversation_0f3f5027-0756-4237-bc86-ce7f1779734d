#!/usr/bin/env node

/**
 * Script simple pour tester l'API multilingue
 */

// Charger les variables d'environnement
require('dotenv').config({ path: '.env.local' });

// Configuration
const TARGET_EMAIL = '<EMAIL>';
const TARGET_NAME = '<PERSON> G<PERSON>lle';
const LANGUAGES = ['fr', 'en'];

// Fonction pour envoyer un email via l'API
async function sendEmailViaAPI(lang) {
  const url = 'http://localhost:3000/api/admin/send-test-emails';
  
  console.log(`📧 Envoi de l'email de test en ${lang}...`);
  
  try {
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${process.env.CRON_SECRET}`
      },
      body: JSON.stringify({
        email: TARGET_EMAIL,
        name: TARGET_NAME,
        language: lang
      })
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const result = await response.json();
    return result;
  } catch (error) {
    throw new Error(`Erreur API pour ${lang}: ${error.message}`);
  }
}

// Fonction pour vérifier que le serveur Next.js est en cours d'exécution
async function checkServer() {
  try {
    const response = await fetch('http://localhost:3000/api/auth/providers', {
      method: 'GET',
      signal: AbortSignal.timeout(5000)
    });
    
    return response.ok;
  } catch (error) {
    return false;
  }
}

// Fonction principale
async function main() {
  console.log('🚀 Test des emails multilingues Stream2Spin');
  console.log(`📧 Destination: ${TARGET_EMAIL}`);
  console.log(`👤 Nom: ${TARGET_NAME}`);
  console.log(`🌍 Langues: ${LANGUAGES.join(', ')}`);
  console.log();
  
  // Vérifier que le serveur Next.js est en cours d'exécution
  console.log('ℹ️  Vérification du serveur Next.js...');
  const isServerRunning = await checkServer();
  
  if (!isServerRunning) {
    console.log('❌ Le serveur Next.js n\'est pas en cours d\'exécution');
    console.log('ℹ️  Lancez d\'abord "pnpm dev" dans un autre terminal');
    process.exit(1);
  }
  
  console.log('✅ Serveur Next.js détecté');
  console.log();
  
  const results = {};
  
  // Envoyer les emails dans chaque langue
  for (const lang of LANGUAGES) {
    console.log(`🌍 === ENVOI EN ${lang.toUpperCase()} ===`);
    
    try {
      const result = await sendEmailViaAPI(lang);
      
      console.log(`✅ Emails envoyés avec succès en ${lang} !`);
      
      const details = result.results ? result.results.details : null;
      
      if (details && details.welcome) {
        if (details.welcome.success) {
          console.log(`✅ Email de bienvenue envoyé ! ID: ${details.welcome.messageId}`);
        } else {
          console.log(`❌ Erreur email de bienvenue: ${details.welcome.error}`);
        }
      }
      
      if (details && details.recommendations) {
        if (details.recommendations.success) {
          console.log(`✅ Email de recommandations envoyé ! ID: ${details.recommendations.messageId}`);
          console.log(`📊 Recommandations: ${details.recommendations.recommendationsCount}`);
        } else {
          console.log(`❌ Erreur email de recommandations: ${details.recommendations.error}`);
        }
      }
      
      results[lang] = {
        success: true,
        welcome: details ? details.welcome : null,
        recommendations: details ? details.recommendations : null
      };
      
    } catch (error) {
      console.log(`❌ Erreur pour ${lang}: ${error.message}`);
      results[lang] = {
        success: false,
        error: error.message
      };
    }
    
    console.log();
    
    // Attendre un peu entre les langues
    if (lang !== LANGUAGES[LANGUAGES.length - 1]) {
      console.log('⏳ Attente de 3 secondes...');
      await new Promise(resolve => setTimeout(resolve, 3000));
    }
  }
  
  // Résumé final
  console.log('📊 === RÉSUMÉ FINAL ===');
  console.log(`👤 Utilisateur: ${TARGET_NAME} (${TARGET_EMAIL})`);
  console.log();
  
  for (const lang of LANGUAGES) {
    const result = results[lang];
    console.log(`🌍 ${lang.toUpperCase()}:`);
    
    if (result.success) {
      console.log(`  Welcome: ${result.welcome.success ? '✅' : '❌'} ${result.welcome.messageId || result.welcome.error}`);
      console.log(`  Recommendations: ${result.recommendations.success ? '✅' : '❌'} ${result.recommendations.messageId || result.recommendations.error}`);
    } else {
      console.log(`  Erreur: ❌ ${result.error}`);
    }
  }
  
  console.log();
  console.log('🎉 Processus terminé ! Vérifiez votre boîte mail.');
  console.log(`📧 Emails envoyés à: ${TARGET_EMAIL}`);
  console.log('ℹ️  Tous les emails utilisent maintenant les templates React avec localisation complète.');
}

// Exécuter le script
main().catch(error => {
  console.error('❌ Erreur fatale:', error);
  process.exit(1);
}); 