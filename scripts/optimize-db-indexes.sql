-- 🚀 Script d'optimisation des index pour améliorer les performances DB
-- À exécuter dans Supabase SQL Editor

-- 1. Index optimisé pour la table users (requêtes de session)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_id_optimized 
ON users (id) 
INCLUDE (name, email, image);

-- 2. Index pour les requêtes de recommandations
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_recommendations_user_timeframe 
ON recommendations (user_id, timeframe, created_at DESC);

-- 3. Index pour la collection Discogs
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_discogs_collection_user 
ON discogs_collection (user_id, master_id);

-- 4. Index pour la wishlist
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_wishlist_user_album 
ON wishlist (user_id, album_id, created_at DESC);

-- 5. Statistiques pour l'optimiseur de requêtes
ANALYZE users;
ANALYZE recommendations;
ANALYZE discogs_collection;
ANALYZE wishlist;

-- 6. Vérifier les index existants
SELECT 
    schemaname,
    tablename,
    indexname,
    indexdef
FROM pg_indexes 
WHERE tablename IN ('users', 'recommendations', 'discogs_collection', 'wishlist')
ORDER BY tablename, indexname;
