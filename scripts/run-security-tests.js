#!/usr/bin/env node

/**
 * Suite de tests de sécurité complète
 * Vérifie tous les aspects de sécurité avant déploiement production
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Configuration des tests
const TEST_CONFIG = {
  stagingUrl: 'https://stream2spin-staging.vercel.app',
  adminUrl: 'https://admin-staging.stream2spin.com',
  timeout: 30000,
  retries: 3
};

// Routes à tester (doivent retourner 404)
const BLOCKED_ROUTES = [
  '/api/public/user-status',
  '/api/public/reset-email-flag', 
  '/api/test/rakuten-token-public',
  '/api/test/nextauth-debug',
  '/api/debug/discogs-status',
  '/debug-email',
  '/test-email',
  '/test-spotify-embed'
];

// Routes normales (doivent fonctionner)
const NORMAL_ROUTES = [
  { path: '/', expectedStatus: [200, 302] }, // Peut rediriger
  { path: '/login', expectedStatus: [200] },
  { path: '/api/auth/providers', expectedStatus: [200] }
];

let testResults = {
  timestamp: new Date().toISOString(),
  passed: 0,
  failed: 0,
  total: 0,
  details: []
};

async function makeRequest(url, options = {}) {
  const fetch = (await import('node-fetch')).default;
  
  try {
    const response = await fetch(url, {
      timeout: TEST_CONFIG.timeout,
      ...options
    });
    
    return {
      status: response.status,
      ok: response.ok,
      headers: response.headers.raw()
    };
  } catch (error) {
    return {
      status: 0,
      ok: false,
      error: error.message
    };
  }
}

async function testRoute(baseUrl, route, expectedStatuses, description) {
  const url = `${baseUrl}${route}`;
  testResults.total++;
  
  console.log(`   Testing: ${route}`);
  
  try {
    const response = await makeRequest(url);
    const actualStatus = response.status;
    
    const isExpected = Array.isArray(expectedStatuses) 
      ? expectedStatuses.includes(actualStatus)
      : actualStatus === expectedStatuses;
    
    if (isExpected) {
      console.log(`   ✅ ${description}: ${actualStatus}`);
      testResults.passed++;
      testResults.details.push({
        route,
        status: 'PASS',
        expected: expectedStatuses,
        actual: actualStatus,
        description
      });
      return true;
    } else {
      console.log(`   ❌ ${description}: Expected ${expectedStatuses}, got ${actualStatus}`);
      testResults.failed++;
      testResults.details.push({
        route,
        status: 'FAIL',
        expected: expectedStatuses,
        actual: actualStatus,
        description,
        error: response.error
      });
      return false;
    }
  } catch (error) {
    console.log(`   ❌ ${description}: Error - ${error.message}`);
    testResults.failed++;
    testResults.details.push({
      route,
      status: 'ERROR',
      expected: expectedStatuses,
      actual: 'ERROR',
      description,
      error: error.message
    });
    return false;
  }
}

async function testSecurityHeaders(baseUrl) {
  console.log('\n🔒 Test des headers de sécurité:');
  
  const response = await makeRequest(baseUrl);
  const headers = response.headers;
  
  const securityHeaders = [
    { name: 'x-frame-options', required: true },
    { name: 'x-content-type-options', required: true },
    { name: 'referrer-policy', required: false },
    { name: 'content-security-policy', required: false }
  ];
  
  securityHeaders.forEach(({ name, required }) => {
    const headerValue = headers[name];
    if (headerValue) {
      console.log(`   ✅ ${name}: ${headerValue}`);
      testResults.passed++;
    } else if (required) {
      console.log(`   ❌ ${name}: Missing (required)`);
      testResults.failed++;
    } else {
      console.log(`   ⚠️ ${name}: Missing (optional)`);
    }
    testResults.total++;
  });
}

async function testAdminPlatform() {
  console.log('\n🛡️ Test de la plateforme admin:');
  
  // Test d'accès sans authentification (doit rediriger vers login)
  const response = await makeRequest(`${TEST_CONFIG.adminUrl}/dashboard`);
  
  if (response.status === 302 || response.status === 401 || response.status === 403) {
    console.log(`   ✅ Dashboard admin protégé: ${response.status}`);
    testResults.passed++;
  } else {
    console.log(`   ❌ Dashboard admin non protégé: ${response.status}`);
    testResults.failed++;
  }
  testResults.total++;
  
  // Test de la page de login admin
  const loginResponse = await makeRequest(`${TEST_CONFIG.adminUrl}/auth/login`);
  
  if (loginResponse.status === 200) {
    console.log(`   ✅ Page login admin accessible: ${loginResponse.status}`);
    testResults.passed++;
  } else {
    console.log(`   ❌ Page login admin inaccessible: ${loginResponse.status}`);
    testResults.failed++;
  }
  testResults.total++;
}

function runStaticAnalysis() {
  console.log('\n🔍 Analyse statique du code:');
  
  try {
    // Exécuter le script de validation
    execSync('node scripts/validate-production-ready.js', { stdio: 'pipe' });
    console.log('   ✅ Validation statique réussie');
    testResults.passed++;
  } catch (error) {
    console.log('   ❌ Validation statique échouée');
    console.log(`   Error: ${error.message}`);
    testResults.failed++;
  }
  testResults.total++;
}

function checkEnvironmentSecurity() {
  console.log('\n🔧 Vérification de la configuration:');
  
  // Vérifier les variables d'environnement sensibles
  const sensitiveVars = [
    'NEXTAUTH_SECRET',
    'DATABASE_URL',
    'RESEND_API_KEY',
    'SPOTIFY_CLIENT_SECRET'
  ];
  
  sensitiveVars.forEach(varName => {
    if (process.env[varName]) {
      console.log(`   ✅ ${varName}: Configuré`);
      testResults.passed++;
    } else {
      console.log(`   ⚠️ ${varName}: Non configuré`);
    }
    testResults.total++;
  });
  
  // Vérifier que DEBUG n'est pas activé
  if (process.env.ENABLE_DEBUG_ROUTES === 'true') {
    console.log('   ❌ ENABLE_DEBUG_ROUTES activé en production !');
    testResults.failed++;
  } else {
    console.log('   ✅ ENABLE_DEBUG_ROUTES désactivé');
    testResults.passed++;
  }
  testResults.total++;
}

function generateReport() {
  const report = {
    ...testResults,
    summary: {
      successRate: ((testResults.passed / testResults.total) * 100).toFixed(2),
      securityLevel: testResults.failed === 0 ? 'SECURE' : testResults.failed <= 2 ? 'WARNING' : 'CRITICAL',
      productionReady: testResults.failed === 0
    },
    recommendations: []
  };
  
  // Ajouter des recommandations basées sur les échecs
  if (testResults.failed > 0) {
    report.recommendations.push('Corriger tous les tests échoués avant déploiement production');
  }
  
  if (testResults.details.some(d => d.route.includes('/api/public/'))) {
    report.recommendations.push('Vérifier que toutes les routes publiques sont supprimées');
  }
  
  // Sauvegarder le rapport
  fs.writeFileSync('security-test-report.json', JSON.stringify(report, null, 2));
  
  return report;
}

async function main() {
  console.log('🔐 Suite de Tests de Sécurité - Stream2Spin');
  console.log('==========================================\n');
  
  // Test 1: Analyse statique
  runStaticAnalysis();
  
  // Test 2: Configuration d'environnement
  checkEnvironmentSecurity();
  
  // Test 3: Routes bloquées
  console.log('\n🚫 Test des routes bloquées (doivent retourner 404):');
  for (const route of BLOCKED_ROUTES) {
    await testRoute(TEST_CONFIG.stagingUrl, route, 404, 'Route bloquée');
  }
  
  // Test 4: Routes normales
  console.log('\n✅ Test des routes normales (doivent fonctionner):');
  for (const { path, expectedStatus } of NORMAL_ROUTES) {
    await testRoute(TEST_CONFIG.stagingUrl, path, expectedStatus, 'Route normale');
  }
  
  // Test 5: Headers de sécurité
  await testSecurityHeaders(TEST_CONFIG.stagingUrl);
  
  // Test 6: Plateforme admin
  await testAdminPlatform();
  
  // Générer le rapport
  const report = generateReport();
  
  // Afficher les résultats
  console.log('\n📊 RÉSULTATS DES TESTS:');
  console.log(`   • Total: ${testResults.total} tests`);
  console.log(`   • Réussis: ${testResults.passed}`);
  console.log(`   • Échoués: ${testResults.failed}`);
  console.log(`   • Taux de réussite: ${report.summary.successRate}%`);
  console.log(`   • Niveau de sécurité: ${report.summary.securityLevel}`);
  
  if (report.summary.productionReady) {
    console.log('\n🎉 ✅ APPLICATION PRÊTE POUR LA PRODUCTION !');
    console.log('   • Tous les tests de sécurité réussis');
    console.log('   • Aucune vulnérabilité détectée');
    console.log('   • Configuration sécurisée');
  } else {
    console.log('\n⚠️ ❌ CORRECTIONS REQUISES AVANT PRODUCTION');
    console.log('   • Des tests de sécurité ont échoué');
    console.log('   • Vérifiez le rapport détaillé');
    
    if (report.recommendations.length > 0) {
      console.log('\n📝 Recommandations:');
      report.recommendations.forEach(rec => {
        console.log(`   • ${rec}`);
      });
    }
  }
  
  console.log(`\n📋 Rapport détaillé: security-test-report.json`);
  
  // Code de sortie
  process.exit(report.summary.productionReady ? 0 : 1);
}

if (require.main === module) {
  main().catch(error => {
    console.error('❌ Erreur lors des tests:', error);
    process.exit(1);
  });
}

module.exports = { testRoute, testSecurityHeaders, makeRequest };
