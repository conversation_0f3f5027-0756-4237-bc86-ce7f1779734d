#!/usr/bin/env node

/**
 * Script simple pour envoyer des emails de test via l'API Next.js
 * Utilise les vrais templates React dans l'environnement Next.js
 */

// Charger les variables d'environnement
require('dotenv').config({ path: '.env.local' });

const http = require('http');
const https = require('https');

// Configuration
const TEST_EMAIL = '<EMAIL>';
const TEST_USER_NAME = 'Simon Gavelle';
const SERVER_URL = 'http://localhost:3000';

// Couleurs pour la console
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(message, color = 'reset') {
  const timestamp = new Date().toISOString();
  console.log(`${colors[color]}[${timestamp}] ${message}${colors.reset}`);
}

function logInfo(message) {
  log(`ℹ️  ${message}`, 'blue');
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

/**
 * Fait une requête HTTP
 */
function makeRequest(options, postData = null) {
  return new Promise((resolve, reject) => {
    const client = options.protocol === 'https:' ? https : http;
    
    const req = client.request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          const parsedData = data ? JSON.parse(data) : {};
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            body: parsedData
          });
        } catch (e) {
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            body: data
          });
        }
      });
    });
    
    req.on('error', (err) => {
      reject(err);
    });
    
    if (postData) {
      req.write(postData);
    }
    
    req.end();
  });
}

/**
 * Vérifie si le serveur Next.js est en cours d'exécution
 */
async function checkServer() {
  logInfo('Vérification du serveur Next.js...');
  
  try {
    const response = await makeRequest({
      hostname: 'localhost',
      port: 3000,
      path: '/api/auth/providers',
      method: 'GET',
      timeout: 3000
    });
    
    if (response.statusCode === 200) {
      logSuccess('Serveur Next.js en cours d\'exécution');
      return true;
    } else {
      logWarning(`Serveur répond avec le code ${response.statusCode}`);
      return false;
    }
  } catch (error) {
    logError('Serveur Next.js non disponible');
    logWarning('Veuillez démarrer le serveur avec: npm run dev');
    return false;
  }
}

/**
 * Envoie les emails de test via l'API
 */
async function sendTestEmails() {
  logInfo('Envoi des emails de test via l\'API...');
  
  const requestData = {
    email: TEST_EMAIL,
    name: TEST_USER_NAME
  };
  
  const postData = JSON.stringify(requestData);
  
  try {
    const response = await makeRequest({
      hostname: 'localhost',
      port: 3000,
      path: '/api/admin/send-test-emails',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(postData),
        'Authorization': `Bearer ${process.env.CRON_SECRET || 'test-secret'}`
      }
    }, postData);
    
    if (response.statusCode === 200 && response.body.success) {
      logSuccess('Emails de test envoyés avec succès !');
      
      // Afficher les détails
      const { results } = response.body;
      
      if (results.welcome && results.details.welcome.success) {
        logSuccess(`Email de bienvenue envoyé ! ID: ${results.details.welcome.messageId}`);
      } else {
        logError(`Email de bienvenue échoué: ${results.details.welcome?.error || 'Unknown error'}`);
      }
      
      if (results.recommendations && results.details.recommendations.success) {
        logSuccess(`Email de recommandations envoyé ! ID: ${results.details.recommendations.messageId}`);
        logInfo(`Nombre de recommandations: ${results.details.recommendations.recommendationsCount}`);
      } else {
        logError(`Email de recommandations échoué: ${results.details.recommendations?.error || 'Unknown error'}`);
      }
      
      return true;
    } else {
      logError(`Erreur API: ${response.body.error || response.body.message || 'Unknown error'}`);
      console.log('Réponse complète:', JSON.stringify(response.body, null, 2));
      return false;
    }
  } catch (error) {
    logError(`Erreur lors de l'appel API: ${error.message}`);
    return false;
  }
}

/**
 * Fonction principale
 */
async function main() {
  log('🚀 Envoi des emails de test Stream2Spin (via API)', 'bold');
  log(`📧 Destination: ${TEST_EMAIL}`, 'blue');
  log(`👤 Nom: ${TEST_USER_NAME}`, 'blue');
  log('', 'reset');
  
  // Vérifier le serveur
  if (!(await checkServer())) {
    logError('Impossible de continuer sans le serveur Next.js');
    process.exit(1);
  }
  
  log('', 'reset');
  
  // Envoyer les emails
  const success = await sendTestEmails();
  
  log('', 'reset');
  
  if (success) {
    logSuccess('🎉 Mission accomplie !');
    logInfo(`📧 Vérifiez votre boîte mail: ${TEST_EMAIL}`);
    logInfo('Les emails utilisent maintenant les vrais templates React avec les styles harmonisés.');
  } else {
    logWarning('❌ Échec de l\'envoi des emails');
  }
  
  process.exit(success ? 0 : 1);
}

// Gestion des erreurs
process.on('unhandledRejection', (error) => {
  logError(`Erreur non gérée: ${error.message}`);
  process.exit(1);
});

// Lancement du script
main().catch(error => {
  logError(`Erreur fatale: ${error.message}`);
  process.exit(1);
}); 