// Test simple de l'API CRON
console.log('🧪 Test simple de l\'API CRON');

fetch('http://localhost:3000/api/cron/send-notifications', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer dev-secret'
  },
  body: JSON.stringify({})
})
.then(response => {
  console.log('Status:', response.status);
  return response.json();
})
.then(data => {
  console.log('Response:', JSON.stringify(data, null, 2));
})
.catch(error => {
  console.error('Error:', error);
});
