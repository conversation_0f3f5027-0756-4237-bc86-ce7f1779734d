#!/usr/bin/env node

/**
 * Script pour envoyer des emails de test à l'adresse <EMAIL>
 */

import { config } from 'dotenv';
import path from 'path';

// Charger les variables d'environnement
config({ path: path.join(process.cwd(), '.env.local') });

// Vérifier que les variables sont chargées
console.log('RESEND_API_KEY:', process.env.RESEND_API_KEY ? 'Définie' : 'Manquante');
console.log('NEXTAUTH_URL:', process.env.NEXTAUTH_URL || 'Manquante');

// Configuration
const TEST_EMAIL = '<EMAIL>';
const TEST_USER_NAME = 'Simon Gavelle';

// Couleurs pour la console
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(message: string, color: keyof typeof colors = 'reset') {
  const timestamp = new Date().toISOString();
  console.log(`${colors[color]}[${timestamp}] ${message}${colors.reset}`);
}

function logInfo(message: string) {
  log(`ℹ️  ${message}`, 'blue');
}

function logSuccess(message: string) {
  log(`✅ ${message}`, 'green');
}

function logError(message: string) {
  log(`❌ ${message}`, 'red');
}

function logWarning(message: string) {
  log(`⚠️  ${message}`, 'yellow');
}

/**
 * Envoie l'email de bienvenue
 */
async function sendWelcomeTestEmail(): Promise<boolean> {
  logInfo(`Envoi de l'email de bienvenue à ${TEST_EMAIL}...`);
  
  try {
    const { sendWelcomeEmail } = await import('../lib/email');
    const result = await sendWelcomeEmail({
      name: TEST_USER_NAME,
      email: TEST_EMAIL
    });

    if (result.success) {
      logSuccess(`Email de bienvenue envoyé ! ID: ${result.messageId}`);
      return true;
    } else {
      logError(`Erreur lors de l'envoi de l'email de bienvenue: ${result.error}`);
      return false;
    }
  } catch (error) {
    logError(`Erreur lors de l'envoi de l'email de bienvenue: ${error instanceof Error ? error.message : String(error)}`);
    return false;
  }
}

/**
 * Envoie l'email de recommandations
 */
async function sendRecommendationsTestEmail(): Promise<boolean> {
  logInfo(`Envoi de l'email de recommandations à ${TEST_EMAIL}...`);
  
  // Données de test pour les recommandations
  const testUser = {
    id: 'test-user-123',
    name: TEST_USER_NAME,
    email: TEST_EMAIL,
    preferredLanguage: 'fr'
  };

  // Recommandations de test
  const testRecommendations = [
    {
      artistName: 'Daft Punk',
      albumTitle: 'Random Access Memories',
      albumCoverUrl: 'https://example.com/cover1.jpg',
      listenScore: 85,
      affiliateLinks: [
        {
          vendor: 'Amazon',
          url: 'https://amazon.fr/daft-punk-ram',
          price: 29.99,
          currency: 'EUR'
        }
      ],
      isOwned: false
    },
    {
      artistName: 'Radiohead',
      albumTitle: 'OK Computer',
      albumCoverUrl: 'https://example.com/cover2.jpg',
      listenScore: 92,
      affiliateLinks: [
        {
          vendor: 'Amazon',
          url: 'https://amazon.fr/radiohead-ok-computer',
          price: 24.99,
          currency: 'EUR'
        }
      ],
      isOwned: false
    },
    {
      artistName: 'Pink Floyd',
      albumTitle: 'The Dark Side of the Moon',
      albumCoverUrl: 'https://example.com/cover3.jpg',
      listenScore: 88,
      affiliateLinks: [
        {
          vendor: 'Amazon',
          url: 'https://amazon.fr/pink-floyd-dark-side',
          price: 32.99,
          currency: 'EUR'
        }
      ],
      isOwned: false
    }
  ];

  try {
    const { sendRecommendationsEmail } = await import('../lib/resend');
    const result = await sendRecommendationsEmail(testUser, testRecommendations);

    if (result.success) {
      logSuccess(`Email de recommandations envoyé ! ID: ${result.messageId}`);
      return true;
    } else {
      logError(`Erreur lors de l'envoi de l'email de recommandations: ${result.error}`);
      return false;
    }
  } catch (error) {
    logError(`Erreur lors de l'envoi de l'email de recommandations: ${error instanceof Error ? error.message : String(error)}`);
    return false;
  }
}

/**
 * Vérifie la configuration
 */
function checkConfiguration(): boolean {
  logInfo('Vérification de la configuration...');
  
  const requiredEnvVars = [
    'RESEND_API_KEY',
    'NEXTAUTH_URL'
  ];

  let isValid = true;
  
  requiredEnvVars.forEach(envVar => {
    if (!process.env[envVar]) {
      logError(`Variable d'environnement manquante: ${envVar}`);
      isValid = false;
    }
  });

  if (isValid) {
    logSuccess('Configuration valide');
    logInfo(`URL de base: ${process.env.NEXTAUTH_URL}`);
    logInfo(`Clé Resend: ${process.env.RESEND_API_KEY ? 'Configurée' : 'Manquante'}`);
  }

  return isValid;
}

/**
 * Fonction principale
 */
async function main() {
  log('🚀 Envoi des emails de test Stream2Spin', 'bold');
  log(`📧 Destination: ${TEST_EMAIL}`, 'blue');
  log('', 'reset');

  // Vérification de la configuration
  if (!checkConfiguration()) {
    logError('Configuration invalide, arrêt du script');
    process.exit(1);
  }

  log('', 'reset');

  const results = {
    welcome: false,
    recommendations: false
  };

  // Envoi de l'email de bienvenue
  logInfo('=== EMAIL DE BIENVENUE ===');
  results.welcome = await sendWelcomeTestEmail();
  
  log('', 'reset');
  
  // Attendre un peu entre les envois
  logInfo('Attente de 2 secondes...');
  await new Promise(resolve => setTimeout(resolve, 2000));
  
  // Envoi de l'email de recommandations
  logInfo('=== EMAIL DE RECOMMANDATIONS ===');
  results.recommendations = await sendRecommendationsTestEmail();
  
  log('', 'reset');

  // Résumé
  logInfo('=== RÉSUMÉ ===');
  const welcomeStatus = results.welcome ? '✅' : '❌';
  const recommendationsStatus = results.recommendations ? '✅' : '❌';
  
  log(`${welcomeStatus} Email de bienvenue`);
  log(`${recommendationsStatus} Email de recommandations`);
  
  const totalSuccess = Object.values(results).filter(Boolean).length;
  const totalEmails = Object.keys(results).length;
  
  log('', 'reset');
  
  if (totalSuccess === totalEmails) {
    logSuccess(`🎉 Tous les emails ont été envoyés avec succès ! (${totalSuccess}/${totalEmails})`);
    logInfo(`📧 Vérifiez votre boîte mail: ${TEST_EMAIL}`);
  } else {
    logWarning(`⚠️  Seulement ${totalSuccess}/${totalEmails} emails envoyés avec succès`);
  }

  process.exit(totalSuccess === totalEmails ? 0 : 1);
}

// Gestion des erreurs
process.on('unhandledRejection', (error) => {
  logError(`Erreur non gérée: ${error instanceof Error ? error.message : String(error)}`);
  process.exit(1);
});

// Lancement du script
main().catch(error => {
  logError(`Erreur fatale: ${error instanceof Error ? error.message : String(error)}`);
  process.exit(1);
}); 