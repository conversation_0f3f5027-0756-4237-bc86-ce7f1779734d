/**
 * Script de test pour le CRON d'envoi d'emails sociaux
 * Teste les deux cas : utilisateur avec/sans followers
 */

const { db } = require('../lib/db');
const { users, followers, recommendations } = require('../lib/db/schema');
const { eq, and, gte, ne, desc, inArray } = require('drizzle-orm');

async function testSocialEmailCron() {
  console.log('🧪 Test du CRON d\'envoi d\'emails sociaux');
  console.log('=====================================\n');

  try {
    // 1. Récupérer tous les utilisateurs avec notifications email activées
    const usersForEmail = await db.query.users.findMany({
      where: and(
        ne(users.emailFrequency, 'never'),
        eq(users.firstRecommendationEmailSent, true)
      ),
      columns: {
        id: true,
        name: true,
        email: true,
        emailFrequency: true,
        preferredLanguage: true
      },
      limit: 5 // Limiter pour les tests
    });

    console.log(`📊 ${usersForEmail.length} utilisateurs avec notifications email activées\n`);

    if (usersForEmail.length === 0) {
      console.log('❌ Aucun utilisateur trouvé pour les tests');
      return;
    }

    // 2. Tester chaque utilisateur
    for (const user of usersForEmail) {
      console.log(`\n👤 Test pour l'utilisateur: ${user.name || user.email}`);
      console.log(`   ID: ${user.id}`);
      console.log(`   Langue: ${user.preferredLanguage}`);
      
      // Vérifier si l'utilisateur suit d'autres personnes
      const followedUsers = await db.query.followers.findMany({
        where: eq(followers.followerId, user.id),
        columns: { followingId: true }
      });

      const hasFollowing = followedUsers.length > 0;
      console.log(`   Suit ${followedUsers.length} utilisateur(s) → ${hasFollowing ? 'Email SOCIAL' : 'Email CLASSIQUE'}`);

      if (hasFollowing) {
        console.log(`   Utilisateurs suivis: ${followedUsers.map(f => f.followingId).join(', ')}`);
      }

      // Calculer les dates pour les recommandations récentes
      const now = new Date();
      const oneWeekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
      
      const personalLimit = hasFollowing ? 5 : 10;

      // Récupérer les recommandations personnelles
      const personalRecommendations = await db.query.recommendations.findMany({
        where: and(
          eq(recommendations.userId, user.id),
          eq(recommendations.timeframe, 'short_term'),
          gte(recommendations.generatedAt, oneWeekAgo)
        ),
        columns: {
          artistName: true,
          albumTitle: true,
          albumCoverUrl: true,
          listenScore: true,
          affiliateLinks: true,
          isOwned: true
        },
        orderBy: [desc(recommendations.listenScore)],
        limit: personalLimit
      });

      console.log(`   📀 ${personalRecommendations.length} recommandations personnelles trouvées`);

      // Récupérer les recommandations de la communauté si applicable
      let communityRecommendations = [];
      if (hasFollowing) {
        const followedUserIds = followedUsers.map(f => f.followingId);
        
        communityRecommendations = await db
          .select({
            artistName: recommendations.artistName,
            albumTitle: recommendations.albumTitle,
            albumCoverUrl: recommendations.albumCoverUrl,
            listenScore: recommendations.listenScore,
            affiliateLinks: recommendations.affiliateLinks,
            isOwned: recommendations.isOwned,
            userName: users.name
          })
          .from(recommendations)
          .innerJoin(users, eq(recommendations.userId, users.id))
          .where(
            and(
              inArray(recommendations.userId, followedUserIds),
              eq(recommendations.timeframe, 'short_term'),
              gte(recommendations.generatedAt, oneWeekAgo),
              eq(users.shareRecommendations, true),
              ne(users.profileVisibility, 'private')
            )
          )
          .orderBy(desc(recommendations.listenScore))
          .limit(5);

        console.log(`   👥 ${communityRecommendations.length} recommandations communautaires trouvées`);
        
        if (communityRecommendations.length > 0) {
          console.log('   Recommandations de la communauté:');
          communityRecommendations.forEach((rec, i) => {
            console.log(`     ${i+1}. ${rec.artistName} - ${rec.albumTitle} (par ${rec.userName}, score: ${rec.listenScore})`);
          });
        }
      }

      const totalRecommendations = personalRecommendations.length + communityRecommendations.length;
      console.log(`   📊 Total: ${totalRecommendations} recommandations`);

      if (totalRecommendations === 0) {
        console.log(`   ⚠️  Aucune recommandation → Email non envoyé`);
        continue;
      }

      // Simuler l'envoi d'email (sans vraiment envoyer)
      console.log(`   📧 Simulation d'envoi d'email:`);
      console.log(`      Type: ${hasFollowing ? 'SOCIAL' : 'CLASSIQUE'}`);
      console.log(`      Recommandations personnelles: ${personalRecommendations.length}`);
      console.log(`      Recommandations communautaires: ${communityRecommendations.length}`);
      console.log(`      Total affiché: ${Math.min(totalRecommendations, 10)}`);

      if (personalRecommendations.length > 0) {
        console.log('      Top 3 recommandations personnelles:');
        personalRecommendations.slice(0, 3).forEach((rec, i) => {
          console.log(`        ${i+1}. ${rec.artistName} - ${rec.albumTitle} (score: ${rec.listenScore})`);
        });
      }

      console.log(`   ✅ Test terminé pour cet utilisateur\n`);
    }

    console.log('\n🎉 Test du CRON terminé avec succès !');
    console.log('\nRésumé:');
    console.log(`- ${usersForEmail.length} utilisateurs testés`);
    console.log('- Logique sociale fonctionnelle');
    console.log('- Recommandations personnelles et communautaires récupérées');
    console.log('- Prêt pour le déploiement !');

  } catch (error) {
    console.error('❌ Erreur lors du test:', error);
    throw error;
  }
}

// Fonction pour tester l'envoi réel d'un email (optionnel)
async function testRealEmailSend(userId) {
  console.log(`\n📧 Test d'envoi réel d'email pour l'utilisateur: ${userId}`);
  
  try {
    // Appeler directement l'API CRON en mode test
    const response = await fetch('http://localhost:3000/api/cron/send-notifications', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${process.env.CRON_SECRET}`
      },
      body: JSON.stringify({ testMode: true, testUserId: userId })
    });

    const result = await response.json();
    console.log('Résultat de l\'envoi:', result);
    
  } catch (error) {
    console.error('❌ Erreur lors de l\'envoi réel:', error);
  }
}

// Exécuter le test
if (require.main === module) {
  testSocialEmailCron()
    .then(() => {
      console.log('\n✅ Script terminé');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ Script échoué:', error);
      process.exit(1);
    });
}

module.exports = { testSocialEmailCron, testRealEmailSend };
