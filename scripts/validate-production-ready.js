#!/usr/bin/env node

/**
 * Script de validation pour vérifier que l'application est prête pour la production
 * Vérifie l'absence de toutes les fonctionnalités de debug
 */

const fs = require('fs');
const path = require('path');

// Patterns dangereux à détecter
const DANGEROUS_PATTERNS = [
  { pattern: /\/api\/public\/user-status/g, description: 'Route API publique user-status' },
  { pattern: /\/api\/public\/reset-email-flag/g, description: 'Route API publique reset-email-flag' },
  { pattern: /\/api\/test\//g, description: 'Routes API de test' },
  { pattern: /\/api\/debug\//g, description: 'Routes API de debug' },
  { pattern: /\/debug-email/g, description: 'Page debug email' },
  { pattern: /\/test-email/g, description: 'Page test email' },
  { pattern: /console\.log\(.*🧪/g, description: 'Console.log de test' },
  { pattern: /console\.log\(.*🔍/g, description: 'Console.log de debug' },
  { pattern: /ENABLE_DEBUG_ROUTES.*true/g, description: 'Debug routes activées' },
  { pattern: /sync-logger/g, description: 'Référence au sync-logger' },
];

// Répertoires à scanner
const DIRECTORIES_TO_SCAN = [
  'app',
  'components', 
  'lib',
  'middleware.ts'
];

// Fichiers à ignorer
const IGNORE_PATTERNS = [
  /node_modules/,
  /\.next/,
  /\.git/,
  /cleanup-debug-routes\.js/,
  /validate-production-ready\.js/,
  /debug-protection\.ts/, // Peut rester car il protège
];

let issues = [];
let scannedFiles = 0;

function shouldIgnoreFile(filePath) {
  return IGNORE_PATTERNS.some(pattern => pattern.test(filePath));
}

function scanFile(filePath) {
  if (shouldIgnoreFile(filePath)) {
    return;
  }

  try {
    const content = fs.readFileSync(filePath, 'utf8');
    scannedFiles++;

    DANGEROUS_PATTERNS.forEach(({ pattern, description }) => {
      const matches = content.match(pattern);
      if (matches) {
        issues.push({
          file: filePath,
          description,
          matches: matches.length,
          pattern: pattern.toString()
        });
      }
    });
  } catch (error) {
    if (error.code !== 'EISDIR') {
      if (process.env.NODE_ENV === 'development') {
      console.warn(`⚠️ Erreur lecture ${filePath}:`, error.message);
      }
    }
  }
}

function scanDirectory(dirPath) {
  if (!fs.existsSync(dirPath)) {
    return;
  }

  const items = fs.readdirSync(dirPath);
  
  for (const item of items) {
    const fullPath = path.join(dirPath, item);
    
    if (shouldIgnoreFile(fullPath)) {
      continue;
    }
    
    const stats = fs.statSync(fullPath);
    
    if (stats.isDirectory()) {
      scanDirectory(fullPath);
    } else if (stats.isFile() && (fullPath.endsWith('.ts') || fullPath.endsWith('.tsx') || fullPath.endsWith('.js') || fullPath.endsWith('.jsx'))) {
      scanFile(fullPath);
    }
  }
}

function checkEnvironmentFiles() {
  const envFiles = ['.env', '.env.local', '.env.production'];
  
  envFiles.forEach(envFile => {
    if (fs.existsSync(envFile)) {
      const content = fs.readFileSync(envFile, 'utf8');
      
      if (content.includes('ENABLE_DEBUG_ROUTES=true')) {
        issues.push({
          file: envFile,
          description: 'Variable ENABLE_DEBUG_ROUTES activée',
          matches: 1,
          pattern: 'ENABLE_DEBUG_ROUTES=true'
        });
      }
    }
  });
}

function checkRouteFiles() {
  const dangerousRoutes = [
    'app/debug-email',
    'app/test-email',
    'app/test-spotify-embed',
    'app/api/public/user-status',
    'app/api/public/reset-email-flag',
    'app/api/test',
    'app/api/debug'
  ];
  
  dangerousRoutes.forEach(route => {
    if (fs.existsSync(route)) {
      // Vérifier si c'est une route debug sécurisée
      if (route === 'app/api/debug') {
        // Vérifier que toutes les routes dans ce dossier sont sécurisées
        const debugRoutes = fs.readdirSync(route, { withFileTypes: true });
        let allSecured = true;
        
        debugRoutes.forEach(item => {
          if (item.isDirectory()) {
            const routeFile = path.join(route, item.name, 'route.ts');
            if (fs.existsSync(routeFile)) {
              const content = fs.readFileSync(routeFile, 'utf8');
              // Vérifier la présence de la sécurisation NODE_ENV
              if (!content.includes('NODE_ENV !== \'development\'') && 
                  !content.includes('NODE_ENV != \'development\'') &&
                  !content.includes('process.env.NODE_ENV !== "development"') &&
                  !content.includes('process.env.NODE_ENV != "development"')) {
                allSecured = false;
              }
            }
          }
        });
        
        if (!allSecured) {
          issues.push({
            file: route,
            description: 'Route de debug non sécurisée (manque la vérification NODE_ENV)',
            matches: 1,
            pattern: 'Sécurisation manquante'
          });
        }
      } else {
        issues.push({
          file: route,
          description: 'Route de debug encore présente',
          matches: 1,
          pattern: 'Existence du fichier/dossier'
        });
      }
    }
  });
}

function generateReport() {
  const report = {
    timestamp: new Date().toISOString(),
    scannedFiles,
    issuesFound: issues.length,
    issues,
    productionReady: issues.length === 0,
    summary: {
      criticalIssues: issues.filter(i => i.description.includes('API publique') || i.description.includes('Route de debug')).length,
      warningIssues: issues.filter(i => i.description.includes('Console.log')).length,
      infoIssues: issues.filter(i => !i.description.includes('API publique') && !i.description.includes('Route de debug') && !i.description.includes('Console.log')).length
    }
  };
  
  // Sauvegarder le rapport
  const reportPath = 'production-readiness-report.json';
  fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
  
  return report;
}

function main() {
  if (process.env.NODE_ENV === 'development') {
  console.log('🔍 Validation de la préparation production...\n');
  }
  
  // Scanner les répertoires
  if (process.env.NODE_ENV === 'development') {
  console.log('📁 Scan des fichiers source...');
  }
  DIRECTORIES_TO_SCAN.forEach(dir => {
    if (fs.existsSync(dir)) {
      const stats = fs.statSync(dir);
      if (stats.isDirectory()) {
        scanDirectory(dir);
      } else {
        scanFile(dir);
      }
    }
  });
  
  // Vérifier les fichiers d'environnement
  if (process.env.NODE_ENV === 'development') {
  console.log('🔧 Vérification des fichiers d\'environnement...');
  }
  checkEnvironmentFiles();
  
  // Vérifier l'existence des routes dangereuses
  if (process.env.NODE_ENV === 'development') {
  console.log('🛣️ Vérification des routes dangereuses...');
  }
  checkRouteFiles();
  
  // Générer le rapport
  const report = generateReport();
  
  // Afficher les résultats
  if (process.env.NODE_ENV === 'development') {
  console.log(`\n📊 Résultats du scan:`);
  }
  console.log(`   • ${scannedFiles} fichiers scannés`);
  if (process.env.NODE_ENV === 'development') {
  console.log(`   • ${issues.length} problèmes détectés`);
  }
  
  if (issues.length === 0) {
    if (process.env.NODE_ENV === 'development') {
    console.log('\n🎉 ✅ APPLICATION PRÊTE POUR LA PRODUCTION !');
    }
    console.log('   • Aucune fonctionnalité de debug détectée');
    if (process.env.NODE_ENV === 'development') {
    console.log('   • Toutes les routes critiques ont été supprimées');
    }
    console.log('   • L\'application est sécurisée');
  } else {
    if (process.env.NODE_ENV === 'development') {
    console.log('\n⚠️ ❌ PROBLÈMES DÉTECTÉS - CORRECTION REQUISE');
    }
    
    // Grouper par criticité
    const critical = issues.filter(i => i.description.includes('API publique') || i.description.includes('Route de debug'));
    const warnings = issues.filter(i => i.description.includes('Console.log'));
    const info = issues.filter(i => !critical.includes(i) && !warnings.includes(i));
    
    if (critical.length > 0) {
      if (process.env.NODE_ENV === 'development') {
      console.log(`\n🚨 CRITIQUE (${critical.length}):`);
      }
      critical.forEach(issue => {
        if (process.env.NODE_ENV === 'development') {
        console.log(`   • ${issue.file}: ${issue.description}`);
        }
      });
    }
    
    if (warnings.length > 0) {
      if (process.env.NODE_ENV === 'development') {
      console.log(`\n⚠️ AVERTISSEMENTS (${warnings.length}):`);
      }
      warnings.forEach(issue => {
        if (process.env.NODE_ENV === 'development') {
        console.log(`   • ${issue.file}: ${issue.description}`);
        }
      });
    }
    
    if (info.length > 0) {
      if (process.env.NODE_ENV === 'development') {
      console.log(`\n💡 INFORMATIONS (${info.length}):`);
      }
      info.forEach(issue => {
        if (process.env.NODE_ENV === 'development') {
        console.log(`   • ${issue.file}: ${issue.description}`);
        }
      });
    }
  }
  
  if (process.env.NODE_ENV === 'development') {
  console.log(`\n📋 Rapport détaillé sauvegardé: production-readiness-report.json`);
  }
  
  // Code de sortie - ne bloquer que sur les problèmes critiques
  const critical = issues.filter(i => i.description.includes('API publique') || i.description.includes('Route de debug'));
  process.exit(critical.length === 0 ? 0 : 1);
}

if (require.main === module) {
  main();
}

module.exports = { scanFile, scanDirectory, checkEnvironmentFiles };
