const { drizzle } = require('drizzle-orm/postgres-js');
const postgres = require('postgres');
const { eq } = require('drizzle-orm');
require('dotenv').config({ path: '.env.local' });

async function resyncCollectionCovers() {
  if (process.env.NODE_ENV === 'development') {
  console.log('🔄 Début de la resynchronisation des couvertures d\'albums');
  }
  
  try {
    // Connexion à la base de données
    const connectionString = process.env.DATABASE_URL;
    if (!connectionString) {
      throw new Error('DATABASE_URL non définie');
    }
    
    const sql = postgres(connectionString);
    const db = drizzle(sql);
    
    // Importer les actions utilisateur
    const { syncDiscogsCollection } = require('../app/actions/user.ts');
    
    // Récupérer tous les utilisateurs avec une collection Discogs
    const usersWithCollection = await sql`
      SELECT DISTINCT u.id, u.name, u.email
      FROM users u
      INNER JOIN accounts a ON a."userId" = u.id
      WHERE a.provider = 'discogs'
      AND a.access_token IS NOT NULL
      AND a.access_token_secret IS NOT NULL
    `;
    
    if (process.env.NODE_ENV === 'development') {
    console.log(`📊 ${usersWithCollection.length} utilisateur(s) avec collection Discogs trouvé(s)`);
    }
    
    if (usersWithCollection.length === 0) {
      if (process.env.NODE_ENV === 'development') {
      console.log('📭 Aucun utilisateur avec collection Discogs trouvé');
      }
      await sql.end();
      return;
    }
    
    // Resynchroniser chaque utilisateur
    for (const user of usersWithCollection) {
      if (process.env.NODE_ENV === 'development') {
      console.log(`🔄 Resynchronisation pour ${user.name || user.email} (${user.id})`);
      }
      
      try {
        const result = await syncDiscogsCollection(user.id);
        
        if (result.success) {
          if (process.env.NODE_ENV === 'development') {
          console.log(`✅ Resynchronisation réussie: ${result.syncedCount} albums`);
          }
        } else {
          if (process.env.NODE_ENV === 'development') {
          console.log(`❌ Erreur lors de la resynchronisation: ${result.error}`);
          }
        }
      } catch (error) {
        console.error(`❌ Erreur pour l'utilisateur ${user.id}:`, error.message);
      }
      
      // Attendre 2 secondes entre chaque utilisateur pour respecter les limites API
      await new Promise(resolve => setTimeout(resolve, 2000));
    }
    
    if (process.env.NODE_ENV === 'development') {
    console.log('✅ Resynchronisation terminée pour tous les utilisateurs');
    }
    
    // Fermer la connexion
    await sql.end();
    
  } catch (error) {
    console.error('❌ Erreur lors de la resynchronisation:', error);
    process.exit(1);
  }
}

resyncCollectionCovers();
