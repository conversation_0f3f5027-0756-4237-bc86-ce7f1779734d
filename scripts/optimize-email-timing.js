#!/usr/bin/env node

/**
 * Script d'optimisation des horaires d'envoi d'emails
 * Analyse l'engagement des utilisateurs pour optimiser les créneaux d'envoi
 */

const { db } = require('../lib/db');
const { users, emailEvents } = require('../lib/db/schema');
const { eq, gte, and, sql } = require('drizzle-orm');

// Couleurs pour la console
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(message, color = 'reset') {
  const timestamp = new Date().toISOString();
  if (process.env.NODE_ENV === 'development') {
  console.log(`${colors[color]}[${timestamp}] ${message}${colors.reset}`);
  }
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

function logInfo(message) {
  log(`ℹ️  ${message}`, 'blue');
}

/**
 * Analyse l'engagement par heure de la journée
 */
async function analyzeEngagementByHour() {
  logInfo('Analyse de l\'engagement par heure...');
  
  try {
    // Récupérer les événements d'ouverture et de clic des 30 derniers jours
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const engagementData = await db
      .select({
        hour: sql`EXTRACT(HOUR FROM ${emailEvents.createdAt})`,
        eventType: emailEvents.eventType,
        count: sql`COUNT(*)`
      })
      .from(emailEvents)
      .where(
        and(
          gte(emailEvents.createdAt, thirtyDaysAgo),
          sql`${emailEvents.eventType} IN ('opened', 'clicked')`
        )
      )
      .groupBy(sql`EXTRACT(HOUR FROM ${emailEvents.createdAt})`, emailEvents.eventType)
      .orderBy(sql`EXTRACT(HOUR FROM ${emailEvents.createdAt})`);

    // Organiser les données par heure
    const hourlyStats = {};
    for (let hour = 0; hour < 24; hour++) {
      hourlyStats[hour] = { opens: 0, clicks: 0, total: 0 };
    }

    engagementData.forEach(row => {
      const hour = parseInt(row.hour);
      const count = parseInt(row.count);
      
      if (row.eventType === 'opened') {
        hourlyStats[hour].opens = count;
      } else if (row.eventType === 'clicked') {
        hourlyStats[hour].clicks = count;
      }
      hourlyStats[hour].total += count;
    });

    // Trouver les heures optimales
    const sortedHours = Object.entries(hourlyStats)
      .map(([hour, stats]) => ({
        hour: parseInt(hour),
        ...stats,
        engagementRate: stats.total > 0 ? ((stats.opens + stats.clicks * 2) / stats.total) : 0
      }))
      .sort((a, b) => b.engagementRate - a.engagementRate);

    logInfo('📊 Engagement par heure (top 10):');
    sortedHours.slice(0, 10).forEach((hourData, index) => {
      const time = `${hourData.hour.toString().padStart(2, '0')}:00`;
      log(`${index + 1}. ${time} - Engagement: ${hourData.engagementRate.toFixed(2)}, Total: ${hourData.total}`, 'blue');
    });

    return sortedHours;
  } catch (error) {
    logError(`Erreur lors de l'analyse par heure: ${error.message}`);
    return [];
  }
}

/**
 * Analyse l'engagement par jour de la semaine
 */
async function analyzeEngagementByDay() {
  logInfo('Analyse de l\'engagement par jour de la semaine...');
  
  try {
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const engagementData = await db
      .select({
        dayOfWeek: sql`EXTRACT(DOW FROM ${emailEvents.createdAt})`, // 0=dimanche, 1=lundi, etc.
        eventType: emailEvents.eventType,
        count: sql`COUNT(*)`
      })
      .from(emailEvents)
      .where(
        and(
          gte(emailEvents.createdAt, thirtyDaysAgo),
          sql`${emailEvents.eventType} IN ('opened', 'clicked')`
        )
      )
      .groupBy(sql`EXTRACT(DOW FROM ${emailEvents.createdAt})`, emailEvents.eventType)
      .orderBy(sql`EXTRACT(DOW FROM ${emailEvents.createdAt})`);

    const dayNames = ['Dimanche', 'Lundi', 'Mardi', 'Mercredi', 'Jeudi', 'Vendredi', 'Samedi'];
    const dailyStats = {};
    
    for (let day = 0; day < 7; day++) {
      dailyStats[day] = { opens: 0, clicks: 0, total: 0, name: dayNames[day] };
    }

    engagementData.forEach(row => {
      const day = parseInt(row.dayOfWeek);
      const count = parseInt(row.count);
      
      if (row.eventType === 'opened') {
        dailyStats[day].opens = count;
      } else if (row.eventType === 'clicked') {
        dailyStats[day].clicks = count;
      }
      dailyStats[day].total += count;
    });

    const sortedDays = Object.values(dailyStats)
      .map(dayData => ({
        ...dayData,
        engagementRate: dayData.total > 0 ? ((dayData.opens + dayData.clicks * 2) / dayData.total) : 0
      }))
      .sort((a, b) => b.engagementRate - a.engagementRate);

    logInfo('📊 Engagement par jour de la semaine:');
    sortedDays.forEach((dayData, index) => {
      log(`${index + 1}. ${dayData.name} - Engagement: ${dayData.engagementRate.toFixed(2)}, Total: ${dayData.total}`, 'blue');
    });

    return sortedDays;
  } catch (error) {
    logError(`Erreur lors de l'analyse par jour: ${error.message}`);
    return [];
  }
}

/**
 * Analyse l'engagement par fuseau horaire
 */
async function analyzeEngagementByTimezone() {
  logInfo('Analyse de l\'engagement par fuseau horaire...');
  
  try {
    const usersWithTimezone = await db
      .select({
        timezone: users.timezone,
        count: sql`COUNT(*)`
      })
      .from(users)
      .where(eq(users.emailNotificationsEnabled, true))
      .groupBy(users.timezone)
      .orderBy(sql`COUNT(*) DESC`);

    logInfo('📊 Distribution des utilisateurs par fuseau horaire:');
    usersWithTimezone.forEach((tz, index) => {
      log(`${index + 1}. ${tz.timezone || 'Non défini'}: ${tz.count} utilisateurs`, 'blue');
    });

    return usersWithTimezone;
  } catch (error) {
    logError(`Erreur lors de l'analyse par fuseau: ${error.message}`);
    return [];
  }
}

/**
 * Génère des recommandations d'optimisation
 */
function generateOptimizationRecommendations(hourlyData, dailyData, timezoneData) {
  logInfo('📋 Génération des recommandations d\'optimisation...');
  
  const recommendations = [];

  // Recommandations horaires
  if (hourlyData.length > 0) {
    const bestHours = hourlyData.slice(0, 3);
    const worstHours = hourlyData.slice(-3);
    
    recommendations.push({
      type: 'timing',
      title: 'Horaires optimaux',
      description: `Les meilleures heures d'envoi sont: ${bestHours.map(h => `${h.hour}h`).join(', ')}`,
      action: `Programmer les envois principaux entre ${bestHours[0].hour}h et ${bestHours[2].hour}h`,
      priority: 'high'
    });

    recommendations.push({
      type: 'timing',
      title: 'Horaires à éviter',
      description: `Éviter les envois entre: ${worstHours.map(h => `${h.hour}h`).join(', ')}`,
      action: 'Reprogrammer les envois automatiques en dehors de ces créneaux',
      priority: 'medium'
    });
  }

  // Recommandations par jour
  if (dailyData.length > 0) {
    const bestDays = dailyData.slice(0, 3);
    const worstDays = dailyData.slice(-2);
    
    recommendations.push({
      type: 'scheduling',
      title: 'Jours optimaux',
      description: `Les meilleurs jours sont: ${bestDays.map(d => d.name).join(', ')}`,
      action: 'Concentrer les envois principaux sur ces jours',
      priority: 'high'
    });

    recommendations.push({
      type: 'scheduling',
      title: 'Jours à éviter',
      description: `Engagement faible le: ${worstDays.map(d => d.name).join(', ')}`,
      action: 'Réduire la fréquence d\'envoi ces jours-là',
      priority: 'low'
    });
  }

  // Recommandations par fuseau horaire
  if (timezoneData.length > 1) {
    recommendations.push({
      type: 'localization',
      title: 'Optimisation par fuseau horaire',
      description: `${timezoneData.length} fuseaux horaires détectés`,
      action: 'Implémenter des envois échelonnés selon les fuseaux horaires',
      priority: 'medium'
    });
  }

  // Afficher les recommandations
  logInfo('🎯 Recommandations d\'optimisation:');
  recommendations.forEach((rec, index) => {
    const priorityColor = rec.priority === 'high' ? 'red' : rec.priority === 'medium' ? 'yellow' : 'blue';
    log(`\n${index + 1}. ${rec.title} (${rec.priority.toUpperCase()})`, priorityColor);
    log(`   Description: ${rec.description}`, 'reset');
    log(`   Action: ${rec.action}`, 'reset');
  });

  return recommendations;
}

/**
 * Génère un rapport d'optimisation
 */
function generateOptimizationReport(hourlyData, dailyData, timezoneData, recommendations) {
  const report = {
    timestamp: new Date().toISOString(),
    analysis: {
      hourly: {
        bestHours: hourlyData.slice(0, 3).map(h => ({ hour: h.hour, engagement: h.engagementRate })),
        worstHours: hourlyData.slice(-3).map(h => ({ hour: h.hour, engagement: h.engagementRate }))
      },
      daily: {
        bestDays: dailyData.slice(0, 3).map(d => ({ day: d.name, engagement: d.engagementRate })),
        worstDays: dailyData.slice(-2).map(d => ({ day: d.name, engagement: d.engagementRate }))
      },
      timezones: timezoneData.map(tz => ({ timezone: tz.timezone, users: tz.count }))
    },
    recommendations,
    summary: {
      totalRecommendations: recommendations.length,
      highPriority: recommendations.filter(r => r.priority === 'high').length,
      mediumPriority: recommendations.filter(r => r.priority === 'medium').length,
      lowPriority: recommendations.filter(r => r.priority === 'low').length
    }
  };

  // Sauvegarder le rapport
  const fs = require('fs');
  const reportPath = `./reports/email-timing-optimization-${Date.now()}.json`;
  
  try {
    if (!fs.existsSync('./reports')) {
      fs.mkdirSync('./reports', { recursive: true });
    }
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
    logSuccess(`Rapport sauvegardé: ${reportPath}`);
  } catch (error) {
    logWarning(`Impossible de sauvegarder le rapport: ${error.message}`);
  }

  return report;
}

/**
 * Fonction principale
 */
async function main() {
  log('🚀 Optimisation des horaires d\'envoi d\'emails', 'bold');
  
  try {
    // Analyser l'engagement
    const hourlyData = await analyzeEngagementByHour();
    const dailyData = await analyzeEngagementByDay();
    const timezoneData = await analyzeEngagementByTimezone();
    
    // Générer les recommandations
    const recommendations = generateOptimizationRecommendations(hourlyData, dailyData, timezoneData);
    
    // Générer le rapport
    const report = generateOptimizationReport(hourlyData, dailyData, timezoneData, recommendations);
    
    logSuccess(`✅ Optimisation terminée - ${recommendations.length} recommandations générées`);
    
    // Code de sortie
    process.exit(0);
    
  } catch (error) {
    logError(`💥 Erreur fatale: ${error.message}`);
    process.exit(1);
  }
}

// Gestion des erreurs
process.on('unhandledRejection', (error) => {
  logError(`Erreur non gérée: ${error.message}`);
  process.exit(1);
});

// Exécution
if (require.main === module) {
  main();
}
