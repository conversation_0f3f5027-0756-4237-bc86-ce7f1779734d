/**
 * Script pour envoyer des emails dans les deux langues avec les vraies données utilisateur
 * Utilise la localisation complète avec les fonctions de traduction
 */

// Charger les variables d'environnement
require('dotenv').config({ path: '.env.local' });

import { db } from '../lib/db';
import { users, recommendations, accounts } from '../lib/db/schema';
import { eq, and, desc } from 'drizzle-orm';
import { render } from '@react-email/render';
import { Resend } from 'resend';
import { readFileSync } from 'fs';
import { join } from 'path';

// Configuration
const TARGET_EMAIL = '<EMAIL>';
const LANGUAGES = ['fr', 'en'] as const;

// Initialiser Resend
const resend = new Resend(process.env.RESEND_API_KEY);

// Configuration des emails
const EMAIL_CONFIG = {
  from: process.env.RESEND_FROM_EMAIL || 'Stream2Spin <<EMAIL>>',
  replyTo: process.env.RESEND_REPLY_TO || '<EMAIL>',
};

type Language = typeof LANGUAGES[number];
type TranslationMessages = Record<string, any>;

/**
 * Charge les messages de traduction
 */
async function loadTranslations(): Promise<Record<Language, TranslationMessages>> {
  const translations: Record<Language, TranslationMessages> = {} as any;
  
  for (const lang of LANGUAGES) {
    const messagesPath = join(__dirname, '..', 'messages', `${lang}.json`);
    const messagesContent = readFileSync(messagesPath, 'utf-8');
    translations[lang] = JSON.parse(messagesContent);
  }
  
  return translations;
}

/**
 * Crée une fonction de traduction pour une langue donnée
 */
function createTranslator(messages: TranslationMessages, lang: Language) {
  return function t(key: string, values: Record<string, any> = {}): string {
    const keys = key.split('.');
    let translation: any = messages;
    
    for (const k of keys) {
      if (translation && typeof translation === 'object') {
        translation = translation[k];
      } else {
        return key; // Retourner la clé si traduction non trouvée
      }
    }
    
    if (typeof translation !== 'string') {
      return key;
    }
    
    // Remplacer les variables simples {name}
    let result = translation;
    for (const [varName, varValue] of Object.entries(values)) {
      result = result.replace(new RegExp(`{${varName}}`, 'g'), String(varValue));
    }
    
    // Gestion simplifiée des pluriels pour totalCount
    if (result.includes('plural')) {
      const totalCount = values.totalCount || 1;
      if (totalCount === 1) {
        result = result.replace(/{totalCount, plural, =1 \{([^}]+)\} other \{([^}]+)\}}/g, '$1');
      } else {
        result = result.replace(/{totalCount, plural, =1 \{([^}]+)\} other \{([^}]+)\}}/g, '$2');
      }
      result = result.replace(/{totalCount}/g, String(totalCount));
    }
    
    return result;
  };
}

/**
 * Récupère les données utilisateur réelles
 */
async function getUserData() {
  console.log(`🔍 Recherche de l'utilisateur ${TARGET_EMAIL}...`);
  
  const user = await db.query.users.findFirst({
    where: eq(users.email, TARGET_EMAIL),
    columns: {
      id: true,
      name: true,
      email: true,
      preferredLanguage: true,
      emailFrequency: true,
      image: true,
    }
  });
  
  if (!user) {
    throw new Error(`Utilisateur ${TARGET_EMAIL} non trouvé`);
  }
  
  console.log(`✅ Utilisateur trouvé: ${user.name} (${user.id})`);
  console.log(`🗣️  Langue préférée: ${user.preferredLanguage}`);
  
  return user;
}

/**
 * Récupère les recommandations réelles de l'utilisateur
 */
async function getUserRecommendations(userId: string) {
  console.log(`🎵 Récupération des recommandations pour l'utilisateur ${userId}...`);
  
  const userRecommendations = await db.query.recommendations.findMany({
    where: and(
      eq(recommendations.userId, userId),
      eq(recommendations.timeframe, 'short_term'),
      eq(recommendations.isOwned, false)
    ),
    orderBy: [desc(recommendations.listenScore)],
    limit: 10,
    columns: {
      artistName: true,
      albumTitle: true,
      albumCoverUrl: true,
      listenScore: true,
      affiliateLinks: true,
      isOwned: true,
      topTrackName: true,
      topTrackId: true,
    }
  });
  
  console.log(`📊 ${userRecommendations.length} recommandations trouvées`);
  
  // Filtrer celles avec des liens d'achat
  const recommendationsWithLinks = userRecommendations.filter(rec => 
    rec.affiliateLinks && 
    Array.isArray(rec.affiliateLinks) && 
    rec.affiliateLinks.length > 0
  );
  
  console.log(`💰 ${recommendationsWithLinks.length} recommandations avec liens d'achat`);
  
  return recommendationsWithLinks.slice(0, 5); // Limiter à 5 pour l'email
}

/**
 * Vérifie si l'utilisateur a un compte Discogs connecté
 */
async function checkDiscogsConnection(userId: string) {
  const discogsAccount = await db.query.accounts.findFirst({
    where: and(
      eq(accounts.userId, userId),
      eq(accounts.provider, 'discogs')
    )
  });
  
  return !!discogsAccount;
}

/**
 * Envoie l'email de bienvenue dans une langue donnée
 */
async function sendWelcomeEmailInLanguage(user: any, t: (key: string, values?: Record<string, any>) => string, lang: Language) {
  console.log(`📧 Envoi de l'email de bienvenue en ${lang}...`);
  
  const { WelcomeEmail } = await import('../emails/welcome-email');
  
  const emailHtml = await render(
    WelcomeEmail({
      name: user.name,
      userEmail: user.email,
      t
    })
  );
  
  const subject = t('emails.welcome.preview');
  
  const result = await resend.emails.send({
    from: EMAIL_CONFIG.from,
    to: user.email,
    replyTo: EMAIL_CONFIG.replyTo,
    subject: `[${lang.toUpperCase()}] ${subject}`,
    html: emailHtml,
    tags: [
      { name: 'type', value: 'welcome' },
      { name: 'language', value: lang },
      { name: 'test', value: 'multilang' }
    ]
  });
  
  if (result.error) {
    throw new Error(`Erreur Resend (${lang}): ${result.error.message}`);
  }
  
  console.log(`✅ Email de bienvenue envoyé en ${lang} (ID: ${result.data?.id})`);
  return result.data?.id;
}

/**
 * Envoie l'email de recommandations dans une langue donnée
 */
async function sendRecommendationsEmailInLanguage(user: any, userRecommendations: any[], t: (key: string, values?: Record<string, any>) => string, lang: Language) {
  console.log(`📧 Envoi de l'email de recommandations en ${lang}...`);
  
  const { RecommendationsEmailTemplate } = await import('../emails/recommendations-email');
  
  const baseUrl = process.env.NEXTAUTH_URL || 'http://localhost:3000';
  const unsubscribeUrl = `${baseUrl}/account?tab=notifications`;
  const viewRecommendationsUrl = `${baseUrl}/recommendations`;
  
  const emailHtml = await render(
    RecommendationsEmailTemplate({
      user: {
        id: user.id,
        name: user.name,
        email: user.email,
        preferredLanguage: lang
      },
      recommendations: userRecommendations,
      totalCount: userRecommendations.length,
      unsubscribeUrl,
      viewRecommendationsUrl,
      t
    })
  );
  
  const subject = t('emails.recommendations.preview', { totalCount: userRecommendations.length });
  
  const result = await resend.emails.send({
    from: EMAIL_CONFIG.from,
    to: user.email,
    replyTo: EMAIL_CONFIG.replyTo,
    subject: `[${lang.toUpperCase()}] ${subject}`,
    html: emailHtml,
    tags: [
      { name: 'type', value: 'recommendations' },
      { name: 'language', value: lang },
      { name: 'test', value: 'multilang' },
      { name: 'count', value: userRecommendations.length.toString() }
    ]
  });
  
  if (result.error) {
    throw new Error(`Erreur Resend (${lang}): ${result.error.message}`);
  }
  
  console.log(`✅ Email de recommandations envoyé en ${lang} (ID: ${result.data?.id})`);
  return result.data?.id;
}

/**
 * Fonction principale
 */
async function main() {
  try {
    console.log('🚀 Envoi des emails multilingues avec vraies données utilisateur');
    console.log(`📧 Destination: ${TARGET_EMAIL}`);
    console.log(`🌍 Langues: ${LANGUAGES.join(', ')}`);
    console.log();
    
    // Vérifier la configuration
    if (!process.env.RESEND_API_KEY) {
      throw new Error('RESEND_API_KEY manquante');
    }
    
    // Charger les traductions
    console.log('📚 Chargement des traductions...');
    const translations = await loadTranslations();
    console.log('✅ Traductions chargées');
    
    // Récupérer les données utilisateur
    const user = await getUserData();
    
    // Récupérer les recommandations
    const userRecommendations = await getUserRecommendations(user.id);
    
    if (userRecommendations.length === 0) {
      console.log('⚠️  Aucune recommandation avec liens d\'achat trouvée');
      console.log('📧 Envoi uniquement des emails de bienvenue...');
    }
    
    // Vérifier la connexion Discogs
    const hasDiscogs = await checkDiscogsConnection(user.id);
    console.log(`🎵 Compte Discogs connecté: ${hasDiscogs ? 'Oui' : 'Non'}`);
    
    console.log();
    
    const results = {
      welcome: {} as Record<Language, { success: boolean; messageId?: string; error?: string }>,
      recommendations: {} as Record<Language, { success: boolean; messageId?: string; error?: string; reason?: string }>
    };
    
    // Envoyer les emails dans chaque langue
    for (const lang of LANGUAGES) {
      console.log(`🌍 === ENVOI EN ${lang.toUpperCase()} ===`);
      
      // Créer la fonction de traduction pour cette langue
      const t = createTranslator(translations[lang], lang);
      
      try {
        // 1. Email de bienvenue
        const welcomeId = await sendWelcomeEmailInLanguage(user, t, lang);
        results.welcome[lang] = { success: true, messageId: welcomeId };
        
        // Attendre un peu
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // 2. Email de recommandations (si on a des recommandations)
        if (userRecommendations.length > 0) {
          const recId = await sendRecommendationsEmailInLanguage(user, userRecommendations, t, lang);
          results.recommendations[lang] = { success: true, messageId: recId };
        } else {
          results.recommendations[lang] = { success: false, reason: 'Pas de recommandations' };
        }
        
      } catch (error) {
        console.error(`❌ Erreur pour la langue ${lang}:`, error instanceof Error ? error.message : error);
        results.welcome[lang] = { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
        results.recommendations[lang] = { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
      }
      
      console.log();
    }
    
    // Résumé final
    console.log('📊 === RÉSUMÉ FINAL ===');
    console.log(`👤 Utilisateur: ${user.name} (${user.email})`);
    console.log(`🎵 Recommandations: ${userRecommendations.length}`);
    console.log(`🎵 Discogs connecté: ${hasDiscogs ? 'Oui' : 'Non'}`);
    console.log();
    
    for (const lang of LANGUAGES) {
      console.log(`🌍 ${lang.toUpperCase()}:`);
      console.log(`  Welcome: ${results.welcome[lang].success ? '✅' : '❌'} ${results.welcome[lang].messageId || results.welcome[lang].error}`);
      console.log(`  Recommendations: ${results.recommendations[lang].success ? '✅' : '❌'} ${results.recommendations[lang].messageId || results.recommendations[lang].reason || results.recommendations[lang].error}`);
    }
    
    console.log();
    console.log('🎉 Processus terminé ! Vérifiez votre boîte mail.');
    
  } catch (error) {
    console.error('❌ Erreur globale:', error instanceof Error ? error.message : error);
    process.exit(1);
  }
}

// Exécuter le script
if (require.main === module) {
  main();
} 