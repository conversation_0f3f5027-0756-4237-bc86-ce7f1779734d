#!/usr/bin/env node

/**
 * Script de validation de la configuration DNS pour la délivrabilité email
 * Vérifie les enregistrements SPF, DKIM, DMARC pour mails.stream2spin.com
 */

const dns = require('dns').promises;
const https = require('https');

const DOMAIN = 'mails.stream2spin.com';
const MAIN_DOMAIN = 'stream2spin.com';

// Couleurs pour la console
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(message, color = 'reset') {
  if (process.env.NODE_ENV === 'development') {
  console.log(`${colors[color]}${message}${colors.reset}`);
  }
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

function logInfo(message) {
  log(`ℹ️  ${message}`, 'blue');
}

/**
 * Vérifie l'enregistrement SPF
 */
async function checkSPF() {
  logInfo('Vérification de l\'enregistrement SPF...');
  
  try {
    const records = await dns.resolveTxt(DOMAIN);
    const spfRecord = records.find(record => 
      record.join('').startsWith('v=spf1')
    );

    if (!spfRecord) {
      logError('Aucun enregistrement SPF trouvé');
      return false;
    }

    const spfValue = spfRecord.join('');
    logSuccess(`Enregistrement SPF trouvé: ${spfValue}`);

    // Vérifier que Resend est inclus
    if (spfValue.includes('include:_spf.resend.com')) {
      logSuccess('Resend est correctement inclus dans SPF');
    } else {
      logError('Resend n\'est pas inclus dans SPF');
      return false;
    }

    // Vérifier la politique
    if (spfValue.includes('~all') || spfValue.includes('-all')) {
      logSuccess('Politique SPF correcte (~all ou -all)');
    } else {
      logWarning('Politique SPF faible (+all ou ?all)');
    }

    return true;
  } catch (error) {
    logError(`Erreur lors de la vérification SPF: ${error.message}`);
    return false;
  }
}

/**
 * Vérifie l'enregistrement DKIM
 */
async function checkDKIM() {
  logInfo('Vérification de l\'enregistrement DKIM...');
  
  try {
    const dkimDomain = `resend._domainkey.${DOMAIN}`;
    const records = await dns.resolveCname(dkimDomain);

    if (records.length === 0) {
      logError('Aucun enregistrement DKIM trouvé');
      return false;
    }

    const dkimValue = records[0];
    logSuccess(`Enregistrement DKIM trouvé: ${dkimValue}`);

    if (dkimValue === 'resend._domainkey.resend.com') {
      logSuccess('DKIM correctement configuré pour Resend');
      return true;
    } else {
      logError('DKIM ne pointe pas vers Resend');
      return false;
    }
  } catch (error) {
    logError(`Erreur lors de la vérification DKIM: ${error.message}`);
    return false;
  }
}

/**
 * Vérifie l'enregistrement DMARC
 */
async function checkDMARC() {
  logInfo('Vérification de l\'enregistrement DMARC...');
  
  try {
    const dmarcDomain = `_dmarc.${DOMAIN}`;
    const records = await dns.resolveTxt(dmarcDomain);
    const dmarcRecord = records.find(record => 
      record.join('').startsWith('v=DMARC1')
    );

    if (!dmarcRecord) {
      logError('Aucun enregistrement DMARC trouvé');
      return false;
    }

    const dmarcValue = dmarcRecord.join('');
    logSuccess(`Enregistrement DMARC trouvé: ${dmarcValue}`);

    // Analyser la politique
    const policyMatch = dmarcValue.match(/p=([^;]+)/);
    if (policyMatch) {
      const policy = policyMatch[1];
      switch (policy) {
        case 'none':
          logWarning('Politique DMARC en mode monitoring (p=none)');
          break;
        case 'quarantine':
          logSuccess('Politique DMARC en quarantaine (p=quarantine)');
          break;
        case 'reject':
          logSuccess('Politique DMARC stricte (p=reject)');
          break;
        default:
          logWarning(`Politique DMARC inconnue: ${policy}`);
      }
    }

    // Vérifier les adresses de rapport
    if (dmarcValue.includes('rua=')) {
      logSuccess('Adresse de rapport agrégé configurée');
    } else {
      logWarning('Aucune adresse de rapport agrégé (rua)');
    }

    return true;
  } catch (error) {
    logError(`Erreur lors de la vérification DMARC: ${error.message}`);
    return false;
  }
}

/**
 * Vérifie l'enregistrement MX
 */
async function checkMX() {
  logInfo('Vérification de l\'enregistrement MX...');
  
  try {
    const records = await dns.resolveMx(DOMAIN);

    if (records.length === 0) {
      logWarning('Aucun enregistrement MX trouvé (optionnel pour l\'envoi)');
      return true;
    }

    records.forEach(record => {
      logSuccess(`Enregistrement MX: ${record.priority} ${record.exchange}`);
    });

    return true;
  } catch (error) {
    logWarning(`Pas d'enregistrement MX (normal pour un domaine d'envoi uniquement)`);
    return true;
  }
}

/**
 * Teste la délivrabilité avec mail-tester.com
 */
async function testDeliverability() {
  logInfo('Test de délivrabilité recommandé...');
  
  log('Pour tester la délivrabilité:', 'yellow');
  log('1. Visitez https://www.mail-tester.com/', 'yellow');
  log('2. Copiez l\'adresse email fournie', 'yellow');
  log('3. Envoyez un email de test depuis Stream2Spin', 'yellow');
  log('4. Vérifiez le score (objectif: 10/10)', 'yellow');
}

/**
 * Vérifie la configuration Resend
 */
async function checkResendConfig() {
  logInfo('Vérification de la configuration Resend...');
  
  // Vérifier les variables d'environnement
  const requiredEnvVars = [
    'RESEND_API_KEY',
    'RESEND_FROM_EMAIL',
    'RESEND_REPLY_TO'
  ];

  let allEnvVarsPresent = true;
  requiredEnvVars.forEach(envVar => {
    if (process.env[envVar]) {
      logSuccess(`Variable ${envVar} configurée`);
    } else {
      logError(`Variable ${envVar} manquante`);
      allEnvVarsPresent = false;
    }
  });

  return allEnvVarsPresent;
}

/**
 * Fonction principale
 */
async function main() {
  log('🚀 Validation de la configuration DNS pour la délivrabilité email', 'bold');
  log(`Domaine testé: ${DOMAIN}`, 'blue');
  log('', 'reset');

  const results = {
    spf: false,
    dkim: false,
    dmarc: false,
    mx: false,
    resend: false
  };

  // Exécuter tous les tests
  results.spf = await checkSPF();
  log('', 'reset');
  
  results.dkim = await checkDKIM();
  log('', 'reset');
  
  results.dmarc = await checkDMARC();
  log('', 'reset');
  
  results.mx = await checkMX();
  log('', 'reset');
  
  results.resend = await checkResendConfig();
  log('', 'reset');

  // Résumé
  log('📊 Résumé de la validation:', 'bold');
  
  const totalTests = Object.keys(results).length;
  const passedTests = Object.values(results).filter(Boolean).length;
  const score = Math.round((passedTests / totalTests) * 100);

  Object.entries(results).forEach(([test, passed]) => {
    const status = passed ? '✅' : '❌';
    const testName = test.toUpperCase();
    log(`${status} ${testName}`);
  });

  log('', 'reset');
  log(`Score global: ${score}% (${passedTests}/${totalTests})`, score >= 80 ? 'green' : 'red');

  if (score >= 80) {
    logSuccess('Configuration DNS correcte pour la délivrabilité !');
  } else {
    logError('Configuration DNS incomplète. Consultez le guide de configuration.');
  }

  log('', 'reset');
  await testDeliverability();

  // Code de sortie
  process.exit(score >= 80 ? 0 : 1);
}

// Gestion des erreurs
process.on('unhandledRejection', (error) => {
  logError(`Erreur non gérée: ${error.message}`);
  process.exit(1);
});

// Exécution
if (require.main === module) {
  main().catch(error => {
    logError(`Erreur fatale: ${error.message}`);
    process.exit(1);
  });
}

module.exports = {
  checkSPF,
  checkDKIM,
  checkDMARC,
  checkMX,
  checkResendConfig
};
