#!/usr/bin/env node

/**
 * Script de test des cron jobs Stream2Spin
 * Permet de tester les endpoints des cron jobs localement
 */

const https = require('https');
const http = require('http');
const fs = require('fs');
const path = require('path');

// Configuration - Lecture automatique des variables d'environnement
function loadEnvFile(envFile) {
  const envPath = path.join(__dirname, '..', envFile);
  if (fs.existsSync(envPath)) {
    const content = fs.readFileSync(envPath, 'utf8');
    const env = {};
    content.split('\n').forEach(line => {
      const match = line.match(/^([^=]+)=(.*)$/);
      if (match) {
        env[match[1]] = match[2].replace(/^["']|["']$/g, ''); // Enlever les guillemets
      }
    });
    return env;
  }
  return {};
}

// Charger les variables d'environnement dans l'ordre de priorité
const localEnv = loadEnvFile('.env.local');
const productionEnv = loadEnvFile('.env.production');
const env = { ...localEnv, ...productionEnv };

const CRON_SECRET = env.CRON_SECRET || "eTrN6Fu!5VZv*LDsdD4Z4mVRM@o^zooniq#zL^*5MRuDh&jcVafWus9mB3VQ9&f$";
const LOCAL_URL = "http://localhost:3000";
const STAGING_URL = "https://stream2spin-staging.vercel.app";
const PRODUCTION_URL = "https://stream2spin-6mpsdda8p-lecoutels-projects.vercel.app";

console.log('🔐 CRON_SECRET chargé depuis:', env.CRON_SECRET ? 'fichier .env' : 'valeur par défaut');

// Endpoints à tester
const ENDPOINTS = [
  {
    name: "Génération de recommandations",
    path: "/api/cron/generate-recommendations"
  },
  {
    name: "Envoi de notifications",
    path: "/api/cron/send-notifications"
  }
];

/**
 * Test un endpoint de cron job
 */
async function testCronEndpoint(baseUrl, endpoint) {
  return new Promise((resolve) => {
    const url = new URL(endpoint.path, baseUrl);
    const isHttps = url.protocol === 'https:';
    const client = isHttps ? https : http;
    
    const options = {
      hostname: url.hostname,
      port: url.port || (isHttps ? 443 : 80),
      path: url.pathname,
      method: 'GET', // Vercel Cron utilise GET par défaut
      headers: {
        'Authorization': `Bearer ${CRON_SECRET}`,
        'Content-Type': 'application/json',
        'User-Agent': 'Stream2Spin-Cron-Test/1.0'
      }
    };

    console.log(`\n🔍 Test de ${endpoint.name} sur ${baseUrl}${endpoint.path}`);
    console.log(`📋 Méthode: GET (comme Vercel Cron)`);
    
    const req = client.request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        console.log(`📊 Status: ${res.statusCode} ${res.statusMessage}`);
        
        if (res.statusCode === 200) {
          console.log(`✅ Succès! Endpoint accessible`);
        } else if (res.statusCode === 401) {
          console.log(`❌ Erreur d'authentification - Vérifier CRON_SECRET`);
        } else if (res.statusCode === 405) {
          console.log(`❌ Méthode non autorisée - Vérifier que GET appelle POST`);
        } else {
          console.log(`❌ Erreur ${res.statusCode}`);
        }
        
        if (data && data.length < 1000) {
          try {
            const jsonData = JSON.parse(data);
            console.log(`📄 Réponse:`, JSON.stringify(jsonData, null, 2));
          } catch (e) {
            console.log(`📄 Réponse (texte):`, data.substring(0, 500));
          }
        }
        
        resolve({
          success: res.statusCode >= 200 && res.statusCode < 300,
          statusCode: res.statusCode,
          data: data
        });
      });
    });

    req.on('error', (error) => {
      console.error(`❌ Erreur de connexion:`, error.message);
      resolve({
        success: false,
        error: error.message
      });
    });

    req.setTimeout(30000, () => {
      console.error(`⏰ Timeout après 30 secondes`);
      req.destroy();
      resolve({
        success: false,
        error: 'Timeout'
      });
    });

    req.end();
  });
}

/**
 * Test principal
 */
async function runTests() {
  console.log('🚀 Test des Cron Jobs Stream2Spin');
  console.log('=====================================');
  console.log(`🔐 Utilisation du CRON_SECRET: ${CRON_SECRET.substring(0, 10)}...`);
  
  // Test local
  console.log('\n📍 Test Local (localhost:3000)');
  console.log('--------------------------------');
  
  for (const endpoint of ENDPOINTS) {
    await testCronEndpoint(LOCAL_URL, endpoint);
  }
  
  // Test staging
  console.log('\n🌐 Test Staging');
  console.log('-------------------');
  
  for (const endpoint of ENDPOINTS) {
    await testCronEndpoint(STAGING_URL, endpoint);
  }
  
  // Test production
  console.log('\n🌐 Test Production');
  console.log('-------------------');
  
  for (const endpoint of ENDPOINTS) {
    await testCronEndpoint(PRODUCTION_URL, endpoint);
  }
  
  console.log('\n✅ Tests terminés');
  console.log('\n📝 Prochaines étapes:');
  console.log('1. Déployer les modifications sur Vercel');
  console.log('2. Vérifier les logs Vercel pour les exécutions automatiques');
  console.log('3. Surveiller les logs le jeudi à 00:30 et 09:30');
}

// Exécution
if (require.main === module) {
  runTests().catch(console.error);
}

module.exports = { testCronEndpoint, runTests }; 