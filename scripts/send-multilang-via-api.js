/**
 * Script pour envoyer des emails multilingues via l'API
 * Version simple qui ne nécessite pas d'imports complexes
 */

const fs = require('fs');
const path = require('path');

// Configuration
const TARGET_EMAIL = '<EMAIL>';
const TARGET_NAME = '<PERSON>';
const LANGUAGES = ['fr', 'en'];

// Charger les variables d'environnement
require('dotenv').config({ path: '.env.local' });

// Vérifier les variables d'environnement
if (!process.env.CRON_SECRET) {
  console.error('❌ CRON_SECRET manquante');
  process.exit(1);
}

// Fonction pour logger avec timestamp
function logWithTimestamp(message) {
  const timestamp = new Date().toISOString();
  console.log(`[${timestamp}] ${message}`);
}

// Fonction pour attendre
function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

// Fonction pour envoyer un email via l'API
async function sendEmailViaAPI(lang = 'fr') {
  const url = 'http://localhost:3000/api/admin/send-test-emails';
  
  try {
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${process.env.CRON_SECRET}`
      },
      body: JSON.stringify({
        email: TARGET_EMAIL,
        name: TARGET_NAME,
        language: lang // Ajouter la langue en paramètre
      })
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const result = await response.json();
    return result;
  } catch (error) {
    throw new Error(`Erreur API pour ${lang}: ${error.message}`);
  }
}

// Fonction pour vérifier que le serveur Next.js est en cours d'exécution
async function checkServer() {
  try {
    const response = await fetch('http://localhost:3000/api/auth/providers', {
      method: 'GET',
      signal: AbortSignal.timeout(5000)
    });
    
    return response.ok;
  } catch (error) {
    return false;
  }
}

// Fonction principale
async function main() {
  logWithTimestamp('🚀 Envoi des emails multilingues Stream2Spin');
  logWithTimestamp(`📧 Destination: ${TARGET_EMAIL}`);
  logWithTimestamp(`👤 Nom: ${TARGET_NAME}`);
  logWithTimestamp(`🌍 Langues: ${LANGUAGES.join(', ')}`);
  logWithTimestamp('');
  
  // Vérifier que le serveur Next.js est en cours d'exécution
  logWithTimestamp('ℹ️  Vérification du serveur Next.js...');
  const isServerRunning = await checkServer();
  
  if (!isServerRunning) {
    logWithTimestamp('❌ Le serveur Next.js n\'est pas en cours d\'exécution');
    logWithTimestamp('ℹ️  Lancez d\'abord "pnpm dev" dans un autre terminal');
    process.exit(1);
  }
  
  logWithTimestamp('✅ Serveur Next.js détecté');
  logWithTimestamp('');
  
  const results = {};
  
  // Envoyer les emails dans chaque langue
  for (const lang of LANGUAGES) {
    logWithTimestamp(`🌍 === ENVOI EN ${lang.toUpperCase()} ===`);
    
    try {
      const result = await sendEmailViaAPI(lang);
      
      if (result.welcome && result.recommendations) {
        logWithTimestamp(`✅ Emails envoyés avec succès en ${lang} !`);
        
        if (result.details.welcome.success) {
          logWithTimestamp(`✅ Email de bienvenue envoyé ! ID: ${result.details.welcome.messageId}`);
        } else {
          logWithTimestamp(`❌ Erreur email de bienvenue: ${result.details.welcome.error}`);
        }
        
        if (result.details.recommendations.success) {
          logWithTimestamp(`✅ Email de recommandations envoyé ! ID: ${result.details.recommendations.messageId}`);
        } else {
          logWithTimestamp(`❌ Erreur email de recommandations: ${result.details.recommendations.error}`);
        }
        
        results[lang] = {
          success: true,
          welcome: result.details.welcome,
          recommendations: result.details.recommendations
        };
      } else {
        logWithTimestamp(`⚠️  Réponse inattendue pour ${lang}:`, result);
        results[lang] = {
          success: false,
          error: 'Réponse inattendue'
        };
      }
      
    } catch (error) {
      logWithTimestamp(`❌ Erreur pour ${lang}: ${error.message}`);
      results[lang] = {
        success: false,
        error: error.message
      };
    }
    
    logWithTimestamp('');
    
    // Attendre un peu entre les langues
    if (lang !== LANGUAGES[LANGUAGES.length - 1]) {
      logWithTimestamp('⏳ Attente de 3 secondes...');
      await sleep(3000);
    }
  }
  
  // Résumé final
  logWithTimestamp('📊 === RÉSUMÉ FINAL ===');
  logWithTimestamp(`👤 Utilisateur: ${TARGET_NAME} (${TARGET_EMAIL})`);
  logWithTimestamp('');
  
  for (const lang of LANGUAGES) {
    const result = results[lang];
    logWithTimestamp(`🌍 ${lang.toUpperCase()}:`);
    
    if (result.success) {
      logWithTimestamp(`  Welcome: ${result.welcome.success ? '✅' : '❌'} ${result.welcome.messageId || result.welcome.error}`);
      logWithTimestamp(`  Recommendations: ${result.recommendations.success ? '✅' : '❌'} ${result.recommendations.messageId || result.recommendations.error}`);
    } else {
      logWithTimestamp(`  Erreur: ❌ ${result.error}`);
    }
  }
  
  logWithTimestamp('');
  logWithTimestamp('🎉 Processus terminé ! Vérifiez votre boîte mail.');
  logWithTimestamp(`📧 Emails envoyés à: ${TARGET_EMAIL}`);
  logWithTimestamp('ℹ️  Tous les emails utilisent maintenant les templates React avec localisation complète.');
}

// Exécuter le script
main().catch(error => {
  console.error('❌ Erreur fatale:', error);
  process.exit(1);
}); 