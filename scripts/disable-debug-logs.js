#!/usr/bin/env node

/**
 * Script pour désactiver les logs de debug pour le staging
 * Conditionne tous les console.log, console.debug, console.warn à l'environnement de développement
 */

const fs = require('fs');
const path = require('path');

// Patterns de logs à conditionner
const DEBUG_PATTERNS = [
  /console\.log\(/g,
  /console\.debug\(/g,
  /console\.warn\(/g
];

// Répertoires à traiter (exclure node_modules et .git)
const DIRECTORIES = [
  'app',
  'components',
  'lib',
  'scripts',
  'hooks',
  'store'
];

// Fichiers à ignorer
const IGNORE_FILES = [
  'node_modules',
  '.git',
  'clean-console-logs.js',
  'disable-debug-logs.js'
];

function shouldConditionLog(line) {
  // Ignorer les lignes déjà conditionnées
  if (line.includes("process.env.NODE_ENV === 'development'") || 
      line.includes("process.env.NODE_ENV !== 'production'")) {
    return false;
  }
  
  // Ignorer les commentaires
  if (line.trim().startsWith('//') || line.trim().startsWith('/*') || line.trim().startsWith('*')) {
    return false;
  }
  
  return DEBUG_PATTERNS.some(pattern => pattern.test(line));
}

function conditionLogLine(line) {
  const indent = line.match(/^(\s*)/)[1];
  return `${indent}if (process.env.NODE_ENV === 'development') {\n${line}\n${indent}}`;
}

function processFile(filePath) {
  // Ignorer les fichiers non pertinents
  if (!filePath.endsWith('.ts') && !filePath.endsWith('.tsx') && !filePath.endsWith('.js') && !filePath.endsWith('.jsx')) {
    return;
  }
  
  // Ignorer les fichiers de configuration
  if (filePath.includes('config') || filePath.includes('test') || filePath.includes('spec')) {
    return;
  }
  
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const lines = content.split('\n');
    let modified = false;
    const newLines = [];
    
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      
      if (shouldConditionLog(line)) {
        console.log(`🔒 Conditionnement: ${filePath}:${i + 1}`);
        newLines.push(conditionLogLine(line));
        modified = true;
      } else {
        newLines.push(line);
      }
    }
    
    if (modified) {
      fs.writeFileSync(filePath, newLines.join('\n'));
      console.log(`✅ Fichier conditionné: ${filePath}`);
    }
  } catch (error) {
    console.error(`❌ Erreur traitement ${filePath}:`, error.message);
  }
}

function processDirectory(dirPath) {
  if (!fs.existsSync(dirPath)) {
    console.log(`⚠️ Répertoire non trouvé: ${dirPath}`);
    return;
  }
  
  const items = fs.readdirSync(dirPath);
  
  for (const item of items) {
    if (IGNORE_FILES.includes(item)) {
      continue;
    }
    
    const fullPath = path.join(dirPath, item);
    const stat = fs.statSync(fullPath);
    
    if (stat.isDirectory()) {
      processDirectory(fullPath);
    } else {
      processFile(fullPath);
    }
  }
}

function main() {
  console.log('🔒 Désactivation des logs de debug pour le staging...\n');
  
  for (const dir of DIRECTORIES) {
    console.log(`📁 Traitement du répertoire: ${dir}`);
    processDirectory(dir);
  }
  
  console.log('\n✅ Désactivation terminée !');
  console.log('\n📝 Actions recommandées :');
  console.log('1. Vérifier les modifications avec git diff');
  console.log('2. Tester l\'application en local');
  console.log('3. Commit les changements si tout fonctionne');
  console.log('4. Pousser vers staging');
}

if (require.main === module) {
  main();
}

module.exports = { processFile, processDirectory }; 