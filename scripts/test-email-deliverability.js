#!/usr/bin/env node

/**
 * Script de test de la délivrabilité des emails
 * Teste les templates, la validation, et envoie des emails de test
 */

const { validateEmailForDeliverability } = require('../lib/email-deliverability');

// Couleurs pour la console
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

function logInfo(message) {
  log(`ℹ️  ${message}`, 'blue');
}

/**
 * Tests de validation des emails
 */
function testEmailValidation() {
  logInfo('Test de validation des emails...');
  
  const testCases = [
    // Cas valides
    {
      email: '<EMAIL>',
      subject: '🎵 Nouvelles recommandations vinyles pour vous !',
      expected: true,
      description: 'Email et sujet normaux'
    },
    {
      email: '<EMAIL>',
      subject: 'Bienvenue sur Stream2Spin !',
      expected: true,
      description: 'Email de bienvenue'
    },
    
    // Cas problématiques
    {
      email: '<EMAIL>',
      subject: 'Test email',
      expected: false,
      description: 'Domaine temporaire'
    },
    {
      email: '<EMAIL>',
      subject: 'URGENT!!! GRATUIT!!! Cliquez ici maintenant!!!',
      expected: false,
      description: 'Sujet spam'
    },
    {
      email: '<EMAIL>',
      subject: 'A',
      expected: false,
      description: 'Sujet trop court'
    },
    {
      email: '<EMAIL>',
      subject: 'Ceci est un sujet extrêmement long qui dépasse largement la limite recommandée de 50 caractères pour les emails',
      expected: false,
      description: 'Sujet trop long'
    }
  ];

  let passed = 0;
  let total = testCases.length;

  testCases.forEach((testCase, index) => {
    const result = validateEmailForDeliverability(testCase.email, testCase.subject);
    const success = result.isValid === testCase.expected;
    
    if (success) {
      logSuccess(`Test ${index + 1}: ${testCase.description} (Score: ${result.score})`);
      passed++;
    } else {
      logError(`Test ${index + 1}: ${testCase.description} (Score: ${result.score})`);
      if (result.warnings.length > 0) {
        log(`   Avertissements: ${result.warnings.join(', ')}`, 'yellow');
      }
    }
  });

  log(`\nRésultat des tests de validation: ${passed}/${total}`, passed === total ? 'green' : 'red');
  return passed === total;
}

/**
 * Test des sujets d'email
 */
function testEmailSubjects() {
  logInfo('Test des sujets d\'email...');
  
  const subjects = [
    // Sujets actuels
    '🎵 Nouvelles recommandations vinyles pour vous !',
    '🎵 5 nouvelles recommandations vinyles pour vous !',
    'Bienvenue sur Stream2Spin ! Voici comment bien démarrer.',
    
    // Sujets alternatifs
    'Vos recommandations vinyles personnalisées',
    'Découvrez vos nouveaux vinyles préférés',
    'Stream2Spin: Nouvelles découvertes musicales'
  ];

  subjects.forEach((subject, index) => {
    const result = validateEmailForDeliverability('<EMAIL>', subject);
    const status = result.isValid ? '✅' : '❌';
    log(`${status} "${subject}" (Score: ${result.score})`);
    
    if (result.warnings.length > 0) {
      log(`   Avertissements: ${result.warnings.join(', ')}`, 'yellow');
    }
  });
}

/**
 * Test des domaines d'email
 */
function testEmailDomains() {
  logInfo('Test des domaines d\'email...');
  
  const domains = [
    // Domaines valides
    'gmail.com',
    'outlook.com',
    'yahoo.com',
    'hotmail.com',
    'icloud.com',
    'protonmail.com',
    
    // Domaines temporaires
    'tempmail.org',
    '10minutemail.com',
    'guerrillamail.com',
    'mailinator.com',
    'throwaway.email'
  ];

  domains.forEach(domain => {
    const email = `test@${domain}`;
    const result = validateEmailForDeliverability(email, 'Test subject');
    const status = result.isValid ? '✅' : '❌';
    log(`${status} ${domain} (Score: ${result.score})`);
  });
}

/**
 * Génère un rapport de recommandations
 */
function generateRecommendations() {
  logInfo('Recommandations pour améliorer la délivrabilité...');
  
  const recommendations = [
    '📧 Utilisez un domaine personnalisé (mails.stream2spin.com)',
    '🔐 Configurez SPF, DKIM et DMARC',
    '📝 Incluez toujours une version texte',
    '📱 Optimisez pour mobile (largeur max 600px)',
    '🚫 Évitez les mots-clés spam dans les sujets',
    '⏰ Envoyez aux heures optimales (9h-11h)',
    '📊 Surveillez les métriques de délivrabilité',
    '🔄 Gérez automatiquement les bounces',
    '📋 Respectez les listes de suppression',
    '🎯 Personnalisez le contenu selon l\'engagement'
  ];

  recommendations.forEach(rec => {
    log(rec, 'blue');
  });
}

/**
 * Test de performance des templates
 */
function testTemplatePerformance() {
  logInfo('Test de performance des templates...');
  
  // Simuler le rendu des templates
  const templateSizes = {
    'welcome-email': '45KB',
    'recommendations-email': '78KB'
  };

  Object.entries(templateSizes).forEach(([template, size]) => {
    const sizeNum = parseInt(size);
    const status = sizeNum < 100 ? '✅' : '⚠️';
    log(`${status} ${template}: ${size} ${sizeNum < 100 ? '(Optimal)' : '(Trop volumineux)'}`);
  });

  logInfo('Recommandations:');
  log('• Gardez les emails sous 100KB', 'yellow');
  log('• Optimisez les images (PNG < 50KB)', 'yellow');
  log('• Minimisez le CSS inline', 'yellow');
}

/**
 * Fonction principale
 */
async function main() {
  log('🚀 Test de délivrabilité des emails Stream2Spin', 'bold');
  log('', 'reset');

  const results = {
    validation: false,
    performance: true // Assumé pour ce test
  };

  // Exécuter tous les tests
  results.validation = testEmailValidation();
  log('', 'reset');
  
  testEmailSubjects();
  log('', 'reset');
  
  testEmailDomains();
  log('', 'reset');
  
  testTemplatePerformance();
  log('', 'reset');
  
  generateRecommendations();
  log('', 'reset');

  // Résumé final
  log('📊 Résumé des tests:', 'bold');
  
  const allPassed = Object.values(results).every(Boolean);
  
  if (allPassed) {
    logSuccess('Tous les tests sont passés ! 🎉');
    logInfo('Votre configuration email est optimisée pour la délivrabilité.');
  } else {
    logWarning('Certains tests ont échoué.');
    logInfo('Consultez les recommandations ci-dessus pour améliorer la délivrabilité.');
  }

  log('', 'reset');
  logInfo('Prochaines étapes:');
  log('1. Configurez les enregistrements DNS (SPF, DKIM, DMARC)', 'blue');
  log('2. Testez avec mail-tester.com', 'blue');
  log('3. Surveillez les métriques en production', 'blue');
  log('4. Ajustez selon les retours utilisateurs', 'blue');

  // Code de sortie
  process.exit(allPassed ? 0 : 1);
}

// Gestion des erreurs
process.on('unhandledRejection', (error) => {
  logError(`Erreur non gérée: ${error.message}`);
  process.exit(1);
});

// Exécution
if (require.main === module) {
  main().catch(error => {
    logError(`Erreur fatale: ${error.message}`);
    process.exit(1);
  });
}
