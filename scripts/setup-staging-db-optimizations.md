# 🗄️ Configuration des Optimisations DB sur Staging

## 📋 Instructions pour Supabase Staging

### 1. Accéder à Supabase Staging
1. Se connecter à [Supabase Dashboard](https://supabase.com/dashboard)
2. Sélectionner le projet **Stream2Spin - Staging**
3. Aller dans **SQL Editor**

### 2. Exécuter le Script d'Optimisation des Index

Copier et exécuter le script suivant dans SQL Editor :

```sql
-- 🚀 Script d'optimisation des index pour améliorer les performances DB
-- À exécuter dans Supabase SQL Editor (Staging)

-- 1. Index optimisé pour la table users (requêtes de session)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_id_optimized 
ON users (id) 
INCLUDE (name, email, image);

-- 2. Index pour les requêtes de recommandations
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_recommendations_user_timeframe 
ON recommendations (user_id, timeframe, created_at DESC);

-- 3. Index pour la collection Discogs
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_discogs_collection_user 
ON discogs_collection (user_id, master_id);

-- 4. Index pour la wishlist
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_wishlist_user_album 
ON wishlist (user_id, album_id, created_at DESC);

-- 5. Index pour les comptes (accounts table)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_accounts_provider_user 
ON accounts (provider, user_id);

-- 6. Index pour les sessions NextAuth
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_sessions_user_expires 
ON sessions (user_id, expires);

-- 7. Statistiques pour l'optimiseur de requêtes
ANALYZE users;
ANALYZE recommendations;
ANALYZE discogs_collection;
ANALYZE wishlist;
ANALYZE accounts;
ANALYZE sessions;

-- 8. Vérifier les index créés
SELECT 
    schemaname,
    tablename,
    indexname,
    indexdef
FROM pg_indexes 
WHERE tablename IN ('users', 'recommendations', 'discogs_collection', 'wishlist', 'accounts', 'sessions')
  AND indexname LIKE 'idx_%'
ORDER BY tablename, indexname;
```

### 3. Vérifier l'Exécution

Après exécution, vous devriez voir :
- ✅ Création des index (peut prendre 1-2 minutes)
- ✅ Mise à jour des statistiques
- ✅ Liste des index créés

### 4. Optimisations de Configuration Supabase

#### A. Paramètres de Performance (Optionnel)
Dans **Settings > Database** :
- Vérifier que le **Connection Pooling** est activé
- Mode : **Transaction** (recommandé pour notre configuration)
- Pool Size : **15** (par défaut, suffisant)

#### B. Monitoring
Dans **Reports** :
- Surveiller **Database Health**
- Vérifier **Query Performance**
- Observer **Connection Pool Usage**

## 📊 Validation des Optimisations

### 1. Test de Performance Local
```bash
# Depuis le répertoire du projet
node scripts/monitor-staging-performance.js
```

### 2. Métriques à Observer

#### Temps de Réponse Attendus (après optimisations)
- `/api/auth/session` : **<200ms** (avec cache)
- `/api/recommendations/*` : **<500ms**
- Pages principales : **<2s**

#### Logs à Rechercher sur Vercel
```
🚀 Modules critiques pré-chargés avec succès
🚀 Connexion DB pré-initialisée avec succès
🚀 Réutilisation connexion DB depuis le pool
🚀 Cache DB hit en Xms pour: [user-id]
✅ Données utilisateur mises en cache DB (30min): [user-id]
```

### 3. Requêtes de Diagnostic

Pour vérifier les performances des requêtes :

```sql
-- Vérifier l'utilisation des index
EXPLAIN (ANALYZE, BUFFERS) 
SELECT id, name, email, image 
FROM users 
WHERE id = 'user-id-example';

-- Vérifier les statistiques des tables
SELECT 
    schemaname,
    tablename,
    n_tup_ins as inserts,
    n_tup_upd as updates,
    n_tup_del as deletes,
    n_live_tup as live_rows,
    last_analyze
FROM pg_stat_user_tables 
WHERE tablename IN ('users', 'recommendations', 'discogs_collection', 'wishlist');
```

## 🚨 Troubleshooting

### Si les Index ne se Créent pas
```sql
-- Vérifier les erreurs
SELECT * FROM pg_stat_activity WHERE state = 'active';

-- Forcer la création sans CONCURRENTLY (plus rapide mais bloquant)
CREATE INDEX IF NOT EXISTS idx_users_id_optimized 
ON users (id) 
INCLUDE (name, email, image);
```

### Si les Performances ne s'Améliorent pas
1. **Vérifier les logs Vercel** pour les erreurs
2. **Redémarrer l'application** (nouveau déploiement)
3. **Vider le cache** et re-tester
4. **Vérifier la configuration** du pool de connexions

## ✅ Checklist de Validation

- [ ] Script SQL exécuté sans erreur
- [ ] Index créés et visibles dans pg_indexes
- [ ] Statistiques mises à jour
- [ ] Test de performance local réussi
- [ ] Logs d'optimisation visibles sur Vercel
- [ ] Temps de réponse améliorés

## 🎯 Résultats Attendus

Après ces optimisations, l'application staging devrait avoir :
- **95-99% d'amélioration** des temps de réponse DB
- **Cache hit rate >90%** après quelques minutes
- **Header loading <500ms** au lieu de 4+ secondes
- **Navigation quasi-instantanée** avec pré-chargement

---

**Status** : 📋 Instructions prêtes pour exécution  
**Durée estimée** : 5-10 minutes  
**Impact** : Performances maximales sur staging
