#!/usr/bin/env node

/**
 * Script pour corriger les problèmes de syntaxe des console.log
 * causés par le script de désactivation des logs de debug
 */

const fs = require('fs');
const path = require('path');

// Répertoires à traiter
const DIRECTORIES = [
  'app',
  'components',
  'lib',
  'hooks',
  'store'
];

// Fichiers à ignorer
const IGNORE_FILES = [
  'node_modules',
  '.git',
  'fix-console-log-syntax.js'
];

function fixConsoleLogSyntax(content) {
  // Pattern pour détecter les console.log avec objets mal formatés
  // Recherche: console.log(`...`, {\n    }\n    propriété: valeur
  const pattern = /(console\.log\([^,]+,\s*\{\s*\}\s*\n\s*)([a-zA-Z_][a-zA-Z0-9_]*\s*:)/g;
  
  let fixed = content;
  let match;
  
  while ((match = pattern.exec(content)) !== null) {
    const beforeProperty = match[1];
    const property = match[2];
    
    // Remplacer la structure incorrecte
    const incorrectStructure = match[0];
    const correctStructure = beforeProperty.replace(/\{\s*\}\s*\n\s*/, '{\n      ') + property;
    
    fixed = fixed.replace(incorrectStructure, correctStructure);
  }
  
  return fixed;
}

function processFile(filePath) {
  // Ignorer les fichiers non pertinents
  if (!filePath.endsWith('.ts') && !filePath.endsWith('.tsx') && !filePath.endsWith('.js') && !filePath.endsWith('.jsx')) {
    return;
  }
  
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const fixed = fixConsoleLogSyntax(content);
    
    if (fixed !== content) {
      fs.writeFileSync(filePath, fixed);
      console.log(`✅ Syntaxe corrigée: ${filePath}`);
    }
  } catch (error) {
    console.error(`❌ Erreur traitement ${filePath}:`, error.message);
  }
}

function processDirectory(dirPath) {
  if (!fs.existsSync(dirPath)) {
    console.log(`⚠️ Répertoire non trouvé: ${dirPath}`);
    return;
  }
  
  const items = fs.readdirSync(dirPath);
  
  for (const item of items) {
    if (IGNORE_FILES.includes(item)) {
      continue;
    }
    
    const fullPath = path.join(dirPath, item);
    const stat = fs.statSync(fullPath);
    
    if (stat.isDirectory()) {
      processDirectory(fullPath);
    } else {
      processFile(fullPath);
    }
  }
}

function main() {
  console.log('🔧 Correction de la syntaxe des console.log...\n');
  
  for (const dir of DIRECTORIES) {
    console.log(`📁 Traitement du répertoire: ${dir}`);
    processDirectory(dir);
  }
  
  console.log('\n✅ Correction terminée !');
}

if (require.main === module) {
  main();
}

module.exports = { fixConsoleLogSyntax, processFile, processDirectory }; 