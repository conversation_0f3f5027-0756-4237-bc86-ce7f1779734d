const fs = require('fs');
const path = require('path');

function fixDiscogsDebugLogs() {
  const files = [
    'app/api/discogs/callback/route.ts',
    'app/api/discogs/connect/route.ts'
  ];

  files.forEach(filePath => {
    console.log(`\n🔧 Traitement de ${filePath}...`);
    
    let content = fs.readFileSync(filePath, 'utf8');
    let modifications = 0;

    // Remplacer les console.log simples (pas les console.error)
    content = content.replace(/^(\s*)(console\.log\([^)]*\);?)$/gm, (match, indent, logStatement) => {
      modifications++;
      return `${indent}if (process.env.NODE_ENV === 'development') {\n${indent}  ${logStatement}\n${indent}}`;
    });

    // Remplacer les console.log multi-lignes
    content = content.replace(/^(\s*)(console\.log\([^;]*\{[^}]*\}\);?)$/gms, (match, indent, logStatement) => {
      modifications++;
      return `${indent}if (process.env.NODE_ENV === 'development') {\n${indent}  ${logStatement}\n${indent}}`;
    });

    if (modifications > 0) {
      fs.writeFileSync(filePath, content);
      console.log(`✅ ${modifications} console.log conditionnés dans ${filePath}`);
    } else {
      console.log(`ℹ️ Aucune modification nécessaire dans ${filePath}`);
    }
  });
}

fixDiscogsDebugLogs();
console.log('\n🎉 Correction des logs de debug Discogs terminée !'); 