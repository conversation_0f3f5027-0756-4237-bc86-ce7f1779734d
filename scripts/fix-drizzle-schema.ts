/**
 * Script pour diagnostiquer et corriger les problèmes de schéma Drizzle
 */

import { db } from '@/lib/db';
import { sql } from 'drizzle-orm';

async function diagnoseDrizzleSchema() {
  if (process.env.NODE_ENV === 'development') {
  console.log('🔍 Diagnostic du schéma Drizzle...');
  }

  try {
    // 1. Vérifier la connexion à la base de données
    if (process.env.NODE_ENV === 'development') {
    console.log('\n1. Test de connexion...');
    }
    const connectionTest = await db.execute(sql`SELECT 1 as test`);
    if (process.env.NODE_ENV === 'development') {
    console.log('✅ Connexion OK:', connectionTest[0]);
    }

    // 2. Vérifier la structure de la table accounts
    if (process.env.NODE_ENV === 'development') {
    console.log('\n2. Structure de la table accounts:');
    }
    const accountsColumns = await db.execute(sql`
      SELECT column_name, data_type, is_nullable
      FROM information_schema.columns 
      WHERE table_name = 'accounts'
      ORDER BY ordinal_position
    `);
    
    if (process.env.NODE_ENV === 'development') {
    console.log('Colonnes accounts:');
    }
    accountsColumns.forEach((col: any) => {
      if (process.env.NODE_ENV === 'development') {
      console.log(`  - ${col.column_name}: ${col.data_type} (nullable: ${col.is_nullable})`);
      }
    });

    // 3. Vérifier la structure de user_discogs_collection
    if (process.env.NODE_ENV === 'development') {
    console.log('\n3. Structure de user_discogs_collection:');
    }
    const discogsColumns = await db.execute(sql`
      SELECT column_name, data_type, is_nullable
      FROM information_schema.columns 
      WHERE table_name = 'user_discogs_collection'
      ORDER BY ordinal_position
    `);
    
    if (process.env.NODE_ENV === 'development') {
    console.log('Colonnes user_discogs_collection:');
    }
    discogsColumns.forEach((col: any) => {
      if (process.env.NODE_ENV === 'development') {
      console.log(`  - ${col.column_name}: ${col.data_type} (nullable: ${col.is_nullable})`);
      }
    });

    // 4. Test des requêtes Drizzle problématiques
    if (process.env.NODE_ENV === 'development') {
    console.log('\n4. Test des requêtes Drizzle...');
    }
    
    try {
      // Test requête accounts avec access_token_secret
      const testAccount = await db.execute(sql`
        SELECT "userId", "provider", "access_token", "access_token_secret"
        FROM accounts 
        WHERE provider = 'discogs'
        LIMIT 1
      `);
      if (process.env.NODE_ENV === 'development') {
      console.log('✅ Requête SQL directe accounts OK');
      }
      
      // Test requête user_discogs_collection
      const testCollection = await db.execute(sql`
        SELECT "userId", "discogsReleaseId", "artistName", "year", "format", "syncedAt"
        FROM user_discogs_collection 
        LIMIT 1
      `);
      if (process.env.NODE_ENV === 'development') {
      console.log('✅ Requête SQL directe user_discogs_collection OK');
      }
      
    } catch (error) {
      console.error('❌ Erreur dans les requêtes SQL directes:', error);
    }

    // 5. Vérifier les contraintes et index
    if (process.env.NODE_ENV === 'development') {
    console.log('\n5. Contraintes et index:');
    }
    const constraints = await db.execute(sql`
      SELECT 
        tc.constraint_name,
        tc.table_name,
        tc.constraint_type,
        kcu.column_name
      FROM information_schema.table_constraints tc
      JOIN information_schema.key_column_usage kcu 
        ON tc.constraint_name = kcu.constraint_name
      WHERE tc.table_name IN ('accounts', 'user_discogs_collection')
      ORDER BY tc.table_name, tc.constraint_type
    `);
    
    constraints.forEach((constraint: any) => {
      if (process.env.NODE_ENV === 'development') {
      console.log(`  ${constraint.table_name}.${constraint.column_name}: ${constraint.constraint_type}`);
      }
    });

  } catch (error) {
    console.error('❌ Erreur lors du diagnostic:', error);
  }
}

async function fixDrizzleSchema() {
  if (process.env.NODE_ENV === 'development') {
  console.log('\n🔧 Tentative de correction...');
  }

  try {
    // 1. Forcer la recréation des métadonnées Drizzle
    if (process.env.NODE_ENV === 'development') {
    console.log('1. Nettoyage du cache Drizzle...');
    }
    
    // 2. Vérifier et corriger les colonnes manquantes
    if (process.env.NODE_ENV === 'development') {
    console.log('2. Vérification des colonnes...');
    }
    
    // Vérifier access_token_secret
    const hasAccessTokenSecret = await db.execute(sql`
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'accounts' AND column_name = 'access_token_secret'
    `);
    
    if (hasAccessTokenSecret.length === 0) {
      if (process.env.NODE_ENV === 'development') {
      console.log('❌ Colonne access_token_secret manquante, ajout...');
      }
      await db.execute(sql`ALTER TABLE accounts ADD COLUMN access_token_secret TEXT`);
      if (process.env.NODE_ENV === 'development') {
      console.log('✅ Colonne access_token_secret ajoutée');
      }
    } else {
      if (process.env.NODE_ENV === 'development') {
      console.log('✅ Colonne access_token_secret existe');
      }
    }

    // Vérifier discogsReleaseId
    const hasDiscogsReleaseId = await db.execute(sql`
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'user_discogs_collection' AND column_name = 'discogsReleaseId'
    `);
    
    if (hasDiscogsReleaseId.length === 0) {
      if (process.env.NODE_ENV === 'development') {
      console.log('❌ Colonne discogsReleaseId manquante, ajout...');
      }
      await db.execute(sql`ALTER TABLE user_discogs_collection ADD COLUMN "discogsReleaseId" BIGINT`);
      if (process.env.NODE_ENV === 'development') {
      console.log('✅ Colonne discogsReleaseId ajoutée');
      }
    } else {
      if (process.env.NODE_ENV === 'development') {
      console.log('✅ Colonne discogsReleaseId existe');
      }
    }

    // Vérifier year
    const hasYear = await db.execute(sql`
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'user_discogs_collection' AND column_name = 'year'
    `);
    
    if (hasYear.length === 0) {
      if (process.env.NODE_ENV === 'development') {
      console.log('❌ Colonne year manquante, ajout...');
      }
      await db.execute(sql`ALTER TABLE user_discogs_collection ADD COLUMN year INTEGER`);
      if (process.env.NODE_ENV === 'development') {
      console.log('✅ Colonne year ajoutée');
      }
    } else {
      if (process.env.NODE_ENV === 'development') {
      console.log('✅ Colonne year existe');
      }
    }

    // Vérifier syncedAt
    const hasSyncedAt = await db.execute(sql`
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'user_discogs_collection' AND column_name = 'syncedAt'
    `);
    
    if (hasSyncedAt.length === 0) {
      if (process.env.NODE_ENV === 'development') {
      console.log('❌ Colonne syncedAt manquante, ajout...');
      }
      await db.execute(sql`ALTER TABLE user_discogs_collection ADD COLUMN "syncedAt" TIMESTAMP DEFAULT NOW()`);
      if (process.env.NODE_ENV === 'development') {
      console.log('✅ Colonne syncedAt ajoutée');
      }
    } else {
      if (process.env.NODE_ENV === 'development') {
      console.log('✅ Colonne syncedAt existe');
      }
    }

    if (process.env.NODE_ENV === 'development') {
    console.log('\n✅ Correction terminée');
    }

  } catch (error) {
    console.error('❌ Erreur lors de la correction:', error);
  }
}

// Exécuter le diagnostic et la correction
async function main() {
  await diagnoseDrizzleSchema();
  await fixDrizzleSchema();
  
  if (process.env.NODE_ENV === 'development') {
  console.log('\n🎯 Redémarrez le serveur pour que les changements prennent effet');
  }
  process.exit(0);
}

main().catch(console.error);
