/**
 * Template d'email de bienvenue pour les nouveaux utilisateurs
 * US 1.3 - Epic 1 : Authentification et Onboarding
 */

import {
  Html,
  Head,
  Body,
  Container,
  Section,
  Heading,
  Text,
  Link,
  Img,
  Button,
  Hr,
  Preview,
} from '@react-email/components';
import { EMAIL_ASSETS } from '@/lib/email-assets';
import { EmailHeader } from '@/components/emails/EmailHeader';

interface WelcomeEmailProps {
  name: string;
  userEmail: string;
  t: (key: string, values?: Record<string, any>) => string; // Ajoute la prop `t`
}

export function WelcomeEmail({ name, userEmail, t }: WelcomeEmailProps) {
  const baseUrl = process.env.NEXTAUTH_URL || 'http://localhost:3000';
  
  return (
    <Html>
      <Head>
        {/* Meta tags pour améliorer la délivrabilité */}
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <meta httpEquiv="Content-Type" content="text/html; charset=UTF-8" />
        <meta name="x-apple-disable-message-reformatting" />
        <meta name="format-detection" content="telephone=no,address=no,email=no,date=no,url=no" />
        <title>{t('emails.welcome.pageTitle')}</title>
      </Head>
      <Preview>{t('emails.welcome.preview')}</Preview>
      <Body style={main}>
        <Container style={container}>
          {/* Header avec logo */}
          <EmailHeader />

          {/* Contenu principal */}
          <Section style={content}>
            <Heading style={h2}>{t('emails.welcome.title')}</Heading>
            
            <Text style={greeting}>{t('emails.welcome.greeting', { name })}</Text>
            
            <Text style={text}>
              {t('emails.welcome.intro')}
            </Text>

            <Hr style={hr} />

            {/* Section des conseils */}
            <Heading style={h3}>{t('emails.welcome.adviceTitle')}</Heading>

            {/* Conseil 1: Discogs */}
            <Section style={adviceSection}>
              <div style={adviceHeader}>
                <Img
                  src={EMAIL_ASSETS.discIcon}
                  width="24"
                  height="24"
                  alt="Collection"
                  style={adviceIcon}
                />
                <Heading style={adviceTitle}>{t('emails.welcome.advice1_title')}</Heading>
              </div>
              <Text style={adviceText}>
                {t('emails.welcome.advice1_text')}
              </Text>
              <Button href={`${baseUrl}/account`} style={secondaryButton}>
                {t('emails.welcome.advice1_cta')}
              </Button>
            </Section>

            {/* Conseil 2: Wishlist */}
            <Section style={adviceSection}>
              <div style={adviceHeader}>
                <Img
                  src={EMAIL_ASSETS.heartIcon}
                  width="24"
                  height="24"
                  alt="Wishlist"
                  style={heartIconStyle}
                />
                <Heading style={adviceTitle}>{t('emails.welcome.advice2_title')}</Heading>
              </div>
              <Text style={adviceText}>
                {t('emails.welcome.advice2_text')}
              </Text>
              <Button href={`${baseUrl}/recommendations`} style={secondaryButton}>
                {t('emails.welcome.advice2_cta')}
              </Button>
            </Section>

            <Hr style={hr} />

            {/* CTA Principal */}
            <Section style={ctaSection}>
              <Button href={`${baseUrl}/recommendations`} style={primaryButton}>
                {t('emails.welcome.main_cta')}
              </Button>
            </Section>

            <Hr style={hr} />

            {/* Footer */}
            <Section style={footer}>
              <Text style={footerText}>
                {t('emails.welcome.footer_sent_to', { userEmail })}
              </Text>
              <Text style={footerText}>
                {t('emails.welcome.footer_copyright', { year: new Date().getFullYear() })}
              </Text>
              <Text style={footerText}>
                <Link href={`${baseUrl}`} style={link}>
                  {t('emails.welcome.footer_visit')}
                </Link>
              </Text>
            </Section>
          </Section>
        </Container>
      </Body>
    </Html>
  );
}

// Styles CSS-in-JS optimisés pour la délivrabilité
const main = {
  backgroundColor: '#f6f9fc',
  fontFamily: '-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,"Helvetica Neue",Ubuntu,sans-serif',
  margin: '0',
  padding: '24px 0',
  width: '100%',
  wordSpacing: 'normal',
};

const container = {
  backgroundColor: '#ffffff',
  margin: '0 auto',
  padding: '0 0 48px',
  marginBottom: '64px',
  maxWidth: '600px',
  width: '100%',
  border: '1px solid #e6e6e6',
  borderRadius: '12px',
};



const h1 = {
  color: '#ffffff',
  fontSize: '28px',
  fontWeight: 'bold',
  margin: '12px 0 0 0',
  padding: '0',
  lineHeight: '36px',
};

const content = {
  padding: '32px 24px',
};

const h2 = {
  color: '#6236FF',
  fontSize: '24px',
  fontWeight: 'bold',
  margin: '0 0 24px 0',
  padding: '0',
  textAlign: 'center' as const,
};

const h3 = {
  color: '#333',
  fontSize: '20px',
  fontWeight: 'bold',
  margin: '24px 0 20px 0',
  padding: '0',
  textAlign: 'center' as const,
};

const greeting = {
  color: '#333',
  fontSize: '18px',
  fontWeight: '600',
  margin: '0 0 16px 0',
};

const text = {
  color: '#333',
  fontSize: '16px',
  lineHeight: '24px',
  margin: '0 0 16px 0',
};

const ctaSection = {
  textAlign: 'center' as const,
  margin: '32px 0',
};

const primaryButton = {
  backgroundColor: '#6236FF',
  borderRadius: '8px',
  color: '#fff',
  fontSize: '16px',
  fontWeight: 'bold',
  textDecoration: 'none',
  textAlign: 'center' as const,
  display: 'inline-block',
  padding: '14px 28px',
  boxShadow: '0 4px 12px rgba(98, 54, 255, 0.3)',
};

const secondaryButton = {
  backgroundColor: 'transparent',
  border: '2px solid #6236FF',
  borderRadius: '8px',
  color: '#6236FF',
  fontSize: '14px',
  fontWeight: 'bold',
  textDecoration: 'none',
  textAlign: 'center' as const,
  display: 'inline-block',
  padding: '10px 20px',
  marginTop: '12px',
};

const helpSection = {
  backgroundColor: '#f8f9fa',
  padding: '20px',
  borderRadius: '8px',
  margin: '24px 0',
};

const helpTitle = {
  color: '#333',
  fontSize: '16px',
  fontWeight: 'bold',
  margin: '0 0 8px 0',
};

const helpText = {
  color: '#666',
  fontSize: '14px',
  lineHeight: '20px',
  margin: '0',
};

const hr = {
  borderColor: '#e6e6e6',
  margin: '24px 0',
};

const footer = {
  textAlign: 'center' as const,
};

const footerText = {
  color: '#666',
  fontSize: '12px',
  lineHeight: '16px',
  margin: '0 0 8px 0',
};

const link = {
  color: '#6236FF',
  textDecoration: 'underline',
};

// Styles pour les conseils
const adviceSection = {
  backgroundColor: '#f8f9fa',
  padding: '20px',
  borderRadius: '8px',
  margin: '16px 0',
  border: '1px solid #e9ecef',
};

const adviceHeader = {
  display: 'flex',
  alignItems: 'center',
  marginBottom: '12px',
};

const adviceIcon = {
  marginRight: '12px',
  filter: 'invert(27%) sepia(51%) saturate(2878%) hue-rotate(246deg) brightness(104%) contrast(97%)', // Couleur #6236FF
};

const heartIconStyle = {
  marginRight: '12px',
  // Pas de filtre pour garder la couleur rouge d'origine
};

const adviceTitle = {
  color: '#333',
  fontSize: '16px',
  fontWeight: 'bold',
  margin: '0',
  padding: '0',
};

const adviceText = {
  color: '#555',
  fontSize: '14px',
  lineHeight: '20px',
  margin: '0 0 12px 0',
};

export default WelcomeEmail;
