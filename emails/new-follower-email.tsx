import { Html, Head, Preview, Body, Container, Section, Heading, Text, Button, Hr } from '@react-email/components';
import { EmailHeader } from '@/components/emails/EmailHeader';

interface NewFollowerEmailProps {
  recipientName: string;
  actorName: string;
  translations: {
    subject: string;
    preview: string;
    greeting: string;
    body: string;
    cta: string;
    footer_info: string;
    footer_manage: string;
    footer_copyright: string;
  };
}

export const NewFollowerEmail = ({ recipientName, actorName, translations }: NewFollowerEmailProps) => {
  const bodyText = translations.body.replace('{name}', actorName);

  return (
    <Html>
      <Head>
        {/* Meta tags pour améliorer la délivrabilité */}
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <meta httpEquiv="Content-Type" content="text/html; charset=UTF-8" />
        <meta name="x-apple-disable-message-reformatting" />
        <meta name="format-detection" content="telephone=no,address=no,email=no,date=no,url=no" />
        {/* Titre pour les clients de messagerie */}
        <title>{translations.subject}</title>
      </Head>
      <Preview>{translations.preview}</Preview>
      <Body style={main}>
        <Container style={container}>
          {/* Header avec logo */}
          <EmailHeader />

          {/* Contenu principal */}
          <Section style={content}>
            <Heading style={h2}>{translations.greeting}</Heading>
            <Text style={text}>{bodyText}</Text>

            {/* Section CTA */}
            <Section style={ctaSection}>
              <Button href={`${process.env.NEXTAUTH_URL}/social`} style={primaryButton}>
                {translations.cta}
              </Button>
            </Section>

            <Hr style={hr} />

            {/* Footer optimisé pour la délivrabilité */}
            <Section style={footer}>
              <Text style={footerText}>{translations.footer_info}</Text>

              {/* Liens de gestion des notifications */}
              <Text style={footerText}>
                <Button
                  href={`${process.env.NEXTAUTH_URL}/account`}
                  style={link}
                >
                  {translations.footer_manage}
                </Button>
              </Text>

              {/* Informations légales */}
              <Text style={footerText}>
                Stream2Spin
                <br />
                Notifications sociales
              </Text>

              <Text style={footerText}>{translations.footer_copyright}</Text>
            </Section>
          </Section>
        </Container>
      </Body>
    </Html>
  );
};

// Styles CSS-in-JS optimisés pour la délivrabilité (identiques au template recommendations)
const main = {
  backgroundColor: '#f6f9fc',
  fontFamily: '-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,"Helvetica Neue",Ubuntu,sans-serif',
  margin: '0',
  padding: '24px 0',
  width: '100%',
  wordSpacing: 'normal',
};

const container = {
  backgroundColor: '#ffffff',
  margin: '0 auto',
  padding: '0 0 48px',
  marginBottom: '64px',
  maxWidth: '600px',
  width: '100%',
  border: '1px solid #e6e6e6',
  borderRadius: '12px',
};

const content = {
  padding: '24px',
};

const h2 = {
  color: '#333',
  fontSize: '20px',
  fontWeight: 'bold',
  margin: '0 0 16px 0',
  padding: '0',
};

const text = {
  color: '#333',
  fontSize: '16px',
  lineHeight: '24px',
  margin: '0 0 24px 0',
};

const ctaSection = {
  textAlign: 'center' as const,
  margin: '32px 0',
};

const primaryButton = {
  backgroundColor: '#6236FF',
  borderRadius: '8px',
  color: '#fff',
  fontSize: '16px',
  fontWeight: 'bold',
  textDecoration: 'none',
  textAlign: 'center' as const,
  display: 'inline-block',
  padding: '12px 24px',
};

const hr = {
  borderColor: '#e6e6e6',
  margin: '32px 0',
};

const footer = {
  textAlign: 'center' as const,
};

const footerText = {
  color: '#666',
  fontSize: '12px',
  lineHeight: '16px',
  margin: '0 0 8px 0',
};

const link = {
  color: '#6236FF',
  textDecoration: 'underline',
  backgroundColor: 'transparent',
  border: 'none',
  fontSize: '12px',
};
