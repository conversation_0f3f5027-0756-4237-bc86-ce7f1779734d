/**
 * Template d'email pour les notifications de recommandations sociales
 * Epic 6 - Notifications Utilisateur + Epic Social V1
 */

import {
  Html,
  Head,
  Body,
  Container,
  Section,
  Row,
  Column,
  Heading,
  Text,
  Link,
  Img,
  Button,
  Hr,
  Preview,
} from '@react-email/components';
import { EMAIL_ASSETS } from '@/lib/email-assets';
import { EmailHeader } from '@/components/emails/EmailHeader';

interface EmailRecommendation {
  artistName: string;
  albumTitle: string;
  albumCoverUrl?: string;
  listenScore: number;
  affiliateLinks?: Array<{
    vendor: string;
    url: string;
    price?: number;
    currency?: string;
  }>;
  isOwned: boolean;
}

interface EmailCommunityRecommendation extends EmailRecommendation {
  userName: string;
  userPublicListId: string;
}

interface EmailUserData {
  id: string;
  name?: string;
  email: string;
  preferredLanguage: string;
}

interface SocialRecommendationsEmailProps {
  user: EmailUserData;
  personalRecommendations: EmailRecommendation[];
  communityRecommendations: EmailCommunityRecommendation[];
  totalCount: number;
  unsubscribeUrl: string;
  viewRecommendationsUrl: string;
  t: (key: string, values?: Record<string, any>) => string;
}

const PersonalRecommendationList = ({
  recommendations,
  t,
}: {
  recommendations: EmailRecommendation[];
  t: (key: string, values?: Record<string, any>) => string;
}) => (
  <>
    {recommendations.map((rec, index) => (
      <Section key={index} style={recommendationCard}>
        <Row>
          <Column style={albumCoverColumn}>
            {rec.albumCoverUrl && (
              <Img
                src={rec.albumCoverUrl}
                width="80"
                height="80"
                alt={`${rec.artistName} - ${rec.albumTitle}`}
                style={albumCover}
              />
            )}
          </Column>
          <Column style={albumInfoColumn}>
            <Text style={artistName}>{rec.artistName}</Text>
            <Text style={albumTitle}>{rec.albumTitle}</Text>
            {rec.affiliateLinks && rec.affiliateLinks.length > 0 && (
              <Link
                href={rec.affiliateLinks[0].url}
                style={buyButton}
                target="_blank"
              >
                {t('emails.recommendations.buy_button')}
              </Link>
            )}
          </Column>
          <Column style={wishlistColumn}>
            <Button
              href={`${process.env.NEXTAUTH_URL}/api/wishlist/add-from-email?artist=${encodeURIComponent(rec.artistName)}&album=${encodeURIComponent(rec.albumTitle)}`}
              style={heartButton}
              title={t('emails.recommendations.add_wishlist')}
            >
              <Img
                src={EMAIL_ASSETS.heartIcon}
                width="16"
                height="16"
                alt="♥"
                style={heartIcon}
              />
            </Button>
          </Column>
        </Row>
      </Section>
    ))}
  </>
);

const CommunityRecommendationList = ({
  recommendations,
  t,
}: {
  recommendations: EmailCommunityRecommendation[];
  t: (key: string, values?: Record<string, any>) => string;
}) => (
  <>
    {recommendations.map((rec, index) => (
      <Section key={index} style={recommendationCard}>
        <Row>
          <Column style={albumCoverColumn}>
            {rec.albumCoverUrl && (
              <Img
                src={rec.albumCoverUrl}
                width="80"
                height="80"
                alt={`${rec.artistName} - ${rec.albumTitle}`}
                style={albumCover}
              />
            )}
          </Column>
          <Column style={albumInfoColumn}>
            <Text style={artistName}>{rec.artistName}</Text>
            <Text style={albumTitle}>{rec.albumTitle}</Text>
            <Text style={communityUser}>
              {t('emails.recommendations.recommended_by_prefix')}{' '}
              <Link
                href={`${process.env.NEXTAUTH_URL}/u/${rec.userPublicListId}`}
                style={userNameLink}
              >
                {rec.userName}
              </Link>
            </Text>
            {rec.affiliateLinks && rec.affiliateLinks.length > 0 && (
              <Link
                href={rec.affiliateLinks[0].url}
                style={buyButton}
                target="_blank"
              >
                {t('emails.recommendations.buy_button')}
              </Link>
            )}
          </Column>
          <Column style={wishlistColumn}>
            <Button
              href={`${process.env.NEXTAUTH_URL}/api/wishlist/add-from-email?artist=${encodeURIComponent(rec.artistName)}&album=${encodeURIComponent(rec.albumTitle)}`}
              style={heartButton}
              title={t('emails.recommendations.add_wishlist')}
            >
              <Img
                src={EMAIL_ASSETS.heartIcon}
                width="16"
                height="16"
                alt="♥"
                style={heartIcon}
              />
            </Button>
          </Column>
        </Row>
      </Section>
    ))}
  </>
);

export function SocialRecommendationsEmailTemplate({
  user,
  personalRecommendations,
  communityRecommendations,
  totalCount,
  unsubscribeUrl,
  viewRecommendationsUrl,
  t,
}: SocialRecommendationsEmailProps) {
  return (
    <Html>
      <Head>
        {/* Meta tags pour améliorer la délivrabilité */}
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <meta httpEquiv="Content-Type" content="text/html; charset=UTF-8" />
        <meta name="x-apple-disable-message-reformatting" />
        <meta name="format-detection" content="telephone=no,address=no,email=no,date=no,url=no" />
        {/* Titre pour les clients de messagerie */}
        <title>{t('emails.recommendations.preview', { totalCount })}</title>
      </Head>
      <Preview>{t('emails.recommendations.preview', { totalCount })}</Preview>
      <Body style={main}>
        <Container style={container}>
          <EmailHeader />

          {/* Contenu principal */}
          <Section style={content}>
            <Heading style={h2}>{t('emails.recommendations.greeting', { name: user.name || 'Music Lover' })}</Heading>
            <Text style={text}>{t('emails.recommendations.intro', { totalCount })}</Text>

            {/* Recommandations personnelles */}
            {personalRecommendations.length > 0 && (
              <>
                <Heading style={h3}>{t('emails.recommendations.personal_title')}</Heading>
                <PersonalRecommendationList recommendations={personalRecommendations} t={t} />
              </>
            )}

            {/* Bouton pour voir toutes les recommandations - seulement si il y a des recommandations communautaires */}
            {personalRecommendations.length > 0 && communityRecommendations.length > 0 && (
              <Section style={viewAllSection}>
                <Button href={viewRecommendationsUrl} style={viewAllButton}>
                  {t('emails.recommendations.view_all_button')}
                </Button>
              </Section>
            )}

            {/* Séparateur et recommandations de la communauté */}
            {communityRecommendations.length > 0 && (
              <>
                <Hr style={hr} />
                <Heading style={h3}>{t('emails.recommendations.community_title')}</Heading>
                <CommunityRecommendationList recommendations={communityRecommendations} t={t} />
              </>
            )}

            {/* Call to action */}
            <Section style={ctaSection}>
              <Button href={viewRecommendationsUrl} style={ctaButton}>
                {t('emails.recommendations.view_all_button')}
              </Button>
            </Section>

            {/* Footer */}
            <Hr style={hr} />
            <Text style={footer}>
              {t('emails.recommendations.footer_text')}
            </Text>
            <Text style={footer}>
              <Link href={unsubscribeUrl} style={unsubscribeLink}>
                {t('emails.recommendations.unsubscribe')}
              </Link>
            </Text>
          </Section>
        </Container>
      </Body>
    </Html>
  );
}

// Styles CSS inline pour la compatibilité email
const main = {
  backgroundColor: '#f6f9fc',
  fontFamily: '-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,"Helvetica Neue",Ubuntu,sans-serif',
};

const container = {
  backgroundColor: '#ffffff',
  margin: '0 auto',
  padding: '20px 0 48px',
  marginBottom: '64px',
};

const content = {
  padding: '0 48px',
};

const h2 = {
  color: '#1f2937',
  fontSize: '24px',
  fontWeight: '600',
  lineHeight: '1.25',
  margin: '16px 0',
};

const h3 = {
  color: '#374151',
  fontSize: '18px',
  fontWeight: '600',
  lineHeight: '1.25',
  margin: '24px 0 16px 0',
};

const text = {
  color: '#6b7280',
  fontSize: '16px',
  lineHeight: '1.5',
  margin: '16px 0',
};

const recommendationCard = {
  border: '1px solid #e5e7eb',
  borderRadius: '8px',
  padding: '16px',
  margin: '12px 0',
  backgroundColor: '#ffffff',
};

const albumCoverColumn = {
  width: '80px',
  verticalAlign: 'top',
};

const albumInfoColumn = {
  paddingLeft: '16px',
  verticalAlign: 'top',
};

const wishlistColumn = {
  width: '40px',
  textAlign: 'right' as const,
  verticalAlign: 'top',
};

const albumCover = {
  borderRadius: '4px',
  objectFit: 'cover' as const,
};

const artistName = {
  color: '#1f2937',
  fontSize: '16px',
  fontWeight: '600',
  margin: '0 0 4px 0',
};

const albumTitle = {
  color: '#374151',
  fontSize: '14px',
  margin: '0 0 8px 0',
};

const communityUser = {
  color: '#6366f1',
  fontSize: '12px',
  fontStyle: 'italic',
  margin: '0 0 4px 0',
};

const listenScore = {
  color: '#6b7280',
  fontSize: '12px',
  margin: '0 0 8px 0',
};

const buyButton = {
  backgroundColor: '#6366f1',
  color: '#ffffff',
  fontSize: '12px',
  fontWeight: '600',
  padding: '6px 12px',
  borderRadius: '4px',
  textDecoration: 'none',
  display: 'inline-block',
};

const heartButton = {
  backgroundColor: 'transparent',
  borderRadius: '50%',
  padding: '8px',
  border: 'none',
  cursor: 'pointer',
};

const heartIcon = {
  display: 'block',
};

const viewAllSection = {
  textAlign: 'center' as const,
  margin: '24px 0',
};

const viewAllButton = {
  backgroundColor: '#6366f1',
  color: '#ffffff',
  fontSize: '14px',
  fontWeight: '600',
  padding: '10px 20px',
  borderRadius: '6px',
  textDecoration: 'none',
  display: 'inline-block',
};

const userNameLink = {
  color: '#6366f1',
  textDecoration: 'underline',
  fontWeight: '600',
};

const ctaSection = {
  textAlign: 'center' as const,
  margin: '32px 0',
};

const ctaButton = {
  backgroundColor: '#6366f1',
  color: '#ffffff',
  fontSize: '16px',
  fontWeight: '600',
  padding: '12px 24px',
  borderRadius: '8px',
  textDecoration: 'none',
  display: 'inline-block',
};

const hr = {
  borderColor: '#e5e7eb',
  margin: '24px 0',
};

const footer = {
  color: '#9ca3af',
  fontSize: '12px',
  lineHeight: '1.5',
  margin: '8px 0',
  textAlign: 'center' as const,
};

const unsubscribeLink = {
  color: '#6366f1',
  textDecoration: 'underline',
};
