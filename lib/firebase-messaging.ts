/**
 * Firebase Cloud Messaging (FCM) Service
 * Epic 6 - Notifications Utilisateur
 * 
 * Ce module gère l'envoi de notifications push via Firebase Cloud Messaging
 * pour notifier les utilisateurs de leurs nouvelles recommandations.
 */

import { initializeApp, getApps, cert } from 'firebase-admin/app';
import { getMessaging, Message, MulticastMessage } from 'firebase-admin/messaging';

// Interface pour les données de notification push
export interface PushNotificationData {
  title: string;
  body: string;
  icon?: string;
  badge?: string;
  image?: string;
  data?: Record<string, string>;
  clickAction?: string;
}

// Interface pour les tokens FCM des utilisateurs
export interface UserFCMToken {
  userId: string;
  token: string;
  deviceInfo?: string;
  createdAt: Date;
}

// Initialiser Firebase Admin SDK
function initializeFirebaseAdmin() {
  if (getApps().length === 0) {
    const serviceAccount = {
      projectId: process.env.FIREBASE_PROJECT_ID,
      clientEmail: process.env.FIREBASE_CLIENT_EMAIL,
      privateKey: process.env.FIREBASE_PRIVATE_KEY?.replace(/\\n/g, '\n'),
    };

    if (!serviceAccount.projectId || !serviceAccount.clientEmail || !serviceAccount.privateKey) {
      throw new Error('Firebase Admin configuration is incomplete. Please check FIREBASE_PROJECT_ID, FIREBASE_CLIENT_EMAIL, and FIREBASE_PRIVATE_KEY environment variables.');
    }

    initializeApp({
      credential: cert(serviceAccount),
      projectId: serviceAccount.projectId,
    });
  }
}

/**
 * Envoie une notification push à un utilisateur spécifique
 */
export async function sendPushNotification(
  fcmToken: string,
  notification: PushNotificationData
): Promise<{ success: boolean; messageId?: string; error?: string }> {
  try {
    if (process.env.NODE_ENV === 'development') {
    console.log(`📱 Envoi de notification push à token: ${fcmToken.substring(0, 20)}...`);
    }

    initializeFirebaseAdmin();
    const messaging = getMessaging();

    const message: Message = {
      token: fcmToken,
      notification: {
        title: notification.title,
        body: notification.body,
        imageUrl: notification.image,
      },
      data: notification.data || {},
      webpush: {
        notification: {
          title: notification.title,
          body: notification.body,
          icon: notification.icon || '/Stream2Spin_icon.svg',
          badge: notification.badge || '/Stream2Spin_icon.svg',
          image: notification.image,
          requireInteraction: true,
          actions: [
            {
              action: 'view',
              title: 'Voir les recommandations',
              icon: '/icons/view.png'
            },
            {
              action: 'dismiss',
              title: 'Fermer',
              icon: '/icons/close.png'
            }
          ]
        },
        fcmOptions: {
          link: notification.clickAction || `${process.env.NEXTAUTH_URL}/recommendations`
        }
      }
    };

    const response = await messaging.send(message);
    
    if (process.env.NODE_ENV === 'development') {
    if (process.env.NODE_ENV === 'development') {
    if (process.env.NODE_ENV === 'development') {
    console.log(`✅ Notification push envoyée avec succès (ID: ${response})`);
    }
    }
    }
    return { success: true, messageId: response };

  } catch (error) {
    console.error('❌ Erreur lors de l\'envoi de notification push:', error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    };
  }
}

/**
 * Envoie des notifications push à plusieurs utilisateurs
 */
export async function sendMulticastPushNotification(
  fcmTokens: string[],
  notification: PushNotificationData
): Promise<{ 
  success: boolean; 
  successCount: number; 
  failureCount: number; 
  responses: Array<{ success: boolean; messageId?: string; error?: string }>;
}> {
  try {
    if (process.env.NODE_ENV === 'development') {
    console.log(`📱 Envoi de notifications push à ${fcmTokens.length} appareils`);
    }

    if (fcmTokens.length === 0) {
      return { success: true, successCount: 0, failureCount: 0, responses: [] };
    }

    initializeFirebaseAdmin();
    const messaging = getMessaging();

    const message: MulticastMessage = {
      tokens: fcmTokens,
      notification: {
        title: notification.title,
        body: notification.body,
        imageUrl: notification.image,
      },
      data: notification.data || {},
      webpush: {
        notification: {
          title: notification.title,
          body: notification.body,
          icon: notification.icon || '/Stream2Spin_icon.svg',
          badge: notification.badge || '/Stream2Spin_icon.svg',
          image: notification.image,
          requireInteraction: true,
        },
        fcmOptions: {
          link: notification.clickAction || `${process.env.NEXTAUTH_URL}/recommendations`
        }
      }
    };

    const response = await messaging.sendEachForMulticast(message);
    
    const responses = response.responses.map((resp, index) => ({
      success: resp.success,
      messageId: resp.messageId,
      error: resp.error?.message,
      token: fcmTokens[index].substring(0, 20) + '...'
    }));

    if (process.env.NODE_ENV === 'development') {
    console.log(`📊 Notifications push envoyées: ${response.successCount} succès, ${response.failureCount} échecs`);
    }
    
    return {
      success: response.failureCount === 0,
      successCount: response.successCount,
      failureCount: response.failureCount,
      responses
    };

  } catch (error) {
    console.error('❌ Erreur lors de l\'envoi de notifications push multicast:', error);
    return {
      success: false,
      successCount: 0,
      failureCount: fcmTokens.length,
      responses: fcmTokens.map(token => ({
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        token: token.substring(0, 20) + '...'
      }))
    };
  }
}

/**
 * Génère le contenu de notification selon la langue et le nombre de recommandations
 */
export function generatePushNotificationContent(
  language: string,
  recommendationsCount: number,
  userName?: string
): PushNotificationData {
  const isEnglish = language === 'en';
  
  if (isEnglish) {
    const title = recommendationsCount === 1 
      ? '🎵 New vinyl recommendation!'
      : `🎵 ${recommendationsCount} new vinyl recommendations!`;
    
    const body = userName
      ? `Hi ${userName}! We found ${recommendationsCount} new vinyl${recommendationsCount > 1 ? 's' : ''} based on your Spotify listening.`
      : `We found ${recommendationsCount} new vinyl${recommendationsCount > 1 ? 's' : ''} based on your Spotify listening.`;
    
    return {
      title,
      body,
      icon: '/Stream2Spin_icon.svg',
      badge: '/Stream2Spin_icon.svg',
      data: {
        type: 'recommendations',
        count: recommendationsCount.toString(),
        language
      },
      clickAction: `${process.env.NEXTAUTH_URL}/recommendations`
    };
  }
  
  // Français par défaut
  const title = recommendationsCount === 1 
    ? '🎵 Nouvelle recommandation vinyle !'
    : `🎵 ${recommendationsCount} nouvelles recommandations vinyles !`;
  
  const body = userName
    ? `Salut ${userName} ! Nous avons trouvé ${recommendationsCount} nouveau${recommendationsCount > 1 ? 'x' : ''} vinyle${recommendationsCount > 1 ? 's' : ''} basé${recommendationsCount > 1 ? 's' : ''} sur tes écoutes Spotify.`
    : `Nous avons trouvé ${recommendationsCount} nouveau${recommendationsCount > 1 ? 'x' : ''} vinyle${recommendationsCount > 1 ? 's' : ''} basé${recommendationsCount > 1 ? 's' : ''} sur tes écoutes Spotify.`;
  
  return {
    title,
    body,
    icon: '/Stream2Spin_icon.svg',
    badge: '/Stream2Spin_icon.svg',
    data: {
      type: 'recommendations',
      count: recommendationsCount.toString(),
      language
    },
    clickAction: `${process.env.NEXTAUTH_URL}/recommendations`
  };
}

/**
 * Envoie une notification push de test
 */
export async function sendTestPushNotification(
  fcmToken: string
): Promise<{ success: boolean; messageId?: string; error?: string }> {
  const testNotification: PushNotificationData = {
    title: '🧪 Test Notification - Stream2Spin',
    body: 'Ceci est une notification de test pour vérifier la configuration Firebase.',
    icon: '/Stream2Spin_icon.svg',
    badge: '/Stream2Spin_icon.svg',
    data: {
      type: 'test'
    },
    clickAction: `${process.env.NEXTAUTH_URL}/account`
  };

  return sendPushNotification(fcmToken, testNotification);
}

/**
 * Vérifie la configuration Firebase
 */
export function checkFirebaseConfiguration(): { isValid: boolean; message: string } {
  const projectId = process.env.FIREBASE_PROJECT_ID;
  const clientEmail = process.env.FIREBASE_CLIENT_EMAIL;
  const privateKey = process.env.FIREBASE_PRIVATE_KEY;
  
  if (!projectId) {
    return {
      isValid: false,
      message: 'FIREBASE_PROJECT_ID n\'est pas configuré dans les variables d\'environnement'
    };
  }
  
  if (!clientEmail) {
    return {
      isValid: false,
      message: 'FIREBASE_CLIENT_EMAIL n\'est pas configuré dans les variables d\'environnement'
    };
  }
  
  if (!privateKey) {
    return {
      isValid: false,
      message: 'FIREBASE_PRIVATE_KEY n\'est pas configuré dans les variables d\'environnement'
    };
  }
  
  return {
    isValid: true,
    message: `Configuration Firebase valide pour le projet: ${projectId}`
  };
}
