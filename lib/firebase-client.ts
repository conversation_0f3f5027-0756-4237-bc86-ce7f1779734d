/**
 * Firebase Client Configuration
 * Epic 6 - Notifications Utilisateur
 * 
 * Ce module gère la configuration Firebase côté client
 * et l'inscription aux notifications push.
 */

import { initializeApp, getApps } from 'firebase/app';
import { getMessaging, getToken, onMessage, isSupported } from 'firebase/messaging';

// Configuration Firebase (côté client)
const firebaseConfig = {
  apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY,
  authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,
  projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
  storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,
  appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID,
};

// Initialiser Firebase côté client
let app: any;
if (typeof window !== 'undefined' && getApps().length === 0) {
  app = initializeApp(firebaseConfig);
}

/**
 * Vérifie si les notifications push sont supportées
 */
export async function isPushNotificationSupported(): Promise<boolean> {
  if (typeof window === 'undefined') return false;
  
  try {
    const supported = await isSupported();
    return supported && 'serviceWorker' in navigator && 'PushManager' in window;
  } catch (error) {
    console.error('Erreur lors de la vérification du support des notifications:', error);
    return false;
  }
}

/**
 * Demande la permission pour les notifications push
 */
export async function requestNotificationPermission(): Promise<{
  granted: boolean;
  permission: NotificationPermission;
  error?: string;
}> {
  if (typeof window === 'undefined') {
    return { granted: false, permission: 'default', error: 'Not in browser environment' };
  }

  try {
    const permission = await Notification.requestPermission();
    
    return {
      granted: permission === 'granted',
      permission,
      error: permission === 'denied' ? 'Permission denied by user' : undefined
    };
  } catch (error) {
    console.error('Erreur lors de la demande de permission:', error);
    return {
      granted: false,
      permission: 'default',
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Obtient le token FCM pour cet appareil
 */
export async function getFCMToken(): Promise<{
  token?: string;
  error?: string;
}> {
  if (typeof window === 'undefined') {
    return { error: 'Not in browser environment' };
  }

  try {
    const supported = await isPushNotificationSupported();
    if (!supported) {
      return { error: 'Push notifications not supported' };
    }

    const permission = await requestNotificationPermission();
    if (!permission.granted) {
      return { error: 'Notification permission not granted' };
    }

    const messaging = getMessaging(app);
    const vapidKey = process.env.NEXT_PUBLIC_FIREBASE_VAPID_KEY;

    if (!vapidKey) {
      return { error: 'VAPID key not configured' };
    }

    const token = await getToken(messaging, {
      vapidKey: vapidKey
    });

    if (!token) {
      return { error: 'Failed to get FCM token' };
    }

    if (process.env.NODE_ENV === 'development') {
    console.log('✅ Token FCM obtenu:', token.substring(0, 20) + '...');
    }
    return { token };

  } catch (error) {
    console.error('❌ Erreur lors de l\'obtention du token FCM:', error);
    return { 
      error: error instanceof Error ? error.message : 'Unknown error' 
    };
  }
}

/**
 * Configure l'écoute des messages en premier plan
 */
export function setupForegroundMessageListener(
  onMessageReceived: (payload: any) => void
): () => void {
  if (typeof window === 'undefined') {
    return () => {};
  }

  try {
    const messaging = getMessaging(app);
    
    const unsubscribe = onMessage(messaging, (payload) => {
      if (process.env.NODE_ENV === 'development') {
      console.log('📱 Message reçu en premier plan:', payload);
      }
      onMessageReceived(payload);
    });

    return unsubscribe;
  } catch (error) {
    console.error('❌ Erreur lors de la configuration de l\'écoute des messages:', error);
    return () => {};
  }
}

/**
 * Enregistre le service worker Firebase
 */
export async function registerFirebaseServiceWorker(): Promise<{
  success: boolean;
  registration?: ServiceWorkerRegistration;
  error?: string;
}> {
  if (typeof window === 'undefined' || !('serviceWorker' in navigator)) {
    return { success: false, error: 'Service Worker not supported' };
  }

  try {
    const registration = await navigator.serviceWorker.register('/firebase-messaging-sw.js');
    
    if (process.env.NODE_ENV === 'development') {
    console.log('✅ Service Worker Firebase enregistré:', registration);
    }
    
    // Attendre que le service worker soit prêt
    await navigator.serviceWorker.ready;
    
    return { success: true, registration };
  } catch (error) {
    console.error('❌ Erreur lors de l\'enregistrement du service worker:', error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    };
  }
}

/**
 * Sauvegarde le token FCM pour l'utilisateur actuel
 */
export async function saveFCMTokenForUser(token: string): Promise<{
  success: boolean;
  error?: string;
}> {
  try {
    const response = await fetch('/api/user/fcm-token', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ 
        token,
        deviceInfo: navigator.userAgent
      }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      return { success: false, error: errorData.error || 'Failed to save token' };
    }

    if (process.env.NODE_ENV === 'development') {
    console.log('✅ Token FCM sauvegardé pour l\'utilisateur');
    }
    return { success: true };

  } catch (error) {
    console.error('❌ Erreur lors de la sauvegarde du token FCM:', error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    };
  }
}

/**
 * Initialise complètement les notifications push pour l'utilisateur
 */
export async function initializePushNotifications(): Promise<{
  success: boolean;
  token?: string;
  error?: string;
}> {
  try {
    if (process.env.NODE_ENV === 'development') {
    console.log('🔄 Initialisation des notifications push...');
    }

    // 1. Vérifier le support
    const supported = await isPushNotificationSupported();
    if (!supported) {
      return { success: false, error: 'Push notifications not supported' };
    }

    // 2. Enregistrer le service worker
    const swResult = await registerFirebaseServiceWorker();
    if (!swResult.success) {
      return { success: false, error: swResult.error };
    }

    // 3. Obtenir le token FCM
    const tokenResult = await getFCMToken();
    if (!tokenResult.token) {
      return { success: false, error: tokenResult.error };
    }

    // 4. Sauvegarder le token pour l'utilisateur
    const saveResult = await saveFCMTokenForUser(tokenResult.token);
    if (!saveResult.success) {
      return { success: false, error: saveResult.error };
    }

    if (process.env.NODE_ENV === 'development') {
    console.log('✅ Notifications push initialisées avec succès');
    }
    return { success: true, token: tokenResult.token };

  } catch (error) {
    console.error('❌ Erreur lors de l\'initialisation des notifications push:', error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    };
  }
}

/**
 * Vérifie la configuration Firebase côté client
 */
export function checkFirebaseClientConfiguration(): { isValid: boolean; message: string } {
  const requiredVars = [
    'NEXT_PUBLIC_FIREBASE_API_KEY',
    'NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN',
    'NEXT_PUBLIC_FIREBASE_PROJECT_ID',
    'NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID',
    'NEXT_PUBLIC_FIREBASE_APP_ID',
    'NEXT_PUBLIC_FIREBASE_VAPID_KEY'
  ];

  const missingVars = requiredVars.filter(varName => !process.env[varName]);

  if (missingVars.length > 0) {
    return {
      isValid: false,
      message: `Variables d'environnement manquantes: ${missingVars.join(', ')}`
    };
  }

  return {
    isValid: true,
    message: 'Configuration Firebase client valide'
  };
}
