import { drizzle } from 'drizzle-orm/postgres-js';
import postgres from 'postgres';
import * as schema from './schema';

// Export du schéma pour l'utilisation dans l'application
export * from './schema';

// Types pour l'API query
export type DbType = ReturnType<typeof drizzle<typeof schema>>;

// Connexion à la base de données Supabase
const databaseUrl = process.env.DATABASE_URL;

if (!databaseUrl) {
  throw new Error('DATABASE_URL manquante. Utilisez la "Transaction pooler" connection string de Supabase.');
}

// Assertion de type pour TypeScript
const dbUrl: string = databaseUrl;

if (process.env.NODE_ENV === 'development') {
console.log('🚀 Connexion DB optimisée (Transaction Pooler):', dbUrl.replace(/:[^:@]*@/, ':***@'));
}

// 🚀 Cache global ultra-optimisé pour éviter TOUTES les reconnexions
let globalClient: postgres.Sql | null = null;
let globalDb: ReturnType<typeof drizzle> | null = null;
let isInitialized = false;

// 🚀 Pool de connexions pré-initialisé
const connectionPool = new Map<string, postgres.Sql>();

// 🚀 Fonction ultra-optimisée pour obtenir le client DB
function getDbClient() {
  if (!globalClient || !isInitialized) {
    const poolKey = `${dbUrl}_main`;

    // Vérifier le pool de connexions d'abord
    if (connectionPool.has(poolKey)) {
      globalClient = connectionPool.get(poolKey)!;
      if (process.env.NODE_ENV === 'development') {
      console.log('🚀 Réutilisation connexion DB depuis le pool');
      }
    } else {
      if (process.env.NODE_ENV === 'development') {
      console.log('🔄 Création nouvelle connexion DB ultra-optimisée...');
      }

      // Configuration ultra-optimisée pour réduire la latence
      globalClient = postgres(dbUrl, {
        prepare: false,
        ssl: 'require',

        // 🚀 Pool optimisé adapté à l'environnement
        max: process.env.NODE_ENV === 'production' ? 5 : 10, // Plus conservateur en production
        idle_timeout: process.env.NODE_ENV === 'production' ? 60 : 120, // Plus court en production
        max_lifetime: 60 * 60, // 1 heure pour éviter les connexions trop longues

        // 🚀 Optimisations de connexion
        connect_timeout: 3, // Timeout de connexion rapide

        // 🚀 Cache des requêtes préparées optimisé
        transform: {
          undefined: null
        },

        // 🚀 Optimisations de performance
        fetch_types: false, // Éviter les requêtes de métadonnées
      });

      // Ajouter au pool
      connectionPool.set(poolKey, globalClient);
    }

    isInitialized = true;
  }
  return globalClient;
}

// 🚀 Fonction pour obtenir l'instance Drizzle avec cache
function getDb(): DbType {
  if (!globalDb) {
    if (process.env.NODE_ENV === 'development') {
    console.log('🔄 Création nouvelle instance Drizzle...');
    }
    globalDb = drizzle(getDbClient(), {
      schema,
      logger: process.env.NODE_ENV === 'development'
    }) as DbType;
  }
  return globalDb as DbType;
}

// Instance drizzle avec le schéma (singleton)
export const db = getDb();