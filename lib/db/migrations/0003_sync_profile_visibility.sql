-- Migration pour synchroniser profileVisibility avec publicListEnabled
-- Date: 2024-12-19
-- Description: Met à jour profileVisibility basé sur publicListEnabled pour assurer la cohérence des données

-- Mettre à jour les utilisateurs avec publicListEnabled = true vers profileVisibility = 'public'
UPDATE users 
SET profile_visibility = 'public' 
WHERE public_list_enabled = true 
  AND (profile_visibility IS NULL OR profile_visibility != 'public');

-- Mettre à jour les utilisateurs avec publicListEnabled = false vers profileVisibility = 'internal'
UPDATE users 
SET profile_visibility = 'internal' 
WHERE public_list_enabled = false 
  AND (profile_visibility IS NULL OR profile_visibility != 'internal');

-- Ajouter un commentaire pour documenter la migration
COMMENT ON COLUMN users.profile_visibility IS 'Synchronisé avec public_list_enabled: true=public, false=internal'; 