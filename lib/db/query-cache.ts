/**
 * Cache intelligent pour les requêtes DB fréquentes
 * Réduit drastiquement les appels DB répétitifs
 */

interface CacheEntry<T> {
  data: T;
  timestamp: number;
  ttl: number;
}

class QueryCache {
  private cache = new Map<string, CacheEntry<any>>();
  // TTL adapté à l'environnement pour la sécurité en production
  private readonly DEFAULT_TTL = process.env.NODE_ENV === 'production' ? 5 * 60 * 1000 : 10 * 60 * 1000; // 5min prod, 10min dev
  private readonly SESSION_TTL = process.env.NODE_ENV === 'production' ? 10 * 60 * 1000 : 30 * 60 * 1000; // 10min prod, 30min dev

  /**
   * Récupère une valeur du cache
   */
  get<T>(key: string): T | null {
    const entry = this.cache.get(key);
    
    if (!entry) {
      return null;
    }

    // Vérifier si le cache est expiré
    if (Date.now() - entry.timestamp > entry.ttl) {
      this.cache.delete(key);
      return null;
    }

    return entry.data;
  }

  /**
   * Met en cache une valeur
   */
  set<T>(key: string, data: T, ttl: number = this.DEFAULT_TTL): void {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl
    });
  }

  /**
   * Invalide une clé spécifique
   */
  invalidate(key: string): void {
    this.cache.delete(key);
  }

  /**
   * Invalide toutes les clés qui matchent un pattern
   */
  invalidatePattern(pattern: string): void {
    for (const key of this.cache.keys()) {
      if (key.includes(pattern)) {
        this.cache.delete(key);
      }
    }
  }

  /**
   * Nettoie les entrées expirées
   */
  cleanup(): void {
    const now = Date.now();
    let cleaned = 0;

    for (const [key, entry] of this.cache.entries()) {
      if (now - entry.timestamp > entry.ttl) {
        this.cache.delete(key);
        cleaned++;
      }
    }

    if (cleaned > 0) {
      if (process.env.NODE_ENV === 'development') {
      console.log(`🧹 Cache DB nettoyé: ${cleaned} entrées expirées`);
      }
    }
  }

  /**
   * Statistiques du cache
   */
  getStats() {
    const now = Date.now();
    let validEntries = 0;
    let expiredEntries = 0;

    for (const entry of this.cache.values()) {
      if (now - entry.timestamp > entry.ttl) {
        expiredEntries++;
      } else {
        validEntries++;
      }
    }

    return {
      totalEntries: this.cache.size,
      validEntries,
      expiredEntries,
      hitRate: this.cache.size > 0 ? (validEntries / this.cache.size * 100).toFixed(2) + '%' : '0%'
    };
  }
}

// Instance singleton du cache
export const queryCache = new QueryCache();

// Nettoyage automatique toutes les 10 minutes
if (typeof window === 'undefined') { // Côté serveur seulement
  setInterval(() => {
    queryCache.cleanup();
  }, 10 * 60 * 1000);
}

/**
 * Helper pour cacher les requêtes utilisateur avec TTL optimisé
 */
export function cacheUserQuery<T>(
  userId: string,
  queryType: string,
  data: T,
  ttl?: number // TTL automatique selon le type
): void {
  const key = `user:${userId}:${queryType}`;

  // TTL optimisé selon le type de données
  let optimizedTtl = ttl;
  if (!optimizedTtl) {
    switch (queryType) {
      case 'profile':
        optimizedTtl = queryCache['SESSION_TTL']; // TTL adapté à l'environnement
        break;
      case 'recommendations':
        optimizedTtl = process.env.NODE_ENV === 'production' ? 3 * 60 * 1000 : 5 * 60 * 1000; // Plus court en production
        break;
      case 'collection':
        optimizedTtl = process.env.NODE_ENV === 'production' ? 10 * 60 * 1000 : 15 * 60 * 1000; // Plus court en production
        break;
      default:
        optimizedTtl = queryCache['DEFAULT_TTL']; // TTL adapté à l'environnement
    }
  }

  queryCache.set(key, data, optimizedTtl);
}

/**
 * Helper pour récupérer les requêtes utilisateur cachées
 */
export function getCachedUserQuery<T>(userId: string, queryType: string): T | null {
  const key = `user:${userId}:${queryType}`;
  return queryCache.get<T>(key);
}

/**
 * Helper pour invalider le cache d'un utilisateur
 */
export function invalidateUserCache(userId: string): void {
  queryCache.invalidatePattern(`user:${userId}`);
  if (process.env.NODE_ENV === 'development') {
  console.log(`🗑️ Cache DB invalidé pour l'utilisateur: ${userId}`);
  }
}
