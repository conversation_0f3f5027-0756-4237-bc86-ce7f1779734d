import { drizzle } from 'drizzle-orm/postgres-js';
import { migrate } from 'drizzle-orm/postgres-js/migrator';
import postgres from 'postgres';

// Connexion pour les migrations
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !serviceRoleKey) {
  throw new Error('Variables d\'environnement Supabase manquantes');
}

const projectId = supabaseUrl.replace('https://', '').replace('.supabase.co', '');
const connectionString = `postgres://postgres.${projectId}:${serviceRoleKey}@aws-0-eu-central-1.pooler.supabase.com:6543/postgres`;

const migrationClient = postgres(connectionString, { max: 1 });
const db = drizzle(migrationClient);

async function main() {
  if (process.env.NODE_ENV === 'development') {
  console.log('Migration en cours...');
  }
  await migrate(db, { migrationsFolder: 'drizzle' });
  if (process.env.NODE_ENV === 'development') {
  console.log('Migration terminée !');
  }
  process.exit(0);
}

main().catch((err) => {
  console.error('Erreur de migration:', err);
  process.exit(1);
});
