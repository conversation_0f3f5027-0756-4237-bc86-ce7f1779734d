/**
 * Helpers pour simuler l'API db.query avec la syntaxe select()
 * Solution temporaire pour corriger les erreurs de build
 */

import { eq, and } from 'drizzle-orm';
import { db } from './index';
import { users, accounts, recommendations, userDiscogsCollection, wishlistItems, sessions } from './schema';

/**
 * Helper pour les requêtes utilisateur
 */
export const queryHelpers = {
  users: {
    async findFirst(options: { where: any }) {
      const result = await db.select().from(users).where(options.where).limit(1);
      return result[0] || null;
    },
    
    async findMany(options?: { where?: any; limit?: number }) {
      let query: any = db.select().from(users);
      if (options?.where) {
        query = query.where(options.where);
      }
      if (options?.limit) {
        query = query.limit(options.limit);
      }
      return await query;
    }
  },

  accounts: {
    async findFirst(options: { where: any }) {
      const result = await db.select().from(accounts).where(options.where).limit(1);
      return result[0] || null;
    },
    
    async findMany(options?: { where?: any; limit?: number }) {
      let query: any = db.select().from(accounts);
      if (options?.where) {
        query = query.where(options.where);
      }
      if (options?.limit) {
        query = query.limit(options.limit);
      }
      return await query;
    }
  },

  recommendations: {
    async findFirst(options: { where: any }) {
      const result = await db.select().from(recommendations).where(options.where).limit(1);
      return result[0] || null;
    },
    
    async findMany(options?: { where?: any; limit?: number; orderBy?: any }) {
      let query: any = db.select().from(recommendations);
      if (options?.where) {
        query = query.where(options.where);
      }
      if (options?.orderBy) {
        query = query.orderBy(options.orderBy);
      }
      if (options?.limit) {
        query = query.limit(options.limit);
      }
      return await query;
    }
  },

  userDiscogsCollection: {
    async findFirst(options: { where: any }) {
      const result = await db.select().from(userDiscogsCollection).where(options.where).limit(1);
      return result[0] || null;
    },
    
    async findMany(options?: { where?: any; limit?: number }) {
      let query: any = db.select().from(userDiscogsCollection);
      if (options?.where) {
        query = query.where(options.where);
      }
      if (options?.limit) {
        query = query.limit(options.limit);
      }
      return await query;
    }
  },

  wishlistItems: {
    async findFirst(options: { where: any }) {
      const result = await db.select().from(wishlistItems).where(options.where).limit(1);
      return result[0] || null;
    },
    
    async findMany(options?: { where?: any; limit?: number; orderBy?: any }) {
      let query: any = db.select().from(wishlistItems);
      if (options?.where) {
        query = query.where(options.where);
      }
      if (options?.orderBy) {
        query = query.orderBy(options.orderBy);
      }
      if (options?.limit) {
        query = query.limit(options.limit);
      }
      return await query;
    }
  },

  sessions: {
    async findFirst(options: { where: any }) {
      const result = await db.select().from(sessions).where(options.where).limit(1);
      return result[0] || null;
    },
    
    async findMany(options?: { where?: any; limit?: number }) {
      let query: any = db.select().from(sessions);
      if (options?.where) {
        query = query.where(options.where);
      }
      if (options?.limit) {
        query = query.limit(options.limit);
      }
      return await query;
    }
  }
};

/**
 * Extension de l'objet db pour inclure l'API query simulée
 */
export const dbWithQuery = {
  ...db,
  query: queryHelpers
};
