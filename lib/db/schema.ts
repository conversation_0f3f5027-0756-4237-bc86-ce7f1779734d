import { pgTable, text, timestamp, integer, primaryKey, bigserial, boolean, jsonb, bigint, unique, index, pgEnum } from 'drizzle-orm/pg-core';
import { relations } from 'drizzle-orm';

// ENUMs pour les fonctionnalités sociales (Epic Social V1)
export const profileVisibilityEnum = pgEnum('profile_visibility', ['private', 'users_only', 'public']);
export const notificationTypeEnum = pgEnum('notification_type', ['new_follower']);

// Table des utilisateurs (compatible avec l'adaptateur Drizzle)
// Seul Spotify OAuth est utilisé, donc pas besoin du champ password
export const users = pgTable('users', {
  id: text('id').primaryKey().$defaultFn(() => crypto.randomUUID()),
  name: text('name'),
  email: text('email').unique(), // Email provenant de Spotify
  emailVerified: timestamp('emailVerified', { mode: 'date' }),
  image: text('image'), // Image de profil Spotify
  preferredLanguage: text('preferredLanguage').default('fr').notNull(),
  emailFrequency: text('emailFrequency').default('weekly').notNull(),
  pushFrequency: text('pushFrequency').default('weekly').notNull(),
  firstRecommendationEmailSent: boolean('firstRecommendationEmailSent').default(false).notNull(),
  // Epic 16: Partage public des recommandations
  publicListEnabled: boolean('publicListEnabled').default(false).notNull(),
  publicListId: text('publicListId').unique().$defaultFn(() => crypto.randomUUID()),

  // --- Colonnes pour les fonctionnalités sociales (Epic Social V1) ---
  profileVisibility: profileVisibilityEnum('profile_visibility').default('users_only').notNull(),
  shareRecommendations: boolean('share_recommendations').default(true).notNull(),
  shareWishlist: boolean('share_wishlist').default(true).notNull(),
  shareCollection: boolean('share_collection').default(true).notNull(),
  emailOnNewFollower: boolean('email_on_new_follower').default(true).notNull(),

  // ... reste des colonnes existantes (publicProfileEnabled, etc.)

  // Epic 17: Profil public granulaire et social
  publicProfileEnabled: boolean('publicProfileEnabled').default(true).notNull(),
  publicRecommendationsEnabled: boolean('publicRecommendationsEnabled').default(true).notNull(),
  publicWishlistEnabled: boolean('publicWishlistEnabled').default(false).notNull(),
  publicCollectionEnabled: boolean('publicCollectionEnabled').default(false).notNull(),
  // Tracking de délivrabilité des emails
  emailNotificationsEnabled: boolean('email_notifications_enabled').default(true),
  lastEmailSent: timestamp('last_email_sent', { mode: 'date' }),
  lastEmailBounce: timestamp('last_email_bounce', { mode: 'date' }),
  emailBounceReason: text('email_bounce_reason'),
  lastEmailComplaint: timestamp('last_email_complaint', { mode: 'date' }),
  emailComplaintReason: text('email_complaint_reason'),
  lastEmailDelivered: timestamp('last_email_delivered', { mode: 'date' }),
  lastEmailOpened: timestamp('last_email_opened', { mode: 'date' }),
  lastEmailClicked: timestamp('last_email_clicked', { mode: 'date' }),
  emailDeliverabilityScore: integer('email_deliverability_score').default(100),
  timezone: text('timezone').default('Europe/Paris'),

  createdAt: timestamp('createdAt', { mode: 'date' }).defaultNow(),
  updatedAt: timestamp('updatedAt', { mode: 'date' }).defaultNow(),
});

// Table des analytics pour les listes publiques (Epic 16)
export const publicListAnalytics = pgTable('public_list_analytics', {
  id: text('id').primaryKey().$defaultFn(() => crypto.randomUUID()),
  userId: text('userId').notNull().references(() => users.id, { onDelete: 'cascade' }),
  publicListId: text('publicListId').notNull().references(() => users.publicListId),
  eventType: text('eventType').notNull(), // 'view', 'share', 'signup_click', 'signup_conversion'
  eventData: jsonb('eventData'), // Données supplémentaires (referrer, user-agent, etc.)
  ipAddress: text('ipAddress'), // Pour éviter les doublons de vues
  userAgent: text('userAgent'),
  referrer: text('referrer'),
  timestamp: timestamp('timestamp', { mode: 'date' }).defaultNow().notNull(),
}, (analytics) => ({
  userIdIdx: index('idx_analytics_userId').on(analytics.userId),
  publicListIdIdx: index('idx_analytics_publicListId').on(analytics.publicListId),
  eventTypeIdx: index('idx_analytics_eventType').on(analytics.eventType),
  timestampIdx: index('idx_analytics_timestamp').on(analytics.timestamp),
}));

/**
 * Table pour gérer les relations de suivi entre utilisateurs.
 */
export const followers = pgTable('followers', {
  followerId: text('follower_id').notNull().references(() => users.id, { onDelete: 'cascade' }),
  followingId: text('following_id').notNull().references(() => users.id, { onDelete: 'cascade' }),
  createdAt: timestamp('created_at', { mode: 'date' }).defaultNow().notNull(),
}, (table) => ({
  pk: primaryKey({ columns: [table.followerId, table.followingId] }),
}));

/**
 * Table pour les notifications in-app et email.
 */
export const notifications = pgTable('notifications', {
  id: text('id').primaryKey().$defaultFn(() => crypto.randomUUID()),
  recipientId: text('recipient_id').notNull().references(() => users.id, { onDelete: 'cascade' }),
  actorId: text('actor_id').notNull().references(() => users.id, { onDelete: 'cascade' }),
  type: notificationTypeEnum('type').notNull(),
  isRead: boolean('is_read').default(false).notNull(),
  createdAt: timestamp('created_at', { mode: 'date' }).defaultNow().notNull(),
});

/**
 * Table pour archiver toutes les recommandations et nourrir l'algorithme de matching.
 */
export const recommendationHistory = pgTable('recommendation_history', {
  id: bigserial('id', { mode: 'number' }).primaryKey(),
  userId: text('user_id').notNull().references(() => users.id, { onDelete: 'cascade' }),
  artistName: text('artist_name').notNull(),
  albumTitle: text('album_title').notNull(),
  spotifyAlbumId: text('spotify_album_id'),
  listenScore: integer('listen_score').notNull(),
  timeframe: text('timeframe').notNull(),
  generatedAt: timestamp('generated_at', { mode: 'date' }).notNull().defaultNow(),
  // On pourra ajouter ici les caractéristiques audio (danceability, energy, etc.) dans une future itération.
});

// Table des comptes (pour OAuth) - compatible avec l'adaptateur Drizzle
export const accounts = pgTable('accounts', {
  userId: text('userId').notNull().references(() => users.id, { onDelete: 'cascade' }),
  type: text('type').notNull(),
  provider: text('provider').notNull(),
  providerAccountId: text('providerAccountId').notNull(),
  refresh_token: text('refresh_token'),
  access_token: text('access_token'),
  access_token_secret: text('access_token_secret'), // Pour OAuth 1.0a (Discogs)
  expires_at: integer('expires_at'),
  token_type: text('token_type'),
  scope: text('scope'),
  id_token: text('id_token'),
  session_state: text('session_state'),
}, (account) => ({
  compoundKey: primaryKey({
    columns: [account.provider, account.providerAccountId],
  }),
}));

// Table des sessions - compatible avec l'adaptateur Drizzle
export const sessions = pgTable('sessions', {
  sessionToken: text('sessionToken').primaryKey(),
  userId: text('userId').notNull().references(() => users.id, { onDelete: 'cascade' }),
  expires: timestamp('expires', { mode: 'date' }).notNull(),
});

// Table des tokens de vérification - compatible avec l'adaptateur Drizzle
export const verificationTokens = pgTable('verificationTokens', {
  identifier: text('identifier').notNull(),
  token: text('token').notNull(),
  expires: timestamp('expires', { mode: 'date' }).notNull(),
}, (vt) => ({
  compoundKey: primaryKey({ columns: [vt.identifier, vt.token] }),
}));

// Relations pour les requêtes avec jointures
export const usersRelations = relations(users, ({ many }) => ({
  accounts: many(accounts),
  sessions: many(sessions),
  recommendations: many(recommendations),
  discogsCollection: many(userDiscogsCollection),
  fcmTokens: many(userFCMTokens),
  // Relations sociales (Epic Social V1)
  followers: many(followers, { relationName: 'follower' }),
  following: many(followers, { relationName: 'following' }),
  notificationsReceived: many(notifications, { relationName: 'recipient' }),
  notificationsSent: many(notifications, { relationName: 'actor' }),
  recommendationHistory: many(recommendationHistory),
}));

export const accountsRelations = relations(accounts, ({ one }) => ({
  user: one(users, {
    fields: [accounts.userId],
    references: [users.id],
  }),
}));

export const sessionsRelations = relations(sessions, ({ one }) => ({
  user: one(users, {
    fields: [sessions.userId],
    references: [users.id],
  }),
}));

// Table des recommandations - Epic 3
export const recommendations = pgTable('recommendations', {
  id: bigserial('id', { mode: 'number' }).primaryKey(),
  userId: text('userId').notNull().references(() => users.id, { onDelete: 'cascade' }),
  artistName: text('artistName').notNull(),
  albumTitle: text('albumTitle').notNull(),
  albumCoverUrl: text('albumCoverUrl'),
  discogsReleaseId: bigint('discogsReleaseId', { mode: 'number' }),
  spotifyAlbumId: text('spotifyAlbumId'),
  listenScore: integer('listenScore').notNull(),
  estimatedPlays: integer('estimatedPlays'), // Nombre d'écoutes estimé
  timeframe: text('timeframe').notNull(), // 'short_term', 'medium_term', 'long_term'
  generatedAt: timestamp('generatedAt', { mode: 'date' }).notNull().defaultNow(),
  isOwned: boolean('isOwned').notNull().default(false),
  affiliateLinks: jsonb('affiliateLinks'), // Array of {vendor, url, price, currency}
  // US 3.6: Informations du titre phare pour l'extrait audio et Spotify Embed
  topTrackName: text('topTrackName'),
  topTrackId: text('topTrackId'), // ID Spotify du track pour l'embed
  topTrackPreviewUrl: text('topTrackPreviewUrl'), // Conservé pour compatibilité
  topTrackListenScore: integer('topTrackListenScore'),
}, (table) => ({
  // Contrainte unique pour éviter les doublons (userId, spotifyAlbumId, timeframe)
  userAlbumTimeframeUnique: unique().on(table.userId, table.spotifyAlbumId, table.timeframe),
}));

// Table de la collection Discogs de l'utilisateur - Epic 2/3
export const userDiscogsCollection = pgTable('user_discogs_collection', {
  id: bigserial('id', { mode: 'number' }).primaryKey(),
  userId: text('userId').notNull().references(() => users.id, { onDelete: 'cascade' }),
  discogsReleaseId: bigint('discogsReleaseId', { mode: 'number' }).notNull(),
  artistName: text('artistName').notNull(),
  albumTitle: text('albumTitle').notNull(),
  albumCoverUrl: text('albumCoverUrl'), // URL de l'image de la pochette
  year: integer('year'),
  format: text('format'), // 'Vinyl', 'CD', etc.
  syncedAt: timestamp('syncedAt', { mode: 'date' }).notNull().defaultNow(),
}, (table) => ({
  // Contrainte unique pour éviter les doublons (userId, discogsReleaseId)
  userReleaseUnique: unique().on(table.userId, table.discogsReleaseId),
}));

// Table des tokens FCM pour les notifications push - Epic 6
export const userFCMTokens = pgTable('user_fcm_tokens', {
  id: bigserial('id', { mode: 'number' }).primaryKey(),
  userId: text('userId').notNull().references(() => users.id, { onDelete: 'cascade' }),
  token: text('token').notNull(),
  deviceInfo: text('deviceInfo'), // User agent ou info sur l'appareil
  isActive: boolean('isActive').notNull().default(true),
  createdAt: timestamp('createdAt', { mode: 'date' }).notNull().defaultNow(),
  lastUsedAt: timestamp('lastUsedAt', { mode: 'date' }).notNull().defaultNow(),
}, (table) => ({
  // Contrainte unique pour éviter les doublons (userId, token)
  userTokenUnique: unique().on(table.userId, table.token),
}));

// Table de la wishlist - Epic 9 (basée sur l'album, pas sur la recommandation spécifique)
export const wishlistItems = pgTable('wishlist_items', {
  userId: text('userId').notNull().references(() => users.id, { onDelete: 'cascade' }),
  artistName: text('artistName').notNull(),
  albumTitle: text('albumTitle').notNull(),
  albumCoverUrl: text('album_cover_url'), // URL de la pochette d'album
  spotifyAlbumId: text('spotify_album_id'), // ID Spotify de l'album
  discogsReleaseId: bigint('discogs_release_id', { mode: 'number' }), // ID Discogs de l'album
  affiliateLinks: jsonb('affiliate_links'), // Liens d'affiliation Amazon
  topTrackName: text('top_track_name'), // Nom de la top track
  topTrackId: text('top_track_id'), // ID Spotify de la top track
  topTrackPreviewUrl: text('top_track_preview_url'), // URL de preview de la top track
  topTrackListenScore: integer('top_track_listen_score'), // Score d'écoute de la top track
  originalUserName: text('original_user_name'), // Nom de l'utilisateur d'origine (pour les ajouts depuis profils publics)
  createdAt: timestamp('createdAt', { mode: 'date' }).notNull().defaultNow(),
}, (table) => ({
  // Clé primaire composite pour empêcher un utilisateur d'ajouter deux fois le même album
  pk: primaryKey({ columns: [table.userId, table.artistName, table.albumTitle] }),
}));

// Relations pour les recommandations
export const recommendationsRelations = relations(recommendations, ({ one }) => ({
  user: one(users, {
    fields: [recommendations.userId],
    references: [users.id],
  }),
}));

// Relations pour la collection Discogs
export const userDiscogsCollectionRelations = relations(userDiscogsCollection, ({ one }) => ({
  user: one(users, {
    fields: [userDiscogsCollection.userId],
    references: [users.id],
  }),
}));

// Relations pour les tokens FCM
export const userFCMTokensRelations = relations(userFCMTokens, ({ one }) => ({
  user: one(users, {
    fields: [userFCMTokens.userId],
    references: [users.id],
  }),
}));

// Relations pour la wishlist (basée sur l'album)
export const wishlistItemsRelations = relations(wishlistItems, ({ one }) => ({
  user: one(users, {
    fields: [wishlistItems.userId],
    references: [users.id],
  }),
}));

// Table des événements d'email pour le tracking de délivrabilité
export const emailEvents = pgTable('email_events', {
  id: bigserial('id', { mode: 'number' }).primaryKey(),
  userId: text('userId').notNull().references(() => users.id, { onDelete: 'cascade' }),
  emailId: text('emailId').notNull(), // ID de l'email depuis Resend
  eventType: text('eventType').notNull(), // sent, delivered, bounced, complained, opened, clicked
  eventData: jsonb('eventData'), // Données supplémentaires de l'événement
  createdAt: timestamp('createdAt', { mode: 'date' }).defaultNow().notNull(),
}, (events) => ({
  userIdIdx: index('idx_email_events_userId').on(events.userId),
  eventTypeIdx: index('idx_email_events_eventType').on(events.eventType),
  createdAtIdx: index('idx_email_events_createdAt').on(events.createdAt),
}));

// Table des domaines en liste noire
export const emailDomainBlacklist = pgTable('email_domain_blacklist', {
  id: bigserial('id', { mode: 'number' }).primaryKey(),
  domain: text('domain').unique().notNull(),
  reason: text('reason'),
  addedAt: timestamp('addedAt', { mode: 'date' }).defaultNow().notNull(),
  isActive: boolean('isActive').default(true).notNull(),
});

// Table des métriques de délivrabilité globales
export const emailDeliverabilityMetrics = pgTable('email_deliverability_metrics', {
  id: bigserial('id', { mode: 'number' }).primaryKey(),
  date: timestamp('date', { mode: 'date' }).notNull(),
  emailsSent: integer('emailsSent').default(0).notNull(),
  emailsDelivered: integer('emailsDelivered').default(0).notNull(),
  emailsBounced: integer('emailsBounced').default(0).notNull(),
  emailsComplained: integer('emailsComplained').default(0).notNull(),
  emailsOpened: integer('emailsOpened').default(0).notNull(),
  emailsClicked: integer('emailsClicked').default(0).notNull(),
  deliverabilityRate: text('deliverabilityRate'), // Stocké comme texte pour les décimales
  openRate: text('openRate'),
  clickRate: text('clickRate'),
  createdAt: timestamp('createdAt', { mode: 'date' }).defaultNow().notNull(),
}, (metrics) => ({
  dateIdx: index('idx_email_metrics_date').on(metrics.date),
  uniqueDate: unique('unique_email_metrics_date').on(metrics.date),
}));

// Relations pour les événements email
export const emailEventsRelations = relations(emailEvents, ({ one }) => ({
  user: one(users, {
    fields: [emailEvents.userId],
    references: [users.id],
  }),
}));

// Relations pour les nouvelles tables sociales (Epic Social V1)
export const followersRelations = relations(followers, ({ one }) => ({
  follower: one(users, {
    fields: [followers.followerId],
    references: [users.id],
  }),
  following: one(users, {
    fields: [followers.followingId],
    references: [users.id],
  }),
}));

export const notificationsRelations = relations(notifications, ({ one }) => ({
  recipient: one(users, {
    fields: [notifications.recipientId],
    references: [users.id],
  }),
  actor: one(users, {
    fields: [notifications.actorId],
    references: [users.id],
  }),
}));

export const recommendationHistoryRelations = relations(recommendationHistory, ({ one }) => ({
  user: one(users, {
    fields: [recommendationHistory.userId],
    references: [users.id],
  }),
}));

