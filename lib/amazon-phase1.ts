/**
 * Amazon Integration Phase 1 - Liens de Recherche Affiliés
 * 
 * Ce module fournit une intégration simple avec Amazon sans utiliser l'API PAAPI.
 * Il construit des liens de recherche affiliés pour permettre aux utilisateurs
 * de rechercher des albums sur Amazon et générer les 3 ventes qualifiées nécessaires
 * pour débloquer l'accès à l'API PAAPI complète.
 * 
 * Epic 8 - Phase 1 (Amorçage)
 */

// Interface pour les offres Amazon Phase 1 (sans prix)
export interface AmazonSearchLink {
  vendor: string;
  url: string;
  price: null;
  currency: null;
  merchantId: string;
  productName: string;
  inStock: boolean;
  searchKeyword: string; // Mot-clé utilisé pour la recherche
}

/**
 * Construit un lien de recherche Amazon affilié pour un album
 * 
 * @param artistName - Nom de l'artiste
 * @param albumTitle - Titre de l'album
 * @returns URL de recherche Amazon avec tag d'affiliation ou null si erreur
 */
export function buildAmazonSearchLink(artistName: string, albumTitle: string): string | null {
  const partnerTag = process.env.AMAZON_AFFILIATE_TAG;
  
  if (!partnerTag) {
    console.error("❌ Amazon Partner Tag is not configured. Please set AMAZON_AFFILIATE_TAG in environment variables.");
    return null;
  }

  try {
    // Construire le mot-clé de recherche (sans "vinyl" pour élargir les résultats)
    const keyword = `${artistName} ${albumTitle}`;

    // Encoder le mot-clé pour l'URL (remplacer les espaces par des +)
    const encodedKeyword = encodeURIComponent(keyword).replace(/%20/g, '+');
    
    // Construire l'URL de recherche Amazon avec le tag d'affiliation
    const searchUrl = `https://www.amazon.fr/s?k=${encodedKeyword}&tag=${partnerTag}`;
    
    if (process.env.NODE_ENV === 'development') {
    console.log(`🔗 Lien de recherche Amazon généré: "${keyword}" -> ${searchUrl}`);
    }
    
    return searchUrl;
    
  } catch (error) {
    console.error("❌ Erreur lors de la construction du lien Amazon:", error);
    return null;
  }
}

/**
 * Crée un objet AmazonSearchLink complet pour la base de données
 * 
 * @param artistName - Nom de l'artiste
 * @param albumTitle - Titre de l'album
 * @returns Objet AmazonSearchLink ou null si erreur
 */
export function createAmazonSearchOffer(artistName: string, albumTitle: string): AmazonSearchLink | null {
  const searchUrl = buildAmazonSearchLink(artistName, albumTitle);
  
  if (!searchUrl) {
    return null;
  }
  
  const keyword = `${artistName} ${albumTitle}`;
  
  return {
    vendor: 'Amazon',
    url: searchUrl,
    price: null, // Phase 1: pas de prix disponible
    currency: null, // Phase 1: pas de devise disponible
    merchantId: 'amazon-fr',
    productName: `${artistName} - ${albumTitle}`,
    inStock: true, // Toujours considéré comme disponible pour la recherche
    searchKeyword: keyword
  };
}

/**
 * Fonction utilitaire pour enrichir les recommandations avec des liens Amazon Phase 1
 * Compatible avec l'interface existante des affiliate links
 * 
 * @param recommendations - Liste des recommandations à enrichir
 * @returns Recommandations enrichies avec liens Amazon
 */
export function enrichWithAmazonSearchLinks(recommendations: any[]): any[] {
  if (process.env.NODE_ENV === 'development') {
  console.log(`🔗 Enrichissement Amazon Phase 1 pour ${recommendations.length} recommandations`);
  }
  
  let successCount = 0;
  let errorCount = 0;
  
  const enrichedRecommendations = recommendations.map(recommendation => {
    try {
      // Créer le lien de recherche Amazon
      const amazonOffer = createAmazonSearchOffer(
        recommendation.artistName, 
        recommendation.albumTitle
      );
      
      if (amazonOffer) {
        successCount++;
        
        // Ajouter le lien Amazon aux liens d'affiliation existants
        const existingLinks = recommendation.affiliateLinks || [];
        const updatedLinks = [amazonOffer, ...existingLinks];
        
        return {
          ...recommendation,
          affiliateLinks: updatedLinks
        };
      } else {
        errorCount++;
        if (process.env.NODE_ENV === 'development') {
        console.warn(`⚠️ Impossible de créer le lien Amazon pour: ${recommendation.artistName} - ${recommendation.albumTitle}`);
        }
        return recommendation;
      }
      
    } catch (error) {
      errorCount++;
      console.error(`❌ Erreur lors de l'enrichissement Amazon pour "${recommendation.albumTitle}":`, error);
      return recommendation;
    }
  });
  
  if (process.env.NODE_ENV === 'development') {
  if (process.env.NODE_ENV === 'development') {
  if (process.env.NODE_ENV === 'development') {
  console.log(`✅ Enrichissement Amazon Phase 1 terminé: ${successCount} succès, ${errorCount} erreurs`);
  }
  }
  }
  
  return enrichedRecommendations;
}

/**
 * Fonction simple pour tester la génération de liens
 * Utile pour les tests et le debugging
 */
export function testAmazonLinkGeneration() {
  const testAlbums = [
    { artist: "Daft Punk", album: "Random Access Memories" },
    { artist: "Pink Floyd", album: "The Dark Side of the Moon" },
    { artist: "The Beatles", album: "Abbey Road" }
  ];
  
  if (process.env.NODE_ENV === 'development') {
  if (process.env.NODE_ENV === 'development') {
  if (process.env.NODE_ENV === 'development') {
  console.log("🧪 Test de génération de liens Amazon Phase 1:");
  }
  }
  }
  
  testAlbums.forEach(({ artist, album }) => {
    const link = buildAmazonSearchLink(artist, album);
    if (process.env.NODE_ENV === 'development') {
    console.log(`   ${artist} - ${album}: ${link ? '✅' : '❌'}`);
    }
    if (link) {
      if (process.env.NODE_ENV === 'development') {
      console.log(`      URL: ${link}`);
      }
    }
  });
}

/**
 * Vérifie si la configuration Amazon est correcte pour la Phase 1
 */
export function checkAmazonPhase1Configuration(): { isValid: boolean; message: string } {
  const partnerTag = process.env.AMAZON_AFFILIATE_TAG;
  
  if (!partnerTag) {
    return {
      isValid: false,
      message: "AMAZON_AFFILIATE_TAG n'est pas configuré dans les variables d'environnement"
    };
  }
  
  if (!partnerTag.includes('-21')) {
    return {
      isValid: false,
      message: "Le tag d'affiliation Amazon doit se terminer par '-21'"
    };
  }
  
  return {
    isValid: true,
    message: `Configuration Amazon Phase 1 valide avec le tag: ${partnerTag}`
  };
}
