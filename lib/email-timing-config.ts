/**
 * Configuration avancée des horaires d'envoi d'emails
 * Optimisée selon les données d'engagement et les bonnes pratiques par région
 */

// Configuration des horaires optimaux par fuseau horaire
export const TIMEZONE_OPTIMAL_HOURS = {
  // Europe
  'Europe/Paris': { 
    primary: [9, 10, 14], // 9h, 10h, 14h
    secondary: [11, 15, 16],
    avoid: [0, 1, 2, 3, 4, 5, 6, 7, 22, 23]
  },
  'Europe/London': { 
    primary: [9, 10, 14], 
    secondary: [11, 15, 16],
    avoid: [0, 1, 2, 3, 4, 5, 6, 7, 22, 23]
  },
  'Europe/Berlin': { 
    primary: [9, 10, 14], 
    secondary: [11, 15, 16],
    avoid: [0, 1, 2, 3, 4, 5, 6, 7, 22, 23]
  },
  'Europe/Madrid': { 
    primary: [10, 11, 15], // Décalé pour les habitudes espagnoles
    secondary: [12, 16, 17],
    avoid: [0, 1, 2, 3, 4, 5, 6, 7, 22, 23]
  },

  // Amérique du Nord
  'America/New_York': { 
    primary: [10, 11, 14], 
    secondary: [9, 15, 16],
    avoid: [0, 1, 2, 3, 4, 5, 6, 7, 22, 23]
  },
  'America/Los_Angeles': { 
    primary: [9, 10, 13], 
    secondary: [11, 14, 15],
    avoid: [0, 1, 2, 3, 4, 5, 6, 7, 22, 23]
  },
  'America/Toronto': { 
    primary: [10, 11, 14], 
    secondary: [9, 15, 16],
    avoid: [0, 1, 2, 3, 4, 5, 6, 7, 22, 23]
  },

  // Asie
  'Asia/Tokyo': { 
    primary: [10, 11, 15], 
    secondary: [9, 14, 16],
    avoid: [0, 1, 2, 3, 4, 5, 6, 7, 22, 23]
  },
  'Asia/Shanghai': { 
    primary: [10, 11, 15], 
    secondary: [9, 14, 16],
    avoid: [0, 1, 2, 3, 4, 5, 6, 7, 22, 23]
  },
  'Asia/Seoul': { 
    primary: [10, 11, 15], 
    secondary: [9, 14, 16],
    avoid: [0, 1, 2, 3, 4, 5, 6, 7, 22, 23]
  },

  // Océanie
  'Australia/Sydney': { 
    primary: [9, 10, 14], 
    secondary: [11, 15, 16],
    avoid: [0, 1, 2, 3, 4, 5, 6, 7, 22, 23]
  },
  'Australia/Melbourne': { 
    primary: [9, 10, 14], 
    secondary: [11, 15, 16],
    avoid: [0, 1, 2, 3, 4, 5, 6, 7, 22, 23]
  },

  // Amérique du Sud
  'America/Sao_Paulo': { 
    primary: [10, 11, 15], 
    secondary: [9, 14, 16],
    avoid: [0, 1, 2, 3, 4, 5, 6, 7, 22, 23]
  },
  'America/Argentina/Buenos_Aires': { 
    primary: [10, 11, 15], 
    secondary: [9, 14, 16],
    avoid: [0, 1, 2, 3, 4, 5, 6, 7, 22, 23]
  }
} as const;

// Jours optimaux par région (0 = dimanche, 1 = lundi, etc.)
export const REGIONAL_OPTIMAL_DAYS = {
  // Europe et Amérique du Nord - éviter les weekends
  'western': [1, 2, 3, 4], // Lundi à jeudi
  
  // Moyen-Orient - weekend vendredi/samedi
  'middle_east': [0, 1, 2, 3], // Dimanche à mercredi
  
  // Asie - généralement similaire à l'Occident
  'asia': [1, 2, 3, 4], // Lundi à jeudi
  
  // Défaut
  'default': [1, 2, 3, 4]
} as const;

// Mapping des fuseaux horaires vers les régions
export const TIMEZONE_TO_REGION = {
  'Europe/Paris': 'western',
  'Europe/London': 'western',
  'Europe/Berlin': 'western',
  'Europe/Madrid': 'western',
  'America/New_York': 'western',
  'America/Los_Angeles': 'western',
  'America/Toronto': 'western',
  'Asia/Tokyo': 'asia',
  'Asia/Shanghai': 'asia',
  'Asia/Seoul': 'asia',
  'Australia/Sydney': 'western',
  'Australia/Melbourne': 'western',
  'America/Sao_Paulo': 'western',
  'America/Argentina/Buenos_Aires': 'western'
} as const;

// Configuration de fréquence adaptative basée sur l'engagement
export const ADAPTIVE_FREQUENCY_CONFIG = {
  // Seuils d'engagement pour ajuster la fréquence
  thresholds: {
    high_engagement: { openRate: 0.3, clickRate: 0.1 },
    medium_engagement: { openRate: 0.15, clickRate: 0.05 },
    low_engagement: { openRate: 0.05, clickRate: 0.01 }
  },
  
  // Fréquences recommandées selon l'engagement
  frequencies: {
    high_engagement: 'weekly',
    medium_engagement: 'bi-weekly',
    low_engagement: 'monthly',
    no_engagement: 'never' // Après 3 mois sans engagement
  },
  
  // Période d'observation pour calculer l'engagement
  observation_period_days: 30
} as const;

// Configuration du throttling avancé
export const ADVANCED_THROTTLING_CONFIG = {
  // Limites par période
  limits: {
    per_minute: 50,
    per_hour: 1000,
    per_day: 10000
  },
  
  // Délais entre les envois pour éviter les pics
  delays: {
    between_emails_ms: 100, // 100ms entre chaque email
    between_batches_ms: 5000, // 5s entre chaque lot de 50
    between_timezones_ms: 300000 // 5min entre chaque fuseau horaire
  },
  
  // Taille des lots
  batch_sizes: {
    high_priority: 10, // Emails importants (bienvenue, etc.)
    normal_priority: 50, // Emails de recommandations
    low_priority: 100 // Emails marketing
  }
} as const;

// Configuration des jours fériés à éviter
export const HOLIDAY_BLACKOUT_DATES = {
  // Dates fixes (format MM-DD)
  fixed: [
    '01-01', // Nouvel An
    '12-25', // Noël
    '12-24', // Veille de Noël
    '07-04', // Fête nationale US
    '07-14', // Fête nationale FR
  ],
  
  // Périodes à éviter (format MM-DD à MM-DD)
  periods: [
    { start: '12-20', end: '01-05' }, // Période de Noël/Nouvel An
    { start: '07-01', end: '08-31' }, // Vacances d'été (Europe)
  ]
} as const;

/**
 * Obtient les horaires optimaux pour un fuseau horaire donné
 */
export function getOptimalHours(timezone: string): { primary: number[]; secondary: number[]; avoid: number[] } {
  const config = TIMEZONE_OPTIMAL_HOURS[timezone as keyof typeof TIMEZONE_OPTIMAL_HOURS] ||
                 TIMEZONE_OPTIMAL_HOURS['Europe/Paris'];

  return {
    primary: [...config.primary],
    secondary: [...config.secondary],
    avoid: [...config.avoid]
  };
}

/**
 * Obtient les jours optimaux pour une région donnée
 */
export function getOptimalDays(timezone: string): number[] {
  const region = TIMEZONE_TO_REGION[timezone as keyof typeof TIMEZONE_TO_REGION] || 'default';
  return [...REGIONAL_OPTIMAL_DAYS[region]];
}

/**
 * Vérifie si une date est un jour férié ou dans une période à éviter
 */
export function isBlackoutDate(date: Date): boolean {
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const day = date.getDate().toString().padStart(2, '0');
  const dateString = `${month}-${day}`;
  
  // Vérifier les dates fixes
  if (HOLIDAY_BLACKOUT_DATES.fixed.includes(dateString as any)) {
    return true;
  }
  
  // Vérifier les périodes
  for (const period of HOLIDAY_BLACKOUT_DATES.periods) {
    if (isDateInPeriod(dateString, period.start, period.end)) {
      return true;
    }
  }
  
  return false;
}

/**
 * Vérifie si une date est dans une période donnée
 */
function isDateInPeriod(date: string, start: string, end: string): boolean {
  // Gestion simple pour les périodes dans la même année
  if (start <= end) {
    return date >= start && date <= end;
  } else {
    // Période qui traverse le nouvel an (ex: 12-20 à 01-05)
    return date >= start || date <= end;
  }
}

/**
 * Calcule la fréquence optimale basée sur l'engagement
 */
export function calculateOptimalFrequency(
  openRate: number, 
  clickRate: number
): 'weekly' | 'bi-weekly' | 'monthly' | 'never' {
  const { thresholds, frequencies } = ADAPTIVE_FREQUENCY_CONFIG;
  
  if (openRate >= thresholds.high_engagement.openRate && 
      clickRate >= thresholds.high_engagement.clickRate) {
    return frequencies.high_engagement as 'weekly';
  }
  
  if (openRate >= thresholds.medium_engagement.openRate && 
      clickRate >= thresholds.medium_engagement.clickRate) {
    return frequencies.medium_engagement as 'bi-weekly';
  }
  
  if (openRate >= thresholds.low_engagement.openRate && 
      clickRate >= thresholds.low_engagement.clickRate) {
    return frequencies.low_engagement as 'monthly';
  }
  
  return frequencies.no_engagement as 'never';
}

/**
 * Obtient le délai optimal entre les envois selon la priorité
 */
export function getOptimalDelay(priority: 'high' | 'normal' | 'low'): number {
  const { delays } = ADVANCED_THROTTLING_CONFIG;
  
  switch (priority) {
    case 'high':
      return delays.between_emails_ms * 2; // Plus de délai pour les emails importants
    case 'normal':
      return delays.between_emails_ms;
    case 'low':
      return delays.between_emails_ms / 2; // Moins de délai pour les emails marketing
    default:
      return delays.between_emails_ms;
  }
}

/**
 * Obtient la taille de lot optimale selon la priorité
 */
export function getOptimalBatchSize(priority: 'high' | 'normal' | 'low'): number {
  const { batch_sizes } = ADVANCED_THROTTLING_CONFIG;
  
  switch (priority) {
    case 'high':
      return batch_sizes.high_priority;
    case 'normal':
      return batch_sizes.normal_priority;
    case 'low':
      return batch_sizes.low_priority;
    default:
      return batch_sizes.normal_priority;
  }
}
