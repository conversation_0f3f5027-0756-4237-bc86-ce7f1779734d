/**
 * Configuration des assets pour les emails
 * Utilise un CDN dédié pour éviter les problèmes d'authentification
 */

// URL du CDN pour les assets d'email
const ASSETS_CDN_URL = process.env.EMAIL_ASSETS_CDN_URL || 'https://assets.stream2spin.com';

/**
 * URLs des assets pour les emails
 * Utilise PNG au lieu de SVG pour une meilleure compatibilité avec les clients de messagerie
 */
export const EMAIL_ASSETS = {
  logo: `${ASSETS_CDN_URL}/Stream2Spin_white_logo.png`, // Maintenant en PNG pour compatibilité
  heartIcon: `${ASSETS_CDN_URL}/heart-icon.png`,
  discIcon: `${ASSETS_CDN_URL}/disc3-icon.png`,
} as const;

/**
 * Fonction utilitaire pour obtenir l'URL d'un asset
 */
export function getEmailAssetUrl(assetName: keyof typeof EMAIL_ASSETS): string {
  return EMAIL_ASSETS[assetName];
}
