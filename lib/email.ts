/**
 * Module d'envoi d'emails
 * US 1.3 - Epic 1 : Authentification et Onboarding
 * 
 * Ce module gère l'envoi d'emails transactionnels via Resend
 */

import * as React from 'react';
import { render } from '@react-email/render';
import { Resend } from 'resend';
import { getTranslations } from 'next-intl/server';
// Imports fs supprimés - utilisation d'imports directs pour Vercel

// Configuration Resend
const resend = new Resend(process.env.RESEND_API_KEY);

// Configuration des emails optimisée pour la délivrabilité
// Utilise le domaine vérifié mails.stream2spin.com pour tous les environnements
const EMAIL_CONFIG = {
  from: process.env.RESEND_FROM_EMAIL || 'Stream2Spin <<EMAIL>>',
  replyTo: process.env.RESEND_REPLY_TO || '<EMAIL>',
  // Configuration pour améliorer la délivrabilité
  headers: {
    'X-Entity-Ref-ID': 'stream2spin-welcome',
    'List-Unsubscribe': '<mailto:<EMAIL>>',
    'List-Unsubscribe-Post': 'List-Unsubscribe=One-Click',
  }
} as const;

// Interface pour les données utilisateur
export interface WelcomeEmailData {
  name: string;
  email: string;
  preferredLanguage?: string;
}

/**
 * Envoie un email de bienvenue à un nouvel utilisateur
 * US 1.3 - Déclenché automatiquement lors de la création d'un compte
 */
export async function sendWelcomeEmail(
  userData: WelcomeEmailData
): Promise<{ success: boolean; messageId?: string; error?: string }> {
  try {
    if (process.env.NODE_ENV === 'development') {
    console.log(`📧 Envoi d'email de bienvenue à ${userData.email} (${userData.name})`);
    }

    // Vérifier la configuration
    if (!process.env.RESEND_API_KEY) {
      throw new Error('RESEND_API_KEY is not configured');
    }

    // Validation simple de l'email
    if (!userData.email || !userData.email.includes('@')) {
      if (process.env.NODE_ENV === 'development') {
      console.warn(`⚠️ Email invalide: ${userData.email}`);
      }
      throw new Error('Email invalide');
    }

    // Charger les traductions (import direct pour Vercel)
    const locale = userData.preferredLanguage || 'fr';
    let messages: any;
    try {
      if (locale === 'en') {
        const module = await import('@/messages/en.json');
        messages = module.default || module;
      } else {
        const module = await import('@/messages/fr.json');
        messages = module.default || module;
      }
    } catch (error) {
      console.warn(`⚠️ Impossible de charger les traductions pour ${locale}, utilisation du français par défaut`);
      const module = await import('@/messages/fr.json');
      messages = module.default || module;
    }

    // Créer la fonction de traduction avec support des pluriels ICU
    function t(key: string, values: Record<string, any> = {}): string {
      const keys = key.split('.');
      let translation: any = messages;
      
      for (const k of keys) {
        if (translation && typeof translation === 'object') {
          translation = translation[k];
        } else {
          return key;
        }
      }

      if (typeof translation === 'string') {
        let result = translation;
        
        // Gestion des pluriels ICU MessageFormat de façon itérative
        // Traiter les pluriels un par un jusqu'à ce qu'il n'y en ait plus
        let hasPlurals = true;
        while (hasPlurals) {
          // Regex pour détecter les pluriels (avec ou sans =0)
          const pluralRegex = /\{(\w+),\s*plural,\s*(?:=0\s*\{([^}]*)\}\s*)?=1\s*\{([^}]*)\}\s*other\s*\{([^}]*)\}\}/;
          const match = result.match(pluralRegex);
          
          if (match) {
            const [fullMatch, varName, zero, singular, plural] = match;
            const count = values[varName] || 0;
            
            let replacement;
            if (count === 0 && zero !== undefined) {
              replacement = zero;
            } else if (count === 1) {
              replacement = singular;
            } else {
              replacement = plural;
            }
            
            result = result.replace(fullMatch, replacement);
          } else {
            hasPlurals = false;
          }
        }

        // Remplacer les variables standard
        Object.keys(values).forEach(key => {
          const regex = new RegExp(`\\{${key}\\}`, 'g');
          result = result.replace(regex, String(values[key] || ''));
        });

        return result;
      }

      return key;
    }

    const subject = t('emails.welcome.subject');

    // Importer le template d'email de bienvenue
    const { WelcomeEmail } = await import('@/emails/welcome-email');

    // Générer le contenu HTML de l'email
    const emailHtml = await render(
      WelcomeEmail({
        name: userData.name,
        userEmail: userData.email,
        t
      })
    );

    // Générer la version texte pour améliorer la délivrabilité
    const baseUrl = process.env.NEXTAUTH_URL || 'https://stream2spin.com';
    const textContent = t('emails.welcome.textContent', { 
      name: userData.name,
      baseUrl: baseUrl
    });

    // Envoyer l'email
    const resend = new Resend(process.env.RESEND_API_KEY);
    const response = await resend.emails.send({
      from: EMAIL_CONFIG.from,
      to: userData.email,
      subject: subject,
      html: emailHtml,
      text: textContent,
      replyTo: EMAIL_CONFIG.replyTo
    });

    if (response.error) {
      if (process.env.NODE_ENV === 'development') {
      console.error('❌ Erreur lors de l\'envoi:', response.error);
      }
      throw new Error(`Erreur Resend: ${response.error.message}`);
    }

    if (process.env.NODE_ENV === 'development') {
    console.log(`✅ Email de bienvenue envoyé avec succès (ID: ${response.data?.id})`);
    }

    return {
      success: true,
      messageId: response.data?.id
    };

  } catch (error) {
    if (process.env.NODE_ENV === 'development') {
    console.error('❌ Erreur lors de l\'envoi d\'email de bienvenue:', error);
    }
    
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Erreur inconnue'
    };
  }
}

/**
 * Envoie un email de test de bienvenue
 */
export async function sendTestWelcomeEmail(
  email: string,
  name: string = 'Utilisateur Test',
  preferredLanguage: string = 'fr'
): Promise<{ success: boolean; messageId?: string; error?: string }> {
  try {
    if (process.env.NODE_ENV === 'development') {
    console.log(`🧪 Envoi d'email de bienvenue de test à ${email}`);
    }

    const result = await sendWelcomeEmail({
      name,
      email,
      preferredLanguage
    });

    if (result.success) {
      if (process.env.NODE_ENV === 'development') {
      console.log(`✅ Email de bienvenue de test envoyé avec succès (ID: ${result.messageId})`);
      }
    }

    return result;

  } catch (error) {
    console.error('❌ Erreur lors de l\'envoi d\'email de bienvenue de test:', error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    };
  }
}

/**
 * Vérifie la configuration email pour les emails de bienvenue
 */
export function checkWelcomeEmailConfiguration(): { isValid: boolean; message: string } {
  const apiKey = process.env.RESEND_API_KEY;
  const fromEmail = process.env.RESEND_FROM_EMAIL;
  
  if (!apiKey) {
    return {
      isValid: false,
      message: 'RESEND_API_KEY n\'est pas configuré dans les variables d\'environnement'
    };
  }
  
  if (!fromEmail) {
    return {
      isValid: false,
      message: 'RESEND_FROM_EMAIL n\'est pas configuré dans les variables d\'environnement'
    };
  }
  
  if (!apiKey.startsWith('re_')) {
    return {
      isValid: false,
      message: 'La clé API Resend doit commencer par "re_"'
    };
  }
  
  return {
    isValid: true,
    message: `Configuration email de bienvenue valide avec l'expéditeur: ${fromEmail}`
  };
}

/**
 * Fonction utilitaire pour valider une adresse email
 */
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

/**
 * Fonction utilitaire pour nettoyer et valider le nom d'utilisateur
 */
export function sanitizeUserName(name: string | null | undefined): string {
  if (!name || typeof name !== 'string') {
    return 'Nouvel utilisateur';
  }
  
  // Nettoyer le nom (supprimer les caractères dangereux)
  const cleanName = name.trim().replace(/[<>]/g, '');
  
  // Retourner le nom nettoyé ou un nom par défaut
  return cleanName || 'Nouvel utilisateur';
}

/**
 * Obtient les statistiques d'envoi d'emails de bienvenue (si disponible)
 */
export async function getWelcomeEmailStats(): Promise<any> {
  try {
    // Note: Resend ne fournit pas d'API de statistiques dans la version gratuite
    // Cette fonction est préparée pour une future implémentation
    if (process.env.NODE_ENV === 'development') {
    console.log('📊 Récupération des statistiques d\'emails de bienvenue...');
    }
    
    return {
      message: 'Statistiques non disponibles avec le plan Resend actuel',
      suggestion: 'Utilisez le dashboard Resend pour voir les statistiques des emails de bienvenue'
    };
    
  } catch (error) {
    console.error('❌ Erreur lors de la récupération des statistiques:', error);
    return null;
  }
}

interface NewFollowerEmailData {
  recipientEmail: string;
  recipientName: string;
  actorName: string;
  locale: string;
}

export async function sendNewFollowerEmail(data: NewFollowerEmailData) {
  try {
    // Préparer toutes les traductions
    const t = await getTranslations({ locale: data.locale, namespace: 'emails.newFollower' });
    
    const translations = {
      subject: t('subject', { name: data.actorName }),
      preview: t('preview', { name: data.actorName }),
      greeting: t('greeting'),
      body: t('body', { name: data.actorName }),
      cta: t('cta'),
      footer_info: t('footer_info'),
      footer_manage: t('footer_manage'),
      footer_copyright: t('footer_copyright', { year: new Date().getFullYear() }),
    };

    const { NewFollowerEmail } = await import('@/emails/new-follower-email');
    const emailComponent = NewFollowerEmail({
      recipientName: data.recipientName,
      actorName: data.actorName,
      translations,
    });

    const emailHtml = await render(emailComponent);

    const emailResult = await resend.emails.send({
      from: EMAIL_CONFIG.from,
      to: data.recipientEmail,
      subject: translations.subject,
      html: emailHtml,
      replyTo: EMAIL_CONFIG.replyTo,
    });

    if (emailResult.error) {
      console.error('❌ Erreur envoi email newFollower:', emailResult.error);
      return { success: false, error: emailResult.error };
    }

    console.log('✅ Email newFollower envoyé avec succès:', emailResult.data?.id);
    return { success: true, id: emailResult.data?.id };

  } catch (error) {
    console.error('❌ Erreur critique sendNewFollowerEmail:', error);
    return { success: false, error };
  }
}
