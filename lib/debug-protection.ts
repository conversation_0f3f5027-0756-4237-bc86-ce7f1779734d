/**
 * Middleware de protection pour les fonctionnalités de debug
 * Désactive automatiquement les routes de debug en production
 */

import { NextRequest, NextResponse } from "next/server";

/**
 * Vérifie si les routes de debug sont autorisées dans l'environnement actuel
 */
export function isDebugAllowed(): boolean {
  // Autoriser en développement
  if (process.env.NODE_ENV === 'development') {
    return true;
  }
  
  // Autoriser en staging/preview si explicitement activé
  if (process.env.VERCEL_ENV === 'preview' && process.env.ENABLE_DEBUG_ROUTES === 'true') {
    return true;
  }
  
  // Bloquer en production par défaut
  return false;
}

/**
 * Middleware de protection pour les routes de debug
 */
export function withDebugProtection(handler: (req: NextRequest) => Promise<NextResponse>) {
  return async (req: NextRequest): Promise<NextResponse> => {
    if (!isDebugAllowed()) {
      if (process.env.NODE_ENV === 'development') {
      console.log(`🚫 Debug route blocked in production: ${req.nextUrl.pathname}`);
      }
      return NextResponse.json(
        { error: 'Not found' },
        { status: 404 }
      );
    }
    
    // Log l'accès aux routes de debug pour audit
    if (process.env.NODE_ENV === 'development') {
    console.log(`🔧 Debug route accessed: ${req.nextUrl.pathname} (${process.env.NODE_ENV})`);
    }
    
    return handler(req);
  };
}

/**
 * Vérifie si une route est une route de debug critique
 */
export function isCriticalDebugRoute(pathname: string): boolean {
  const criticalRoutes = [
    '/api/public/',
    '/api/test/',
    '/api/debug/',
    '/debug-email',
    '/test-email',
    '/test-spotify-embed',
    '/test-error-pages'
  ];
  
  return criticalRoutes.some(route => pathname.startsWith(route));
}

/**
 * Middleware spécifique pour les pages de debug
 */
export function withPageDebugProtection() {
  if (!isDebugAllowed()) {
    // Rediriger vers 404 si debug non autorisé
    return {
      notFound: true
    };
  }
  
  return null;
}

/**
 * Logger sécurisé qui ne log qu'en développement
 */
export class SecureLogger {
  static debug(message: string, data?: any) {
    if (process.env.NODE_ENV === 'development') {
      if (process.env.NODE_ENV === 'development') {
      console.log(`[DEBUG] ${message}`, data);
      }
    }
  }
  
  static info(message: string, data?: any) {
    if (process.env.NODE_ENV === 'development') {
    console.log(`[INFO] ${message}`, data);
    }
  }
  
  static warn(message: string, data?: any) {
    if (process.env.NODE_ENV === 'development') {
    console.warn(`[WARN] ${message}`, data);
    }
  }
  
  static error(message: string, data?: any) {
    console.error(`[ERROR] ${message}`, data);
  }
  
  /**
   * Sanitise les données utilisateur pour les logs
   */
  static sanitizeUserData(user: any) {
    if (!user) return null;
    
    return {
      id: user.id?.substring(0, 8) + '...',
      email: user.email?.replace(/(.{2}).*(@.*)/, '$1***$2'),
      name: user.name?.substring(0, 10) + '...',
      // Ne pas exposer d'autres données sensibles
    };
  }
  
  /**
   * Log sécurisé pour les actions admin
   */
  static adminAction(adminId: string, action: string, target?: string, success: boolean = true) {
    this.info(`Admin action: ${action}`, {
      adminId: adminId?.substring(0, 8) + '...',
      target: target?.substring(0, 20) + '...',
      success,
      timestamp: new Date().toISOString()
    });
  }
}
