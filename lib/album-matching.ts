/**
 * Utilitaires pour la correspondance entre albums Spotify et Discogs
 * Epic 3 - US 3.2: Croisement avec la Collection Discogs
 */

/**
 * Normalise une chaîne de caractères pour la comparaison d'albums
 * Supprime les caractères spéciaux, les termes entre parenthèses/crochets,
 * et convertit en minuscules pour une correspondance plus robuste
 */
export function normalizeAlbumTitle(title: string): string {
  if (!title) return '';

  return title
    // Convertir en minuscules
    .toLowerCase()
    // Supprimer les termes courants entre parenthèses ou crochets
    .replace(/\s*\([^)]*\)/g, '') // Supprime (remastered), (deluxe edition), etc.
    .replace(/\s*\[[^\]]*\]/g, '') // Supprime [remastered], [deluxe], etc.
    // Supprimer les caractères non alphanumériques (garder les espaces)
    .replace(/[^a-z0-9\s]/g, '')
    // Normaliser les espaces multiples en un seul espace
    .replace(/\s+/g, ' ')
    // Supprimer les espaces en début et fin
    .trim();
}

/**
 * Table de translittération cyrillique vers latin
 * Basée sur la translittération ISO 9 simplifiée pour la correspondance d'albums
 */
const CYRILLIC_TO_LATIN: Record<string, string> = {
  'а': 'a', 'б': 'b', 'в': 'v', 'г': 'g', 'д': 'd', 'е': 'e', 'ё': 'yo', 'ж': 'zh',
  'з': 'z', 'и': 'i', 'й': 'y', 'к': 'k', 'л': 'l', 'м': 'm', 'н': 'n', 'о': 'o',
  'п': 'p', 'р': 'r', 'с': 's', 'т': 't', 'у': 'u', 'ф': 'f', 'х': 'kh', 'ц': 'ts',
  'ч': 'ch', 'ш': 'sh', 'щ': 'shch', 'ъ': '', 'ы': 'y', 'ь': '', 'э': 'e', 'ю': 'yu', 'я': 'ya',
  'А': 'A', 'Б': 'B', 'В': 'V', 'Г': 'G', 'Д': 'D', 'Е': 'E', 'Ё': 'Yo', 'Ж': 'Zh',
  'З': 'Z', 'И': 'I', 'Й': 'Y', 'К': 'K', 'Л': 'L', 'М': 'M', 'Н': 'N', 'О': 'O',
  'П': 'P', 'Р': 'R', 'С': 'S', 'Т': 'T', 'У': 'U', 'Ф': 'F', 'Х': 'Kh', 'Ц': 'Ts',
  'Ч': 'Ch', 'Ш': 'Sh', 'Щ': 'Shch', 'Ъ': '', 'Ы': 'Y', 'Ь': '', 'Э': 'E', 'Ю': 'Yu', 'Я': 'Ya'
};

/**
 * Translittère les caractères cyrilliques en caractères latins
 */
function transliterateCyrillic(text: string): string {
  return text.split('').map(char => CYRILLIC_TO_LATIN[char] || char).join('');
}

/**
 * Normalise le nom d'un artiste pour la comparaison
 * Gère les cas comme "The Beatles" vs "Beatles", les accents, et la translittération cyrillique
 */
export function normalizeArtistName(artist: string): string {
  if (!artist) return '';

  return artist
    // Translittérer les caractères cyrilliques en premier
    .split('').map(char => CYRILLIC_TO_LATIN[char] || char).join('')
    // Convertir en minuscules
    .toLowerCase()
    // Supprimer les accents de manière robuste
    .normalize('NFD')
    .replace(/[\u0300-\u036f]/g, '')
    // Remplacements spécifiques pour les caractères problématiques
    .replace(/[àáâãäå]/g, 'a')
    .replace(/[èéêë]/g, 'e')
    .replace(/[ìíîï]/g, 'i')
    .replace(/[òóôõö]/g, 'o')
    .replace(/[ùúûü]/g, 'u')
    .replace(/[ýÿ]/g, 'y')
    .replace(/[ñ]/g, 'n')
    .replace(/[ç]/g, 'c')
    // Supprimer "the " au début (ex: "The Beatles" -> "Beatles")
    .replace(/^the\s+/, '')
    // Supprimer les caractères non alphanumériques (garder les espaces)
    .replace(/[^a-z0-9\s]/g, '')
    // Normaliser les espaces
    .replace(/\s+/g, ' ')
    .trim();
}

/**
 * Vérifie si deux albums correspondent en comparant artiste et titre normalisés
 */
export function albumsMatch(
  spotifyArtist: string,
  spotifyAlbum: string,
  discogsArtist: string,
  discogsAlbum: string
): boolean {
  const normalizedSpotifyArtist = normalizeArtistName(spotifyArtist);
  const normalizedSpotifyAlbum = normalizeAlbumTitle(spotifyAlbum);
  const normalizedDiscogsArtist = normalizeArtistName(discogsArtist);
  const normalizedDiscogsAlbum = normalizeAlbumTitle(discogsAlbum);

  // Correspondance exacte sur artiste et album normalisés
  const exactMatch = normalizedSpotifyArtist === normalizedDiscogsArtist && 
                     normalizedSpotifyAlbum === normalizedDiscogsAlbum;

  if (exactMatch) {
    return true;
  }

  // Correspondance partielle: même artiste et album contient le titre Spotify
  // (pour gérer les cas où Discogs a des titres plus longs)
  const partialMatch = normalizedSpotifyArtist === normalizedDiscogsArtist &&
                       normalizedDiscogsAlbum.includes(normalizedSpotifyAlbum) &&
                       normalizedSpotifyAlbum.length > 3; // Éviter les correspondances trop courtes

  return partialMatch;
}

/**
 * Interface pour représenter un album de la collection Discogs
 */
export interface DiscogsCollectionItem {
  artistName: string;
  albumTitle: string;
  discogsReleaseId: number;
  year?: number | null;
  format?: string | null;
}

/**
 * Interface pour représenter une recommandation Spotify
 */
export interface SpotifyRecommendation {
  artistName: string;
  albumTitle: string;
  spotifyAlbumId?: string;
  albumCoverUrl?: string;
  listenScore: number;
  timeframe: string;
}

/**
 * Interface pour les éléments de wishlist
 */
export interface WishlistItem {
  artistName: string;
  albumTitle: string;
}

/**
 * Marque les recommandations comme possédées en croisant avec la collection Discogs
 * Optimisé pour les performances avec un Set pour les recherches rapides
 */
export function markOwnedAlbums(
  recommendations: SpotifyRecommendation[],
  discogsCollection: DiscogsCollectionItem[]
): (SpotifyRecommendation & { isOwned: boolean })[] {
  // Créer un Set de clés normalisées pour la collection Discogs (recherche O(1))
  const normalizedCollection = new Set<string>();
  
  // Créer aussi une Map pour les correspondances partielles
  const collectionMap = new Map<string, DiscogsCollectionItem[]>();

  discogsCollection.forEach(item => {
    const normalizedArtist = normalizeArtistName(item.artistName);
    const normalizedAlbum = normalizeAlbumTitle(item.albumTitle);
    
    // Clé pour correspondance exacte
    const exactKey = `${normalizedArtist}|${normalizedAlbum}`;
    normalizedCollection.add(exactKey);

    // Grouper par artiste pour les correspondances partielles
    if (!collectionMap.has(normalizedArtist)) {
      collectionMap.set(normalizedArtist, []);
    }
    collectionMap.get(normalizedArtist)!.push(item);
  });

  // Marquer chaque recommandation comme possédée ou non
  return recommendations.map(recommendation => {
    const normalizedSpotifyArtist = normalizeArtistName(recommendation.artistName);
    const normalizedSpotifyAlbum = normalizeAlbumTitle(recommendation.albumTitle);
    
    // Vérifier correspondance exacte d'abord
    const exactKey = `${normalizedSpotifyArtist}|${normalizedSpotifyAlbum}`;
    if (normalizedCollection.has(exactKey)) {
      return { ...recommendation, isOwned: true };
    }

    // Vérifier correspondances partielles pour cet artiste
    const artistItems = collectionMap.get(normalizedSpotifyArtist);
    if (artistItems) {
      const isOwned = artistItems.some(item => 
        albumsMatch(
          recommendation.artistName,
          recommendation.albumTitle,
          item.artistName,
          item.albumTitle
        )
      );
      
      if (isOwned) {
        return { ...recommendation, isOwned: true };
      }
    }

    return { ...recommendation, isOwned: false };
  });
}

/**
 * Marque les recommandations comme étant dans la wishlist en croisant avec la wishlist utilisateur
 * Optimisé pour les performances avec un Set pour les recherches rapides
 */
export function markWishlistedAlbums(
  recommendations: SpotifyRecommendation[],
  wishlistItems: WishlistItem[]
): (SpotifyRecommendation & { isWishlisted: boolean })[] {
  // Créer un Set de clés normalisées pour la wishlist (recherche O(1))
  const normalizedWishlist = new Set<string>();

  // Créer aussi une Map pour les correspondances partielles
  const wishlistMap = new Map<string, WishlistItem[]>();

  // Normaliser et indexer la wishlist
  for (const item of wishlistItems) {
    const normalizedArtist = normalizeArtistName(item.artistName);
    const normalizedAlbum = normalizeAlbumTitle(item.albumTitle);
    const key = `${normalizedArtist}|${normalizedAlbum}`;

    normalizedWishlist.add(key);

    // Grouper par artiste pour les correspondances partielles
    if (!wishlistMap.has(normalizedArtist)) {
      wishlistMap.set(normalizedArtist, []);
    }
    wishlistMap.get(normalizedArtist)!.push(item);
  }

  // Marquer chaque recommandation comme étant dans la wishlist ou non
  return recommendations.map(recommendation => {
    const normalizedSpotifyArtist = normalizeArtistName(recommendation.artistName);
    const normalizedSpotifyAlbum = normalizeAlbumTitle(recommendation.albumTitle);

    // Vérifier correspondance exacte d'abord
    const exactKey = `${normalizedSpotifyArtist}|${normalizedSpotifyAlbum}`;
    if (normalizedWishlist.has(exactKey)) {
      return { ...recommendation, isWishlisted: true };
    }

    // Vérifier correspondances partielles pour cet artiste
    const artistItems = wishlistMap.get(normalizedSpotifyArtist);
    if (artistItems) {
      const isWishlisted = artistItems.some(item =>
        albumsMatch(
          recommendation.artistName,
          recommendation.albumTitle,
          item.artistName,
          item.albumTitle
        )
      );

      if (isWishlisted) {
        return { ...recommendation, isWishlisted: true };
      }
    }

    return { ...recommendation, isWishlisted: false };
  });
}

/**
 * Cache en mémoire pour les collections Discogs
 * Évite les requêtes répétées à la base de données pendant le traitement
 */
const discogsCollectionCache = new Map<string, {
  collection: DiscogsCollectionItem[];
  timestamp: number;
}>();

// Durée de vie du cache: 5 minutes
const CACHE_TTL = 5 * 60 * 1000;

/**
 * Récupère la collection Discogs d'un utilisateur depuis la base de données
 * avec mise en cache pour optimiser les performances
 */
export async function getUserDiscogsCollection(
  userId: string,
  db: any // Type générique pour éviter les dépendances circulaires
): Promise<DiscogsCollectionItem[]> {
  // Vérifier le cache d'abord
  const cached = discogsCollectionCache.get(userId);
  const now = Date.now();

  if (cached && (now - cached.timestamp) < CACHE_TTL) {
    if (process.env.NODE_ENV === 'development') {
    console.log(`🚀 Collection Discogs récupérée depuis le cache pour l'utilisateur ${userId}`);
    }
    return cached.collection;
  }

  // Récupérer depuis la base de données
  const { userDiscogsCollection } = await import('@/lib/db/schema');
  const { eq } = await import('drizzle-orm');

  const collection = await db.query.userDiscogsCollection.findMany({
    where: eq(userDiscogsCollection.userId, userId),
    columns: {
      artistName: true,
      albumTitle: true,
      discogsReleaseId: true,
      year: true,
      format: true,
    }
  });

  const formattedCollection = collection.map((item: any) => ({
    artistName: item.artistName,
    albumTitle: item.albumTitle,
    discogsReleaseId: Number(item.discogsReleaseId),
    year: item.year,
    format: item.format,
  }));

  // Mettre en cache
  discogsCollectionCache.set(userId, {
    collection: formattedCollection,
    timestamp: now
  });

  if (process.env.NODE_ENV === 'development') {
  console.log(`💾 Collection Discogs mise en cache pour l'utilisateur ${userId} (${formattedCollection.length} albums)`);
  }

  return formattedCollection;
}

/**
 * Invalide le cache de collection Discogs pour un utilisateur
 * À appeler après une synchronisation ou une déconnexion Discogs
 */
export function invalidateDiscogsCache(userId: string): void {
  discogsCollectionCache.delete(userId);
  if (process.env.NODE_ENV === 'development') {
  console.log(`🗑️ Cache Discogs invalidé pour l'utilisateur ${userId}`);
  }
}

/**
 * Nettoie les entrées expirées du cache
 * À appeler périodiquement pour éviter l'accumulation de mémoire
 */
export function cleanupDiscogsCache(): void {
  const now = Date.now();
  let cleanedCount = 0;

  for (const [userId, cached] of discogsCollectionCache.entries()) {
    if ((now - cached.timestamp) >= CACHE_TTL) {
      discogsCollectionCache.delete(userId);
      cleanedCount++;
    }
  }

  if (cleanedCount > 0) {
    if (process.env.NODE_ENV === 'development') {
    console.log(`🧹 ${cleanedCount} entrées expirées supprimées du cache Discogs`);
    }
  }
}

/**
 * Cache en mémoire pour les wishlists
 * Évite les requêtes répétées à la base de données pendant le traitement
 */
const wishlistCache = new Map<string, {
  wishlist: WishlistItem[];
  timestamp: number;
}>();

/**
 * Récupère la wishlist d'un utilisateur depuis la base de données
 * avec mise en cache pour optimiser les performances
 */
export async function getUserWishlist(
  userId: string,
  db: any // Type générique pour éviter les dépendances circulaires
): Promise<WishlistItem[]> {
  // Vérifier le cache d'abord
  const cached = wishlistCache.get(userId);
  const now = Date.now();

  if (cached && (now - cached.timestamp) < CACHE_TTL) {
    if (process.env.NODE_ENV === 'development') {
    console.log(`🚀 Wishlist récupérée depuis le cache pour l'utilisateur ${userId}`);
    }
    return cached.wishlist;
  }

  // Récupérer depuis la base de données
  const { wishlistItems, recommendations } = await import('@/lib/db/schema');
  const { eq } = await import('drizzle-orm');

  const wishlistData = await db.query.wishlistItems.findMany({
    where: eq(wishlistItems.userId, userId),
    columns: {
      artistName: true,
      albumTitle: true,
    },
  });

  const formattedWishlist = wishlistData.map((item: any) => ({
    artistName: item.artistName,
    albumTitle: item.albumTitle,
  }));

  // Mettre en cache
  wishlistCache.set(userId, {
    wishlist: formattedWishlist,
    timestamp: now
  });

  if (process.env.NODE_ENV === 'development') {
  console.log(`💾 Wishlist mise en cache pour l'utilisateur ${userId} (${formattedWishlist.length} albums)`);
  }
  return formattedWishlist;
}

/**
 * Invalide le cache de la wishlist pour un utilisateur
 * Utile après un ajout/suppression pour forcer le rechargement
 */
export function invalidateWishlistCache(userId: string) {
  wishlistCache.delete(userId);
  if (process.env.NODE_ENV === 'development') {
  console.log(`🗑️ Cache wishlist invalidé pour l'utilisateur ${userId}`);
  }
}

/**
 * Nettoie les entrées expirées du cache wishlist
 * À appeler périodiquement pour éviter l'accumulation de mémoire
 */
export function cleanupWishlistCache(): void {
  const now = Date.now();
  let cleanedCount = 0;

  for (const [userId, cached] of wishlistCache.entries()) {
    if ((now - cached.timestamp) >= CACHE_TTL) {
      wishlistCache.delete(userId);
      cleanedCount++;
    }
  }

  if (cleanedCount > 0) {
    if (process.env.NODE_ENV === 'development') {
    console.log(`🧹 ${cleanedCount} entrées expirées supprimées du cache wishlist`);
    }
  }
}
