import { Client } from "@upstash/qstash";

const qstashClient = new Client({
  token: process.env.QSTASH_TOKEN!,
});

export async function enqueueRefresh(userId: string) {
  // Ne pas utiliser QStash en développement local
  const isLocalDevelopment = process.env.NEXTAUTH_URL?.includes('127.0.0.1') || 
                            process.env.NEXTAUTH_URL?.includes('localhost') ||
                            process.env.NODE_ENV === 'development';
  
  if (isLocalDevelopment) {
    console.log('🔄 Développement local détecté - ignorer QStash pour userId:', userId);
    return { success: true, local: true };
  }

  return await qstashClient.publishJSON({
    url: `${process.env.NEXTAUTH_URL}/api/queues/refresh-recommendations`,
    body: { userId },
    retries: 3,
  });
}
