/**
 * Utilitaires de logging pour la synchronisation Discogs
 * Fournit des logs structurés pour le monitoring et le debugging
 */

export interface SyncLogEntry {
  timestamp: string;
  userId: string;
  action: 'sync_start' | 'sync_success' | 'sync_error' | 'sync_skip' | 'api_error' | 'rate_limit';
  details?: any;
  duration?: number;
  error?: string;
}

/**
 * Logger centralisé pour les opérations de synchronisation Discogs
 */
export class DiscogsSyncLogger {
  private static logs: SyncLogEntry[] = [];
  private static readonly MAX_LOGS = 1000; // Garder les 1000 derniers logs en mémoire

  /**
   * Enregistre un événement de synchronisation
   */
  static log(entry: Omit<SyncLogEntry, 'timestamp'>) {
    const logEntry: SyncLogEntry = {
      ...entry,
      timestamp: new Date().toISOString()
    };

    // Ajouter à la collection en mémoire
    this.logs.push(logEntry);
    
    // Maintenir la taille maximale
    if (this.logs.length > this.MAX_LOGS) {
      this.logs = this.logs.slice(-this.MAX_LOGS);
    }

    // Log console avec formatage approprié
    this.logToConsole(logEntry);
  }

  /**
   * Formate et affiche le log dans la console
   */
  private static logToConsole(entry: SyncLogEntry) {
    const timestamp = new Date(entry.timestamp).toLocaleTimeString();
    const userId = entry.userId.substring(0, 8) + '...';

    switch (entry.action) {
      case 'sync_start':
        if (process.env.NODE_ENV === 'development') {
        console.log(`🔄 [${timestamp}] Début sync Discogs pour ${userId}`);
        }
        break;
      case 'sync_success':
        const duration = entry.duration ? ` (${Math.round(entry.duration / 1000)}s)` : '';
        const count = entry.details?.syncedCount || 0;
        if (process.env.NODE_ENV === 'development') {
        if (process.env.NODE_ENV === 'development') {
        if (process.env.NODE_ENV === 'development') {
        console.log(`✅ [${timestamp}] Sync réussie pour ${userId}: ${count} albums${duration}`);
        }
        }
        }
        break;
      case 'sync_error':
        console.error(`❌ [${timestamp}] Erreur sync pour ${userId}: ${entry.error}`);
        break;
      case 'sync_skip':
        if (process.env.NODE_ENV === 'development') {
        console.log(`⏰ [${timestamp}] Sync ignorée pour ${userId}: ${entry.details?.reason}`);
        }
        break;
      case 'api_error':
        if (process.env.NODE_ENV === 'development') {
        console.warn(`⚠️ [${timestamp}] Erreur API Discogs pour ${userId}: ${entry.error}`);
        }
        break;
      case 'rate_limit':
        if (process.env.NODE_ENV === 'development') {
        console.warn(`🚦 [${timestamp}] Limite de taux atteinte pour ${userId}`);
        }
        break;
    }
  }

  /**
   * Récupère les logs récents pour un utilisateur
   */
  static getUserLogs(userId: string, limit: number = 10): SyncLogEntry[] {
    return this.logs
      .filter(log => log.userId === userId)
      .slice(-limit);
  }

  /**
   * Récupère les statistiques de synchronisation
   */
  static getStats(since?: Date): {
    total: number;
    success: number;
    errors: number;
    skipped: number;
    avgDuration: number;
    errorRate: number;
  } {
    const sinceTime = since?.getTime() || 0;
    const relevantLogs = this.logs.filter(log => 
      new Date(log.timestamp).getTime() >= sinceTime
    );

    const syncLogs = relevantLogs.filter(log => 
      ['sync_success', 'sync_error', 'sync_skip'].includes(log.action)
    );

    const successLogs = syncLogs.filter(log => log.action === 'sync_success');
    const errorLogs = syncLogs.filter(log => log.action === 'sync_error');
    const skippedLogs = syncLogs.filter(log => log.action === 'sync_skip');

    const durations = successLogs
      .map(log => log.duration)
      .filter(d => d !== undefined) as number[];

    const avgDuration = durations.length > 0 
      ? durations.reduce((a, b) => a + b, 0) / durations.length 
      : 0;

    return {
      total: syncLogs.length,
      success: successLogs.length,
      errors: errorLogs.length,
      skipped: skippedLogs.length,
      avgDuration: Math.round(avgDuration),
      errorRate: syncLogs.length > 0 ? Math.round((errorLogs.length / syncLogs.length) * 100) : 0
    };
  }

  /**
   * Récupère tous les logs récents
   */
  static getRecentLogs(limit: number = 50): SyncLogEntry[] {
    return this.logs.slice(-limit);
  }

  /**
   * Nettoie les anciens logs
   */
  static cleanup(olderThan: Date) {
    const cutoffTime = olderThan.getTime();
    this.logs = this.logs.filter(log => 
      new Date(log.timestamp).getTime() >= cutoffTime
    );
  }
}

/**
 * Types d'erreurs Discogs avec gestion spécifique
 */
export enum DiscogsErrorType {
  RATE_LIMIT = 'rate_limit',
  INVALID_TOKEN = 'invalid_token',
  NETWORK_ERROR = 'network_error',
  API_ERROR = 'api_error',
  PARSING_ERROR = 'parsing_error',
  UNKNOWN = 'unknown'
}

/**
 * Analyse une erreur Discogs et retourne le type approprié
 */
export function categorizeDiscogsError(error: any): DiscogsErrorType {
  if (!error) return DiscogsErrorType.UNKNOWN;

  const errorMessage = error.message || error.toString().toLowerCase();

  if (errorMessage.includes('rate limit') || errorMessage.includes('429')) {
    return DiscogsErrorType.RATE_LIMIT;
  }
  
  if (errorMessage.includes('unauthorized') || errorMessage.includes('401')) {
    return DiscogsErrorType.INVALID_TOKEN;
  }
  
  if (errorMessage.includes('network') || errorMessage.includes('timeout')) {
    return DiscogsErrorType.NETWORK_ERROR;
  }
  
  if (errorMessage.includes('api') || error.status) {
    return DiscogsErrorType.API_ERROR;
  }
  
  if (errorMessage.includes('parse') || errorMessage.includes('json')) {
    return DiscogsErrorType.PARSING_ERROR;
  }

  return DiscogsErrorType.UNKNOWN;
}

/**
 * Détermine la stratégie de retry basée sur le type d'erreur
 */
export function getRetryStrategy(errorType: DiscogsErrorType): {
  shouldRetry: boolean;
  delayMs: number;
  maxRetries: number;
} {
  switch (errorType) {
    case DiscogsErrorType.RATE_LIMIT:
      return { shouldRetry: true, delayMs: 60000, maxRetries: 3 }; // 1 minute
    case DiscogsErrorType.NETWORK_ERROR:
      return { shouldRetry: true, delayMs: 5000, maxRetries: 2 }; // 5 secondes
    case DiscogsErrorType.API_ERROR:
      return { shouldRetry: true, delayMs: 10000, maxRetries: 1 }; // 10 secondes
    case DiscogsErrorType.INVALID_TOKEN:
    case DiscogsErrorType.PARSING_ERROR:
    case DiscogsErrorType.UNKNOWN:
    default:
      return { shouldRetry: false, delayMs: 0, maxRetries: 0 };
  }
}
