/**
 * Pré-chargement des routes critiques pour améliorer les performances
 */

// Pré-charger les modules critiques au démarrage
if (typeof window === 'undefined') { // Côté serveur seulement
  // Pré-charger les composants critiques (seulement ceux qui existent)
  Promise.all([
    import('@/components/layout/header'),
    import('@/components/layout/sidebar'),
    import('@/components/layout/user-nav'),
    import('@/components/layout/search-bar'),
    import('@/lib/db'),
    import('@/lib/db/query-cache'),
  ]).then(() => {
    if (process.env.NODE_ENV === 'development') {
    console.log('🚀 Modules critiques pré-chargés avec succès');
    }
  }).catch((error) => {
    if (process.env.NODE_ENV === 'development') {
    console.warn('⚠️ Erreur lors du pré-chargement des modules:', error);
    }
  });

  // Pré-initialiser la connexion DB
  setTimeout(async () => {
    try {
      const { db } = await import('@/lib/db');
      // Requête simple pour initialiser la connexion
      await db.execute('SELECT 1 as health_check');
      if (process.env.NODE_ENV === 'development') {
      console.log('🚀 Connexion DB pré-initialisée avec succès');
      }
    } catch (error) {
      if (process.env.NODE_ENV === 'development') {
      console.warn('⚠️ Erreur lors de la pré-initialisation DB:', error);
      }
    }
  }, 100);
}

/**
 * Pré-chargement côté client des routes critiques
 */
export function preloadCriticalRoutes() {
  if (typeof window !== 'undefined') {
    // Pré-charger les routes critiques
    const criticalRoutes = [
      '/recommendations',
      '/wishlist', 
      '/collection',
      '/account'
    ];

    criticalRoutes.forEach(route => {
      const link = document.createElement('link');
      link.rel = 'prefetch';
      link.href = route;
      document.head.appendChild(link);
    });

    if (process.env.NODE_ENV === 'development') {
    console.log('🚀 Routes critiques pré-chargées côté client');
    }
  }
}

/**
 * Optimisation du cache DNS
 */
export function optimizeDNS() {
  if (typeof window !== 'undefined') {
    const domains = [
      'aws-0-eu-west-3.pooler.supabase.com',
      'api.spotify.com',
      'api.discogs.com',
      'i.scdn.co'
    ];

    domains.forEach(domain => {
      const link = document.createElement('link');
      link.rel = 'dns-prefetch';
      link.href = `//${domain}`;
      document.head.appendChild(link);
    });

    if (process.env.NODE_ENV === 'development') {
    console.log('🚀 DNS pré-chargé pour les domaines critiques');
    }
  }
}
