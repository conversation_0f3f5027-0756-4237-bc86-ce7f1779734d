/**
 * Authentification Rakuten Advertising
 * Génération et gestion des tokens d'accès OAuth 2.0
 */

interface RakutenTokenResponse {
  access_token: string;
  token_type: string;
  expires_in: number;
  scope?: string;
}

interface RakutenTokenError {
  error: string;
  error_description?: string;
}

/**
 * Génère un token d'accès Rakuten Advertising
 * Selon la documentation officielle: https://developers.rakutenadvertising.com/guides/access_tokens
 */
export async function generateRakutenToken(): Promise<string> {
  const clientId = process.env.RAKUTEN_CLIENT_ID;
  const clientSecret = process.env.RAKUTEN_CLIENT_SECRET;

  if (!clientId || !clientSecret) {
    throw new Error("Variables d'environnement manquantes: RAKUTEN_CLIENT_ID et RAKUTEN_CLIENT_SECRET requis");
  }

  try {
    if (process.env.NODE_ENV === 'development') {
    console.log("🔑 Génération du token d'accès Rakuten...");
    }

    // Endpoint selon la documentation officielle
    const tokenUrl = "https://api.linksynergy.com/token";

    // Méthode 1: Essayer avec Basic Auth (Client Credentials standard)
    if (process.env.NODE_ENV === 'development') {
    console.log("🔄 Tentative avec Basic Auth...");
    }

    const credentials = Buffer.from(`${clientId}:${clientSecret}`).toString('base64');

    const params = new URLSearchParams({
      grant_type: "client_credentials"
      // Note: Le scope peut être optionnel selon la documentation
    });

    const response = await fetch(tokenUrl, {
      method: "POST",
      headers: {
        "Authorization": `Basic ${credentials}`,
        "Content-Type": "application/x-www-form-urlencoded",
        "Accept": "application/json",
        "User-Agent": "Stream2Spin/1.0"
      },
      body: params.toString()
    });

    if (process.env.NODE_ENV === 'development') {
    console.log(`📡 Réponse HTTP: ${response.status} ${response.statusText}`);
    }

    // Log des headers de réponse pour debugging
    const responseHeaders: Record<string, string> = {};
    response.headers.forEach((value, key) => {
      responseHeaders[key] = value;
    });
    if (process.env.NODE_ENV === 'development') {
    console.log("📋 Headers de réponse:", responseHeaders);
    }

    const responseText = await response.text();
    if (process.env.NODE_ENV === 'development') {
    console.log("📄 Réponse brute:", responseText);
    }

    if (!response.ok) {
      // Essayer de parser comme JSON, sinon utiliser le texte brut
      let errorData: any;
      try {
        errorData = JSON.parse(responseText);
      } catch {
        errorData = { error: "unknown", error_description: responseText };
      }

      throw new Error(`Erreur HTTP ${response.status}: ${errorData.error || 'Unknown'} - ${errorData.error_description || responseText}`);
    }

    // Parser la réponse
    let tokenData: RakutenTokenResponse;
    try {
      tokenData = JSON.parse(responseText);
    } catch (parseError) {
      throw new Error(`Erreur de parsing JSON: ${parseError}. Réponse: ${responseText}`);
    }

    if (!tokenData.access_token) {
      throw new Error(`Token manquant dans la réponse: ${JSON.stringify(tokenData)}`);
    }

    if (process.env.NODE_ENV === 'development') {
    if (process.env.NODE_ENV === 'development') {
    if (process.env.NODE_ENV === 'development') {
    console.log(`✅ Token généré avec succès (expire dans ${tokenData.expires_in || 'inconnu'} secondes)`);
    }
    }
    }

    return tokenData.access_token;

  } catch (error) {
    console.error("❌ Erreur lors de la génération du token Rakuten:", error);

    // Ajouter des informations de debugging
    if (error instanceof Error) {
      console.error("📋 Détails de l'erreur:", {
        message: error.message,
        stack: error.stack,
        clientIdLength: process.env.RAKUTEN_CLIENT_ID?.length || 0,
        clientSecretLength: process.env.RAKUTEN_CLIENT_SECRET?.length || 0
      });
    }

    throw error;
  }
}

/**
 * Cache simple pour le token (en mémoire)
 * En production, vous pourriez vouloir utiliser Redis ou une base de données
 */
class RakutenTokenCache {
  private token: string | null = null;
  private expiresAt: number = 0;

  async getValidToken(): Promise<string> {
    // Vérifier si le token est encore valide (avec une marge de 5 minutes)
    const now = Date.now();
    const bufferTime = 5 * 60 * 1000; // 5 minutes en millisecondes

    if (this.token && now < (this.expiresAt - bufferTime)) {
      if (process.env.NODE_ENV === 'development') {
      console.log("🔄 Utilisation du token en cache");
      }
      return this.token;
    }

    // Générer un nouveau token
    if (process.env.NODE_ENV === 'development') {
    console.log("🔄 Génération d'un nouveau token (cache expiré ou vide)");
    }
    this.token = await generateRakutenToken();
    
    // Les tokens Rakuten expirent généralement après 1 heure
    this.expiresAt = now + (60 * 60 * 1000); // 1 heure

    return this.token;
  }

  clearCache(): void {
    this.token = null;
    this.expiresAt = 0;
    if (process.env.NODE_ENV === 'development') {
    console.log("🗑️ Cache de token vidé");
    }
  }
}

// Instance singleton du cache
const tokenCache = new RakutenTokenCache();

/**
 * Obtient un token d'accès valide (utilise le cache si possible)
 */
export async function getRakutenToken(): Promise<string> {
  return tokenCache.getValidToken();
}

/**
 * Vide le cache de token (utile en cas d'erreur d'authentification)
 */
export function clearRakutenTokenCache(): void {
  tokenCache.clearCache();
}

/**
 * Teste la validité d'un token en faisant un appel simple à l'API
 */
export async function testRakutenToken(token: string): Promise<boolean> {
  try {
    // Test simple avec l'API Product Search
    const testUrl = "https://api.linksynergy.com/productsearch/1.0";
    const params = new URLSearchParams({
      keyword: "test",
      max: "1",
      pagenumber: "1"
    });

    const response = await fetch(`${testUrl}?${params}`, {
      method: "GET",
      headers: {
        "Authorization": `Bearer ${token}`,
        "Accept": "application/json"
      }
    });

    return response.ok;
  } catch (error) {
    console.error("❌ Erreur lors du test du token:", error);
    return false;
  }
}

/**
 * Fonction utilitaire pour générer et tester un token
 * Utile pour les tests et le debugging
 */
export async function generateAndTestToken(): Promise<{
  token: string;
  isValid: boolean;
  expiresIn: string;
  debugInfo?: any;
}> {
  try {
    const token = await generateRakutenToken();
    const isValid = await testRakutenToken(token);

    return {
      token: token.substring(0, 20) + "...", // Masquer le token complet pour la sécurité
      isValid,
      expiresIn: "1 hour",
      debugInfo: {
        tokenLength: token.length,
        tokenStart: token.substring(0, 10),
        hasBearer: token.includes('Bearer'),
        isJWT: token.includes('.')
      }
    };
  } catch (error) {
    throw new Error(`Échec de génération/test du token: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Fonction de test alternative pour essayer différentes méthodes d'authentification
 * Utile pour le debugging avec la documentation Rakuten
 */
export async function debugTokenGeneration(): Promise<any> {
  const clientId = process.env.RAKUTEN_CLIENT_ID;
  const clientSecret = process.env.RAKUTEN_CLIENT_SECRET;

  if (!clientId || !clientSecret) {
    throw new Error("Variables d'environnement manquantes");
  }

  const results: any = {
    clientId: clientId.substring(0, 8) + "...",
    clientSecret: clientSecret.substring(0, 8) + "...",
    attempts: []
  };

  // Tentative 1: Basic Auth avec grant_type=client_credentials
  try {
    if (process.env.NODE_ENV === 'development') {
    if (process.env.NODE_ENV === 'development') {
    if (process.env.NODE_ENV === 'development') {
    console.log("🧪 Test 1: Basic Auth standard");
    }
    }
    }
    const credentials = Buffer.from(`${clientId}:${clientSecret}`).toString('base64');

    const response1 = await fetch("https://api.linksynergy.com/token", {
      method: "POST",
      headers: {
        "Authorization": `Basic ${credentials}`,
        "Content-Type": "application/x-www-form-urlencoded",
        "Accept": "application/json"
      },
      body: new URLSearchParams({
        grant_type: "client_credentials"
      }).toString()
    });

    const text1 = await response1.text();
    results.attempts.push({
      method: "Basic Auth",
      status: response1.status,
      statusText: response1.statusText,
      response: text1.substring(0, 200),
      headers: Object.fromEntries(response1.headers.entries())
    });
  } catch (error) {
    results.attempts.push({
      method: "Basic Auth",
      error: error instanceof Error ? error.message : "Unknown error"
    });
  }

  // Tentative 2: POST avec client_id et client_secret dans le body
  try {
    if (process.env.NODE_ENV === 'development') {
    if (process.env.NODE_ENV === 'development') {
    if (process.env.NODE_ENV === 'development') {
    console.log("🧪 Test 2: Credentials dans le body");
    }
    }
    }
    const response2 = await fetch("https://api.linksynergy.com/token", {
      method: "POST",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
        "Accept": "application/json"
      },
      body: new URLSearchParams({
        grant_type: "client_credentials",
        client_id: clientId,
        client_secret: clientSecret
      }).toString()
    });

    const text2 = await response2.text();
    results.attempts.push({
      method: "Body Credentials",
      status: response2.status,
      statusText: response2.statusText,
      response: text2.substring(0, 200),
      headers: Object.fromEntries(response2.headers.entries())
    });
  } catch (error) {
    results.attempts.push({
      method: "Body Credentials",
      error: error instanceof Error ? error.message : "Unknown error"
    });
  }

  return results;
}
