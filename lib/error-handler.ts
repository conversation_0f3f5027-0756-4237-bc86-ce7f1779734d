/**
 * Gestionnaire d'erreurs global pour les erreurs HTTP et les erreurs d'application
 */

import { signOut } from "next-auth/react";

export interface ErrorHandlerOptions {
  showToast?: boolean;
  redirectOnUnauthorized?: boolean;
  logError?: boolean;
}

export class AppError extends Error {
  constructor(
    message: string,
    public statusCode: number,
    public code?: string
  ) {
    super(message);
    this.name = "AppError";
  }
}

/**
 * Gestionnaire d'erreurs HTTP pour les réponses d'API
 */
export async function handleHttpError(
  response: Response,
  options: ErrorHandlerOptions = {}
): Promise<never> {
  const {
    showToast = true,
    redirectOnUnauthorized = true,
    logError = true,
  } = options;

  let errorMessage = "Une erreur est survenue";
  let errorCode: string | undefined;

  try {
    const errorData = await response.json();
    errorMessage = errorData.message || errorData.error || errorMessage;
    errorCode = errorData.code;
  } catch {
    // Si on ne peut pas parser la réponse JSON, utiliser le message par défaut
  }

  // Log l'erreur (sans données sensibles)
  if (logError) {
    console.error("HTTP Error:", {
      status: response.status,
      statusText: response.statusText,
      url: response.url,
      message: errorMessage,
      code: errorCode,
      timestamp: new Date().toISOString(),
    });
  }

  // Gestion spécifique selon le code de statut
  switch (response.status) {
    case 401:
      if (redirectOnUnauthorized) {
        // Nettoyer la session et rediriger vers la page de connexion
        await signOut({ redirect: false });
        
        if (typeof window !== "undefined") {
          const currentPath = window.location.pathname;
          const loginUrl = `/login?callbackUrl=${encodeURIComponent(currentPath)}`;
          window.location.href = loginUrl;
        }
      }
      throw new AppError("Vous devez vous connecter pour accéder à cette ressource", 401, errorCode);

    case 403:
      throw new AppError("Vous n'avez pas les permissions pour accéder à cette ressource", 403, errorCode);

    case 404:
      throw new AppError("La ressource demandée n'a pas été trouvée", 404, errorCode);

    case 429:
      throw new AppError("Trop de requêtes. Veuillez patienter avant de réessayer", 429, errorCode);

    case 500:
    case 502:
    case 503:
    case 504:
      throw new AppError("Erreur serveur. Veuillez réessayer plus tard", response.status, errorCode);

    default:
      throw new AppError(errorMessage, response.status, errorCode);
  }
}

/**
 * Wrapper pour fetch qui gère automatiquement les erreurs HTTP
 */
export async function fetchWithErrorHandling(
  url: string,
  options: RequestInit = {},
  errorOptions: ErrorHandlerOptions = {}
): Promise<Response> {
  try {
    const response = await fetch(url, options);

    if (!response.ok) {
      await handleHttpError(response, errorOptions);
    }

    return response;
  } catch (error) {
    if (error instanceof AppError) {
      throw error;
    }

    // Erreur réseau ou autre erreur non HTTP
    console.error("Network or other error:", error);
    throw new AppError("Erreur de connexion. Vérifiez votre connexion internet", 0);
  }
}

/**
 * Utilitaire pour naviguer vers une page d'erreur avec le contexte approprié
 */
export function navigateToErrorPage(
  statusCode: number,
  fromUrl?: string,
  router?: any
) {
  if (typeof window === "undefined" || !router) return;

  const currentPath = fromUrl || window.location.pathname;
  
  switch (statusCode) {
    case 401:
    case 403:
      // Pour les erreurs de permission, utiliser un composant spécialisé
      // qui gère la déconnexion automatique
      router.push(`/error/permission?from=${encodeURIComponent(currentPath)}`);
      break;
    
    case 404:
      router.push("/404");
      break;
    
    case 500:
    default:
      router.push(`/error/server?from=${encodeURIComponent(currentPath)}`);
      break;
  }
}
