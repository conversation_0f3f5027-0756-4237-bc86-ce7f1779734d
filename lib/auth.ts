import { getServerSession } from "next-auth/next";
import { authOptions } from "@/auth";
import { db } from "@/lib/db";
import { recommendations } from "@/lib/db/schema";
import { eq, desc } from "drizzle-orm";

export const getSession = () => getServerSession(authOptions);
export const auth = getSession; // Alias pour compatibilité

export { signIn, signOut } from "next-auth/react";

/**
 * Vérifie si l'utilisateur a des recommandations récentes (moins de 24h)
 * @param userId - ID de l'utilisateur
 * @returns true si les recommandations sont récentes, false sinon
 */
export async function hasRecentRecommendations(userId: string): Promise<boolean> {
  try {
    // Récupérer la recommandation la plus récente
    const latestRecommendation = await db.query.recommendations.findFirst({
      where: eq(recommendations.userId, userId),
      orderBy: [desc(recommendations.generatedAt)],
      columns: {
        generatedAt: true
      }
    });

    if (!latestRecommendation) {
      return false; // Aucune recommandation trouvée
    }

    // Calculer la différence en heures
    const now = new Date();
    const generatedAt = new Date(latestRecommendation.generatedAt);
    const hoursDifference = (now.getTime() - generatedAt.getTime()) / (1000 * 60 * 60);

    // Retourner true si les recommandations ont moins de 24h
    return hoursDifference < 24;
  } catch (error) {
    console.error("Erreur lors de la vérification de la fraîcheur des recommandations:", error);
    return false; // En cas d'erreur, considérer comme non récent pour forcer la régénération
  }
}
