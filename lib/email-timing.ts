/**
 * Module d'optimisation des horaires d'envoi d'emails
 * Améliore la délivrabilité en envoyant aux moments optimaux
 */

import { db } from '@/lib/db';
import { users } from '@/lib/db/schema';
import { eq, and, gte, lte } from 'drizzle-orm';
import {
  getOptimalHours,
  getOptimalDays,
  isBlackoutDate,
  calculateOptimalFrequency as calculateOptimalFrequencyConfig,
  getOptimalDelay,
  getOptimalBatchSize
} from '@/lib/email-timing-config';

// Horaires optimaux par fuseau horaire (heure locale)
const OPTIMAL_SEND_TIMES = {
  'Europe/Paris': { hour: 9, minute: 30 }, // 9h30 en France
  'America/New_York': { hour: 10, minute: 0 }, // 10h00 EST
  'America/Los_Angeles': { hour: 9, minute: 0 }, // 9h00 PST
  'Asia/Tokyo': { hour: 10, minute: 30 }, // 10h30 JST
  'Australia/Sydney': { hour: 9, minute: 0 }, // 9h00 AEST
  'Europe/London': { hour: 9, minute: 30 }, // 9h30 GMT
  'America/Toronto': { hour: 10, minute: 0 }, // 10h00 EST
  'Europe/Berlin': { hour: 9, minute: 30 }, // 9h30 CET
} as const;

// Jours de la semaine optimaux (0 = dimanche, 1 = lundi, etc.)
const OPTIMAL_DAYS = [2, 3, 4]; // Mardi, mercredi, jeudi

// Heures à éviter (format 24h)
const AVOID_HOURS = [0, 1, 2, 3, 4, 5, 6, 22, 23]; // Nuit et très tard le soir

/**
 * Calcule l'heure optimale d'envoi pour un utilisateur (version améliorée)
 */
export function calculateOptimalSendTime(
  userTimezone: string = 'Europe/Paris',
  preferredDay?: number,
  priority: 'high' | 'normal' | 'low' = 'normal'
): Date {
  const now = new Date();
  const optimalHours = getOptimalHours(userTimezone);
  const optimalDays = getOptimalDays(userTimezone);

  // Créer une date cible
  let targetDate = new Date();
  let daysToAdd = 1;

  // Trouver le prochain jour optimal qui n'est pas un jour férié
  while (daysToAdd <= 14) { // Limite à 2 semaines pour éviter les boucles infinies
    const candidateDate = new Date(now);
    candidateDate.setDate(now.getDate() + daysToAdd);

    const dayOfWeek = candidateDate.getDay();
    const isOptimalDay = preferredDay !== undefined ?
      dayOfWeek === preferredDay :
      optimalDays.includes(dayOfWeek);

    if (isOptimalDay && !isBlackoutDate(candidateDate)) {
      targetDate = candidateDate;
      break;
    }

    daysToAdd++;
  }

  // Choisir l'heure optimale selon la priorité
  let targetHour: number;
  if (priority === 'high') {
    // Emails prioritaires : première heure optimale
    targetHour = optimalHours.primary[0];
  } else if (priority === 'normal') {
    // Emails normaux : heure optimale aléatoire pour répartir la charge
    const allOptimalHours = [...optimalHours.primary, ...optimalHours.secondary];
    targetHour = allOptimalHours[Math.floor(Math.random() * allOptimalHours.length)];
  } else {
    // Emails de faible priorité : heures secondaires
    targetHour = optimalHours.secondary[Math.floor(Math.random() * optimalHours.secondary.length)];
  }

  // Ajouter une variation aléatoire de ±15 minutes pour éviter les pics
  const randomMinutes = Math.floor(Math.random() * 31) - 15; // -15 à +15 minutes
  targetDate.setHours(targetHour, Math.max(0, Math.min(59, 30 + randomMinutes)), 0, 0);

  return targetDate;
}

/**
 * Vérifie si c'est un bon moment pour envoyer des emails (version améliorée)
 */
export function isGoodTimeToSend(
  userTimezone: string = 'Europe/Paris',
  currentTime: Date = new Date()
): boolean {
  const hour = currentTime.getHours();
  const day = currentTime.getDay();

  // Vérifier si c'est un jour férié
  if (isBlackoutDate(currentTime)) {
    return false;
  }

  // Obtenir la configuration pour ce fuseau horaire
  const optimalHours = getOptimalHours(userTimezone);
  const optimalDays = getOptimalDays(userTimezone);

  // Éviter les heures à éviter
  if (optimalHours.avoid.includes(hour)) {
    return false;
  }

  // Vérifier si c'est un jour optimal
  if (!optimalDays.includes(day)) {
    return false;
  }

  // Préférer les heures optimales (primaires ou secondaires)
  const allOptimalHours = [...optimalHours.primary, ...optimalHours.secondary];
  return allOptimalHours.includes(hour);
}

/**
 * Calcule le délai avant le prochain créneau optimal
 */
export function getDelayUntilOptimalTime(
  userTimezone: string = 'Europe/Paris'
): number {
  const now = new Date();
  const optimalTime = calculateOptimalSendTime(userTimezone);
  
  return Math.max(0, optimalTime.getTime() - now.getTime());
}

/**
 * Groupe les utilisateurs par fuseau horaire pour l'envoi en lot
 */
export async function groupUsersByTimezone(): Promise<Map<string, string[]>> {
  const allUsers = await db
    .select({
      id: users.id,
      timezone: users.timezone,
      emailNotificationsEnabled: users.emailNotificationsEnabled
    })
    .from(users)
    .where(eq(users.emailNotificationsEnabled, true));

  const timezoneGroups = new Map<string, string[]>();

  for (const user of allUsers) {
    const timezone = user.timezone || 'Europe/Paris';
    
    if (!timezoneGroups.has(timezone)) {
      timezoneGroups.set(timezone, []);
    }
    
    timezoneGroups.get(timezone)!.push(user.id);
  }

  return timezoneGroups;
}

/**
 * Planifie l'envoi d'emails selon les fuseaux horaires
 */
export interface EmailSchedule {
  timezone: string;
  userIds: string[];
  sendTime: Date;
  delayMs: number;
}

export async function createEmailSchedule(): Promise<EmailSchedule[]> {
  const timezoneGroups = await groupUsersByTimezone();
  const schedule: EmailSchedule[] = [];

  for (const [timezone, userIds] of timezoneGroups) {
    const sendTime = calculateOptimalSendTime(timezone);
    const delayMs = getDelayUntilOptimalTime(timezone);

    schedule.push({
      timezone,
      userIds,
      sendTime,
      delayMs
    });
  }

  // Trier par délai croissant
  schedule.sort((a, b) => a.delayMs - b.delayMs);

  return schedule;
}

/**
 * Vérifie si un utilisateur a reçu un email récemment (éviter le spam)
 */
export async function hasRecentEmail(
  userId: string,
  hoursThreshold: number = 24
): Promise<boolean> {
  const thresholdDate = new Date();
  thresholdDate.setHours(thresholdDate.getHours() - hoursThreshold);

  const user = await db
    .select({
      lastEmailSent: users.lastEmailSent
    })
    .from(users)
    .where(eq(users.id, userId))
    .limit(1);

  if (user.length === 0 || !user[0].lastEmailSent) {
    return false;
  }

  return user[0].lastEmailSent > thresholdDate;
}

/**
 * Met à jour la date du dernier email envoyé
 */
export async function updateLastEmailSent(userId: string): Promise<void> {
  await db
    .update(users)
    .set({
      lastEmailSent: new Date()
    })
    .where(eq(users.id, userId));
}

/**
 * Calcule la fréquence d'envoi optimale selon l'engagement de l'utilisateur
 */
export function calculateOptimalFrequency(
  openRate: number,
  clickRate: number,
  currentFrequency: 'weekly' | 'biweekly' | 'monthly'
): 'weekly' | 'biweekly' | 'monthly' {
  // Si l'engagement est élevé, on peut envoyer plus fréquemment
  if (openRate > 0.3 && clickRate > 0.1) {
    return 'weekly';
  }
  
  // Si l'engagement est moyen, bi-hebdomadaire
  if (openRate > 0.15 && clickRate > 0.05) {
    return 'biweekly';
  }
  
  // Si l'engagement est faible, mensuel
  return 'monthly';
}

/**
 * Génère un rapport de timing pour le monitoring
 */
export interface TimingReport {
  totalUsers: number;
  timezoneDistribution: Record<string, number>;
  nextSendTimes: Record<string, string>;
  usersWithRecentEmails: number;
}

export async function generateTimingReport(): Promise<TimingReport> {
  const timezoneGroups = await groupUsersByTimezone();
  const totalUsers = Array.from(timezoneGroups.values()).reduce((sum, users) => sum + users.length, 0);
  
  const timezoneDistribution: Record<string, number> = {};
  const nextSendTimes: Record<string, string> = {};
  
  for (const [timezone, userIds] of timezoneGroups) {
    timezoneDistribution[timezone] = userIds.length;
    nextSendTimes[timezone] = calculateOptimalSendTime(timezone).toISOString();
  }

  // Compter les utilisateurs avec des emails récents
  const recentEmailUsers = await db
    .select({ count: users.id })
    .from(users)
    .where(
      and(
        eq(users.emailNotificationsEnabled, true),
        gte(users.lastEmailSent, new Date(Date.now() - 24 * 60 * 60 * 1000))
      )
    );

  return {
    totalUsers,
    timezoneDistribution,
    nextSendTimes,
    usersWithRecentEmails: recentEmailUsers.length
  };
}
