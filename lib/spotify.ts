/**
 * Utilitaires pour l'API Spotify
 * Epic 3 - Gestion des tokens et appels API
 */

import { db } from "@/lib/db";
import { accounts } from "@/lib/db/schema";
import { eq, and } from "drizzle-orm";

export interface SpotifyTokens {
  access_token: string;
  refresh_token: string;
  expires_at: number;
}

export interface SpotifyTrack {
  id: string;
  name: string;
  preview_url: string | null; // URL de l'extrait audio de 30 secondes
  album: {
    id: string;
    name: string;
    images: Array<{ url: string; height: number; width: number }>;
    artists: Array<{ id: string; name: string }>;
  };
  artists: Array<{ id: string; name: string }>;
}

export interface SpotifyTopTracksResponse {
  items: SpotifyTrack[];
  total: number;
  limit: number;
  offset: number;
}

/**
 * Obtient un access token Spotify valide pour un utilisateur
 * Rafraîchit automatiquement le token si nécessaire
 */
export async function getValidSpotifyToken(userId: string): Promise<string | null> {
  try {
    // Récupérer les informations du compte Spotify de l'utilisateur
    const spotifyAccount = await db.query.accounts.findFirst({
      where: and(
        eq(accounts.userId, userId),
        eq(accounts.provider, "spotify")
      ),
    });

    if (!spotifyAccount) {
      console.error(`❌ Aucun compte Spotify trouvé pour l'utilisateur ${userId}`);
      return null;
    }

    if (!spotifyAccount.refresh_token) {
      console.error(`❌ Pas de refresh token pour l'utilisateur ${userId}`);
      return null;
    }

    // Vérifier si le token actuel est encore valide
    const now = Math.floor(Date.now() / 1000);
    if (spotifyAccount.expires_at && spotifyAccount.expires_at > now && spotifyAccount.access_token) {
      // Token encore valide
      return spotifyAccount.access_token;
    }

    // Token expiré, le rafraîchir
    if (process.env.NODE_ENV === 'development') {
    console.log(`🔄 Rafraîchissement du token Spotify pour l'utilisateur ${userId}`);
    }
    
    const refreshedTokens = await refreshSpotifyToken(spotifyAccount.refresh_token);
    
    if (!refreshedTokens) {
      console.error(`❌ Impossible de rafraîchir le token pour l'utilisateur ${userId}`);
      return null;
    }

    // Mettre à jour les tokens en base de données
    await db.update(accounts)
      .set({
        access_token: refreshedTokens.access_token,
        expires_at: refreshedTokens.expires_at,
        // Le refresh_token peut changer selon la configuration Spotify
        refresh_token: refreshedTokens.refresh_token || spotifyAccount.refresh_token,
      })
      .where(and(
        eq(accounts.userId, userId),
        eq(accounts.provider, "spotify")
      ));

    if (process.env.NODE_ENV === 'development') {
    if (process.env.NODE_ENV === 'development') {
    if (process.env.NODE_ENV === 'development') {
    console.log(`✅ Token Spotify rafraîchi avec succès pour l'utilisateur ${userId}`);
    }
    }
    }
    return refreshedTokens.access_token;

  } catch (error) {
    console.error(`❌ Erreur lors de l'obtention du token Spotify pour l'utilisateur ${userId}:`, error);
    return null;
  }
}

/**
 * Rafraîchit un access token Spotify en utilisant le refresh token
 */
async function refreshSpotifyToken(refreshToken: string): Promise<SpotifyTokens | null> {
  try {
    const clientId = process.env.AUTH_SPOTIFY_ID;
    const clientSecret = process.env.AUTH_SPOTIFY_SECRET;

    if (!clientId || !clientSecret) {
      throw new Error("Configuration Spotify manquante");
    }

    const response = await fetch("https://accounts.spotify.com/api/token", {
      method: "POST",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
        "Authorization": `Basic ${Buffer.from(`${clientId}:${clientSecret}`).toString("base64")}`,
      },
      body: new URLSearchParams({
        grant_type: "refresh_token",
        refresh_token: refreshToken,
      }),
    });

    if (!response.ok) {
      const errorData = await response.text();
      throw new Error(`Erreur lors du refresh token: ${response.status} ${errorData}`);
    }

    const data = await response.json();

    return {
      access_token: data.access_token,
      refresh_token: data.refresh_token || refreshToken, // Spotify peut ne pas renvoyer un nouveau refresh_token
      expires_at: Math.floor(Date.now() / 1000) + data.expires_in,
    };

  } catch (error) {
    console.error("❌ Erreur lors du rafraîchissement du token Spotify:", error);
    return null;
  }
}

/**
 * Récupère les top tracks d'un utilisateur via l'API Spotify
 */
export async function fetchUserTopTracks(
  accessToken: string,
  timeRange: "short_term" | "medium_term" | "long_term" = "short_term",
  limit: number = 50
): Promise<SpotifyTrack[] | null> {
  try {
    const url = `https://api.spotify.com/v1/me/top/tracks?limit=${limit}&time_range=${timeRange}`;
    
    const response = await fetch(url, {
      headers: {
        "Authorization": `Bearer ${accessToken}`,
        "Content-Type": "application/json",
      },
    });

    if (!response.ok) {
      const errorData = await response.text();
      throw new Error(`Erreur API Spotify: ${response.status} ${errorData}`);
    }

    const data: SpotifyTopTracksResponse = await response.json();
    return data.items || [];

  } catch (error) {
    console.error("❌ Erreur lors de la récupération des top tracks:", error);
    return null;
  }
}

/**
 * Analyse les tracks et calcule les scores d'albums
 * Implémente l'algorithme de pondération basé sur le classement
 * US 3.6: Inclut maintenant les informations du titre phare pour chaque album
 * Amélioration: Calcule le nombre d'écoutes estimé basé sur la fréquence des tracks
 */
export function analyzeTracksAndCalculateScores(tracks: SpotifyTrack[], timeframe: string = "short_term"): Array<{
  spotifyAlbumId: string;
  artistName: string;
  albumTitle: string;
  albumCoverUrl: string | null;
  listenScore: number;
  estimatedPlays: number; // Nouveau: nombre d'écoutes estimé
  timeframe: string;
  // US 3.6: Informations du titre phare
  topTrackName: string | null;
  topTrackId: string | null;
  topTrackPreviewUrl: string | null;
  topTrackListenScore: number | null;
}> {
  const albumScores = new Map<string, any>();

  tracks.forEach((track, index) => {
    const album = track.album;
    if (!album) return;

    const albumKey = album.id;
    const position = index + 1; // Position dans le classement (1-50)

    // Calcul du score : plus la position est haute, plus le score est élevé
    // Score = 51 - position (track #1 = 50 points, track #50 = 1 point)
    const trackScore = 51 - position;

    // Estimation du nombre d'écoutes basée sur la position
    // Les tracks en haut du classement ont plus d'écoutes
    const estimatedPlaysForTrack = Math.max(1, Math.round((51 - position) * 2.5));

    if (albumScores.has(albumKey)) {
      // Album déjà présent, additionner le score et les écoutes estimées
      const existingAlbum = albumScores.get(albumKey);
      existingAlbum.listenScore += trackScore;
      existingAlbum.estimatedPlays += estimatedPlaysForTrack;

      // US 3.6: Stratégie améliorée pour le titre phare
      const shouldUpdateTopTrack =
        // Cas 1: Ce track a un meilleur score
        trackScore > (existingAlbum.topTrackListenScore || 0) ||
        // Cas 2: Score égal mais ce track a une preview_url et l'actuel n'en a pas
        (trackScore === (existingAlbum.topTrackListenScore || 0) &&
         track.preview_url && !existingAlbum.topTrackPreviewUrl) ||
        // Cas 3: L'actuel n'a pas de preview_url et ce track en a une (même avec un score plus faible)
        (!existingAlbum.topTrackPreviewUrl && track.preview_url);

      if (shouldUpdateTopTrack) {
        existingAlbum.topTrackName = track.name;
        existingAlbum.topTrackId = track.id;
        existingAlbum.topTrackPreviewUrl = track.preview_url;
        existingAlbum.topTrackListenScore = trackScore;
      }
    } else {
      // Nouvel album
      albumScores.set(albumKey, {
        spotifyAlbumId: album.id,
        artistName: album.artists[0]?.name || "Unknown Artist",
        albumTitle: album.name,
        albumCoverUrl: album.images[0]?.url || null,
        listenScore: trackScore,
        estimatedPlays: estimatedPlaysForTrack,
        timeframe: timeframe,
        // US 3.6: Informations du titre phare (premier track de cet album)
        topTrackName: track.name,
        topTrackId: track.id,
        topTrackPreviewUrl: track.preview_url,
        topTrackListenScore: trackScore,
      });
    }
  });

  // Convertir en array et trier par score décroissant
  return Array.from(albumScores.values())
    .sort((a, b) => b.listenScore - a.listenScore)
    .slice(0, 50); // Garder les 50 meilleurs albums selon les spécifications
}

/**
 * Valide qu'un token Spotify est encore utilisable
 */
export async function validateSpotifyToken(accessToken: string): Promise<boolean> {
  try {
    const response = await fetch("https://api.spotify.com/v1/me", {
      headers: {
        "Authorization": `Bearer ${accessToken}`,
      },
    });

    return response.ok;
  } catch (error) {
    return false;
  }
}
