import { db } from "@/lib/db";
import { publicListAnalytics, users } from "@/lib/db/schema";
import { eq, and, desc, count, sql } from "drizzle-orm";
import { headers } from "next/headers";

export type AnalyticsEventType = 'view' | 'share' | 'signup_click' | 'signup_conversion';

interface AnalyticsEventData {
  timeframe?: string;
  albumTitle?: string;
  artistName?: string;
  referrer?: string;
  userAgent?: string;
  [key: string]: any;
}

/**
 * Enregistre un événement analytics pour une liste publique
 */
export async function trackPublicListEvent(
  publicListId: string,
  eventType: AnalyticsEventType,
  eventData?: AnalyticsEventData
) {
  try {
    // Récupérer l'utilisateur propriétaire de la liste
    const user = await db.query.users.findFirst({
      where: eq(users.publicListId, publicListId),
      columns: { id: true, publicListEnabled: true },
    });

    if (!user || !user.publicListEnabled) {
      if (process.env.NODE_ENV === 'development') {
      console.warn(`Liste publique introuvable ou privée: ${publicListId}`);
      }
      return null;
    }

    // Récupérer les headers de la requête
    const headersList = await headers();
    const ipAddress = headersList.get('x-forwarded-for') || 
                     headersList.get('x-real-ip') || 
                     'unknown';
    const userAgent = headersList.get('user-agent') || 'unknown';
    const referrer = headersList.get('referer') || eventData?.referrer || null;

    // Pour les vues, éviter les doublons récents (même IP dans les 5 dernières minutes)
    if (eventType === 'view') {
      const recentView = await db.query.publicListAnalytics.findFirst({
        where: and(
          eq(publicListAnalytics.publicListId, publicListId),
          eq(publicListAnalytics.eventType, 'view'),
          eq(publicListAnalytics.ipAddress, ipAddress),
          sql`${publicListAnalytics.timestamp} > NOW() - INTERVAL '5 minutes'`
        ),
      });

      if (recentView) {
        if (process.env.NODE_ENV === 'development') {
        console.log(`Vue dupliquée ignorée pour ${publicListId} depuis ${ipAddress}`);
        }
        return null;
      }
    }

    // Enregistrer l'événement
    const analyticsEvent = await db.insert(publicListAnalytics).values({
      userId: user.id,
      publicListId,
      eventType,
      eventData: eventData ? JSON.stringify(eventData) : null,
      ipAddress,
      userAgent,
      referrer,
    }).returning();

    if (process.env.NODE_ENV === 'development') {
    console.log(`📊 Événement analytics enregistré: ${eventType} pour ${publicListId}`);
    }
    return analyticsEvent[0];

  } catch (error) {
    console.error('Erreur lors de l\'enregistrement analytics:', error);
    return null;
  }
}

/**
 * Récupère les métriques d'une liste publique pour son propriétaire
 */
export async function getPublicListMetrics(userId: string, publicListId: string) {
  try {
    // Vérifier que l'utilisateur est propriétaire de la liste
    const user = await db.query.users.findFirst({
      where: and(
        eq(users.id, userId),
        eq(users.publicListId, publicListId)
      ),
    });

    if (!user) {
      throw new Error('Liste non trouvée ou accès non autorisé');
    }

    // Récupérer les métriques globales
    const totalViews = await db.select({ count: count() })
      .from(publicListAnalytics)
      .where(and(
        eq(publicListAnalytics.publicListId, publicListId),
        eq(publicListAnalytics.eventType, 'view')
      ));

    const totalShares = await db.select({ count: count() })
      .from(publicListAnalytics)
      .where(and(
        eq(publicListAnalytics.publicListId, publicListId),
        eq(publicListAnalytics.eventType, 'share')
      ));

    const signupClicks = await db.select({ count: count() })
      .from(publicListAnalytics)
      .where(and(
        eq(publicListAnalytics.publicListId, publicListId),
        eq(publicListAnalytics.eventType, 'signup_click')
      ));

    const signupConversions = await db.select({ count: count() })
      .from(publicListAnalytics)
      .where(and(
        eq(publicListAnalytics.publicListId, publicListId),
        eq(publicListAnalytics.eventType, 'signup_conversion')
      ));

    // Récupérer les vues des 7 derniers jours
    const recentViews = await db.select({
      date: sql<string>`DATE(${publicListAnalytics.timestamp})`,
      count: count(),
    })
      .from(publicListAnalytics)
      .where(and(
        eq(publicListAnalytics.publicListId, publicListId),
        eq(publicListAnalytics.eventType, 'view'),
        sql`${publicListAnalytics.timestamp} > NOW() - INTERVAL '7 days'`
      ))
      .groupBy(sql`DATE(${publicListAnalytics.timestamp})`)
      .orderBy(sql`DATE(${publicListAnalytics.timestamp}) DESC`);

    // Récupérer les referrers les plus fréquents
    const topReferrers = await db.select({
      referrer: publicListAnalytics.referrer,
      count: count(),
    })
      .from(publicListAnalytics)
      .where(and(
        eq(publicListAnalytics.publicListId, publicListId),
        eq(publicListAnalytics.eventType, 'view'),
        sql`${publicListAnalytics.referrer} IS NOT NULL`
      ))
      .groupBy(publicListAnalytics.referrer)
      .orderBy(desc(count()))
      .limit(5);

    // Calculer le taux de conversion
    const conversionRate = signupClicks[0]?.count > 0 
      ? (signupConversions[0]?.count / signupClicks[0]?.count) * 100 
      : 0;

    return {
      totalViews: totalViews[0]?.count || 0,
      totalShares: totalShares[0]?.count || 0,
      signupClicks: signupClicks[0]?.count || 0,
      signupConversions: signupConversions[0]?.count || 0,
      conversionRate: Math.round(conversionRate * 100) / 100,
      recentViews: recentViews || [],
      topReferrers: topReferrers || [],
    };

  } catch (error) {
    console.error('Erreur lors de la récupération des métriques:', error);
    throw error;
  }
}
