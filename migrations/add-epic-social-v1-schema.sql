-- Migration pour Epic Social V1: Fonctionnalités Sociales - Feed, Suivi et Découverte
-- US-01 : Mise à jour du Schéma de la Base de Données

-- 1. <PERSON><PERSON>er les nouveaux ENUMs
DO $$ BEGIN
    CREATE TYPE profile_visibility AS ENUM ('private', 'users_only', 'public');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE notification_type AS ENUM ('new_follower');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- 2. Ajouter les nouvelles colonnes à la table users
ALTER TABLE users ADD COLUMN IF NOT EXISTS "profile_visibility" profile_visibility NOT NULL DEFAULT 'users_only';
ALTER TABLE users ADD COLUMN IF NOT EXISTS "share_recommendations" BOOLEAN NOT NULL DEFAULT TRUE;
ALTER TABLE users ADD COLUMN IF NOT EXISTS "share_wishlist" BOOLEAN NOT NULL DEFAULT TRUE;
ALTER TABLE users ADD COLUMN IF NOT EXISTS "share_collection" BOOLEAN NOT NULL DEFAULT TRUE;

-- 3. <PERSON><PERSON>er la table followers pour gérer les relations de suivi
CREATE TABLE IF NOT EXISTS followers (
    follower_id TEXT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    following_id TEXT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    created_at TIMESTAMP DEFAULT NOW() NOT NULL,
    PRIMARY KEY (follower_id, following_id)
);

-- 4. Créer la table notifications pour les notifications in-app et email
CREATE TABLE IF NOT EXISTS notifications (
    id TEXT PRIMARY KEY DEFAULT gen_random_uuid(),
    recipient_id TEXT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    actor_id TEXT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    type notification_type NOT NULL,
    is_read BOOLEAN DEFAULT FALSE NOT NULL,
    created_at TIMESTAMP DEFAULT NOW() NOT NULL
);

-- 5. Créer la table recommendation_history pour archiver les recommandations
CREATE TABLE IF NOT EXISTS recommendation_history (
    id BIGSERIAL PRIMARY KEY,
    user_id TEXT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    artist_name TEXT NOT NULL,
    album_title TEXT NOT NULL,
    spotify_album_id TEXT,
    listen_score INTEGER NOT NULL,
    timeframe TEXT NOT NULL,
    generated_at TIMESTAMP DEFAULT NOW() NOT NULL
);

-- 6. Créer des index pour optimiser les performances
CREATE INDEX IF NOT EXISTS idx_users_profile_visibility ON users(profile_visibility);
CREATE INDEX IF NOT EXISTS idx_users_share_recommendations ON users(share_recommendations);

CREATE INDEX IF NOT EXISTS idx_followers_follower_id ON followers(follower_id);
CREATE INDEX IF NOT EXISTS idx_followers_following_id ON followers(following_id);
CREATE INDEX IF NOT EXISTS idx_followers_created_at ON followers(created_at);

CREATE INDEX IF NOT EXISTS idx_notifications_recipient_id ON notifications(recipient_id);
CREATE INDEX IF NOT EXISTS idx_notifications_is_read ON notifications(is_read);
CREATE INDEX IF NOT EXISTS idx_notifications_created_at ON notifications(created_at);

CREATE INDEX IF NOT EXISTS idx_recommendation_history_user_id ON recommendation_history(user_id);
CREATE INDEX IF NOT EXISTS idx_recommendation_history_generated_at ON recommendation_history(generated_at);
CREATE INDEX IF NOT EXISTS idx_recommendation_history_artist_name ON recommendation_history(artist_name);

-- 7. Commentaires pour la documentation
COMMENT ON COLUMN users.profile_visibility IS 'Niveau de visibilité du profil: private, users_only ou public (Epic Social V1)';
COMMENT ON COLUMN users.share_recommendations IS 'Autorise le partage des recommandations dans le feed social (Epic Social V1)';
COMMENT ON COLUMN users.share_wishlist IS 'Autorise le partage de la wishlist sur le profil public (Epic Social V1)';
COMMENT ON COLUMN users.share_collection IS 'Autorise le partage de la collection sur le profil public (Epic Social V1)';

COMMENT ON TABLE followers IS 'Table des relations de suivi entre utilisateurs (Epic Social V1)';
COMMENT ON TABLE notifications IS 'Table des notifications in-app et email (Epic Social V1)';
COMMENT ON TABLE recommendation_history IS 'Archive des recommandations pour l''algorithme de matching (Epic Social V1)'; 