-- Migration: Ajouter les champs enrichis manquants à la table wishlist_items
-- Date: 2025-01-03
-- Description: Ajoute les champs nécessaires pour la persistance complète des données de wishlist
-- Résout le bug où les albums disparaissaient de manière dégradée quand ils n'étaient plus recommandés

-- Ajouter les nouveaux champs manquants à la table wishlist_items
ALTER TABLE wishlist_items 
ADD COLUMN IF NOT EXISTS affiliate_links JSONB,
ADD COLUMN IF NOT EXISTS top_track_name TEXT,
ADD COLUMN IF NOT EXISTS top_track_id TEXT,
ADD COLUMN IF NOT EXISTS top_track_preview_url TEXT,
ADD COLUMN IF NOT EXISTS top_track_listen_score INTEGER,
ADD COLUMN IF NOT EXISTS original_user_name TEXT;

-- Commentaires pour documenter les nouveaux champs
COMMENT ON COLUMN wishlist_items.affiliate_links IS 'Liens d''affiliation Amazon et autres vendeurs (JSON)';
COMMENT ON COLUMN wishlist_items.top_track_name IS 'Nom de la chanson la plus écoutée de l''album';
COMMENT ON COLUMN wishlist_items.top_track_id IS 'ID Spotify de la chanson la plus écoutée';
COMMENT ON COLUMN wishlist_items.top_track_preview_url IS 'URL de prévisualisation de la chanson (30s)';
COMMENT ON COLUMN wishlist_items.top_track_listen_score IS 'Score d''écoute de la chanson la plus écoutée';
COMMENT ON COLUMN wishlist_items.original_user_name IS 'Nom de l''utilisateur d''origine (pour les ajouts depuis profils publics)';

-- Créer un index sur les liens d'affiliation pour améliorer les performances des requêtes
CREATE INDEX IF NOT EXISTS idx_wishlist_items_affiliate_links ON wishlist_items USING GIN (affiliate_links); 