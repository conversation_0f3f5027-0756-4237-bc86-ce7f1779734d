-- Migration: Ajouter les champs albumCoverUrl, spotifyAlbumId et discogsReleaseId à la table wishlist_items
-- Date: 2025-06-30
-- Description: Permet de sauvegarder les données d'album lors de l'ajout depuis les profils publics

-- Ajouter les nouveaux champs à la table wishlist_items
ALTER TABLE wishlist_items 
ADD COLUMN IF NOT EXISTS album_cover_url TEXT,
ADD COLUMN IF NOT EXISTS spotify_album_id TEXT,
ADD COLUMN IF NOT EXISTS discogs_release_id BIGINT;

-- Commentaires pour documenter les nouveaux champs
COMMENT ON COLUMN wishlist_items.album_cover_url IS 'URL de la pochette d''album';
COMMENT ON COLUMN wishlist_items.spotify_album_id IS 'ID Spotify de l''album';
COMMENT ON COLUMN wishlist_items.discogs_release_id IS 'ID Discogs de l''album';
