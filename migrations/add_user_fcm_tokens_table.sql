-- Migration: Ajouter la table user_fcm_tokens pour les notifications push
-- Date: 2025-01-23
-- Epic: 6 - Notifications Utilisateur
-- Description: Table pour stocker les tokens FCM des utilisateurs pour les notifications push

-- Créer la table user_fcm_tokens
CREATE TABLE IF NOT EXISTS user_fcm_tokens (
    id BIGSERIAL PRIMARY KEY,
    "userId" TEXT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    token TEXT NOT NULL,
    "deviceInfo" TEXT,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    "lastUsedAt" TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    UNIQUE("userId", token)
);

-- Ajouter des index pour les performances
CREATE INDEX IF NOT EXISTS idx_user_fcm_tokens_userId ON user_fcm_tokens("userId");
CREATE INDEX IF NOT EXISTS idx_user_fcm_tokens_isActive ON user_fcm_tokens("isActive");
CREATE INDEX IF NOT EXISTS idx_user_fcm_tokens_lastUsedAt ON user_fcm_tokens("lastUsedAt");

-- Ajouter des commentaires pour documenter la table
COMMENT ON TABLE user_fcm_tokens IS 'Tokens FCM pour les notifications push des utilisateurs';
COMMENT ON COLUMN user_fcm_tokens."userId" IS 'Référence vers l''utilisateur propriétaire du token';
COMMENT ON COLUMN user_fcm_tokens.token IS 'Token FCM pour les notifications push';
COMMENT ON COLUMN user_fcm_tokens."deviceInfo" IS 'Informations sur l''appareil (User Agent)';
COMMENT ON COLUMN user_fcm_tokens."isActive" IS 'Indique si le token est actif et utilisable';
COMMENT ON COLUMN user_fcm_tokens."createdAt" IS 'Date de création du token';
COMMENT ON COLUMN user_fcm_tokens."lastUsedAt" IS 'Dernière utilisation du token pour l''envoi de notifications';
