-- Migration pour Epic 16: Partage Public des Recommandations
-- Ajouter les colonnes pour gérer la visibilité publique des listes de recommandations

-- Ajouter la colonne publicListEnabled (par défaut false pour la confidentialité)
ALTER TABLE users ADD COLUMN IF NOT EXISTS "publicListEnabled" BOOLEAN NOT NULL DEFAULT FALSE;

-- Ajouter la colonne publicListId avec un UUID unique
ALTER TABLE users ADD COLUMN IF NOT EXISTS "publicListId" TEXT UNIQUE DEFAULT gen_random_uuid();

-- Créer un index sur publicListId pour les performances lors des requêtes publiques
CREATE INDEX IF NOT EXISTS idx_users_publicListId ON users("publicListId");

-- Commentaires pour la documentation
COMMENT ON COLUMN users."publicListEnabled" IS 'Indique si la liste de recommandations de l''utilisateur est publique (Epic 16)';
COMMENT ON COLUMN users."publicListId" IS 'Identifiant unique pour l''URL publique de la liste de recommandations (Epic 16)';
