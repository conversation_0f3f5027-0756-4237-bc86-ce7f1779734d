-- Migration pour ajouter le tracking de délivrabilité des emails
-- Epic 6 - Optimisation de la délivrabilité des emails

-- Ajouter les colonnes de tracking des emails à la table users
ALTER TABLE users 
ADD COLUMN IF NOT EXISTS last_email_bounce TIMESTAMP,
ADD COLUMN IF NOT EXISTS email_bounce_reason TEXT,
ADD COLUMN IF NOT EXISTS last_email_complaint TIMESTAMP,
ADD COLUMN IF NOT EXISTS email_complaint_reason TEXT,
ADD COLUMN IF NOT EXISTS last_email_delivered TIMESTAMP,
ADD COLUMN IF NOT EXISTS last_email_opened TIMESTAMP,
ADD COLUMN IF NOT EXISTS last_email_clicked TIMESTAMP,
ADD COLUMN IF NOT EXISTS email_deliverability_score INTEGER DEFAULT 100;

-- Créer une table pour tracker les événements d'email détaillés
CREATE TABLE IF NOT EXISTS email_events (
  id SERIAL PRIMARY KEY,
  user_id TEXT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  email_id TEXT NOT NULL, -- ID de l'email depuis Resend
  event_type TEXT NOT NULL, -- sent, delivered, bounced, complained, opened, clicked
  event_data JSONB, -- Données supplémentaires de l'événement
  created_at TIMESTAMP DEFAULT NOW(),
  
  -- Index pour les requêtes fréquentes
  INDEX idx_email_events_user_id (user_id),
  INDEX idx_email_events_type (event_type),
  INDEX idx_email_events_created_at (created_at)
);

-- Créer une table pour les domaines en liste noire
CREATE TABLE IF NOT EXISTS email_domain_blacklist (
  id SERIAL PRIMARY KEY,
  domain TEXT UNIQUE NOT NULL,
  reason TEXT,
  added_at TIMESTAMP DEFAULT NOW(),
  is_active BOOLEAN DEFAULT TRUE
);

-- Insérer les domaines temporaires connus
INSERT INTO email_domain_blacklist (domain, reason) VALUES
('tempmail.org', 'Domaine d''email temporaire'),
('10minutemail.com', 'Domaine d''email temporaire'),
('guerrillamail.com', 'Domaine d''email temporaire'),
('mailinator.com', 'Domaine d''email temporaire'),
('throwaway.email', 'Domaine d''email temporaire'),
('temp-mail.org', 'Domaine d''email temporaire'),
('yopmail.com', 'Domaine d''email temporaire')
ON CONFLICT (domain) DO NOTHING;

-- Créer une table pour les métriques de délivrabilité globales
CREATE TABLE IF NOT EXISTS email_deliverability_metrics (
  id SERIAL PRIMARY KEY,
  date DATE NOT NULL,
  emails_sent INTEGER DEFAULT 0,
  emails_delivered INTEGER DEFAULT 0,
  emails_bounced INTEGER DEFAULT 0,
  emails_complained INTEGER DEFAULT 0,
  emails_opened INTEGER DEFAULT 0,
  emails_clicked INTEGER DEFAULT 0,
  deliverability_rate DECIMAL(5,2), -- Pourcentage de délivrabilité
  open_rate DECIMAL(5,2), -- Taux d'ouverture
  click_rate DECIMAL(5,2), -- Taux de clic
  created_at TIMESTAMP DEFAULT NOW(),
  
  UNIQUE(date)
);

-- Créer une vue pour les statistiques de délivrabilité par utilisateur
CREATE OR REPLACE VIEW user_email_stats AS
SELECT 
  u.id,
  u.email,
  u.email_notifications_enabled,
  u.email_deliverability_score,
  u.last_email_bounce,
  u.email_bounce_reason,
  u.last_email_complaint,
  u.last_email_delivered,
  u.last_email_opened,
  u.last_email_clicked,
  COUNT(CASE WHEN ee.event_type = 'sent' THEN 1 END) as total_emails_sent,
  COUNT(CASE WHEN ee.event_type = 'delivered' THEN 1 END) as total_emails_delivered,
  COUNT(CASE WHEN ee.event_type = 'bounced' THEN 1 END) as total_emails_bounced,
  COUNT(CASE WHEN ee.event_type = 'opened' THEN 1 END) as total_emails_opened,
  COUNT(CASE WHEN ee.event_type = 'clicked' THEN 1 END) as total_emails_clicked,
  CASE 
    WHEN COUNT(CASE WHEN ee.event_type = 'sent' THEN 1 END) > 0 
    THEN ROUND(
      (COUNT(CASE WHEN ee.event_type = 'delivered' THEN 1 END)::DECIMAL / 
       COUNT(CASE WHEN ee.event_type = 'sent' THEN 1 END)) * 100, 2
    )
    ELSE NULL 
  END as user_deliverability_rate,
  CASE 
    WHEN COUNT(CASE WHEN ee.event_type = 'delivered' THEN 1 END) > 0 
    THEN ROUND(
      (COUNT(CASE WHEN ee.event_type = 'opened' THEN 1 END)::DECIMAL / 
       COUNT(CASE WHEN ee.event_type = 'delivered' THEN 1 END)) * 100, 2
    )
    ELSE NULL 
  END as user_open_rate
FROM users u
LEFT JOIN email_events ee ON u.id = ee.user_id
GROUP BY u.id, u.email, u.email_notifications_enabled, u.email_deliverability_score,
         u.last_email_bounce, u.email_bounce_reason, u.last_email_complaint,
         u.last_email_delivered, u.last_email_opened, u.last_email_clicked;

-- Fonction pour calculer le score de délivrabilité d'un utilisateur
CREATE OR REPLACE FUNCTION calculate_user_deliverability_score(user_id_param TEXT)
RETURNS INTEGER AS $$
DECLARE
  base_score INTEGER := 100;
  bounce_penalty INTEGER := 0;
  complaint_penalty INTEGER := 0;
  engagement_bonus INTEGER := 0;
  final_score INTEGER;
BEGIN
  -- Pénalité pour les bounces récents (derniers 30 jours)
  SELECT COUNT(*) * 10 INTO bounce_penalty
  FROM email_events 
  WHERE user_id = user_id_param 
    AND event_type = 'bounced' 
    AND created_at > NOW() - INTERVAL '30 days';
  
  -- Pénalité sévère pour les plaintes
  SELECT COUNT(*) * 50 INTO complaint_penalty
  FROM email_events 
  WHERE user_id = user_id_param 
    AND event_type = 'complained';
  
  -- Bonus pour l'engagement (ouvertures et clics récents)
  SELECT LEAST(20, COUNT(*) * 2) INTO engagement_bonus
  FROM email_events 
  WHERE user_id = user_id_param 
    AND event_type IN ('opened', 'clicked')
    AND created_at > NOW() - INTERVAL '30 days';
  
  final_score := base_score - bounce_penalty - complaint_penalty + engagement_bonus;
  
  RETURN GREATEST(0, LEAST(100, final_score));
END;
$$ LANGUAGE plpgsql;

-- Trigger pour mettre à jour automatiquement le score de délivrabilité
CREATE OR REPLACE FUNCTION update_deliverability_score_trigger()
RETURNS TRIGGER AS $$
BEGIN
  UPDATE users 
  SET email_deliverability_score = calculate_user_deliverability_score(NEW.user_id)
  WHERE id = NEW.user_id;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER email_events_update_score
  AFTER INSERT ON email_events
  FOR EACH ROW
  EXECUTE FUNCTION update_deliverability_score_trigger();

-- Commentaires pour la documentation
COMMENT ON TABLE email_events IS 'Événements détaillés des emails pour le tracking de délivrabilité';
COMMENT ON TABLE email_domain_blacklist IS 'Domaines d''email en liste noire (temporaires, spam, etc.)';
COMMENT ON TABLE email_deliverability_metrics IS 'Métriques globales de délivrabilité par jour';
COMMENT ON VIEW user_email_stats IS 'Statistiques de délivrabilité par utilisateur';
COMMENT ON FUNCTION calculate_user_deliverability_score IS 'Calcule le score de délivrabilité d''un utilisateur (0-100)';
