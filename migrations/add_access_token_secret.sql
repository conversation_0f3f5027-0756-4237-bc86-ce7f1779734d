-- Migration pour ajouter le champ access_token_secret à la table accounts
-- Nécessaire pour le stockage des tokens OAuth 1.0a (Discogs)

-- Ajouter la colonne access_token_secret si elle n'existe pas déjà
ALTER TABLE accounts 
ADD COLUMN IF NOT EXISTS access_token_secret TEXT;

-- Migrer les données existantes de oauth_token_secret vers access_token_secret
-- pour les comptes Discogs existants
UPDATE accounts 
SET access_token_secret = session_state 
WHERE provider = 'discogs' 
  AND session_state IS NOT NULL 
  AND access_token_secret IS NULL;

-- Nettoyer les anciennes données temporaires Discogs si elles existent
DELETE FROM accounts 
WHERE provider = 'discogs_temp';

-- Commentaire pour documentation
COMMENT ON COLUMN accounts.access_token_secret IS 'Token secret pour OAuth 1.0a (utilisé par Discogs)';
