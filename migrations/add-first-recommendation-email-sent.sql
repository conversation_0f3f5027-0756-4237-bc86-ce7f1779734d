-- Migration pour ajouter le champ firstRecommendationEmailSent à la table users
-- Ce champ permet de tracker si l'utilisateur a déjà reçu son premier email de recommandations

-- Ajouter la colonne avec une valeur par défaut false
ALTER TABLE users 
ADD COLUMN "firstRecommendationEmailSent" BOOLEAN NOT NULL DEFAULT false;

-- Mettre à jour les utilisateurs existants qui ont déjà des recommandations
-- On considère qu'ils ont déjà reçu leur premier email pour éviter de les spammer
UPDATE users 
SET "firstRecommendationEmailSent" = true 
WHERE id IN (
  SELECT DISTINCT "userId" 
  FROM recommendations 
  WHERE "generatedAt" < NOW() - INTERVAL '1 day'
);

-- Commentaire pour expliquer la logique
COMMENT ON COLUMN users."firstRecommendationEmailSent" IS 'Indique si l''utilisateur a déjà reçu son premier email de recommandations après la génération initiale';
