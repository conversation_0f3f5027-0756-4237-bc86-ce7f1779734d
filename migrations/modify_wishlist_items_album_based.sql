-- Migration: Modifier wishlist_items pour être basé sur l'album plutôt que sur la recommandation spécifique
-- Date: 2025-06-22
-- Description: Permet d'avoir un album en wishlist pour tous les timeframes

-- Étape 1: Créer une nouvelle table temporaire avec la nouvelle structure
CREATE TABLE IF NOT EXISTS wishlist_items_new (
    "userId" TEXT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    "artistName" TEXT NOT NULL,
    "albumTitle" TEXT NOT NULL,
    "createdAt" TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    PRIMARY KEY ("userId", "artistName", "albumTitle")
);

-- Étape 2: Migrer les données existantes en évitant les doublons
-- On prend la première occurrence de chaque album par utilisateur
INSERT INTO wishlist_items_new ("userId", "artistName", "albumTitle", "createdAt")
SELECT DISTINCT ON (wi."userId", r."artistName", r."albumTitle")
    wi."userId",
    r."artistName",
    r."albumTitle",
    wi."createdAt"
FROM wishlist_items wi
INNER JOIN recommendations r ON wi."recommendationId" = r.id
ORDER BY wi."userId", r."artistName", r."albumTitle", wi."createdAt" ASC;

-- Étape 3: Supprimer l'ancienne table
DROP TABLE IF EXISTS wishlist_items;

-- Étape 4: Renommer la nouvelle table
ALTER TABLE wishlist_items_new RENAME TO wishlist_items;

-- Étape 5: Créer les index pour les performances
CREATE INDEX IF NOT EXISTS idx_wishlist_items_userId ON wishlist_items("userId");
CREATE INDEX IF NOT EXISTS idx_wishlist_items_createdAt ON wishlist_items("createdAt" DESC);
CREATE INDEX IF NOT EXISTS idx_wishlist_items_album ON wishlist_items("artistName", "albumTitle");

-- Commentaires pour la documentation
COMMENT ON TABLE wishlist_items IS 'Table de liaison pour la fonctionnalité de wishlist basée sur l''album - Epic 9 modifié';
COMMENT ON COLUMN wishlist_items."userId" IS 'Référence vers l''utilisateur propriétaire de la wishlist';
COMMENT ON COLUMN wishlist_items."artistName" IS 'Nom de l''artiste de l''album en wishlist';
COMMENT ON COLUMN wishlist_items."albumTitle" IS 'Titre de l''album en wishlist';
COMMENT ON COLUMN wishlist_items."createdAt" IS 'Date d''ajout de l''album à la wishlist';
