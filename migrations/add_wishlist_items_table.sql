-- Migration: Ajouter la table wishlist_items pour l'Epic 9
-- Date: 2025-06-22
-- Description: Création de la table de liaison pour la fonctionnalité de wishlist

-- Créer la table wishlist_items
CREATE TABLE IF NOT EXISTS wishlist_items (
    "userId" TEXT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    "recommendationId" BIGINT NOT NULL REFERENCES recommendations(id) ON DELETE CASCADE,
    "createdAt" TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    PRIMARY KEY ("userId", "recommendationId")
);

-- Créer les index pour optimiser les performances
CREATE INDEX IF NOT EXISTS idx_wishlist_items_userId ON wishlist_items("userId");
CREATE INDEX IF NOT EXISTS idx_wishlist_items_createdAt ON wishlist_items("createdAt" DESC);

-- Activer Row Level Security
ALTER TABLE wishlist_items ENABLE ROW LEVEL SECURITY;

-- <PERSON><PERSON><PERSON> les politiques RLS
CREATE POLICY "Users can view their own wishlist items"
ON wishlist_items FOR SELECT
USING (auth.uid()::text = "userId");

CREATE POLICY "Users can insert their own wishlist items"
ON wishlist_items FOR INSERT
WITH CHECK (auth.uid()::text = "userId");

CREATE POLICY "Users can delete their own wishlist items"
ON wishlist_items FOR DELETE
USING (auth.uid()::text = "userId");

-- Commentaires pour la documentation
COMMENT ON TABLE wishlist_items IS 'Table de liaison pour la fonctionnalité de wishlist - Epic 9';
COMMENT ON COLUMN wishlist_items."userId" IS 'Référence vers l''utilisateur propriétaire de la wishlist';
COMMENT ON COLUMN wishlist_items."recommendationId" IS 'Référence vers la recommandation sauvegardée';
COMMENT ON COLUMN wishlist_items."createdAt" IS 'Date d''ajout de l''item à la wishlist';
