-- Migration pour ajouter la colonne manquante email_notifications_enabled
-- Cette colonne était référencée dans add-email-deliverability-tracking.sql mais n'était pas créée

-- Ajouter la colonne manquante à la table users
ALTER TABLE users 
ADD COLUMN IF NOT EXISTS email_notifications_enabled BOOLEAN DEFAULT TRUE;

-- Mettre à jour les utilisateurs existants pour activer les notifications par défaut
UPDATE users 
SET email_notifications_enabled = TRUE 
WHERE email_notifications_enabled IS NULL;

-- Commentaire pour la documentation
COMMENT ON COLUMN users.email_notifications_enabled IS 'Contrôle si l''utilisateur reçoit les notifications par email'; 