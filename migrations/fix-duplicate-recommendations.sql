-- Migration pour corriger les doublons dans la table recommendations
-- et ajouter une contrainte unique pour les éviter à l'avenir

-- Étape 1: Identifier et supprimer les doublons en gardant le plus récent
-- (basé sur userId, spotifyAlbumId, timeframe)

-- Créer une table temporaire avec les IDs des recommandations à conserver
CREATE TEMP TABLE recommendations_to_keep AS
SELECT DISTINCT ON ("userId", "spotifyAlbumId", "timeframe") id
FROM recommendations
WHERE "spotifyAlbumId" IS NOT NULL
ORDER BY "userId", "spotifyAlbumId", "timeframe", "generatedAt" DESC;

-- Supprimer les doublons (garder seulement les IDs dans la table temporaire)
DELETE FROM recommendations
WHERE "spotifyAlbumId" IS NOT NULL
  AND id NOT IN (SELECT id FROM recommendations_to_keep);

-- Étape 2: <PERSON><PERSON>rer les recommandations sans spotifyAlbumId
-- <PERSON>ur celles-ci, on utilise artistName + albumTitle + timeframe comme critère d'unicité
CREATE TEMP TABLE recommendations_no_spotify_to_keep AS
SELECT DISTINCT ON ("userId", "artistName", "albumTitle", "timeframe") id
FROM recommendations
WHERE "spotifyAlbumId" IS NULL
ORDER BY "userId", "artistName", "albumTitle", "timeframe", "generatedAt" DESC;

-- Supprimer les doublons pour les recommandations sans spotifyAlbumId
DELETE FROM recommendations
WHERE "spotifyAlbumId" IS NULL
  AND id NOT IN (SELECT id FROM recommendations_no_spotify_to_keep);

-- Étape 3: Ajouter la contrainte unique pour éviter les futurs doublons
-- Note: Cette contrainte ne s'applique qu'aux recommandations avec spotifyAlbumId
ALTER TABLE recommendations 
ADD CONSTRAINT unique_user_spotify_album_timeframe 
UNIQUE ("userId", "spotifyAlbumId", "timeframe");

-- Étape 4: Créer un index partiel pour les recommandations sans spotifyAlbumId
-- pour améliorer les performances et détecter les doublons potentiels
CREATE UNIQUE INDEX IF NOT EXISTS unique_user_artist_album_timeframe_no_spotify
ON recommendations ("userId", "artistName", "albumTitle", "timeframe")
WHERE "spotifyAlbumId" IS NULL;

-- Étape 5: Afficher les statistiques de nettoyage
DO $$
DECLARE
    total_count INTEGER;
    spotify_count INTEGER;
    no_spotify_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO total_count FROM recommendations;
    SELECT COUNT(*) INTO spotify_count FROM recommendations WHERE "spotifyAlbumId" IS NOT NULL;
    SELECT COUNT(*) INTO no_spotify_count FROM recommendations WHERE "spotifyAlbumId" IS NULL;
    
    RAISE NOTICE 'Nettoyage terminé:';
    RAISE NOTICE '- Total des recommandations: %', total_count;
    RAISE NOTICE '- Avec Spotify ID: %', spotify_count;
    RAISE NOTICE '- Sans Spotify ID: %', no_spotify_count;
END $$;
