-- Migration pour Epic 17: Profil public granulaire et social
-- Ajouter les colonnes pour gérer la visibilité granulaire du profil

-- Ajouter la colonne publicProfileEnabled (par défaut true selon les specs)
ALTER TABLE users ADD COLUMN IF NOT EXISTS "publicProfileEnabled" BOOLEAN NOT NULL DEFAULT TRUE;

-- Ajouter la colonne publicRecommendationsEnabled (par défaut true)
ALTER TABLE users ADD COLUMN IF NOT EXISTS "publicRecommendationsEnabled" BOOLEAN NOT NULL DEFAULT TRUE;

-- Ajouter la colonne publicWishlistEnabled (par défaut false)
ALTER TABLE users ADD COLUMN IF NOT EXISTS "publicWishlistEnabled" BOOLEAN NOT NULL DEFAULT FALSE;

-- Ajouter la colonne publicCollectionEnabled (par défaut false)
ALTER TABLE users ADD COLUMN IF NOT EXISTS "publicCollectionEnabled" BOOLEAN NOT NULL DEFAULT FALSE;

-- <PERSON><PERSON><PERSON> des index pour les performances lors des requêtes publiques
CREATE INDEX IF NOT EXISTS idx_users_publicProfileEnabled ON users("publicProfileEnabled");
CREATE INDEX IF NOT EXISTS idx_users_publicRecommendationsEnabled ON users("publicRecommendationsEnabled");

-- Commentaires pour la documentation
COMMENT ON COLUMN users."publicProfileEnabled" IS 'Indique si le profil de l''utilisateur est visible par les autres membres (Epic 17)';
COMMENT ON COLUMN users."publicRecommendationsEnabled" IS 'Indique si les recommandations sont visibles sur le profil public (Epic 17)';
COMMENT ON COLUMN users."publicWishlistEnabled" IS 'Indique si la wishlist est visible sur le profil public (Epic 17)';
COMMENT ON COLUMN users."publicCollectionEnabled" IS 'Indique si la collection Discogs est visible sur le profil public (Epic 17)';
