-- Migration: Correction des tables NextAuth pour l'environnement staging
-- Date: 2025-01-06
-- Description: Recréation des tables d'authentification NextAuth avec la structure correcte
-- Problème résolu: Erreur de callback lors de la connexion OAuth

-- Supprimer et recréer les tables d'authentification NextAuth
-- Attention: cela supprime toutes les sessions actives

DROP TABLE IF EXISTS public.sessions CASCADE;
DROP TABLE IF EXISTS public.accounts CASCADE;
DROP TABLE IF EXISTS public.verification_tokens CASCADE;

-- Recréer la table accounts avec la structure NextAuth correcte
CREATE TABLE public.accounts (
  "userId" TEXT NOT NULL,
  type TEXT NOT NULL,
  provider TEXT NOT NULL,
  "providerAccountId" TEXT NOT NULL,
  refresh_token TEXT,
  access_token TEXT,
  access_token_secret TEXT,
  expires_at INTEGER,
  token_type TEXT,
  scope TEXT,
  id_token TEXT,
  session_state TEXT,
  
  PRIMARY KEY (provider, "providerAccountId"),
  FOREI<PERSON><PERSON> KEY ("userId") REFERENCES public.users(id) ON DELETE CASCADE
);

-- Recréer la table sessions avec la structure NextAuth correcte
CREATE TABLE public.sessions (
  "sessionToken" TEXT PRIMARY KEY,
  "userId" TEXT NOT NULL,
  expires TIMESTAMP NOT NULL,
  
  FOREIGN KEY ("userId") REFERENCES public.users(id) ON DELETE CASCADE
);

-- Recréer la table verification_tokens
CREATE TABLE public.verification_tokens (
  identifier TEXT NOT NULL,
  token TEXT NOT NULL,
  expires TIMESTAMP NOT NULL,
  
  PRIMARY KEY (identifier, token),
  UNIQUE (token)
);

-- Recréer les comptes utilisateurs existants (avec des tokens temporaires)
INSERT INTO public.accounts (
  "userId",
  type,
  provider,
  "providerAccountId",
  access_token,
  token_type,
  scope
) VALUES 
(
  '9dcf3ed0-525a-40a6-b91b-210fa3459226',
  'oauth',
  'spotify',
  '**********',
  'temp_token_staging',
  'Bearer',
  'user-read-private user-read-email user-top-read user-read-recently-played'
),
(
  '9dcf3ed0-525a-40a6-b91b-210fa3459226',
  'oauth',
  'discogs',
  '2494720',
  'temp_token_staging',
  'Bearer',
  'read'
),
(
  '62e1c3b8-90e3-411a-8ffc-************',
  'oauth',
  'spotify',
  'mrn.g',
  'temp_token_staging',
  'Bearer',
  'user-read-private user-read-email user-top-read user-read-recently-played'
);

-- Commentaires pour la documentation
COMMENT ON TABLE public.accounts IS 'Comptes OAuth des utilisateurs (NextAuth.js)';
COMMENT ON TABLE public.sessions IS 'Sessions utilisateur actives (NextAuth.js)';
COMMENT ON TABLE public.verification_tokens IS 'Tokens de vérification email (NextAuth.js)';

-- Note: Les tokens d'accès réels devront être régénérés lors de la prochaine connexion OAuth
