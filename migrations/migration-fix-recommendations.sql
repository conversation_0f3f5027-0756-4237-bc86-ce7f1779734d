-- Migration pour corriger la table recommendations
-- Renommer listenCount en listenScore et s'assurer que la table a la bonne structure

-- Supprimer la table existante si elle existe (pour éviter les conflits)
DROP TABLE IF EXISTS recommendations CASCADE;

-- Recréer la table avec la bonne structure
CREATE TABLE recommendations (
    id BIGSERIAL PRIMARY KEY,
    "userId" UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    "artistName" TEXT NOT NULL,
    "albumTitle" TEXT NOT NULL,
    "albumCoverUrl" TEXT,
    "discogsReleaseId" BIGINT,
    "spotifyAlbumId" TEXT,
    "listenScore" INTEGER NOT NULL,
    "timeframe" TEXT NOT NULL,
    "generatedAt" TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    "isOwned" BOOLEAN NOT NULL DEFAULT FALSE,
    "affiliateLinks" JSONB
);

-- Créer l'index pour les performances
CREATE INDEX idx_recommendations_userId_generatedAt ON recommendations("userId", "generatedAt" DESC);

-- Activer Row Level Security
ALTER TABLE recommendations ENABLE ROW LEVEL SECURITY;

-- Politique RLS pour que les utilisateurs voient leurs propres recommandations
CREATE POLICY "Users can view own recommendations" ON recommendations FOR SELECT USING (auth.uid() = "userId");

-- Politique pour permettre l'insertion (pour le système de recommandations)
CREATE POLICY "System can insert recommendations" ON recommendations FOR INSERT WITH CHECK (true);

-- Politique pour permettre la suppression (pour le nettoyage)
CREATE POLICY "System can delete recommendations" ON recommendations FOR DELETE USING (true);

-- Politique pour permettre la mise à jour (pour marquer isOwned)
CREATE POLICY "System can update recommendations" ON recommendations FOR UPDATE USING (true);
